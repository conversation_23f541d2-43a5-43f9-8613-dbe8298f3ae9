import { nextTick } from 'vue'

/**
 * 滚动到顶部的组合式函数
 * @param {Object} options 配置选项
 * @param {string} options.containerSelector 容器选择器，默认为自动检测dialog中的滚动容器
 * @param {string} options.behavior 滚动行为，'smooth' 或 'auto'
 * @param {number} options.delay 延迟时间(ms)
 */
export function useScrollToTop(options = {}) {
  const { containerSelector = null, behavior = 'auto', delay = 0 } = options

  /**
   * 滚动到顶部
   * @param {string} customSelector 自定义选择器
   */
  const scrollToTop = async (customSelector = null) => {
    await nextTick()

    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay))
    }

    let scrollContainer = null
    const selector = customSelector || containerSelector

    if (selector) {
      scrollContainer = document.querySelector(selector)
    } else {
      // 自动检测dialog中的滚动容器
      const dialogSelectors = [
        '.el-dialog .max-h-\\[70vh\\]', // 匹配当前项目的对话框容器
        '.el-dialog .overflow-y-auto',
        '.el-dialog__body .overflow-y-auto',
        '.el-dialog__body',
        '.dialog-scroll-container',
      ]

      for (const dialogSelector of dialogSelectors) {
        scrollContainer = document.querySelector(dialogSelector)
        if (scrollContainer) break
      }
    }

    if (scrollContainer) {
      if (behavior === 'smooth' && scrollContainer.scrollTo) {
        scrollContainer.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      } else {
        scrollContainer.scrollTop = 0
      }
    } else {
      // 如果没找到容器，则滚动window
      if (behavior === 'smooth') {
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      } else {
        window.scrollTo(0, 0)
      }
    }
  }

  /**
   * 滚动到指定位置
   * @param {number} top 滚动位置
   * @param {string} customSelector 自定义选择器
   */
  const scrollTo = async (top = 0, customSelector = null) => {
    await nextTick()

    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay))
    }

    const selector = customSelector || containerSelector
    let scrollContainer = null

    if (selector) {
      scrollContainer = document.querySelector(selector)
    }

    if (scrollContainer) {
      if (behavior === 'smooth' && scrollContainer.scrollTo) {
        scrollContainer.scrollTo({
          top,
          behavior: 'smooth',
        })
      } else {
        scrollContainer.scrollTop = top
      }
    } else {
      if (behavior === 'smooth') {
        window.scrollTo({
          top,
          behavior: 'smooth',
        })
      } else {
        window.scrollTo(0, top)
      }
    }
  }

  /**
   * 滚动到指定元素
   * @param {string} elementSelector 元素选择器
   * @param {Object} scrollOptions 滚动选项
   */
  const scrollToElement = async (elementSelector, scrollOptions = {}) => {
    await nextTick()

    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay))
    }

    const element = document.querySelector(elementSelector)
    if (element) {
      element.scrollIntoView({
        behavior: behavior,
        block: 'start',
        inline: 'nearest',
        ...scrollOptions,
      })
    }
  }

  return {
    scrollToTop,
    scrollTo,
    scrollToElement,
  }
}

/**
 * 专门为dialog设计的快捷函数
 */
export function useDialogScrollToTop(options = {}) {
  return useScrollToTop({
    containerSelector: '.el-dialog .max-h-\\[70vh\\]',
    delay: 100, // dialog打开需要一点时间
    ...options,
  })
}
