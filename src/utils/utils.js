import { format, parseISO, addDays, subDays, getDay } from 'date-fns'
import { ElMessage } from 'element-plus'
import { formatDateRanges } from '@/utils/dateUtils.js'
// 文件上传相关工具函数

/**
 * 从URL中提取文件名
 * @param {string} url - 文件URL
 * @returns {string} 文件名
 */
export function url2pathname(url) {
  return url.replace(/^https?:\/\/[^/]+(?::\d+)?\//, '')
}
/**
 * 从URL中提取文件名（支持带查询参数的URL）
 * @param {string} url - 文件URL
 * @param {number} maxLength - 最大显示长度，默认30
 * @returns {string} 文件名
 */
export function url2filename(url, maxLength = 40) {
  if (!url) return ''

  // 去除查询参数和锚点
  const urlWithoutParams = url.split('?')[0].split('#')[0]

  // 提取文件名
  let filename = urlWithoutParams.split('/').pop()

  // 解码 URL 编码的字符
  try {
    filename = decodeURIComponent(filename)
  } catch (e) {
    // 如果解码失败，使用原始文件名
    console.warn('URL解码失败:', e)
  }

  // 如果文件名过长，显示省略号
  if (filename && filename.length > maxLength) {
    const extension = filename.split('.').pop()
    const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'))
    
    if (extension && nameWithoutExt) {
      // 有扩展名的情况：保留扩展名，中间显示省略号
      const maxNameLength = maxLength - extension.length - 4 // 4 = "..." + "."
      const truncatedName = nameWithoutExt.substring(0, maxNameLength)
      return `${truncatedName}...${extension}`
    } else {
      // 没有扩展名的情况：直接截断
      return filename.substring(0, maxLength - 3) + '...'
    }
  }

  return filename
}
export function openFileLink(filename) {
  window.open(addFileUrlPrefix(filename), '_blank')
}
export function addFileUrlPrefix(url) {
  if (url.startsWith('http')) {
    return url
  } else {
    return `${import.meta.env.VITE_API_BASE_URL}/${url}`
  }
}

// 日期相关工具函数

/**
 * 格式化日期为 YYYY-MM-DD 字符串
 * @param {Date|string} date - 日期对象或日期字符串
 * @returns {string} 格式化的日期字符串
 */
export function formatDateString(date = new Date()) {
  if (typeof date === 'string') {
    return format(parseISO(date), 'yyyy-MM-dd')
  }
  return format(date, 'yyyy-MM-dd')
}

/**
 * 获取今天的日期字符串
 * @returns {string} 今天的日期 YYYY-MM-DD
 */
export function getTodayString() {
  return format(new Date(), 'yyyy-MM-dd')
}

/**
 * 获取 N 天前的日期字符串
 * @param {number} days - 天数
 * @returns {string} N天前的日期 YYYY-MM-DD
 */
export function getDaysAgoString(days) {
  return format(subDays(new Date(), days), 'yyyy-MM-dd')
}

/**
 * 获取 N 天后的日期字符串
 * @param {number} days - 天数
 * @returns {string} N天后的日期 YYYY-MM-DD
 */
export function getDaysLaterString(days) {
  return format(addDays(new Date(), days), 'yyyy-MM-dd')
}

/**
 * 获取星期几
 * @param {Date|string} date - 日期对象或日期字符串
 * @returns {string} 星期几
 */
export function getWeekDay(date) {
  const dateObj = typeof date === 'string' ? parseISO(date) : date
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekDays[getDay(dateObj)]
}

/**
 * 获取下一个可用日期（不在禁用日期列表中）
 * @param {Array} disabledDates - 禁用日期列表 ['YYYY-MM-DD', ...]
 * @param {string} startDate - 起始日期，默认为今天
 * @param {number} maxDays - 最大查找天数，默认30天
 * @returns {string} 下一个可用日期 YYYY-MM-DD
 */
export function getNextAvailableDate(disabledDates = [], startDate = null, maxDays = 30) {
  let currentDate = startDate ? parseISO(startDate) : new Date()

  for (let i = 0; i < maxDays; i++) {
    const dateStr = format(currentDate, 'yyyy-MM-dd')
    if (!disabledDates.includes(dateStr)) {
      return dateStr
    }
    currentDate = addDays(currentDate, 1)
  }

  // 如果30天内都找不到可用日期，返回今天
  return getTodayString()
}

/**
 * 格式化布尔值显示
 * @param {boolean} value - 布尔值
 * @returns {string} 格式化后的字符串
 */
export const formatBoolean = (value) => {
  return value ? '是' : '否'
}

/**
 * 格式化数值显示
 * @param {number|string} value - 数值
 * @param {string} unit - 单位
 * @returns {string} 格式化后的字符串
 */
export const formatNumber = (value, unit = '') => {
  return value ? `${value}${unit}` : '-'
}

/**
 * 格式化文本显示
 * @param {string} value - 文本值
 * @param {string} fallback - 默认值
 * @returns {string} 格式化后的字符串
 */
export const formatText = (value, fallback = '-') => {
  return value || fallback
}

/**
 * 下载文件
 * @param {string} url - 文件URL
 * @param {string} filename - 文件名
 * @param {boolean} openInNewTab - 是否在新窗口打开（备用方案）
 */
export const downloadFile = (url, filename, openInNewTab = false) => {
  try {
    if (openInNewTab) {
      // 在新窗口打开文件
      window.open(url, '_blank')
      return
    }

    // 创建隐藏的可下载链接
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', filename || 'download')
    link.style.display = 'none'

    // 添加到DOM并触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
  } catch (error) {
    console.error('下载文件失败:', error)
    // 尝试备用方案：在新窗口打开
    try {
      window.open(url, '_blank')
    } catch (fallbackError) {
      console.error('备用下载方案也失败:', fallbackError)
      throw new Error('文件下载失败，请手动访问链接下载')
    }
  }
}

/**
 * 获取完整的文件URL
 * @param {string} relativePath - 相对路径
 * @returns {string} 完整的URL
 */
export const getFullFileUrl = (relativePath) => {
  if (!relativePath) return ''

  // 如果已经是完整的URL，直接返回
  if (relativePath.startsWith('http')) {
    return relativePath
  }

  // 构建完整的URL
  const baseUrl = window.location.origin
  return `${baseUrl}${relativePath}`
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取文件扩展名（支持URL和文件名）
 * @param {string} filename - 文件名或URL
 * @returns {string} 文件扩展名
 */
export const getFileExtension = (filename) => {
  if (!filename) return ''

  // 如果是URL，先提取文件名
  const actualFilename = filename.includes('/') ? url2filename(filename) : filename

  const parts = actualFilename.split('.')
  return parts.length > 1 ? parts.pop().toLowerCase() : ''
}

/**
 * 检查是否为图片文件（支持URL和文件名）
 * @param {string} fileUrlOrName - 文件URL或文件名
 * @returns {boolean} 是否为图片
 */
export const isImageFile = (fileUrlOrName) => {
  if (!fileUrlOrName) return false

  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']
  const ext = getFileExtension(fileUrlOrName)
  return imageExtensions.includes(ext)
}

/**
 * 检查是否为PDF文件
 * @param {string} filename - 文件名
 * @returns {boolean} 是否为PDF
 */
export const isPdfFile = (filename) => {
  return getFileExtension(filename) === 'pdf'
}

/**
 * 生成随机字符串
 * @param {number} length - 长度
 * @returns {string} 随机字符串
 */
export const generateRandomString = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, limit) => {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof Array) return obj.map((item) => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 验证手机号
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
export const validatePhone = (phone) => {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email - 邮箱
 * @returns {boolean} 是否有效
 */
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证身份证号
 * @param {string} idCard - 身份证号
 * @returns {boolean} 是否有效
 */
export const validateIdCard = (idCard) => {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

export const showErrorTip = (error) => {
  if (error === 'cancel') return
  console.log('showErrorTip error', error)
  let errorMsg = ''
  if (error.message) {
    errorMsg = error.message
  } else if (error.data) {
    console.log('showErrorTip  error.data.errors', error.data.errors)
    if (error.data.errors && error.data.errors.length > 0 && error.data.errors[0].msg) {
      if (Array.isArray(error.data.errors[0].msg)) {
        let type = error.data.errors[0].type
        let dates = formatDateRanges(error.data.errors[0].msg)
        if (type === 'conflict') {
          errorMsg = `和已有排班冲突：${dates}`
        } else if (type === 'multiple') {
          errorMsg = `一天之内有多个排班：${dates}`
        } else if (type === 'duplicate') {
          errorMsg = `排班发生冲突于：${dates}`
        }
      } else {
        errorMsg = error.data.errors[0].msg
      }
    } else {
      // 递归提取嵌套对象中的错误信息
      const extractErrorMessages = (obj) => {
        const messages = []
        for (const key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key]
            if (Array.isArray(value)) {
              messages.push(...value)
            } else if (typeof value === 'object' && value !== null) {
              messages.push(...extractErrorMessages(value))
            } else if (typeof value === 'string') {
              messages.push(value)
            }
          }
        }
        return messages
      }

      const errorMessages = extractErrorMessages(error.data)
      errorMsg = errorMessages[0]
    }

  } else if (error.msg) {
    errorMsg = error.msg
  } else {
    // 递归提取错误信息，处理验证错误格式
    const extractErrorMessages = (obj) => {
      const messages = []
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const value = obj[key]
          if (Array.isArray(value)) {
            // 处理数组中的错误对象
            for (const item of value) {
              if (typeof item === 'object' && item !== null && item.message) {
                messages.push(item.message)
              } else if (typeof item === 'string') {
                messages.push(item)
              } else if (typeof item === 'object' && item !== null) {
                messages.push(...extractErrorMessages(item))
              }
            }
          } else if (typeof value === 'object' && value !== null) {
            messages.push(...extractErrorMessages(value))
          } else if (typeof value === 'string') {
            messages.push(value)
          }
        }
      }
      return messages
    }

    const errorMessages = extractErrorMessages(error)
    errorMsg = errorMessages[0]
  }
  ElMessage.error(errorMsg || '操作失败，请重试')
}
