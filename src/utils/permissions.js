import { useAuthStore } from '@/stores/auth'

/**
 * 权限工具类
 * 提供便捷的权限检查方法
 */
export class PermissionUtils {
  static getAuthStore() {
    return useAuthStore()
  }

  /**
   * 检查用户是否拥有指定权限
   * @param {string} permission 权限字符串
   * @returns {boolean}
   */
  static hasPermission(permission) {
    const authStore = this.getAuthStore()
    return authStore.hasPermission(permission)
  }

  /**
   * 检查用户是否拥有任一权限
   * @param {string[]} permissions 权限数组
   * @returns {boolean}
   */
  static hasAnyPermission(permissions) {
    const authStore = this.getAuthStore()
    return authStore.hasAnyPermission(permissions)
  }

  /**
   * 检查用户是否拥有所有权限
   * @param {string[]} permissions 权限数组
   * @returns {boolean}
   */
  static hasAllPermissions(permissions) {
    const authStore = this.getAuthStore()
    return authStore.hasAllPermissions(permissions)
  }

  /**
   * 检查是否有查看权限
   * @param {string} module 模块名称 (如: 'housekeeping', 'maternal', 'charge')
   * @returns {boolean}
   */
  static canView(module) {
    const viewPermissions = {
      dashboard: 'dashboard.view',
      housekeeping: 'cus.housekeeping.view',
      maternal: 'cus.maternal.core.record.view',
      charge: 'ogm.charge.view',
      backend: 'sys.backend.management.view',
      infection: 'ogm.infection_control_environment.view',
    }

    const permission = viewPermissions[module]
    return permission ? this.hasPermission(permission) : false
  }

  /**
   * 检查是否有编辑权限
   * @param {string} module 模块名称
   * @returns {boolean}
   */
  static canEdit(module) {
    const editPermissions = {
      housekeeping: 'cus.housekeeping.edit',
      maternal: 'cus.maternal.core.record.edit',
      charge: 'ogm.charge.edit',
      backend: 'sys.backend.management.edit',
      infection: 'ogm.infection_control_environment.edit',
    }

    const permission = editPermissions[module]
    return permission ? this.hasPermission(permission) : false
  }

  /**
   * 获取用户所有权限
   * @returns {string[]}
   */
  static getUserPermissions() {
    const authStore = this.getAuthStore()
    return authStore.userPermissions
  }

  /**
   * 获取用户角色名称
   * @returns {string|null}
   */
  static getUserRole() {
    const authStore = this.getAuthStore()
    return authStore.userRole
  }

  /**
   * 检查用户是否已登录
   * @returns {boolean}
   */
  static isLoggedIn() {
    const authStore = this.getAuthStore()
    return authStore.isLoggedIn
  }
}

/**
 * Vue 组合式 API 权限钩子
 */
export function usePermissions() {
  const authStore = useAuthStore()

  return {
    hasPermission: authStore.hasPermission,
    hasAnyPermission: authStore.hasAnyPermission,
    hasAllPermissions: authStore.hasAllPermissions,
    userPermissions: authStore.userPermissions,
    userRole: authStore.userRole,
    isLoggedIn: authStore.isLoggedIn,
    canView: PermissionUtils.canView,
    canEdit: PermissionUtils.canEdit,
  }
}

// 导出默认的权限检查方法
export const {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  canView,
  canEdit,
  getUserPermissions,
  getUserRole,
  isLoggedIn,
} = PermissionUtils
