import { format, parseISO } from 'date-fns'
import { zhCN } from 'date-fns/locale'

/**
 * 将服务器返回的日期字符串格式化为 Element Plus 日期选择器需要的格式
 * @param {string} dateString - 服务器返回的日期字符串 "YYYY-MM-DD HH:mm:ss"
 * @returns {string} Element Plus 需要的格式 "YYYY-MM-DDTHH:mm:ss"
 */
export const formatDateForInput = (dateString) => {
  if (!dateString) return ''
  try {
    // 处理 "YYYY-MM-DD HH:mm:ss" 格式
    const date = parseISO(dateString.replace(' ', 'T'))
    return format(date, "yyyy-MM-dd'T'HH:mm:ss")
  } catch (error) {
    console.warn('日期格式转换失败:', dateString, error)
    return ''
  }
}

/**
 * 将 Element Plus 日期选择器的值格式化为 API 需要的格式
 * @param {string} dateString - Element Plus 的日期字符串
 * @returns {string} API 需要的格式 "YYYY-MM-DD HH:mm:ss"
 */
export const formatDateForAPI = (dateString) => {
  if (!dateString) return ''
  try {
    const date = parseISO(dateString.replace(' ', 'T'))
    return format(date, 'yyyy-MM-dd HH:mm:ss')
  } catch (error) {
    console.warn('日期格式转换失败:', dateString, error)
    return ''
  }
}

/**
 * 格式化日期时间显示
 * @param {string} dateTime - 日期时间字符串
 * @param {string} formatStr - 格式字符串，默认为 'yyyy年MM月dd日 HH:mm:ss'
 * @returns {string} 格式化后的日期时间
 */
export const formatDateTime = (dateTime, formatStr = 'yyyy年MM月dd日 HH:mm:ss') => {
  if (!dateTime) return '-'
  try {
    return format(new Date(dateTime), formatStr, { locale: zhCN })
  } catch {
    return '-'
  }
}

/**
 * 格式化日期显示
 * @param {string} date - 日期字符串
 * @param {string} formatStr - 格式字符串，默认为 'yyyy年MM月dd日'
 * @returns {string} 格式化后的日期
 */
export const formatDate = (date, formatStr = 'yyyy年MM月dd日') => {
  if (!date) return '-'
  try {
    return format(new Date(date), formatStr, { locale: zhCN })
  } catch {
    return '-'
  }
}

/**
 * 格式化时间显示
 * @param {string} datetime - 日期时间字符串
 * @returns {string} 格式化后的时间 (HH:mm)
 */
export const formatTime = (datetime) => {
  if (!datetime) return '-'
  try {
    const date = new Date(datetime.replace(' ', 'T'))
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return '-'
  }
}

/**
 * 处理对象中的日期字段
 * @param {Object} data - 包含日期字段的对象
 * @param {string} direction - 转换方向：'toInput' 或 'toAPI'
 * @param {string[]} dateFields - 需要处理的日期字段数组
 * @returns {Object} 处理后的对象
 */
export const processDateFields = (data, direction = 'toInput', dateFields = []) => {
  if (!data || typeof data !== 'object') return data

  const result = { ...data }
  const handler = direction === 'toInput' ? formatDateForInput : formatDateForAPI

  dateFields.forEach((field) => {
    if (result[field] && typeof result[field] === 'string') {
      result[field] = handler(result[field])
    }
  })

  return result
}

/**
 * 获取当前时间的标准格式
 * @param {string} formatType - 格式类型：'input'、'api'、'display'
 * @returns {string} 格式化后的当前时间
 */
export const getCurrentTime = (formatType = 'input') => {
  const now = new Date()

  switch (formatType) {
    case 'date':
      return format(now, 'yyyy-MM-dd')
    case 'input':
      return format(now, "yyyy-MM-dd'T'HH:mm:ss")
    case 'api':
      return format(now, 'yyyy-MM-dd HH:mm:ss')
    case 'display':
      return format(now, 'yyyy年MM月dd日 HH:mm:ss', { locale: zhCN })
    default:
      return format(now, "yyyy-MM-dd'T'HH:mm:ss")
  }
}

/**
 * 格式化日期范围，将连续的日期合并为范围显示
 * @param {string[]} dates - 日期数组 ['YYYY-MM-DD', ...]
 * @returns {string} 格式化后的日期范围字符串，如 "2024年1月1日至3日、2024年1月5日"
 */
export const formatDateRanges = (dates) => {
  if (!dates || dates.length === 0) return ''

  // 排序日期
  const sortedDates = [...dates].sort()
  const ranges = []
  let start = sortedDates[0]
  let end = start

  for (let i = 1; i < sortedDates.length; i++) {
    const current = sortedDates[i]
    const currentDate = new Date(current)
    const endDate = new Date(end)

    // 检查是否连续
    if (currentDate - endDate === 24 * 60 * 60 * 1000) {
      end = current
    } else {
      // 添加当前范围
      if (start === end) {
        ranges.push(formatDateString(start))
      } else {
        ranges.push(`${formatDateString(start)}至${formatDateString(end)}`)
      }
      start = current
      end = current
    }
  }

  // 添加最后一个范围
  if (start === end) {
    ranges.push(formatDateString(start))
  } else {
    ranges.push(`${formatDateString(start)}至${formatDateString(end)}`)
  }

  return ranges.join('、')
}

/**
 * 格式化日期字符串为中文格式
 * @param {string} dateStr - 日期字符串 'YYYY-MM-DD'
 * @returns {string} 中文格式的日期，如 "2024年1月1日"
 */
export const formatDateString = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`
}
