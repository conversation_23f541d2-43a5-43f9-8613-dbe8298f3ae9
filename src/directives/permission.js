import { useAuthStore } from '@/stores/auth'

/**
 * 权限指令
 * 用法：
 * v-permission="'cus.housekeeping.view'" - 检查单个权限
 * v-permission="['cus.housekeeping.view', 'cus.housekeeping.edit']" - 检查多个权限（任一）
 * v-permission:all="['perm1', 'perm2']" - 检查多个权限（全部）
 */
export const permission = {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  updated(el, binding) {
    checkPermission(el, binding)
  },
}

function checkPermission(el, binding) {
  const { value, arg } = binding
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    removeElement(el)
    return
  }

  let hasPermission = false

  if (Array.isArray(value)) {
    // 数组权限检查
    if (arg === 'all') {
      // 需要拥有所有权限
      hasPermission = authStore.hasAllPermissions(value)
    } else {
      // 默认：拥有任一权限即可
      hasPermission = authStore.hasAnyPermission(value)
    }
  } else if (typeof value === 'string') {
    // 单个权限检查
    hasPermission = authStore.hasPermission(value)
  } else {
    console.warn('v-permission 指令的值必须是字符串或字符串数组')
    return
  }

  if (!hasPermission) {
    removeElement(el)
  }
}

function removeElement(el) {
  return
  if (el.parentNode) {
    el.parentNode.removeChild(el)
  }
}

/**
 * 权限显示指令
 * 用法：
 * v-permission-show="'cus.housekeeping.view'" - 无权限时隐藏元素
 */
export const permissionShow = {
  mounted(el, binding) {
    checkPermissionShow(el, binding)
  },
  updated(el, binding) {
    checkPermissionShow(el, binding)
  },
}

function checkPermissionShow(el, binding) {
  const { value, arg } = binding
  const authStore = useAuthStore()

  if (!authStore.isLoggedIn) {
    el.style.display = 'none'
    return
  }

  let hasPermission = false

  if (Array.isArray(value)) {
    if (arg === 'all') {
      hasPermission = authStore.hasAllPermissions(value)
    } else {
      hasPermission = authStore.hasAnyPermission(value)
    }
  } else if (typeof value === 'string') {
    hasPermission = authStore.hasPermission(value)
  }

  el.style.display = hasPermission ? '' : 'none'
}
