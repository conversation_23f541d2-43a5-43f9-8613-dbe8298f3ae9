<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center mb-6">
      <el-icon class="text-pink-500 mr-2">
        <Lock />
      </el-icon>
      <h3 class="text-lg font-semibold text-gray-800">权限设置</h3>
    </div>

    <div class="space-y-6">
      <!-- 活动发布权限 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">活动发布权限</label>
        <el-select v-model="settings.publishPermission" placeholder="请选择发布权限" class="w-full">
          <el-option
            v-for="option in permissionOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          >
            <div class="flex items-center justify-between w-full">
              <div class="flex items-center">
                <el-icon class="mr-2" :class="option.iconColor">
                  <component :is="option.icon" />
                </el-icon>
                {{ option.label }}
              </div>
              <span class="text-xs text-gray-400">{{ option.description }}</span>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 活动编辑权限 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">活动编辑权限</label>
        <el-select v-model="settings.editPermission" placeholder="请选择编辑权限" class="w-full">
          <el-option
            v-for="option in permissionOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          >
            <div class="flex items-center justify-between w-full">
              <div class="flex items-center">
                <el-icon class="mr-2" :class="option.iconColor">
                  <component :is="option.icon" />
                </el-icon>
                {{ option.label }}
              </div>
              <span class="text-xs text-gray-400">{{ option.description }}</span>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 活动删除权限 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">活动删除权限</label>
        <el-select v-model="settings.deletePermission" placeholder="请选择删除权限" class="w-full">
          <el-option
            v-for="option in permissionOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          >
            <div class="flex items-center justify-between w-full">
              <div class="flex items-center">
                <el-icon class="mr-2" :class="option.iconColor">
                  <component :is="option.icon" />
                </el-icon>
                {{ option.label }}
              </div>
              <span class="text-xs text-gray-400">{{ option.description }}</span>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 权限说明 -->
      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div class="flex items-start">
          <el-icon class="text-orange-500 mt-0.5 mr-2">
            <WarningFilled />
          </el-icon>
          <div class="text-sm text-orange-700">
            <p class="font-medium mb-1">权限说明</p>
            <ul class="space-y-1 text-xs">
              <li>• <strong>仅管理员:</strong> 只有系统管理员可以执行此操作</li>
              <li>• <strong>所有员工:</strong> 所有登录员工都可以执行此操作</li>
              <li>• <strong>指定角色:</strong> 只有被指定的角色用户可以执行此操作</li>
              <li>• 建议根据机构管理需求合理配置权限级别</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { markRaw } from 'vue'
import { ElSelect, ElOption, ElIcon } from 'element-plus'
import { Lock, Avatar, UserFilled, User, WarningFilled } from '@element-plus/icons-vue'

// Props
defineProps({
  settings: {
    type: Object,
    required: true,
  },
})

// Emits
defineEmits(['update:settings'])

// 权限选项
const permissionOptions = [
  {
    value: 'admin',
    label: '仅管理员',
    description: '最高权限',
    icon: markRaw(Avatar),
    iconColor: 'text-red-500',
  },
  {
    value: 'staff',
    label: '所有员工',
    description: '普通权限',
    icon: markRaw(User),
    iconColor: 'text-green-500',
  },
  {
    value: 'selected',
    label: '指定角色',
    description: '自定义权限',
    icon: markRaw(UserFilled),
    iconColor: 'text-blue-500',
  },
]
</script>

<style scoped>
:deep(.el-select) {
  width: 100%;
}
</style>
