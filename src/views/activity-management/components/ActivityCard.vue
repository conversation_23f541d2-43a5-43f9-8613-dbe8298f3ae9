<template>
  <div
    class="activity-card bg-white border border-gray-200 rounded-lg overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-pink-200 cursor-pointer"
    @click="handleCardClick"
  >
    <!-- 活动图标背景 -->
    <div class="activity-image">
      <div class="activity-icon">
        <Calendar v-if="activity.icon === 'calendar-check'" />
        <Reading v-else-if="activity.icon === 'book-medical'" />
        <Bowl v-else-if="activity.icon === 'utensils'" />
        <Coffee v-else-if="activity.icon === 'coffee'" />
        <Calendar v-else />
      </div>
    </div>

    <!-- 活动内容 -->
    <div class="activity-content p-4">
      <h3 class="activity-title text-lg font-semibold text-gray-800 mb-3 line-clamp-2">
        {{ activity.title }}
      </h3>

      <div class="activity-info space-y-2">
        <div class="info-item">
          <Clock class="info-icon" />
          <span>{{ activity.date }} {{ activity.time }}</span>
        </div>
        <div class="info-item">
          <Location class="info-icon" />
          <span>{{ activity.location }}</span>
        </div>
        <div class="info-item">
          <User class="info-icon" />
          <span>{{ activity.instructor }}</span>
        </div>
      </div>

      <!-- 活动描述 -->
      <p class="activity-description text-sm text-gray-600 mb-3 line-clamp-2">
        {{ activity.description }}
      </p>

      <!-- 活动操作按钮 -->
      <div class="activity-actions flex justify-end space-x-2 pt-3 border-t border-gray-100">
        <button
          @click.stop="$emit('edit', activity)"
          class="action-btn text-blue-600 hover:text-blue-700 hover:bg-blue-50"
          title="编辑"
        >
          <Edit class="w-4 h-4" />
        </button>
        <button
          @click.stop="$emit('view', activity)"
          class="action-btn text-green-600 hover:text-green-700 hover:bg-green-50"
          title="查看详情"
        >
          <View class="w-4 h-4" />
        </button>
        <button
          @click.stop="$emit('delete', activity)"
          class="action-btn text-red-600 hover:text-red-700 hover:bg-red-50"
          title="删除"
        >
          <Delete class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Calendar,
  Clock,
  User,
  Location,
  View,
  Edit,
  Delete,
  Plus,
  CircleCheck,
  Reading,
  Bowl,
  Coffee,
} from '@element-plus/icons-vue'

// 定义属性
defineProps({
  activity: {
    type: Object,
    required: true,
  },
})

// 定义事件
defineEmits(['edit', 'view', 'delete'])

// 点击卡片事件
const handleCardClick = () => {
  // 可以在这里添加点击卡片的逻辑，比如跳转到详情页
}
</script>

<style scoped>
.activity-card {
  min-height: 320px;
  display: flex;
  flex-direction: column;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.activity-image {
  width: 100%;
  height: 160px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e77fa1 100%);
  background-size: 20px 20px;
  background-image: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.1) 75%,
    transparent 75%,
    transparent
  );
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  overflow: hidden;
  animation: move 2s linear infinite;
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 40px 40px;
  }
}

.activity-icon {
  position: relative;
  z-index: 1;
  font-size: 2.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.activity-title {
  line-height: 1.4;
}

.activity-info {
  flex: 1;
}

.activity-status {
  font-weight: 500;
}

.activity-description {
  line-height: 1.4;
}

.activity-actions {
  margin-top: auto;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.activity-card:hover .activity-actions {
  opacity: 1;
}

/* 截断文本样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 按钮样式优化 */
:deep(.el-button) {
  font-size: 12px;
  padding: 4px 8px;
  height: auto;
  border-radius: 4px;
}

:deep(.el-button--small) {
  font-size: 12px;
}

/* 悬停效果 */
.activity-card:hover .activity-image {
  background: linear-gradient(135deg, #e77fa1 0%, #d56c8e 100%);
}

.activity-card:hover .activity-actions {
  background-color: rgba(248, 250, 252, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activity-card {
    min-height: 280px;
  }

  .activity-image {
    height: 120px;
  }

  .activity-title {
    font-size: 1rem;
  }

  .activity-info {
    font-size: 0.875rem;
  }
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #6b7280;
}

.info-icon {
  width: 1rem;
  height: 1rem;
  margin-right: 0.5rem;
  color: #ec4899;
  flex-shrink: 0;
}

.action-btn {
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  transform: scale(1.1);
}
</style>
