<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center mb-6">
      <el-icon class="text-pink-500 mr-2">
        <Bell />
      </el-icon>
      <h3 class="text-lg font-semibold text-gray-800">通知设置</h3>
    </div>

    <div class="space-y-6">
      <!-- 活动提醒开关 -->
      <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 class="text-sm font-medium text-gray-900">启用活动提醒</h4>
          <p class="text-xs text-gray-500 mt-1">开启后，系统会在活动开始前发送提醒通知</p>
        </div>
        <el-switch
          v-model="settings.activityReminder"
          size="default"
          active-color="#ec4899"
          inactive-color="#d1d5db"
        />
      </div>

      <!-- 邮件通知开关 -->
      <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 class="text-sm font-medium text-gray-900">启用邮件通知</h4>
          <p class="text-xs text-gray-500 mt-1">通过邮件发送活动相关通知</p>
        </div>
        <el-switch
          v-model="settings.emailNotification"
          size="default"
          active-color="#ec4899"
          inactive-color="#d1d5db"
        />
      </div>

      <!-- 短信通知开关 -->
      <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 class="text-sm font-medium text-gray-900">启用短信通知</h4>
          <p class="text-xs text-gray-500 mt-1">通过短信发送重要活动通知</p>
        </div>
        <el-switch
          v-model="settings.smsNotification"
          size="default"
          active-color="#ec4899"
          inactive-color="#d1d5db"
        />
      </div>

      <!-- 通知设置说明 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
          <el-icon class="text-blue-500 mt-0.5 mr-2">
            <InfoFilled />
          </el-icon>
          <div class="text-sm text-blue-700">
            <p class="font-medium mb-1">通知说明</p>
            <ul class="space-y-1 text-xs">
              <li>• 活动提醒会在设定时间前自动发送</li>
              <li>• 邮件通知适用于详细的活动信息推送</li>
              <li>• 短信通知建议用于紧急或重要活动提醒</li>
              <li>• 可在活动详情中单独设置特定通知方式</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElSwitch, ElIcon } from 'element-plus'
import { Bell, InfoFilled } from '@element-plus/icons-vue'

// Props
defineProps({
  settings: {
    type: Object,
    required: true,
  },
})

// Emits
defineEmits(['update:settings'])
</script>

<style scoped>
.hover\:border-pink-200:hover {
  border-color: #fce7f3;
}
</style>
