<template>
  <el-dialog
    v-model="visible"
    title="活动详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>活动名称：</label>
              <span class="font-semibold">{{ detailData.name }}</span>
            </div>
            <div class="detail-item">
              <label>活动类型：</label>
              <el-tag type="primary">{{ detailData.activity_type }}</el-tag>
            </div>
            <div class="detail-item">
              <label>主讲人：</label>
              <span>{{ detailData.speaker }}</span>
            </div>
            <div class="detail-item">
              <label>活动地点：</label>
              <span>{{ detailData.location }}</span>
            </div>
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ detailData.creator_name }}</span>
            </div>
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <h3 class="section-title">时间信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>开始时间：</label>
              <span>{{ formatDateTime(detailData.start_time) }}</span>
            </div>
            <div class="detail-item">
              <label>结束时间：</label>
              <span>{{ formatDateTime(detailData.end_time) }}</span>
            </div>
            <div class="detail-item">
              <label>活动时长：</label>
              <span>{{ calculateDuration(detailData.start_time, detailData.end_time) }}</span>
            </div>
          </div>
        </div>

        <!-- 活动封面 -->
        <div v-if="detailData.cover_url" class="detail-section">
          <h3 class="section-title">活动封面</h3>
          <div class="cover-container">
            <img
              :src="detailData.cover_url"
              :alt="detailData.name"
              class="max-w-full h-auto rounded-lg shadow-md"
              @error="handleImageError"
            />
          </div>
        </div>

        <!-- 活动介绍 -->
        <div v-if="detailData.introduction" class="detail-section">
          <h3 class="section-title">活动介绍</h3>
          <div class="introduction-content p-4 bg-gray-50 rounded-lg">
            <p class="text-gray-700 leading-relaxed">{{ detailData.introduction }}</p>
          </div>
        </div>

        <!-- 活动详情 -->
        <div v-if="detailData.details" class="detail-section">
          <h3 class="section-title">活动详情</h3>
          <div class="details-content p-4 bg-gray-50 rounded-lg">
            <pre class="text-gray-700 leading-relaxed whitespace-pre-wrap font-sans">{{
              detailData.details
            }}</pre>
          </div>
        </div>

        <!-- 注意事项 -->
        <div v-if="detailData.attention" class="detail-section">
          <h3 class="section-title">注意事项</h3>
          <div class="attention-content p-4 bg-yellow-50 rounded-lg">
            <pre class="text-yellow-800 leading-relaxed whitespace-pre-wrap font-sans">{{
              detailData.attention
            }}</pre>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="detail-section">
          <h3 class="section-title">其他信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>活动ID：</label>
              <span>{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(detailData.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDateTime(detailData.updated_at) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'

import { format, differenceInMinutes } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  activityId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.activityId) {
    fetchDetail()
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.activityId) return

  loading.value = true
  try {
    const response = await get(`customer-service/activity/detail/${props.activityId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取活动详情失败:', error)
    ElMessage.error('获取活动详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
  detailData.value = null
}

// 时间格式化
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm', { locale: zhCN })
}

// 计算活动时长
const calculateDuration = (startTime, endTime) => {
  if (!startTime || !endTime) return '-'
  const start = new Date(startTime)
  const end = new Date(endTime)
  const minutes = differenceInMinutes(end, start)

  if (minutes < 60) {
    return `${minutes}分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }
}

// 图片加载错误处理
const handleImageError = (event) => {
  const img = event.target
  img.style.display = 'none'
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 6rem;
  flex-shrink: 0;
}

.introduction-content,
.details-content,
.attention-content {
  color: #374151;
  line-height: 1.625;
}

.cover-container {
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}

/* pre标签样式优化 */
pre {
  font-family: inherit;
  margin: 0;
  padding: 0;
}
</style>
