<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center mb-6">
      <el-icon class="text-pink-500 mr-2">
        <Setting />
      </el-icon>
      <h3 class="text-lg font-semibold text-gray-800">基本设置</h3>
    </div>

    <div class="space-y-6">
      <!-- 默认活动类型 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">默认活动类型</label>
        <el-select
          v-model="settings.defaultActivityType"
          placeholder="请选择默认活动类型"
          class="w-full"
        >
          <el-option
            v-for="option in activityTypes"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          >
            <div class="flex items-center">
              <el-icon class="mr-2 text-pink-500">
                <component :is="option.icon" />
              </el-icon>
              {{ option.label }}
            </div>
          </el-option>
        </el-select>
        <p class="text-xs text-gray-400 mt-1">系统在创建新活动时将自动选择此类型</p>
      </div>

      <!-- 默认活动地点 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">默认活动地点</label>
        <el-input
          v-model="settings.defaultLocation"
          placeholder="请输入默认活动地点"
          maxlength="50"
          show-word-limit
        />
        <p class="text-xs text-gray-400 mt-1">可在创建活动时进行修改</p>
      </div>

      <!-- 活动提醒时间 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">活动提醒时间（提前）</label>
        <div class="flex items-center space-x-2">
          <el-input-number
            v-model="settings.reminderTime"
            :min="5"
            :max="120"
            :step="5"
            controls-position="right"
            class="w-32"
          />
          <span class="text-sm text-gray-500">分钟</span>
        </div>
        <p class="text-xs text-gray-400 mt-1">系统将在活动开始前指定时间发送提醒</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElSelect, ElOption, ElInput, ElInputNumber, ElIcon } from 'element-plus'
import { Setting, Reading, UserFilled, Trophy } from '@element-plus/icons-vue'

// Props
defineProps({
  settings: {
    type: Object,
    required: true,
  },
})

// Emits
defineEmits(['update:settings'])

// 活动类型选项
const activityTypes = [
  { value: 'education', label: '健康讲座', icon: Reading },
  { value: 'social', label: '社交活动', icon: UserFilled },
  { value: 'workshop', label: '工作坊', icon: Trophy },
]
</script>

<style scoped>
:deep(.el-select) {
  width: 100%;
}
</style>
