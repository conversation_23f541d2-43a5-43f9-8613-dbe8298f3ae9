<template>
  <div class="activity-grid-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Calendar />
          </el-icon>
          活动列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 个活动</div>
      </div>
    </div>

    <!-- 活动卡片内容 -->
    <div class="cards-content px-6 py-4" v-loading="loading">
      <div v-if="tableData.length === 0 && !loading" class="text-center py-12 text-gray-500">
        <el-icon class="text-4xl mb-2 text-gray-400">
          <Calendar />
        </el-icon>
        <p>暂无活动数据</p>
      </div>

      <!-- 活动卡片网格 -->
      <div
        v-else
        class="activity-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
      >
        <div
          v-for="activity in tableData"
          :key="activity.id"
          class="activity-card bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 hover:border-pink-300 flex flex-col cursor-pointer overflow-hidden"
          @click="handleCardClick(activity)"
        >
          <!-- 活动图片区域 -->
          <div class="activity-image-container relative h-48 overflow-hidden">
            <!-- 默认背景（总是显示） -->
            <div
              class="w-full h-full bg-gradient-to-br from-pink-100 to-pink-300 flex items-center justify-center"
            ></div>
            <!-- 实际图片（如果存在且加载成功） -->
            <img
              v-if="activity.cover"
              :src="activity.cover"
              :alt="activity.name"
              class="absolute inset-0 w-full h-full object-cover"
              @error="handleImageError"
            />

            <!-- 状态标签叠加层 -->
            <div class="absolute top-3 left-3">
              <el-tag :type="getActivityStatusTagType(activity.status)" size="small">
                {{ getActivityStatusText(activity.status) }}
              </el-tag>
            </div>
          </div>

          <!-- 卡片内容区 -->
          <div class="card-content flex-1 p-4">
            <!-- 活动标题 -->
            <h4 class="text-lg font-semibold text-gray-800 mb-3 line-clamp-2">
              {{ activity.name }}
            </h4>

            <!-- 活动信息 -->
            <div class="activity-info space-y-2 mb-4">
              <div class="info-item">
                <el-icon class="info-icon text-pink-500">
                  <Clock />
                </el-icon>
                <span class="text-sm text-gray-600">{{ formatDateTime(activity.start_time) }}</span>
              </div>
              <div class="info-item">
                <el-icon class="info-icon text-pink-500">
                  <Location />
                </el-icon>
                <span class="text-sm text-gray-600">{{ activity.location }}</span>
              </div>
              <div class="info-item">
                <el-icon class="info-icon text-pink-500">
                  <User />
                </el-icon>
                <span class="text-sm text-gray-600">{{ activity.speaker }}</span>
              </div>
            </div>
          </div>

          <!-- 卡片底部操作按钮 -->
          <div class="card-footer p-4 pt-0">
            <div class="flex gap-2">
              <el-button
                type="primary"
                size="small"
                class="flex-1"
                :disabled="activity.status !== 'UPCOMING'"
                @click.stop="handleEdit(activity)"
              >
                <el-icon class="mr-1">
                  <Edit />
                </el-icon>
                编辑
              </el-button>
              <el-button
                type="info"
                size="small"
                class="flex-1"
                @click.stop="handleViewDetail(activity)"
              >
                <el-icon class="mr-1">
                  <View />
                </el-icon>
                查看
              </el-button>
              <el-button
                type="danger"
                size="small"
                class="flex-1"
                @click.stop="handleDelete(activity)"
              >
                <el-icon class="mr-1">
                  <Delete />
                </el-icon>
                删除
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[12, 24, 48, 96]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 活动详情对话框 -->
    <ActivityDetailDialog
      v-model="detailDialogVisible"
      :activity-id="currentActivityId"
      @close="detailDialogVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Calendar, Clock, Location, User, Edit, Delete, View } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { get } from '@/utils/request.js'
import { getActivityStatusText, getActivityStatusTagType } from '@/utils/constants.js'
import ActivityDetailDialog from './ActivityDetailDialog.vue'

const emit = defineEmits(['edit', 'view', 'delete'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/activity/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 详情对话框相关
const detailDialogVisible = ref(false)
const currentActivityId = ref(null)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(12)
const total = computed(() => totalCount.value)

// 转换API数据格式
const transformActivityData = (apiData) => {
  return apiData.map((item) => washActivityData(item))
}

const washActivityData = (item) => {
  return {
    id: item.rid, // 使用rid作为id
    rid: item.rid, // 保留原始rid字段
    name: item.name,
    start_time: item.start_time,
    end_time: item.end_time,
    location: item.location,
    speaker: item.speaker,
    status: item.status,
    status_display: item.status_display,
    created_at: item.created_at,
    updated_at: item.updated_at,
    cover: item.cover || null, // 新接口直接返回完整URL
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的dateRange字段
    const processedFilters = { ...props.filters }

    // 处理日期范围过滤
    if (processedFilters.dateRange && Array.isArray(processedFilters.dateRange)) {
      processedFilters.start_date = processedFilters.dateRange[0]
      processedFilters.end_date = processedFilters.dateRange[1]
      delete processedFilters.dateRange
    }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = transformActivityData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取活动列表失败:', error)
    ElMessage.error('获取活动列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 时间格式化
const formatDateTime = (dateTime) => {
  return format(new Date(dateTime), 'MM-dd HH:mm', { locale: zhCN })
}

// 图片加载错误处理
const handleImageError = (event) => {
  // 图片加载失败时隐藏img元素，显示默认背景
  const img = event.target
  img.style.display = 'none'
}

// 事件处理
const handleEdit = (activity) => {
  emit('edit', activity)
}

const handleDelete = (activity) => {
  emit('delete', activity)
}

const handleCardClick = (activity) => {
  emit('view', activity)
}

// 查看详情
const handleViewDetail = (activity) => {
  currentActivityId.value = activity.id
  detailDialogVisible.value = true
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 移除对filters的监听，改为手动刷新
// 用户点击搜索时会调用resetPagination方法
// watch(
//   () => props.filters,
//   () => {
//     currentPage.value = 1
//     loadData()
//   },
//   { deep: true },
// )

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.activity-grid-container {
  transition: all 0.3s ease;
  width: 100%;
}

.activity-grid-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.activity-card {
  transition: all 0.3s ease;
  min-height: 400px;
}

.activity-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.activity-image-container img {
  transition: transform 0.3s ease;
}

.activity-card:hover .activity-image-container img {
  transform: scale(1.05);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 截断文本样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .activity-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .activity-grid {
    grid-template-columns: 1fr;
  }

  .table-header {
    padding: 1rem;
  }

  .cards-content {
    padding: 1rem;
  }
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}

/* 加载状态 */
:deep(.el-loading-mask) {
  border-radius: 0.5rem;
}
</style>
