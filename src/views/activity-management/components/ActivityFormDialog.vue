<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="activity-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="activity-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="活动标题" prop="name">
              <el-input v-model="formData.name" placeholder="请输入活动标题" />
            </el-form-item>
            <el-form-item label="活动类型" prop="activity_type">
              <el-select
                v-model="formData.activity_type"
                placeholder="请选择活动类型"
                class="w-full"
              >
                <el-option label="健康讲座" value="健康讲座" />
                <el-option label="育儿讲座" value="育儿讲座" />
                <el-option label="社交活动" value="社交活动" />
                <el-option label="工作坊" value="工作坊" />
              </el-select>
            </el-form-item>
            <el-form-item label="活动地点" prop="location">
              <el-input v-model="formData.location" placeholder="如：多功能厅、会议室A" />
            </el-form-item>
            <el-form-item label="主讲人/负责人" prop="speaker">
              <el-input v-model="formData.speaker" placeholder="请输入主讲人或负责人姓名" />
            </el-form-item>
          </div>
        </div>

        <!-- 时间安排 -->
        <div class="form-section mb-6">
          <h4 class="section-title">时间安排</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="开始时间" prop="start_time">
              <el-date-picker
                v-model="formData.start_time"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="end_time">
              <el-date-picker
                v-model="formData.end_time"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 活动内容 -->
        <div class="form-section mb-6">
          <h4 class="section-title">活动内容</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="活动封面" prop="cover">
              <FileUpload
                v-model="formData.cover"
                :file-types="['jpg', 'png', 'gif']"
                :max-size="5"
                :multiple="false"
                :limit="1"
                action="file/activity/cover/upload/"
                field="activity_cover_file"
                upload-text="上传活动封面"
                custom-tip-text="支持 JPG、PNG、GIF 格式，文件大小不超过 5MB，建议尺寸 16:9"
                :urls="formData.cover_url"
              />
            </el-form-item>
            <el-form-item label="活动简介" prop="introduction">
              <el-input
                v-model="formData.introduction"
                type="textarea"
                :rows="2"
                placeholder="请输入活动简介，简短描述活动亮点..."
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="活动详情" prop="details">
              <el-input
                v-model="formData.details"
                type="textarea"
                :rows="6"
                placeholder="请详细描述活动内容、流程安排、预期效果等..."
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="注意事项" prop="attention">
              <el-input
                v-model="formData.attention"
                type="textarea"
                :rows="3"
                placeholder="请输入参与活动的注意事项，如着装要求、携带物品等..."
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '发布活动' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, nextTick } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElDatePicker,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import FileUpload from '@/components/FileUpload.vue'
import { post, put, get } from '@/utils/request.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const loading = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  activity_type: '',
  location: '',
  speaker: '',
  start_time: '',
  end_time: '',
  cover: '',
  cover_url: '',
  introduction: '',
  details: '',
  attention: '',
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑活动信息' : '发布新活动'
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入活动标题', trigger: 'blur' }],
  activity_type: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
  location: [{ required: true, message: '请输入活动地点', trigger: 'blur' }],
  speaker: [{ required: true, message: '请输入主讲人或负责人', trigger: 'blur' }],
  start_time: [{ required: true, message: '请选择开始时间', trigger: 'change' }],
  end_time: [
    { required: true, message: '请选择结束时间', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && formData.start_time && new Date(value) <= new Date(formData.start_time)) {
          callback(new Error('结束时间必须晚于开始时间'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  introduction: [{ required: true, message: '请输入活动简介', trigger: 'blur' }],
  details: [{ required: true, message: '请输入活动详情', trigger: 'blur' }],
}

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(() => {
      resetForm()
    }, 100)
    if (visible) {
      await scrollToTop()

      if (props.itemId) {
        // 编辑模式：获取详情数据
        await fetchActivityDetail(props.itemId)
      } else {
        // 新增模式：设置默认值
        const tomorrow = new Date()
        tomorrow.setDate(tomorrow.getDate() + 1)
        tomorrow.setHours(14, 0, 0, 0)
        const endTime = new Date(tomorrow)
        endTime.setHours(16, 0, 0, 0)

        formData.start_time = tomorrow.toISOString().slice(0, 16).replace('T', ' ')
        formData.end_time = endTime.toISOString().slice(0, 16).replace('T', ' ')
      }
    }
  },
)

// 方法
// 滚动到顶部
const scrollToTop = async () => {
  await nextTick()
  const scrollContainer = document.querySelector('.activity-dialog .max-h-\\[70vh\\]')
  if (scrollContainer) {
    scrollContainer.scrollTop = 0
  }
}

// 获取活动详情数据（用于编辑模式）
const fetchActivityDetail = async (activityId) => {
  if (!activityId) return

  loading.value = true
  try {
    const response = await get(`customer-service/activity/detail/${activityId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(formData, processedData)
    } else {
      ElMessage.error('未获取到活动详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取活动详情失败:', error)
    ElMessage.error('获取活动详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    id: apiData.rid, // 使用rid作为id
    name: apiData.name || '',
    activity_type: apiData.activity_type || '',
    location: apiData.location || '',
    speaker: apiData.speaker || '',
    start_time: apiData.start_time || '',
    end_time: apiData.end_time || '',
    cover: apiData.cover || '', // 现在存储文件ID
    cover_url: apiData.cover_url || '', // 用于显示的URL
    introduction: apiData.introduction || '',
    details: apiData.details || '',
    attention: apiData.attention || '',
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = async () => {
  Object.assign(formData, {
    id: '',
    name: '',
    activity_type: '',
    location: '',
    speaker: '',
    start_time: '',
    end_time: '',
    cover: '',
    cover_url: '',
    introduction: '',
    details: '',
    attention: '',
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSave = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (saving.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    // 构造提交数据
    const submitData = {
      name: formData.name,
      activity_type: formData.activity_type,
      start_time: formData.start_time,
      end_time: formData.end_time,
      location: formData.location,
      speaker: formData.speaker,
      cover: formData.cover,
      introduction: formData.introduction,
      details: formData.details,
      attention: formData.attention,
    }

    let res
    if (!props.itemId) {
      res = await post('customer-service/activity/create/', submitData)
    } else {
      res = await put(`customer-service/activity/update/${formData.id}/`, submitData)
    }

    ElMessage.success(props.itemId ? '活动更新成功' : '活动创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    console.error(props.itemId ? '更新活动失败:' : '创建活动失败:', error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
