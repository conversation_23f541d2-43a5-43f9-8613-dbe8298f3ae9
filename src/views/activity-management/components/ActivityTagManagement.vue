<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <el-icon class="text-pink-500 mr-2">
          <CollectionTag />
        </el-icon>
        <h3 class="text-lg font-semibold text-gray-800">标签管理</h3>
      </div>
    </div>

    <div class="space-y-4">
      <!-- 添加新标签 -->
      <div class="border border-gray-200 rounded-lg p-4">
        <label class="block text-sm font-medium text-gray-700 mb-3">添加新标签</label>
        <div class="flex items-center space-x-2">
          <el-input
            v-model="newTagName"
            placeholder="输入标签名称"
            maxlength="20"
            show-word-limit
            class="flex-1"
          />
          <el-button
            type="primary"
            @click="handleAddTag"
            :disabled="!newTagName.trim()"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-1">
              <Plus />
            </el-icon>
            添加
          </el-button>
        </div>
        <p class="text-xs text-gray-400 mt-2">标签用于活动分类和筛选</p>
      </div>

      <!-- 现有标签列表 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">现有标签</label>
        <div class="flex flex-wrap gap-2">
          <div
            v-for="tag in tags"
            :key="tag.id"
            class="flex items-center space-x-2 px-3 py-2 rounded-full border border-gray-200 bg-gray-50 text-gray-700 transition-all hover:shadow-md hover:border-pink-200 hover:bg-pink-50"
          >
            <div class="w-2 h-2 rounded-full bg-pink-500"></div>
            <span class="text-sm font-medium">{{ tag.name }}</span>
            <el-button
              size="small"
              @click="handleDeleteTag(tag.id)"
              class="!p-0 !min-w-[16px] !h-4 hover:text-red-500"
            >
              <el-icon class="text-xs">
                <Close />
              </el-icon>
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!tags || tags.length === 0" class="text-center py-8 text-gray-500">
          <el-icon class="text-4xl mb-2">
            <CollectionTag />
          </el-icon>
          <p>暂无活动标签</p>
          <p class="text-xs mt-1">添加标签后可在创建活动时使用</p>
        </div>
      </div>

      <!-- 预设标签 -->
      <div class="border-t pt-4">
        <label class="block text-sm font-medium text-gray-700 mb-3">快速添加预设标签</label>
        <div class="flex flex-wrap gap-2">
          <el-button
            v-for="preset in presetTags"
            :key="preset.name"
            size="small"
            @click="addPresetTag(preset)"
            class="text-gray-600 border-gray-300 hover:text-pink-500 hover:border-pink-300"
          >
            <div class="w-2 h-2 rounded-full mr-2 bg-pink-500"></div>
            {{ preset.name }}
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElInput, ElButton, ElIcon } from 'element-plus'
import { CollectionTag, Plus, Close } from '@element-plus/icons-vue'

// Props
defineProps({
  tags: {
    type: Array,
    default: () => [],
  },
})

// Emits
const emit = defineEmits(['update:tags', 'add', 'delete'])

// 响应式数据
const newTagName = ref('')

// 预设标签
const presetTags = [
  { name: '孕期瑜伽' },
  { name: '育儿课堂' },
  { name: '产后恢复' },
  { name: '心理疏导' },
  { name: '营养讲座' },
  { name: '亲子活动' },
]

// 方法
const handleAddTag = () => {
  const name = newTagName.value.trim()
  if (!name) return

  emit('add', {
    name,
  })

  // 重置输入
  newTagName.value = ''
}

const handleDeleteTag = (tagId) => {
  emit('delete', tagId)
}

const addPresetTag = (preset) => {
  emit('add', {
    name: preset.name,
  })
}
</script>
