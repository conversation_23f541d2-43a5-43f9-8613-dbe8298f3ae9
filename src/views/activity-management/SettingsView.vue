<template>
  <div class="settings-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">活动设置</h1>
            <p class="text-sm text-gray-600 mt-1">配置活动相关参数和规则</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleSaveSettings"
            :loading="saving"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Check />
            </el-icon>
            保存设置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 设置卡片容器 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 基本设置 -->
      <ActivityBasicSettings v-model:settings="basicSettings" class="lg:col-span-1" />

      <!-- 通知设置 -->
      <ActivityNotificationSettings v-model:settings="notificationSettings" class="lg:col-span-1" />

      <!-- 标签管理 -->
      <ActivityTagManagement
        v-model:tags="activityTags"
        @add="handleAddTag"
        @delete="handleDeleteTag"
        class="lg:col-span-1"
      />

      <!-- 权限设置 -->
      <ActivityPermissionSettings v-model:settings="permissionSettings" class="lg:col-span-1" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElMessage, ElIcon } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import ActivityBasicSettings from './components/ActivityBasicSettings.vue'
import ActivityNotificationSettings from './components/ActivityNotificationSettings.vue'
import ActivityTagManagement from './components/ActivityTagManagement.vue'
import ActivityPermissionSettings from './components/ActivityPermissionSettings.vue'

// 响应式数据
const saving = ref(false)

// 基本设置
const basicSettings = reactive({
  defaultActivityType: 'education',
  defaultLocation: '多功能厅',
  reminderTime: 30,
})

// 通知设置
const notificationSettings = reactive({
  activityReminder: true,
  emailNotification: true,
  smsNotification: false,
})

// 活动标签
const activityTags = ref([
  { id: '1', name: '产后护理' },
  { id: '2', name: '营养餐' },
  { id: '3', name: '瑜伽' },
  { id: '4', name: '新生儿护理' },
  { id: '5', name: '心理健康' },
])

// 权限设置
const permissionSettings = reactive({
  publishPermission: 'admin',
  editPermission: 'admin',
  deletePermission: 'admin',
})

// 方法
const handleSaveSettings = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const handleAddTag = (tagData) => {
  activityTags.value.push({
    ...tagData,
    id: Date.now().toString(),
  })
  ElMessage.success('标签添加成功')
}

const handleDeleteTag = (tagId) => {
  const index = activityTags.value.findIndex((tag) => tag.id === tagId)
  if (index !== -1) {
    activityTags.value.splice(index, 1)
    ElMessage.success('标签删除成功')
  }
}

onMounted(() => {
  // 初始化加载数据
})
</script>

<style scoped>
.settings-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
