<template>
  <div class="activity-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">活动管理 - 活动列表</h1>
            <p class="text-sm text-gray-600 mt-1">管理活动信息，查看活动状态</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleAddActivity"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            发布新活动
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 活动列表组件 -->
    <ActivityGrid
      ref="activityGridRef"
      :filters="currentFilters"
      @edit="handleEditActivity"
      @view="handleViewActivity"
      @delete="handleDeleteActivity"
    />

    <!-- 活动表单弹窗 -->
    <ActivityFormDialog
      v-model="formVisible"
      :item-id="currentActivityId"
      @success="handleSaveActivity"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElButton, ElIcon, ElMessageBox, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { del } from '@/utils/request.js'
import { ACTIVITY_STATUS_OPTIONS } from '@/utils/constants.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import FilterPanel from '@/components/FilterPanel.vue'
import ActivityGrid from './components/ActivityGrid.vue'
import ActivityFormDialog from './components/ActivityFormDialog.vue'

// 使用基础数据 store
const baseDataStore = useBaseDataStore()

// 响应式数据
const formVisible = ref(false)
const currentActivityId = ref(null)

// 获取组件引用
const activityGridRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  atype: '',
  ts: '',
  sk: '',
  aps: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'atype',
    type: 'select',
    label: '活动类型',
    placeholder: '选择活动类型',
    options: baseDataStore.activityTypes.getOptions(),
  },
  {
    key: 'ts',
    type: 'date',
    label: '活动日期',
    placeholder: '选择日期',
  },
  {
    key: 'sk',
    type: 'input',
    label: '关键词',
    placeholder: '活动标题或内容关键词',
  },
  {
    key: 'aps',
    type: 'select',
    label: '活动状态',
    placeholder: '选择活动状态',
    options: ACTIVITY_STATUS_OPTIONS,
  },
])

// 方法
const handleAddActivity = () => {
  currentActivityId.value = null
  formVisible.value = true
}

const handleEditActivity = (activity) => {
  currentActivityId.value = activity.id
  formVisible.value = true
}

const handleViewActivity = () => {}

const handleDeleteActivity = async (activity) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除活动 "${activity.name}" 吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    )

    // 调用删除接口
    await del(`customer-service/activity/delete/${activity.id}/`)
    ElMessage.success('活动删除成功')

    // 刷新列表
    activityGridRef.value?.refresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除活动失败:', error)
      ElMessage.error('删除活动失败，请稍后重试')
    }
  }
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  activityGridRef.value?.resetPagination()
}

const handleSaveActivity = () => {
  formVisible.value = false
  currentActivityId.value = null
  activityGridRef.value?.refresh() // 刷新数据
}
</script>

<style scoped>
.activity-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
