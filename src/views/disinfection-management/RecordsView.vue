<template>
  <div class="disinfection-records-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">感染防控 - 清洁消毒记录</h1>
            <p class="text-sm text-gray-600 mt-1">管理清洁消毒记录，确保环境卫生安全</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新建记录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <DisinfectionTable
      ref="disinfectionTableRef"
      :filters="currentFilters"
      @edit="handleEdit"
      @row-click="handleRowClick"
    />

    <!-- 表单对话框 -->
    <DisinfectionFormDialog
      v-model="showFormDialog"
      :mode="formMode"
      :item-id="currentRow?.rid"
      @success="handleSubmitSuccess"
    />

    <!-- 详情查看对话框 -->
    <DisinfectionDetailDialog
      v-model="showDetailDialog"
      :item-id="currentRow?.rid"
      @edit="handleEditFromDetail"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import DisinfectionTable from '@/views/disinfection-management/components/DisinfectionTable.vue'
import DisinfectionFormDialog from '@/views/disinfection-management/components/DisinfectionFormDialog.vue'
import DisinfectionDetailDialog from '@/views/disinfection-management/components/DisinfectionDetailDialog.vue'

// 响应式数据
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref(null)
const formMode = ref('add') // 'add' | 'edit'

// 获取 table 组件引用
const disinfectionTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
  clean_area: '',
  clean_type: '',
  disinfection_document: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入清洁人员或监督人',
  },
  {
    key: 'clean_area',
    type: 'input',
    label: '清洁区域',
    placeholder: '请输入清洁区域',
  },
  {
    key: 'clean_type',
    type: 'input',
    label: '清洁类型',
    placeholder: '请输入清洁类型',
  },
  {
    key: 'disinfection_document',
    type: 'input',
    label: '消毒规范',
    placeholder: '请输入消毒规范文档名称',
  },
]

// 新增记录
const handleCreate = () => {
  formMode.value = 'add'
  currentRow.value = null
  showFormDialog.value = true
}

// 编辑记录
const handleEdit = (row) => {
  formMode.value = 'edit'
  currentRow.value = row
  showFormDialog.value = true
}

// 从详情对话框编辑
const handleEditFromDetail = (row) => {
  currentRow.value = row
  showDetailDialog.value = false
  formMode.value = 'edit'
  showFormDialog.value = true
}

// 表单提交成功
const handleSubmitSuccess = () => {
  showFormDialog.value = false
  currentRow.value = null
  disinfectionTableRef.value?.refresh() // 刷新表格数据
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  disinfectionTableRef.value?.resetPagination()
}

// 行点击 - 查看详情
const handleRowClick = (row) => {
  currentRow.value = row
  showDetailDialog.value = true
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentRow.value = null
}
</script>

<style scoped>
.disinfection-records-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
