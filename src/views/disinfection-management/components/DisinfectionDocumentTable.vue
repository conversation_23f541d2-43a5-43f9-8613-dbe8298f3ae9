<template>
  <div
    class="disinfection-document-table-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Document />
          </el-icon>
          消毒规范文档列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="rid" label="文档ID" min-width="200" fixed="left">
        <template #default="{ row }">
          <span class="font-mono text-sm">{{ row.rid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="name" label="文档名称" min-width="300">
        <template #default="{ row }">
          <div class="">
            <div class="font-medium text-gray-800">{{ row.name }}</div>
            <div class="text-sm text-gray-500 mt-1">版本: {{ row.version }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="publishDate" label="发布日期" min-width="150">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            {{ formatDate(row.publishDate) }}
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="320" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
            >
              查看
            </el-button>
            <el-button @click.stop="handleEdit(row)" type="default" size="small"> 编辑 </el-button>
            <el-button
              @click.stop="handleDelete(row)"
              type="danger"
              size="small"
              class="text-red-600 border-red-200 hover:bg-red-50"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get, del } from '@/utils/request.js'
import {
  getDisinfectionDocumentStatusText,
  getDisinfectionDocumentStatusTagType,
} from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const emit = defineEmits(['edit', 'row-click'])

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 转换API数据格式
const transformDocumentData = (apiData) => {
  return apiData.map((item) => washDocumentData(item))
}

const washDocumentData = (item) => {
  return {
    rid: item.rid,
    name: item.name,
    version: item.version,
    publishDate: item.publish_date,
    status: item.status || item.publish_status || 'IN_EFFECT', // 从API获取状态，默认为生效中
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的publish_date字段，如果存在且为日期格式，转换为日期时间格式
    const processedFilters = { ...props.filters }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get('customer-service/disinfection-document/list/', requestParams)
    tableData.value = transformDocumentData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取消毒规范文档列表失败:', error)
    ElMessage.error('获取消毒规范文档列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 状态相关方法 - 使用全局常量工具函数
const getStatusType = (status) => {
  return getDisinfectionDocumentStatusTagType(status)
}

const getStatusLabel = (status) => {
  return getDisinfectionDocumentStatusText(status)
}

// 时间格式化
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return format(new Date(dateStr), 'yyyy-MM-dd')
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 删除文档
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除文档"${row.name}"吗？删除后无法恢复。`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    })

    const deleteUrl = `customer-service/disinfection-document/delete/${row.rid}/`
    await del(deleteUrl)

    ElMessage.success('删除成功')

    // 重新加载数据
    loadData()
  } catch (error) {
    showErrorTip(error)
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.disinfection-document-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.disinfection-document-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
