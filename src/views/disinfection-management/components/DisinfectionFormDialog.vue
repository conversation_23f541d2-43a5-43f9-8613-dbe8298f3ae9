<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="disinfection-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="disinfection-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-form-item label="清洁区域" prop="clean_area">
              <el-input v-model="form.clean_area" placeholder="请输入清洁区域" />
            </el-form-item>

            <el-form-item label="清洁类型" prop="clean_type">
              <el-input v-model="form.clean_type" placeholder="请输入清洁类型" />
            </el-form-item>
          </div>

          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="消毒规范文档" prop="disinfection_document">
              <el-select
                v-model="form.disinfection_document"
                placeholder="请选择消毒规范文档"
                class="w-full"
                :loading="
                  baseDataStore.disinfectionDocs.isLoading() ||
                  baseDataStore.disinfectionDocs.searchLoading
                "
                filterable
                remote
                :remote-method="baseDataStore.disinfectionDocs.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.disinfectionDocs.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.disinfectionDocs.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="清洁时间" prop="clean_time">
              <el-date-picker
                v-model="form.clean_time"
                type="datetime"
                placeholder="请选择清洁时间"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 人员信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">人员信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-form-item label="清洁人员" prop="cleaner">
              <el-input v-model="form.cleaner" placeholder="请输入清洁人员姓名" />
            </el-form-item>

            <el-form-item label="监督人员" prop="supervisor">
              <el-input v-model="form.supervisor" placeholder="请输入监督人员姓名" />
            </el-form-item>
          </div>
        </div>

        <!-- 消毒信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">消毒信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="执行结果" prop="result">
              <el-select v-model="form.result" placeholder="请选择执行结果" class="w-full">
                <el-option
                  v-for="option in resultOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="消毒剂信息" prop="disinfectant_info">
              <el-input
                v-model="form.disinfectant_info"
                placeholder="请输入消毒剂名称、浓度、用量等信息，如：75%酒精，500ml"
              />
            </el-form-item>

            <el-form-item label="消毒过程" prop="disinfection_process">
              <el-input
                v-model="form.disinfection_process"
                type="textarea"
                :rows="4"
                placeholder="请详细描述清洁消毒的具体过程和步骤"
              />
            </el-form-item>

            <el-form-item label="备注信息" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息（可选）"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '提交记录' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { CLEAN_DISINFECTION_RESULT_OPTIONS } from '@/utils/constants.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

// 基础数据store
const baseDataStore = useBaseDataStore()

const submitting = ref(false)
const loading = ref(false)
const formRef = ref()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'edit' ? '编辑清洁消毒记录' : '新建清洁消毒记录'
})

// 执行结果选项
const resultOptions = CLEAN_DISINFECTION_RESULT_OPTIONS

// 表单数据
const form = reactive({
  clean_area: '',
  clean_type: '',
  disinfection_document: '',
  clean_time: '',
  cleaner: '',
  supervisor: '',
  result: '',
  disinfectant_info: '',
  disinfection_process: '',
  remark: '',
})

// 表单验证规则
const rules = computed(() => ({
  clean_area: [{ required: true, message: '请输入清洁区域', trigger: 'blur' }],
  clean_type: [{ required: true, message: '请输入清洁类型', trigger: 'blur' }],
  disinfection_document: [{ required: true, message: '请选择消毒规范文档', trigger: 'change' }],
  clean_time: [{ required: true, message: '请选择清洁时间', trigger: 'change' }],
  cleaner: [{ required: true, message: '请输入清洁人员', trigger: 'blur' }],
  supervisor: [{ required: true, message: '请输入监督人员', trigger: 'blur' }],
  result: [{ required: true, message: '请选择执行结果', trigger: 'change' }],
  disinfectant_info: [{ required: true, message: '请输入消毒剂信息', trigger: 'blur' }],
  disinfection_process: [{ required: true, message: '请描述消毒过程', trigger: 'blur' }],
}))

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(resetForm, 5)
    if (visible) {
      if (props.mode === 'edit' && props.itemId) {
        // 编辑模式：获取详情数据
        await fetchDisinfectionDetail(props.itemId)
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 获取记录详情数据（用于编辑模式）
const fetchDisinfectionDetail = async (recordId) => {
  if (!recordId) return

  loading.value = true
  try {
    const response = await get(`customer-service/disinfection/detail/${recordId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到记录详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取记录详情失败:', error)
    ElMessage.error('获取记录详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    clean_area: apiData.clean_area || '',
    clean_type: apiData.clean_type || '',
    disinfection_document: apiData.disinfection_document?.rid || '',
    clean_time: apiData.clean_time || '',
    cleaner: apiData.cleaner || '',
    supervisor: apiData.supervisor || '',
    result: apiData.result || '',
    disinfectant_info: apiData.disinfectant_info || '',
    disinfection_process: apiData.disinfection_process || '',
    remark: apiData.remark || '',
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    clean_area: '',
    clean_type: '',
    disinfection_document: '',
    clean_time: '',
    cleaner: '',
    supervisor: '',
    result: '',
    disinfectant_info: '',
    disinfection_process: '',
    remark: '',
  })

  // 清理搜索状态
  baseDataStore.disinfectionDocs.clearSearch()

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      clean_area: form.clean_area,
      clean_type: form.clean_type,
      disinfection_document: form.disinfection_document,
      clean_time: form.clean_time,
      cleaner: form.cleaner,
      supervisor: form.supervisor,
      result: form.result,
      disinfectant_info: form.disinfectant_info,
      disinfection_process: form.disinfection_process,
      remark: form.remark,
    }

    let res
    if (props.mode === 'add') {
      res = await post('customer-service/disinfection/create/', submitData)
    } else {
      res = await put(`customer-service/disinfection/update/${props.itemId}/`, submitData)
    }

    ElMessage.success(props.mode === 'edit' ? '记录更新成功' : '记录创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>
