<template>
  <div
    class="disinfection-table-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          清洁消毒记录列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <!-- <el-table-column prop="rid" label="记录ID" min-width="200" fixed="left">
        <template #default="{ row }">
          <span class="font-mono text-xs">{{ row.rid }}</span>
        </template>
      </el-table-column> -->

      <el-table-column prop="clean_area" label="清洁区域" min-width="120">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.clean_area }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="clean_type" label="清洁类型" min-width="100">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.clean_type }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="disinfection_document_name" label="消毒规范" min-width="150">
        <template #default="{ row }">
          <el-tooltip
            :content="row.disinfection_document_name"
            placement="top"
            :disabled="row.disinfection_document_name?.length <= 15"
          >
            <span class="text-gray-700">
              {{
                row.disinfection_document_name?.length > 15
                  ? row.disinfection_document_name?.substring(0, 15) + '...'
                  : row.disinfection_document_name
              }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="clean_time" label="清洁时间" min-width="140">
        <template #default="{ row }">
          <div class="text-sm">
            <div class="font-medium text-gray-800">{{ formatDate(row.clean_time) }}</div>
            <div class="text-gray-500">{{ formatTime(row.clean_time) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="cleaner" label="清洁人员" min-width="100">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="24" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.cleaner?.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.cleaner }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="disinfectant_info" label="消毒剂信息" min-width="150">
        <template #default="{ row }">
          <el-tooltip
            :content="row.disinfectant_info"
            placement="top"
            :disabled="row.disinfectant_info?.length <= 20"
          >
            <span class="text-gray-700">
              {{
                row.disinfectant_info?.length > 20
                  ? row.disinfectant_info?.substring(0, 20) + '...'
                  : row.disinfectant_info
              }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="result" label="执行结果" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getResultTagType(row.result)" size="small">
            {{ getResultText(row.result) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="supervisor" label="监督人" min-width="100">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="24" class="mr-2 bg-blue-100 text-blue-600">
              {{ row.supervisor?.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.supervisor }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="290" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="default"
              size="small"
              @click.stop="handleRowClick(row)"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
            >
              查看
            </el-button>
            <el-button @click.stop="handleEdit(row)" type="default" size="small"> 编辑 </el-button>
            <el-button
              @click.stop="handleDelete(row)"
              type="danger"
              size="small"
              class="text-red-600 border-red-200 hover:bg-red-50"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { List } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get, del } from '@/utils/request.js'
import {
  getCleanDisinfectionResultText,
  getCleanDisinfectionResultTagType,
} from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const emit = defineEmits(['edit', 'row-click'])

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 转换API数据格式
const transformDisinfectionData = (apiData) => {
  return apiData.map((item) => washDisinfectionData(item))
}

const washDisinfectionData = (item) => {
  return {
    rid: item.rid,
    clean_area: item.clean_area,
    clean_type: item.clean_type,
    disinfection_document_name: item.disinfection_document_name,
    clean_time: item.clean_time,
    cleaner: item.cleaner,
    disinfectant_info: item.disinfectant_info,
    result: item.result,
    supervisor: item.supervisor,
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get('customer-service/disinfection/list/', requestParams)
    tableData.value = transformDisinfectionData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取清洁消毒记录列表失败:', error)
    ElMessage.error('获取清洁消毒记录列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 状态相关方法 - 使用全局常量工具函数
const getResultText = (result) => {
  return getCleanDisinfectionResultText(result)
}

const getResultTagType = (result) => {
  return getCleanDisinfectionResultTagType(result)
}

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm')
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 删除记录
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除记录ID为 ${row.rid} 的清洁消毒记录吗？删除后无法恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    )

    const deleteUrl = `customer-service/disinfection/delete/${row.rid}/`
    await del(deleteUrl)

    ElMessage.success('删除成功')

    // 重新加载数据
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      showErrorTip(error)
    }
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.disinfection-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.disinfection-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}
</style>
