<template>
  <el-dialog
    v-model="visible"
    title="清洁消毒记录详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 记录基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">记录信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>记录ID：</label>
              <span class="font-mono text-sm">{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <label>清洁时间：</label>
              <span>{{ detailData.clean_time }}</span>
            </div>
            <div class="detail-item">
              <label>清洁区域：</label>
              <el-tag type="info" size="small">{{ detailData.clean_area }}</el-tag>
            </div>
            <div class="detail-item">
              <label>清洁类型：</label>
              <span>{{ detailData.clean_type }}</span>
            </div>
            <div class="detail-item">
              <label>执行结果：</label>
              <el-tag :type="getResultTagType(detailData.result)" size="small">
                {{ detailData.result_display }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 人员信息 -->
        <div class="detail-section">
          <h3 class="section-title">人员信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>清洁人员：</label>
              <div class="flex items-center">
                <el-avatar :size="32" class="mr-2 bg-pink-100 text-white!">
                  {{ detailData.cleaner?.charAt(0) }}
                </el-avatar>
                <span class="font-medium">{{ detailData.cleaner }}</span>
              </div>
            </div>
            <div class="detail-item">
              <label>监督人员：</label>
              <div class="flex items-center">
                <el-avatar :size="32" class="mr-2 bg-blue-100 text-white!">
                  {{ detailData.supervisor?.charAt(0) }}
                </el-avatar>
                <span class="font-medium">{{ detailData.supervisor }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 消毒规范文档 -->
        <div v-if="detailData.disinfection_document" class="detail-section">
          <h3 class="section-title">消毒规范文档</h3>
          <div class="p-4 bg-gray-50 rounded-lg">
            <div class="grid grid-cols-2 gap-4 mb-4 ">
              <div class="detail-item">
                <label>文档名称：</label>
                <span class="font-medium">{{ detailData.disinfection_document.name }}</span>
              </div>
              <div class="detail-item">
                <label>版本号：</label>
                <el-tag type="success" size="small">{{
                  detailData.disinfection_document.version
                }}</el-tag>
              </div>
              <div class="detail-item">
                <label>发布日期：</label>
                <span>{{ detailData.disinfection_document.publish_date }}</span>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <el-tag
                  :type="getDocumentStatusTagType(detailData.disinfection_document.status)"
                  size="small"
                >
                  {{ detailData.disinfection_document.status_display }}
                </el-tag>
              </div>
            </div>

            <div class="detail-item mb-4">
              <label>适用范围：</label>
              <p class="text-gray-700 text-sm mt-1">
                {{ detailData.disinfection_document.applicable_scope }}
              </p>
            </div>

            <div class="detail-item">
              <label>文档描述：</label>
              <p class="text-gray-700 text-sm mt-1">
                {{ detailData.disinfection_document.description }}
              </p>
            </div>
          </div>
        </div>

        <!-- 消毒信息 -->
        <div class="detail-section">
          <h3 class="section-title">消毒信息</h3>
          <div class="space-y-4">
            <div class="detail-item">
              <label>消毒剂信息：</label>
              <span class="text-gray-700">{{ detailData.disinfectant_info }}</span>
            </div>

            <div class="detail-item">
              <label>消毒过程：</label>
              <div class="mt-2 p-3 bg-gray-50 rounded-lg">
                <p class="text-gray-700 text-sm whitespace-pre-wrap">
                  {{ detailData.disinfection_process }}
                </p>
              </div>
            </div>

            <div v-if="detailData.remark" class="detail-item">
              <label>备注信息：</label>
              <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p class="text-gray-700 text-sm whitespace-pre-wrap">{{ detailData.remark }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 记录元信息 -->
        <div class="detail-section">
          <h3 class="section-title">记录信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ detailData.creator_name }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ detailData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>最后更新：</label>
              <span>{{ detailData.updated_at }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          @click="handleEdit"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑记录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import {
  getCleanDisinfectionResultTagType,
  getDisinfectionDocumentStatusTagType,
} from '@/utils/constants.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`customer-service/disinfection/detail/${props.itemId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取清洁消毒记录详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 编辑记录
const handleEdit = () => {
  emit('edit', detailData.value)
}

// 状态相关方法
const getResultTagType = (result) => {
  return getCleanDisinfectionResultTagType(result)
}

const getDocumentStatusTagType = (status) => {
  return getDisinfectionDocumentStatusTagType(status)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.detail-item label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.detail-item span,
.detail-item p {
  color: #374151;
}

.detail-item .el-tag {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
