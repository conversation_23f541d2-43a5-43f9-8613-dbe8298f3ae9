<template>
  <el-dialog
    v-model="visible"
    title="消毒规范文档详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>文档标题：</label>
              <span>{{ detailData.title || detailData.name }}</span>
            </div>
            <div class="detail-item">
              <label>版本号：</label>
              <span>{{ detailData.version_number || detailData.version }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(detailData.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>发布状态：</label>
              <el-tag :type="getStatusType(detailData.publish_status || detailData.status)">
                {{ detailData.status_display || getStatusLabel(detailData.status) }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 发布信息 -->
        <div class="detail-section">
          <h3 class="section-title">发布信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>发布时间：</label>
              <span>{{
                detailData.publish_date ? formatDate(detailData.publish_date) : '未发布'
              }}</span>
            </div>
            <div class="detail-item">
              <label>文档ID：</label>
              <span class="font-mono">{{ detailData.rid || detailData.id }}</span>
            </div>
          </div>
        </div>

        <!-- 文档内容 -->
        <div class="detail-section">
          <h3 class="section-title">文档内容</h3>
          
          <!-- 适用范围 -->
          <div v-if="detailData.applicable_scope" class="detail-item mb-4">
            <label>适用范围：</label>
            <p class="text-gray-700 text-sm mt-1">
              {{ detailData.applicable_scope }}
            </p>
          </div>

          <!-- 文档描述 -->
          <div v-if="detailData.description" class="detail-item mb-4">
            <label>文档描述：</label>
            <div class="mt-2 p-3 bg-gray-50 rounded-lg">
              <p class="text-gray-700 text-sm whitespace-pre-wrap">
                {{ detailData.description }}
              </p>
            </div>
          </div>

          <!-- 文档文件 -->
          <div v-if="detailData.file" class="detail-item">
            <label>文档文件：</label>
            <div class="mt-2">
              <FileDisplayList :file-list="detailData.file_url" max-height="300px" />
            </div>
          </div>
        </div>

        <!-- 附件信息 -->
        <div v-if="detailData.infection_file" class="detail-section">
          <h3 class="section-title">附件信息</h3>
          <div class="attachment-content p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center">
              <el-icon class="text-2xl text-gray-500 mr-3">
                <Document />
              </el-icon>
              <div>
                <div class="font-medium text-gray-800">{{ detailData.infection_file_name }}</div>
                <div class="text-sm text-gray-500 mt-1">
                  文件ID: {{ detailData.infection_file }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          @click="handleEdit"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑文档
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { Document } from '@element-plus/icons-vue'
import {
  getDisinfectionDocumentStatusTagType,
  getDisinfectionDocumentStatusText,
} from '@/utils/constants.js'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import { format } from 'date-fns'
import { formatDate } from '@/utils/dateUtils.js'
import FileDisplayList from '@/components/FileDisplayList.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`customer-service/disinfection-document/detail/${props.itemId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取消毒规范文档详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 编辑文档
const handleEdit = () => {
  emit('edit', detailData.value)
}

// 状态相关方法
const getStatusType = (status) => {
  return getDisinfectionDocumentStatusTagType(status)
}

const getStatusLabel = (status) => {
  return getDisinfectionDocumentStatusText(status)
}

// 时间格式化
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.attachment-content {
  color: #374151;
  line-height: 1.625;
}

.timeline-content {
  position: relative;
  padding-left: 1.5rem;
}

.timeline-item {
  position: relative;
  padding-bottom: 1rem;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -1.375rem;
  top: 1.5rem;
  width: 2px;
  height: calc(100% - 1rem);
  background-color: #e5e7eb;
}

.timeline-dot {
  position: absolute;
  left: -1.5rem;
  top: 0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #e5e7eb;
}

.timeline-content-item {
  padding-left: 0.5rem;
}

.timeline-time {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.timeline-desc {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
