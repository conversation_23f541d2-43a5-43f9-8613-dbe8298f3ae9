<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="disinfection-document-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="disinfection-document-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="文档标题" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入文档标题，如：产房消毒操作规范"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="版本号" prop="version">
              <el-input
                v-model="form.version"
                placeholder="请输入版本号，如：v1.0.0"
                maxlength="20"
              />
            </el-form-item>

            <el-form-item label="适用范围" prop="applicable_scope">
              <el-input
                v-model="form.applicable_scope"
                type="textarea"
                :rows="3"
                placeholder="请输入适用范围，如：适用于产房、新生儿护理室等区域的消毒操作"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="发布日期" prop="publish_date">
              <el-date-picker
                v-model="form.publish_date"
                type="date"
                placeholder="选择发布日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">详细信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="文档描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                placeholder="请输入文档描述，详细说明消毒流程、操作步骤等内容"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="文档状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择文档状态" class="w-full">
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 文件上传 -->
        <div class="form-section mb-6">
          <h4 class="section-title">文件上传</h4>
          <el-form-item label="文档文件" prop="file">
            <FileUpload
              v-model="form.file"
              :urls="form.file_url"
              :file-types="['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']"
              :max-size="10"
              :multiple="false"
              :limit="1"
              action="file/disinfect/file/upload/"
              field="disinfect_file"
              upload-text="上传文档文件"
              custom-tip-text="支持 PDF、Word、图片格式，文件大小不超过 10MB"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '创建文档' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { DISINFECTION_DOCUMENT_STATUS_OPTIONS } from '@/utils/constants.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'
import FileUpload from '@/components/FileUpload.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑消毒规范文档' : '新增消毒规范文档'
})

// 状态选项
const statusOptions = DISINFECTION_DOCUMENT_STATUS_OPTIONS

// 表单数据
const form = reactive({
  name: '',
  version: '',
  applicable_scope: '',
  publish_date: '',
  description: '',
  status: 'DRAFT',
  file: '',
})

// 表单验证规则
const rules = computed(() => ({
  name: [{ required: true, message: '请输入文档标题', trigger: 'blur' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
  applicable_scope: [{ required: true, message: '请输入适用范围', trigger: 'blur' }],
  publish_date: [{ required: true, message: '请选择发布日期', trigger: 'change' }],
  description: [{ required: true, message: '请输入文档描述', trigger: 'blur' }],
  status: [{ required: true, message: '请选择文档状态', trigger: 'change' }],
  file: [{ required: true, message: '请上传文档文件', trigger: 'change' }],
}))

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(resetForm, 5)
    if (visible) {
      if (props.itemId) {
        // 编辑模式：获取详情数据
        await fetchDocumentDetail(props.itemId)
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 获取文档详情数据（用于编辑模式）
const fetchDocumentDetail = async (documentId) => {
  if (!documentId) return

  loading.value = true
  try {
    const response = await get(`customer-service/disinfection-document/detail/${documentId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到文档详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取文档详情失败:', error)
    ElMessage.error('获取文档详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    name: apiData.title || apiData.name || '',
    version: apiData.version_number || apiData.version || '',
    applicable_scope: apiData.applicable_scope || '',
    publish_date:
      apiData.publish_date || (apiData.published_at ? apiData.published_at.split('T')[0] : ''),
    description: apiData.description || '',
    status: apiData.publish_status || 'DRAFT',
    file: apiData.file || '',
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    name: '',
    version: '',
    applicable_scope: '',
    publish_date: '',
    description: '',
    status: 'DRAFT',
    file: '',
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  console.log('form', form.file)
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      name: form.name,
      version: form.version,
      applicable_scope: form.applicable_scope,
      publish_date: form.publish_date,
      description: form.description,
      status: form.status,
      file: form.file,
    }

    let res
    if (!props.itemId) {
      res = await post('customer-service/disinfection-document/create/', submitData)
    } else {
      res = await put(`customer-service/disinfection-document/update/${props.itemId}/`, submitData)
    }

    ElMessage.success(props.itemId ? '文档更新成功' : '文档创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-date-editor.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-editor.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
