<template>
  <div class="standards-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">消毒管理 - 消毒规范文库</h1>
            <p class="text-sm text-gray-600 mt-1">管理消毒规范文档，制定和维护消毒标准</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增规范
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <DisinfectionDocumentTable
      ref="documentTableRef"
      :filters="currentFilters"
      @edit="handleEdit"
      @row-click="handleRowClick"
    />

    <!-- 表单对话框 -->
    <DisinfectionDocumentFormDialog
      v-model="showFormDialog"
      :item-id="currentRow?.rid"
      @success="handleSubmitSuccess"
    />

    <!-- 详情查看对话框 -->
    <DisinfectionDocumentDetailDialog
      v-model="showDetailDialog"
      :item-id="currentRow?.rid"
      @edit="handleEditFromDetail"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import DisinfectionDocumentTable from '@/views/disinfection-management/components/DisinfectionDocumentTable.vue'
import DisinfectionDocumentFormDialog from '@/views/disinfection-management/components/DisinfectionDocumentFormDialog.vue'
import DisinfectionDocumentDetailDialog from '@/views/disinfection-management/components/DisinfectionDocumentDetailDialog.vue'

// 响应式数据
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref(null)

// 获取 table 组件引用
const documentTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
  publish_date: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入文档名称或版本号',
  },
  {
    key: 'publish_date',
    type: 'date',
    label: '发布日期',
    placeholder: '选择发布日期',
  },
]

// 新增文档
const handleCreate = () => {
  currentRow.value = null
  showFormDialog.value = true
}

// 编辑文档
const handleEdit = (row) => {
  currentRow.value = row
  showFormDialog.value = true
}

// 表单提交成功
const handleSubmitSuccess = () => {
  showFormDialog.value = false
  currentRow.value = null
  documentTableRef.value?.refresh() // 刷新表格数据
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  documentTableRef.value?.resetPagination()
}

// 行点击 - 查看详情
const handleRowClick = (row) => {
  currentRow.value = row
  showDetailDialog.value = true
}

// 从详情对话框编辑
const handleEditFromDetail = (row) => {
  currentRow.value = row
  showDetailDialog.value = false
  showFormDialog.value = true
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentRow.value = null
}
</script>

<style scoped>
.standards-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
