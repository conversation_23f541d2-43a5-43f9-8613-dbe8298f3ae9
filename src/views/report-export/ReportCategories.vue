<template>
  <div class="report-categories bg-white rounded-lg border border-gray-200 p-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-2">报表分类</h3>

    <div class="space-y-1">
      <div v-for="category in categories" :key="category.id" class="category-group">
        <!-- 分类标题 -->
        <div class="category-header flex items-center p-3 font-medium text-gray-700">
          <span>{{ category.name }}</span>
        </div>

        <!-- 报表列表 -->
        <div class="ml-2 space-y-1">
          <div
            v-for="report in category.reports"
            :key="report.id"
            @click="selectReport(report.id)"
            :class="[
              'report-item flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200',
              activeReport === report.id
                ? 'bg-pink-50 text-pink-700 border-l-4 border-pink-500'
                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800',
            ]"
          >
            <el-icon class="w-6! h-6! mr-3">
              <component :is="getIconComponent(report.icon)" />
            </el-icon>
            <span class="text-sm">{{ report.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  TrendCharts,
  Document,
  User,
  Calendar,
  House,
  Monitor,
  Coin,
} from '@element-plus/icons-vue'

const props = defineProps({
  categories: {
    type: Array,
    required: true,
  },
  activeReport: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['select-report'])

const selectReport = (reportId) => {
  emit('select-report', reportId)
}

const getIconComponent = (iconName) => {
  const iconMap = {
    TrendingUp: TrendCharts,
    FileText: Document,
    Users: User,
    Calendar: Calendar,
    Home: House,
    Activity: Monitor,
    DollarSign: Coin,
  }
  return iconMap[iconName] || Document
}
</script>

<style scoped>
.report-item {
  border-left: 4px solid transparent;
}

.report-item:hover {
  transform: translateX(2px);
}

.category-group:not(:last-child) {
  margin-bottom: 1px;
}
</style>
