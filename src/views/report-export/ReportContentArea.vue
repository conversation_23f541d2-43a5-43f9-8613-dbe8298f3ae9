<template>
  <div class="report-content-area">
    <!-- 报表标题和导出按钮 -->
    <div class="report-header bg-white rounded-lg border border-gray-200 p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h2 class="text-xl font-semibold text-gray-800">
            {{ reportConfig.title || '请选择报表' }}
          </h2>
          <p class="text-sm text-gray-600 mt-1">
            {{ getReportDescription() }}
          </p>
        </div>

        <div v-if="reportData" class="flex gap-3">
          <el-button
            @click="handleExport('csv')"
            type="success"
            size="small"
            class="bg-green-500 hover:bg-green-600 border-green-500"
          >
            <el-icon class="mr-1">
              <Download />
            </el-icon>
            导出CSV
          </el-button>
          <el-button
            @click="handleExport('pdf')"
            type="info"
            size="small"
            class="bg-blue-500 hover:bg-blue-600 border-blue-500"
          >
            <el-icon class="mr-1">
              <Document />
            </el-icon>
            导出PDF
          </el-button>
        </div>
      </div>
    </div>

    <!-- 报表内容 -->
    <div class="report-content bg-white rounded-lg border border-gray-200 p-6">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-20">
        <el-icon class="is-loading text-2xl text-pink-500 mr-3">
          <Loading />
        </el-icon>
        <span class="text-gray-600">正在生成报表...</span>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!reportData" class="flex flex-col items-center justify-center py-20">
        <TrendCharts class="w-16 h-16 text-gray-300 mb-4" />
        <p class="text-gray-500 mb-2">请选择报表并设置筛选条件</p>
        <p class="text-sm text-gray-400">点击"搜索"按钮生成报表</p>
      </div>

      <!-- 报表数据 -->
      <div v-else class="report-data">
        <!-- 图表区域 -->
        <div v-if="reportConfig.type === 'table_chart' && reportData.chartData" class="mb-8">
          <h3 class="text-lg font-medium text-gray-800 mb-4">趋势图表</h3>
          <ReportChart :chart-data="reportData.chartData" :chart-type="getChartType()" />
        </div>

        <!-- 数据表格 -->
        <div v-if="reportData.tableData && reportData.tableData.length > 0">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-800">数据详情</h3>
            <div class="text-sm text-gray-600">共 {{ reportData.tableData.length }} 条记录</div>
          </div>
          <ReportTable :table-data="reportData.tableData" :columns="reportConfig.columns" />
        </div>

        <!-- 无数据状态 -->
        <div v-else class="flex flex-col items-center justify-center py-12">
          <Search class="w-12 h-12 text-gray-300 mb-3" />
          <p class="text-gray-500">暂无数据</p>
          <p class="text-sm text-gray-400 mt-1">请调整筛选条件后重新搜索</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { Download, Document, Loading, TrendCharts, Search } from '@element-plus/icons-vue'

import ReportChart from './ReportChart.vue'
import ReportTable from './ReportTable.vue'

const props = defineProps({
  reportData: {
    type: Object,
    default: null,
  },
  reportConfig: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['export'])

// 获取报表描述
const getReportDescription = () => {
  if (!props.reportConfig.title) {
    return '选择左侧分类中的报表类型开始分析'
  }

  const descriptions = {
    入住率统计报表: '分析月子中心房间入住率变化趋势',
    房间周转率报表: '统计房间周转效率和利用情况',
    房型收入分析: '分析不同房型的收入贡献度',
    新生儿喂养汇总: '统计新生儿喂养情况和营养摄入',
    产妇康复进展统计: '跟踪产妇康复情况和健康指标',
    员工工时与负荷报表: '分析员工工作负荷和排班情况',
    客户满意度汇总报告: '汇总客户反馈和满意度评分',
    月度营收汇总: '统计月度收入和财务指标',
  }

  return descriptions[props.reportConfig.title] || '报表数据分析'
}

// 获取图表类型
const getChartType = () => {
  const chartTypes = {
    入住率统计报表: 'line',
    房间周转率报表: 'bar',
    房型收入分析: 'pie',
    月度营收汇总: 'bar',
  }

  return chartTypes[props.reportConfig.title] || 'line'
}

// 处理导出
const handleExport = (type) => {
  emit('export', type)
}
</script>

<style scoped>
.report-data {
  min-height: 400px;
}
</style>
