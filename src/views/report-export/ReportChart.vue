<template>
  <div class="report-chart">
    <div
      ref="chartContainer"
      class="chart-container bg-gray-50 border border-gray-200 rounded-lg p-6"
    >
      <!-- 图表占位符 -->
      <div class="flex flex-col items-center justify-center h-80">
        <TrendCharts class="w-16 h-16 text-gray-400 mb-4" />
        <p class="text-gray-500 text-lg font-medium mb-2">{{ getChartTitle() }}</p>
        <p class="text-gray-400 text-sm">{{ getChartDescription() }}</p>

        <!-- 简单的模拟图表 -->
        <div class="mt-8 w-full max-w-md">
          <div v-if="chartType === 'line'" class="line-chart-mock">
            <div class="chart-title text-center text-sm font-medium text-gray-600 mb-4">
              {{ getDataLabel() }}
            </div>
            <div class="flex items-end justify-between h-32 border-b border-l border-gray-300">
              <div
                v-for="(value, index) in chartData.occupancyRates || []"
                :key="index"
                class="flex flex-col items-center"
              >
                <div
                  class="bg-pink-500 rounded-t-sm transition-all duration-500 ease-in-out"
                  :style="{
                    height: `${(value / 100) * 120}px`,
                    width: '20px',
                    marginBottom: '8px',
                  }"
                ></div>
                <span class="text-xs text-gray-500 transform -rotate-45 origin-bottom-left">
                  {{ formatDate(chartData.dates?.[index]) }}
                </span>
              </div>
            </div>
            <div class="text-center mt-2 text-xs text-gray-500">日期</div>
          </div>

          <div v-else-if="chartType === 'bar'" class="bar-chart-mock">
            <div class="chart-title text-center text-sm font-medium text-gray-600 mb-4">
              数据条形图
            </div>
            <div class="space-y-2">
              <div
                v-for="(value, index) in chartData.values || [80, 65, 90, 75]"
                :key="index"
                class="flex items-center"
              >
                <span class="w-16 text-xs text-gray-600">项目{{ index + 1 }}</span>
                <div class="flex-1 bg-gray-200 rounded-full h-6 ml-2">
                  <div
                    class="bg-gradient-to-r from-pink-500 to-pink-400 h-6 rounded-full transition-all duration-1000 ease-out"
                    :style="{ width: `${value}%` }"
                  ></div>
                </div>
                <span class="ml-2 text-xs font-medium text-gray-700">{{ value }}%</span>
              </div>
            </div>
          </div>

          <div v-else-if="chartType === 'pie'" class="pie-chart-mock">
            <div class="chart-title text-center text-sm font-medium text-gray-600 mb-4">
              饼状图分布
            </div>
            <div class="flex justify-center">
              <div class="relative w-32 h-32">
                <!-- 简单的饼图模拟 -->
                <div
                  class="w-full h-full rounded-full bg-gradient-to-r from-pink-400 via-purple-400 to-blue-400"
                ></div>
                <div class="absolute inset-0 flex items-center justify-center">
                  <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center">
                    <span class="text-xs font-bold text-gray-700">100%</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="mt-4 flex justify-center space-x-4 text-xs">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-pink-400 rounded-full mr-1"></div>
                <span class="text-gray-600">类型A</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-purple-400 rounded-full mr-1"></div>
                <span class="text-gray-600">类型B</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-400 rounded-full mr-1"></div>
                <span class="text-gray-600">类型C</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { TrendCharts } from '@element-plus/icons-vue'

const props = defineProps({
  chartData: {
    type: Object,
    required: true,
  },
  chartType: {
    type: String,
    default: 'line',
    validator: (value) => ['line', 'bar', 'pie'].includes(value),
  },
})

// 获取图表标题
const getChartTitle = () => {
  const titles = {
    line: '趋势变化图表',
    bar: '对比分析图表',
    pie: '占比分布图表',
  }
  return titles[props.chartType] || '数据图表'
}

// 获取图表描述
const getChartDescription = () => {
  const descriptions = {
    line: '展示数据随时间的变化趋势',
    bar: '比较不同类别的数据差异',
    pie: '显示各部分占总体的比例',
  }
  return descriptions[props.chartType] || '数据可视化展示'
}

// 获取数据标签
const getDataLabel = () => {
  if (props.chartData.occupancyRates) {
    return '入住率趋势 (%)'
  }
  return '数据趋势'
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return dateStr.split('-').slice(1).join('/')
}
</script>

<style scoped>
.chart-container {
  min-height: 400px;
}

.line-chart-mock {
  animation: fadeInUp 0.8s ease-out;
}

.bar-chart-mock {
  animation: fadeInLeft 0.8s ease-out;
}

.pie-chart-mock {
  animation: fadeInScale 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
