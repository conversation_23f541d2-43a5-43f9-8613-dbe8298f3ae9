<template>
  <div class="report-table">
    <el-table
      :data="tableData"
      stripe
      border
      class="w-full"
      :header-cell-style="{
        background: '#f8fafc',
        color: '#374151',
        fontWeight: '600',
        borderColor: '#e5e7eb',
      }"
      :cell-style="{ borderColor: '#e5e7eb' }"
      :row-style="{ borderColor: '#e5e7eb' }"
    >
      <el-table-column
        v-for="column in columns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :formatter="getFormatter(column.prop)"
        :align="getAlign(column.prop)"
        show-overflow-tooltip
      >
        <template #default="scope" v-if="needsCustomRender(column.prop)">
          <!-- 百分比格式 -->
          <span v-if="column.prop.includes('Rate')" class="font-medium text-green-600">
            {{ scope.row[column.prop] }}
          </span>
          <!-- 数量格式 -->
          <span v-else-if="isNumericColumn(column.prop)" class="font-mono">
            {{ formatNumber(scope.row[column.prop]) }}
          </span>
          <!-- 日期格式 -->
          <span v-else-if="column.prop === 'date'" class="text-gray-600">
            {{ formatDate(scope.row[column.prop]) }}
          </span>
          <!-- 默认格式 -->
          <span v-else>
            {{ scope.row[column.prop] }}
          </span>
        </template>
      </el-table-column>

      <!-- 空数据插槽 -->
      <template #empty>
        <div class="flex flex-col items-center justify-center py-12">
          <Grid class="w-12 h-12 text-gray-300 mb-3" />
          <p class="text-gray-500">暂无数据</p>
        </div>
      </template>
    </el-table>

    <!-- 分页器 (如果需要) -->
    <div v-if="showPagination && tableData.length > pageSize" class="flex justify-center mt-6">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="tableData.length"
        layout="total, sizes, prev, pager, next, jumper"
        background
        class="pagination-custom"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElTable, ElTableColumn, ElPagination } from 'element-plus'
import { Grid } from '@element-plus/icons-vue'

const props = defineProps({
  tableData: {
    type: Array,
    required: true,
  },
  columns: {
    type: Array,
    required: true,
  },
  showPagination: {
    type: Boolean,
    default: false,
  },
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)

// 判断列是否需要自定义渲染
const needsCustomRender = (prop) => {
  return prop.includes('Rate') || isNumericColumn(prop) || prop === 'date'
}

// 判断是否为数字列
const isNumericColumn = (prop) => {
  const numericProps = ['totalRooms', 'occupiedRooms', 'vacantRooms', 'totalMilk', 'feedingTimes']
  return numericProps.includes(prop)
}

// 获取列对齐方式
const getAlign = (prop) => {
  if (isNumericColumn(prop) || prop.includes('Rate')) {
    return 'center'
  }
  return 'left'
}

// 获取格式化函数
const getFormatter = (prop) => {
  return null // 使用模板中的自定义渲染
}

// 格式化数字
const formatNumber = (value) => {
  if (typeof value === 'number') {
    return value.toLocaleString()
  }
  return value
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}
</script>

<style scoped>
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-table__body-wrapper) {
  border-radius: 0 0 8px 8px;
}

:deep(.pagination-custom .el-pagination__total) {
  color: #6b7280;
}

:deep(.pagination-custom .el-pager li.is-active) {
  background-color: #ec4899;
  border-color: #ec4899;
}

:deep(.pagination-custom .el-pager li:hover) {
  color: #ec4899;
}

:deep(.pagination-custom .btn-next:hover),
:deep(.pagination-custom .btn-prev:hover) {
  color: #ec4899;
}
</style>
