<template>
  <div class="customer-feedback-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">客户反馈与满意度管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理客户反馈信息、满意度问卷及统计分析</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="mb-6">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="" type="border-card">
        <el-tab-pane label="反馈列表" name="feedbackList">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <ChatLineRound />
              </el-icon>
              反馈列表
            </div>
          </template>
          <!-- 筛选搜索组件 -->
          <FilterPanel
            :fields="filterFields"
            :filters="filters"
            @search="handleSearch"
            class="mb-6"
          />

          <!-- 反馈表格 -->
          <FeedbackTable
            ref="tableRef"
            :filters="filters"
            @view="handleViewFeedback"
            @refresh="handleSearch"
          />

          <!-- 反馈详情对话框 -->
          <FeedbackDetailDialog
            v-model="detailDialogVisible"
            :item-id="selectedFeedbackId"
            @close="handleCloseDetailDialog"
            @refresh="handleSearch"
          />
        </el-tab-pane>

        <el-tab-pane label="问卷管理" name="questionnaire">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Edit />
              </el-icon>
              问卷管理
            </div>
          </template>
          <QuestionnaireTab />
        </el-tab-pane>

        <el-tab-pane label="问卷回复" name="questionnaireResponse">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Document />
              </el-icon>
              问卷回复
            </div>
          </template>
          <QuestionnaireResponseTab />
        </el-tab-pane>

        <el-tab-pane label="统计报告" name="statistics">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <TrendCharts />
              </el-icon>
              统计报告
            </div>
          </template>
          <StatisticsTab />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElTabs, ElTabPane, ElIcon } from 'element-plus'
import { ChatLineRound, Edit, TrendCharts, Document } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import FeedbackTable from '@/components/customer-feedback/FeedbackTable.vue'
import FeedbackDetailDialog from '@/components/customer-feedback/FeedbackDetailDialog.vue'
import QuestionnaireTab from '@/components/customer-feedback/QuestionnaireTab.vue'
import QuestionnaireResponseTab from '@/components/customer-feedback/QuestionnaireResponseTab.vue'
import StatisticsTab from '@/components/customer-feedback/StatisticsTab.vue'
import { FEEDBACK_TYPE_OPTIONS, FEEDBACK_STATUS_OPTIONS } from '@/utils/constants.js'

// 响应式数据
const activeTab = ref('feedbackList')
const detailDialogVisible = ref(false)
const selectedFeedbackId = ref(null)
const tableRef = ref(null)

// 筛选条件
const filters = reactive({
  sk: '',
  fd_type: '',
  fd_time: '',
  status: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '产妇名字/房间号',
  },
  {
    key: 'fd_type',
    type: 'select',
    label: '反馈类型',
    placeholder: '选择反馈类型',
    options: FEEDBACK_TYPE_OPTIONS,
  },
  {
    key: 'fd_time',
    type: 'datetime',
    label: '反馈时间',
    placeholder: '选择反馈时间',
  },
  {
    key: 'status',
    type: 'select',
    label: '处理状态',
    placeholder: '选择处理状态',
    options: FEEDBACK_STATUS_OPTIONS,
  },
])

// 方法
const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)
}

const handleSearch = () => {
  // 触发表格刷新数据
  if (tableRef.value) {
    tableRef.value.resetPagination()
  }
}

const handleViewFeedback = (feedback) => {
  selectedFeedbackId.value = feedback.fid
  detailDialogVisible.value = true
}

// 对话框相关方法
const handleCloseDetailDialog = () => {
  detailDialogVisible.value = false
  selectedFeedbackId.value = null
}
</script>

<style scoped>
.customer-feedback-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

/* 自定义 tabs 样式 */
:deep(.el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

:deep(.el-tabs__content) {
  padding: 24px;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
}

:deep(.el-tabs__item:hover) {
  color: #ec4899;
}
</style>
