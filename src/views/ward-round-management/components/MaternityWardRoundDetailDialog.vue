<template>
  <el-dialog
    v-model="visible"
    title="产妇查房记录详情"
    width="700px"
    align-center
    :before-close="handleClose"
    class="maternity-ward-round-detail-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <div v-if="wardRoundData" class="ward-round-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">查房时间:</span>
              <span class="value">{{ wardRoundData.record_time }}</span>
            </div>
            <div class="detail-item">
              <span class="label">产妇信息:</span>
              <span class="value">{{ wardRoundData.maternity }}</span>
            </div>
            <div class="detail-item">
              <span class="label">入院编号:</span>
                <el-tag type="info" size="small">{{ wardRoundData.maternity_admission || '-' }}</el-tag>

            </div>
            <div class="detail-item">
              <span class="label">医生类型:</span>
              <el-tag type="info" size="small" effect="light">
                {{ wardRoundData.doctor_type }}
              </el-tag>
            </div>
            <div class="detail-item">
              <span class="label">查房医生:</span>
              <span class="value">{{ wardRoundData.doctor }}</span>
            </div>
            <div class="detail-item">
              <span class="label">异常情况:</span>
              <el-tag
                :type="wardRoundData.has_abnormal ? 'danger' : 'success'"
                size="small"
                effect="light"
              >
                {{ wardRoundData.has_abnormal ? '有异常' : '正常' }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 查房内容 -->
        <div class="detail-section mb-6">
          <h4 class="section-title">查房内容</h4>
          <div class="content-box">
            <p class="content-text">{{ wardRoundData.record_content || '暂无内容' }}</p>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="detail-section">
          <h4 class="section-title">系统信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <span class="label">创建时间:</span>
              <el-tag type="info" size="small">{{ wardRoundData.created_at }}</el-tag>
            </div>
            <div class="detail-item">
              <span class="label">更新时间:</span>
              <el-tag type="info" size="small">{{ wardRoundData.updated_at }}</el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty description="暂无数据" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">
          编辑记录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: String,
    default: '',
  },
  itemData: {
    type: Object,
    default: null,
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'close', 'edit'])

// 响应式数据
const loading = ref(false)
const wardRoundData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`customer-service/wardround/maternity/detail/${props.itemId}/`)
    wardRoundData.value = response
  } catch (error) {
    console.error('获取查房记录详情失败:', error)
    ElMessage.error('获取查房记录详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('close')
}

// 编辑记录
const handleEdit = () => {
  emit('edit', wardRoundData.value)
  handleClose()
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && props.itemId) {
      fetchDetail()
    } else {
      wardRoundData.value = null
    }
  }
)
</script>

<style scoped>
.maternity-ward-round-detail-dialog {
  --el-dialog-border-radius: 8px;
}

.ward-round-detail-content {
  padding: 0.5rem;
}

.detail-section {
  background: #f8fafc;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  padding-left: 0.75rem;
  border-left: 4px solid #ec4899;
  display: inline-block;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.value {
  color: #374151;
  flex: 1;
}

.content-box {
  background: white;
  border-radius: 4px;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

.content-text {
  color: #374151;
  line-height: 1.6;
  margin: 0;
  white-space: pre-wrap;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
</style>
