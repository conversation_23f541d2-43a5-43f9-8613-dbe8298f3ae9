<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    class="maternity-ward-round-form-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        class="ward-round-form"
        v-loading="loading"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          
          <el-form-item label="产妇信息" prop="maternity_admission" required>
            <el-select
              v-model="form.maternity_admission"
              placeholder="请选择产妇"
              filterable
              :disabled="isEdit"
              :loading="maternityLoading"
              style="width: 100%"
              @change="handleMaternityChange"
              @focus="loadMaternityList"
            >
              <el-option
                v-for="item in maternityOptions"
                :key="item.aid"
                :label="`${item.maternity}`"
                :value="item.aid"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="查房时间" prop="record_time" required>
            <el-date-picker
              v-model="form.record_time"
              type="datetime"
              placeholder="请选择查房时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="医生类型" prop="doctor_type" required>
            <el-input
              v-model="form.doctor_type"
              placeholder="请输入医生类型，如：产科医生、新生儿科医生等"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="查房医生" prop="doctor" required>
            <el-input
              v-model="form.doctor"
              placeholder="请输入查房医生姓名"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="异常情况" prop="has_abnormal">
            <el-radio-group v-model="form.has_abnormal">
              <el-radio :value="false">正常</el-radio>
              <el-radio :value="true">有异常</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <!-- 查房内容 -->
        <div class="form-section">
          <h4 class="section-title">查房内容</h4>
          
          <el-form-item label="查房记录" prop="record_content" required>
            <el-input
              v-model="form.record_content"
              type="textarea"
              :rows="6"
              placeholder="请详细记录查房情况，包括产妇状态、检查结果、医嘱等"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { get, post, put } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
  itemId: {
    type: String,
    default: '',
  },
  itemData: {
    type: Object,
    default: null,
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const maternityLoading = ref(false)
const maternityOptions = ref([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => props.mode === 'edit')

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑产妇查房记录' : '新建产妇查房记录'
})

// 表单数据
const form = reactive({
  maternity_admission: '',
  record_time: '',
  doctor_type: '',
  doctor: '',
  has_abnormal: false,
  record_content: '',
})

// 表单验证规则
const formRules = {
  maternity_admission: [
    { required: true, message: '请选择产妇', trigger: 'change' }
  ],
  record_time: [
    { required: true, message: '请选择查房时间', trigger: 'change' }
  ],
  doctor_type: [
    { required: true, message: '请输入医生类型', trigger: 'blur' }
  ],
  doctor: [
    { required: true, message: '请输入查房医生', trigger: 'blur' }
  ],
  record_content: [
    { required: true, message: '请输入查房记录', trigger: 'blur' }
  ],
}

// 使用滚动到顶部的组合式函数
useDialogScrollToTop(visible)

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`customer-service/wardround/maternity/detail/${props.itemId}/`)
    fillFormData(response)
  } catch (error) {
    console.error('获取查房记录详情失败:', error)
    ElMessage.error('获取查房记录详情失败')
  } finally {
    loading.value = false
  }
}

// 加载产妇列表
const loadMaternityList = async () => {
  if (isEdit.value || maternityOptions.value.length > 0) {
    return // 编辑模式或已加载时不重复加载
  }

  maternityLoading.value = true
  try {
    const data = await get('customer-service/wardround/maternity-select-list/')
    maternityOptions.value = data || []
  } catch (error) {
    console.error('获取产妇列表失败:', error)
    ElMessage.error('获取产妇列表失败')
    maternityOptions.value = []
  } finally {
    maternityLoading.value = false
  }
}

// 产妇选择变化处理
const handleMaternityChange = (value) => {
  // 可以在这里处理产妇选择变化的逻辑
  console.log('Selected maternity:', value)
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    maternity_admission: '',
    record_time: getCurrentTime(),
    doctor_type: '',
    doctor: '',
    has_abnormal: false,
    record_content: '',
  })
  
  maternityOptions.value = []
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 填充表单数据（编辑模式）
const fillFormData = (data) => {
  if (!data) return

  Object.assign(form, {
    maternity_admission: data.maternity_admission || '',
    record_time: data.record_time || '',
    doctor_type: data.doctor_type || '',
    doctor: data.doctor || '',
    has_abnormal: data.has_abnormal || false,
    record_content: data.record_content || '',
  })

  // 编辑模式下，添加当前产妇到选项列表中，确保下拉框能正确显示
  if (data.maternity && data.maternity_admission) {
    maternityOptions.value = [{
      aid: data.maternity_admission,
      maternity: data.maternity // 直接使用接口返回的产妇信息
    }]
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    let submitData
    let res

    if (isEdit.value && props.itemId) {
      // 更新模式：只发送允许更新的字段（不包括产妇信息）
      submitData = {
        record_time: form.record_time,
        doctor_type: form.doctor_type,
        doctor: form.doctor,
        has_abnormal: form.has_abnormal,
        record_content: form.record_content,
      }
      console.log('Update data:', submitData)
      res = await put(`customer-service/wardround/maternity/update/${props.itemId}/`, submitData)
      ElMessage.success('产妇查房记录更新成功！')
    } else {
      // 创建模式：发送所有字段
      submitData = { ...form }
      console.log('Create data:', submitData)
      res = await post('customer-service/wardround/maternity/create/', submitData)
      ElMessage.success('产妇查房记录创建成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      if (isEdit.value && props.itemId) {
        // 编辑模式：获取详情数据
        fetchDetail()
      } else {
        // 新建模式：重置表单
        resetForm()
      }
    }
  }
)
</script>

<style scoped>
.maternity-ward-round-form-dialog {
  --el-dialog-border-radius: 8px;
}

.ward-round-form {
  padding: 0.5rem;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #ec4899;
  display: inline-block;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}
</style>
