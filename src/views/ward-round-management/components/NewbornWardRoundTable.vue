<template>
  <div class="newborn-ward-round-table-container">
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @row-click="handleRowClick"
        class="ward-round-table"
      >

        <el-table-column prop="record_time" label="查房时间" width="180" align="center">
        <template #default="{ row }">
          <div class="text-sm">
            <div class="font-medium text-gray-800">{{ formatDate(row.record_time) }}</div>
            <div class="text-gray-500">{{ formatTime(row.record_time) }}</div>
          </div>
        </template>
      </el-table-column>

        <el-table-column prop="maternity" label="产妇信息" align="center">
          <template #default="{ row }">
            <div class="maternity-cell">
              <span class="font-medium">{{ row.maternity }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="maternity" label="新生儿" align="center">
          <template #default="{ row }">
            <div class="maternity-cell">
              <span class="font-medium">{{ row.newborn }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="doctor_type" label="医生类型" align="center">
          <template #default="{ row }">
            <el-tag type="info" size="small" effect="light">
              {{ row.doctor_type }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="doctor" label="查房医生" align="center">
          <template #default="{ row }">
            <div class="doctor-cell">
              <el-icon class="mr-1 text-blue-500">
                <Avatar />
              </el-icon>
              <span>{{ row.doctor }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="has_abnormal" label="异常情况" width="150" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.has_abnormal ? 'danger' : 'success'"
              size="small"
              effect="light"
            >
              {{ row.has_abnormal ? '有异常' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="record_content" label="查房内容" min-width="150" align="center">
          <template #default="{ row }">
            <div class="content-cell">
              <span class="content-text">{{ truncateText(row.record_content, 50) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="320" align="center" fixed="right">
          <template #default="{ row }">
            <div class="flex justify-center gap-2">
              <el-button
                type="primary"
                size="small"
                @click.stop="handleView(row)"
                class="bg-blue-500 hover:bg-blue-600 border-blue-500"
              >
                查看详情
              </el-button>
              <el-button
                type="success"
                size="small"
                @click.stop="handleEdit(row)"
                class="bg-green-500 hover:bg-green-600 border-green-500"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click.stop="handleDelete(row)"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="pagination"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock, User, Avatar } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'
import { formatDate, formatTime } from '@/utils/dateUtils.js'

// 定义组件属性
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['row-click', 'edit', 'view', 'delete'])

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get('customer-service/wardround/newborn/list/', requestParams)
    tableData.value = data.list || []
    totalCount.value = data.total_count || 0
  } catch (error) {
    ElMessage.error('获取新生儿查房记录列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 文本截断
const truncateText = (text, maxLength) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 行点击处理
const handleRowClick = (row) => {
  emit('row-click', row)
}

// 编辑处理
const handleEdit = (row) => {
  emit('edit', row)
}

// 查看处理
const handleView = (row) => {
  emit('row-click', row)
}

// 删除处理
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条查房记录吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 注意：这里使用新生儿查房的API端点，需要根据实际API调整
    await del(`customer-service/wardround/newborn/delete/${row.rid}/`)
    ElMessage.success('查房记录删除成功')
    emit('delete', row)
    loadData() // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除查房记录失败:', error)
      ElMessage.error('删除查房记录失败')
    }
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 监听过滤条件变化
watch(
  () => props.filters,
  () => {
    currentPage.value = 1 // 重置到第一页
    loadData()
  },
  { deep: true }
)

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})

// 暴露方法给父组件
defineExpose({
  loadData,
})
</script>

<style scoped>
.newborn-ward-round-table-container {
  background: white;
  border-radius: 0.5rem;
}

.table-card {
  border: none;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.ward-round-table {
  --el-table-header-bg-color: #f8fafc;
  --el-table-header-text-color: #374151;
}

.time-cell,
.maternity-cell,
.doctor-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.content-cell {
  text-align: center;
}

.content-text {
  color: #6b7280;
  line-height: 1.4;
}



.pagination-container {
  padding: 1rem 0;
  border-top: 1px solid #f3f4f6;
}

.pagination {
  --el-pagination-button-color: #6b7280;
  --el-pagination-hover-color: #ec4899;
}
</style>
