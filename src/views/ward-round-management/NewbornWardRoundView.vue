<template>
  <div class="newborn-ward-round-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">查房管理 - 新生儿查房</h1>
            <p class="text-sm text-gray-600 mt-1">管理新生儿查房记录，跟踪新生儿健康状况</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新建查房记录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <NewbornWardRoundTable
      ref="wardRoundTableRef"
      :filters="currentFilters"
      @row-click="handleRowClick"
      @edit="handleEdit"
    />

    <!-- 表单对话框 -->
    <NewbornWardRoundFormDialog
      v-model="showFormDialog"
      :mode="formMode"
      :item-id="currentRow?.rid"
      :item-data="currentRow"
      @success="handleSubmitSuccess"
    />

    <!-- 详情查看对话框 -->
    <NewbornWardRoundDetailDialog
      v-model="showDetailDialog"
      :item-id="currentRow?.rid"
      :item-data="currentRow"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import NewbornWardRoundTable from './components/NewbornWardRoundTable.vue'
import NewbornWardRoundDetailDialog from './components/NewbornWardRoundDetailDialog.vue'
import NewbornWardRoundFormDialog from './components/NewbornWardRoundFormDialog.vue'

// 响应式数据
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref(null)
const formMode = ref('add') // 'add' | 'edit'

// 获取 table 组件引用
const wardRoundTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
  has_abnormal: '',
})

// 过滤字段配置
const filterFields = [
  {
    key: 'sk',
    label: '产妇/新生儿',
    type: 'input',
    placeholder: '请输入产妇或新生儿姓名进行搜索',
  },
  {
    key: 'has_abnormal',
    label: '异常情况',
    type: 'select',
    placeholder: '请选择异常情况',
    options: [
      { label: '全部', value: '' },
      { label: '正常', value: 'False' },
      { label: '有异常', value: 'True' },
    ],
  },
]

// 搜索处理
const handleSearch = (filters) => {
  Object.assign(currentFilters, filters)
  wardRoundTableRef.value?.loadData()
}

// 新建记录
const handleCreate = () => {
  currentRow.value = null
  formMode.value = 'add'
  showFormDialog.value = true
}

// 编辑记录
const handleEdit = (row) => {
  currentRow.value = row
  formMode.value = 'edit'
  showFormDialog.value = true
}

// 行点击处理
const handleRowClick = (row) => {
  currentRow.value = row
  showDetailDialog.value = true
}

// 表单提交成功
const handleSubmitSuccess = () => {
  showFormDialog.value = false
  wardRoundTableRef.value?.loadData()
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentRow.value = null
}
</script>

<style scoped>
.newborn-ward-round-container {
  padding: 1.5rem;
}

.page-header {
  background: white;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}
</style>
