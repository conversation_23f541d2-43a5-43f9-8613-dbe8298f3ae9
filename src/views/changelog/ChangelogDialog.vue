<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="更新日志" 
    :width="800"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="changelog-content">
      <div v-if="loading" class="text-center py-8">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p class="text-gray-500 mt-2">加载中...</p>
      </div>
      
      <div v-else-if="changelogList.length === 0" class="text-center py-8">
        <p class="text-gray-500">暂无更新日志</p>
      </div>
      
      <div v-else class="space-y-6">
        <div 
          v-for="item in changelogList" 
          :key="item.version" 
          class="border border-gray-200 rounded-lg p-4 bg-white"
        >
          <!-- 版本头部 -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <h3 class="text-lg font-semibold text-gray-900">{{ item.version }}</h3>
              <el-tag :type="getChangeTypeColor(item.change_type)" size="small">
                {{ getChangeTypeLabel(item.change_type) }}
              </el-tag>
            </div>
            <span class="text-sm text-gray-500">{{ formatDate(item.release_date) }}</span>
          </div>
          
          <!-- 标题 -->
          <h4 class="text-base font-medium text-gray-800 mb-2">{{ item.title }}</h4>
          
          <!-- 摘要 -->
          <p class="text-gray-600 mb-3">{{ item.summary }}</p>
          
          <!-- 详细内容 -->
          <div v-if="item.details" class="bg-gray-50 rounded-md p-3">
            <h5 class="text-sm font-medium text-gray-700 mb-2">详细更新:</h5>
            <div class="text-sm text-gray-600 whitespace-pre-line">{{ item.details }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = ref(false)
const loading = ref(false)
const changelogList = ref([])

// 监听外部传入的显示状态
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
  if (val) {
    fetchChangelogList()
  }
})

// 监听内部对话框状态变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 获取更新日志列表
const fetchChangelogList = async () => {
  try {
    loading.value = true
    const data = await get('changelog/list/')
    changelogList.value = data || []
  } catch (error) {
    console.error('获取更新日志失败:', error)
    ElMessage.error('获取更新日志失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 获取变更类型颜色
const getChangeTypeColor = (type) => {
  const colorMap = {
    FEATURE: 'success',
    IMPROVEMENT: 'primary',
    BUGFIX: 'warning',
    SECURITY: 'danger',
    PERFORMANCE: 'info',
    UI: '',
    CONFIG: '',
    OTHER: ''
  }
  return colorMap[type] || ''
}

// 获取变更类型标签
const getChangeTypeLabel = (type) => {
  const labelMap = {
    FEATURE: '新功能',
    IMPROVEMENT: '功能改进',
    BUGFIX: '错误修复',
    SECURITY: '安全更新',
    PERFORMANCE: '性能优化',
    UI: '界面优化',
    CONFIG: '配置变更',
    OTHER: '其他'
  }
  return labelMap[type] || '其他'
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.changelog-content {
  max-height: 500px;
  overflow-y: auto;
}

.changelog-content::-webkit-scrollbar {
  width: 6px;
}

.changelog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.changelog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.changelog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>