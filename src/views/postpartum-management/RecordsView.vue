<template>
  <div class="postpartum-records-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">产后康复 - 产康记录</h1>
            <p class="text-sm text-gray-600 mt-1">管理产后康复记录，跟踪康复进度</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新建记录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <PostpartumRecordTable
      ref="recordTableRef"
      :filters="currentFilters"
      @row-click="handleRowClick"
      @edit="handleEdit"
    />

    <!-- 表单对话框 -->
    <PostpartumRecordFormDialog
      v-model="showFormDialog"
      :mode="formMode"
      :item-id="currentRow?.rid"
      :item-data="currentRow"
      @success="handleSubmitSuccess"
    />

    <!-- 详情查看对话框 -->
    <PostpartumRecordDetailDialog
      v-model="showDetailDialog"
      :item-id="currentRow?.rid"
      :item-data="currentRow"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import PostpartumRecordTable from './components/PostpartumRecordTable.vue'
import PostpartumRecordDetailDialog from './components/PostpartumRecordDetailDialog.vue'
import PostpartumRecordFormDialog from './components/PostpartumRecordFormDialog.vue'

// 响应式数据
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref(null)
const formMode = ref('add') // 'add' | 'edit'

// 获取 table 组件引用
const recordTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入产妇姓名、房间号或康复项目关键字',
  },
]

// 新增记录
const handleCreate = () => {
  formMode.value = 'add'
  currentRow.value = null
  showFormDialog.value = true
}

// 编辑记录
const handleEdit = (row) => {
  formMode.value = 'edit'
  currentRow.value = row
  showFormDialog.value = true
}

// 查看详情
const handleRowClick = (row) => {
  currentRow.value = row
  showDetailDialog.value = true
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentRow.value = null
}

// 提交成功处理
const handleSubmitSuccess = () => {
  showFormDialog.value = false
  currentRow.value = null
  // 刷新表格数据
  if (recordTableRef.value) {
    recordTableRef.value.loadData()
  }
}

// 搜索处理
const handleSearch = (filters) => {
  Object.assign(currentFilters, filters)
}
</script>

<style scoped>
.postpartum-records-container {
  padding: 24px;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.placeholder-content {
  margin-top: 24px;
}
</style>
