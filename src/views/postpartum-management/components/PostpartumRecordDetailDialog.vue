<template>
  <el-dialog
    v-model="visible"
    title="产康记录详情"
    width="700px"
    align-center
    :before-close="handleClose"
    class="postpartum-record-detail-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <div v-if="recordData" class="record-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section mb-6">
          <h4 class="section-title">康复记录信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">康复时间</label>
              <div class="detail-value">
                  {{ formatDateTime(recordData.recovery_time) }}
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">产妇信息</label>
              <div class="detail-value">
                  {{ recordData.maternity || '-' }}
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">入院编号</label>
              <div class="detail-value">
                <el-tag type="info" size="small">{{ recordData.maternity_admission || '-' }}</el-tag>
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">康复项目</label>
              <div class="detail-value">
                  {{ recordData.project?.name || recordData.project_name || '-' }}
              </div>
            </div>
            <div class="detail-item full-width">
              <label class="detail-label">康复评价</label>
              <div class="detail-value">{{ recordData.recovery_evaluation || '-' }}</div>
            </div>
            <div class="detail-item full-width">
              <label class="detail-label">康复备注</label>
              <div class="detail-value">{{ recordData.recovery_remark || '-' }}</div>
            </div>
          </div>
        </div>

        <!-- 项目详情 -->
        <div v-if="recordData.project" class="detail-section mb-6">
          <h4 class="section-title">项目详情</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">项目名称</label>
              <div class="detail-value">{{ recordData.project.name || '-' }}</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">项目状态</label>
              <div class="detail-value">
                <el-tag :type="getProjectStatusTagType(recordData.project.status)" size="small">
                  {{ recordData.project.status_label || '-' }}
                </el-tag>
              </div>
            </div>
            <div class="detail-item full-width">
              <label class="detail-label">项目描述</label>
              <div class="detail-value">{{ recordData.project.description || '-' }}</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">项目创建时间</label>
              <div class="detail-value">{{ formatDateTime(recordData.project.created_at) }}</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">项目更新时间</label>
              <div class="detail-value">{{ formatDateTime(recordData.project.updated_at) }}</div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="detail-section">
          <h4 class="section-title">系统信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">创建时间</label>
              <div class="detail-value">
                <el-tag type="info" size="small">{{ recordData.created_at }}</el-tag>
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">最后更新时间</label>
              <div class="detail-value">
                <el-tag type="info" size="small">{{ recordData.updated_at }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'
import { getPostpartumProjectStatusTagType } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
  itemData: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'close'])

const loading = ref(false)
const recordData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && props.itemId) {
      nextTick(() => {
        loadRecordData()
      })
    }
  },
)

// 加载记录数据
const loadRecordData = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    // 调用详情API获取完整数据
    const data = await get(`customer-service/postpartum-record/detail/${props.itemId}/`)
    recordData.value = data
  } catch (error) {
    console.error('加载记录详情失败:', error)
    showErrorTip(error)
  } finally {
    loading.value = false
  }
}

// 项目状态相关方法
const getProjectStatusTagType = (status) => {
  return getPostpartumProjectStatusTagType(status)
}

// 时间格式化
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('close')
}
</script>

<style scoped>
.postpartum-record-detail-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.record-detail-content {
  color: #374151;
}

.detail-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #ec4899;
  margin-right: 10px;
  border-radius: 2px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  font-size: 14px;
  color: #374151;
  min-height: 20px;
  word-break: break-word;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
