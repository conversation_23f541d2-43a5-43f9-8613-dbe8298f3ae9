<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="postpartum-record-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="postpartum-record-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="康复项目" prop="project">
              <el-select
                v-model="form.project"
                placeholder="请选择康复项目"
                style="width: 100%"
                filterable
                :loading="projectsLoading"
              >
                <el-option
                  v-for="project in projectOptions"
                  :key="project.rid"
                  :label="project.name"
                  :value="project.rid"
                  :disabled="project.status === 'DISABLED' && props.mode === 'add'"
                >
                  <div class="flex justify-between items-center">
                    <span>{{ project.name }}</span>
                    <el-tag
                      v-if="project.status === 'DISABLED'"
                      type="danger"
                      size="small"
                    >
                      已禁用
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="产妇信息" prop="maternity_admission">
              <el-select
                v-model="form.maternity_admission"
                placeholder="请选择产妇"
                style="width: 100%"
                filterable
                :loading="maternityLoading"
                :disabled="props.mode === 'edit'"
              >
                <el-option
                  v-for="maternity in maternityOptions"
                  :key="maternity.aid"
                  :label="maternity.maternity"
                  :value="maternity.aid"
                />
              </el-select>
              <div v-if="props.mode === 'edit'" class="text-xs text-gray-500 mt-1">
                编辑模式下产妇信息不可修改
              </div>
            </el-form-item>

            <el-form-item label="康复时间" prop="recovery_time">
              <el-date-picker
                v-model="form.recovery_time"
                type="datetime"
                placeholder="请选择康复时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>

            <el-form-item label="康复评价" prop="recovery_evaluation">
              <el-input
                v-model="form.recovery_evaluation"
                type="textarea"
                :rows="3"
                placeholder="请输入康复评价（选填）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="康复备注" prop="recovery_remark">
              <el-input
                v-model="form.recovery_remark"
                type="textarea"
                :rows="3"
                placeholder="请输入康复备注（选填）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ submitting ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
  itemData: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const submitting = ref(false)
const loading = ref(false)
const formRef = ref()

// 项目和产妇选项
const projectOptions = ref([])
const maternityOptions = ref([])
const projectsLoading = ref(false)
const maternityLoading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'edit' ? '编辑产康记录' : '新建产康记录'
})

// 表单数据
const form = reactive({
  project: '',
  maternity_admission: '',
  recovery_time: '',
  recovery_evaluation: '',
  recovery_remark: '',
})

// 表单验证规则
const rules = computed(() => ({
  project: [
    { required: true, message: '请选择康复项目', trigger: 'change' },
  ],
  maternity_admission: props.mode === 'edit' ? [] : [
    { required: true, message: '请选择产妇', trigger: 'change' },
  ],
  recovery_time: [
    { required: true, message: '请选择康复时间', trigger: 'change' },
  ],
  recovery_evaluation: [
    { max: 500, message: '康复评价长度不能超过 500 个字符', trigger: 'blur' },
  ],
  recovery_remark: [
    { max: 500, message: '康复备注长度不能超过 500 个字符', trigger: 'blur' },
  ],
}))

// 监听对话框显示状态
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      await nextTick()
      resetForm()
      await loadOptions()
      if (props.mode === 'edit' && props.itemId) {
        await loadRecordData()
      }
    }
  },
)

// 组件挂载时加载选项数据
onMounted(() => {
  loadOptions()
})

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    project: '',
    maternity_admission: '',
    recovery_time: '',
    recovery_evaluation: '',
    recovery_remark: '',
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 加载选项数据
const loadOptions = async () => {
  // 编辑模式下不需要加载产妇选择接口
  if (props.mode === 'edit') {
    await loadProjectOptions()
  } else {
    await Promise.all([loadProjectOptions(), loadMaternityOptions()])
  }
}

// 加载康复项目选项
const loadProjectOptions = async () => {
  projectsLoading.value = true
  try {
    const data = await get('customer-service/postpartum-project/list/', {
      page: 1,
      page_size: 1000,
      status: 'ENABLED',
    })
    projectOptions.value = data.list || []
  } catch (error) {
    console.error('加载康复项目列表失败:', error)
    showErrorTip(error)
  } finally {
    projectsLoading.value = false
  }
}

// 加载产妇选项
const loadMaternityOptions = async () => {
  maternityLoading.value = true
  try {
    const data = await get('customer-service/postpartum/maternity-select-list/')
    maternityOptions.value = data || []
  } catch (error) {
    console.error('加载产妇列表失败:', error)
    showErrorTip(error)
  } finally {
    maternityLoading.value = false
  }
}

// 加载记录数据（编辑模式）
const loadRecordData = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    // 调用详情API获取完整数据
    const data = await get(`customer-service/postpartum-record/detail/${props.itemId}/`)

    // 格式化时间，去掉时区信息
    let recoveryTime = data.recovery_time
    if (recoveryTime && recoveryTime.includes('T')) {
      recoveryTime = recoveryTime.replace('T', ' ').replace(/\+.*$/, '')
    }

    // 如果详情接口返回了项目信息，将其添加到项目选项中
    if (data.project) {
      const existingProject = projectOptions.value.find(p => p.rid === data.project.rid)
      if (!existingProject) {
        // 如果项目不在当前选项中（比如已禁用的项目），添加到选项中
        projectOptions.value.unshift(data.project)
      }
    }

    Object.assign(form, {
      project: data.project?.rid || '',
      maternity_admission: data.maternity_admission || '',
      recovery_time: recoveryTime || '',
      recovery_evaluation: data.recovery_evaluation || '',
      recovery_remark: data.recovery_remark || '',
    })

    // 等待项目选项加载完成后，确保项目选择器显示正确
    await nextTick()
  } catch (error) {
    console.error('加载记录数据失败:', error)
    showErrorTip(error)
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    console.log('表单验证失败:', error)
    return
  }

  submitting.value = true
  try {
    if (props.mode === 'edit' && props.itemId) {
      // 编辑模式 - 不包含产妇信息
      const submitData = {
        project: form.project,
        recovery_time: form.recovery_time,
        recovery_evaluation: form.recovery_evaluation,
        recovery_remark: form.recovery_remark,
      }
      await put(`customer-service/postpartum-record/update/${props.itemId}/`, submitData)
      ElMessage.success('康复记录更新成功')
    } else {
      // 新建模式 - 包含所有字段
      const submitData = {
        project: form.project,
        maternity_admission: form.maternity_admission,
        recovery_time: form.recovery_time,
        recovery_evaluation: form.recovery_evaluation,
        recovery_remark: form.recovery_remark,
      }
      await post('customer-service/postpartum-record/create/', submitData)
      ElMessage.success('康复记录创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (submitting.value) return
  visible.value = false
}
</script>

<style scoped>
.postpartum-record-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #ec4899;
  margin-right: 10px;
  border-radius: 2px;
}

.postpartum-record-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
