<template>
  <el-dialog
    v-model="visible"
    title="产后康复项目详情"
    width="700px"
    align-center
    :before-close="handleClose"
    class="postpartum-project-detail-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <div v-if="projectData" class="project-detail-content">
        <!-- 基本信息 -->
        <div class="detail-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">项目名称</label>
              <div class="detail-value">{{ projectData.name || '-' }}</div>
            </div>
            <div class="detail-item">
              <label class="detail-label">状态</label>
              <div class="detail-value">
                <el-tag :type="getStatusTagType(projectData.status)" size="small">
                  {{ projectData.status_label || '-' }}
                </el-tag>
              </div>
            </div>
            <div class="detail-item full-width">
              <label class="detail-label">项目描述</label>
              <div class="detail-value">{{ projectData.description || '-' }}</div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="detail-section">
          <h4 class="section-title">系统信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label class="detail-label">创建时间</label>
              <div class="detail-value">
                <div>{{ formatDateTime(projectData.created_at) }}</div>
              </div>
            </div>
            <div class="detail-item">
              <label class="detail-label">更新时间</label>
              <div class="detail-value">
                <div>{{ formatDateTime(projectData.updated_at) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleEdit"
          class="bg-blue-500 hover:bg-blue-600 border-blue-500"
        >
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'
import { getPostpartumProjectStatusTagType } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
  itemData: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'edit', 'close'])

const loading = ref(false)
const projectData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && props.itemData) {
      nextTick(() => {
        loadProjectData()
      })
    }
  },
)

// 加载项目数据
const loadProjectData = async () => {
  if (!props.itemData) return

  loading.value = true
  try {
    // 直接使用传入的数据
    projectData.value = props.itemData
  } catch (error) {
    console.error('加载项目详情失败:', error)
    showErrorTip(error)
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusTagType = (status) => {
  return getPostpartumProjectStatusTagType(status)
}

// 时间格式化
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 编辑项目
const handleEdit = () => {
  emit('edit', projectData.value)
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  emit('close')
}
</script>

<style scoped>
.postpartum-project-detail-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.project-detail-content {
  color: #374151;
}

.detail-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #ec4899;
  margin-right: 10px;
  border-radius: 2px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.detail-value {
  font-size: 14px;
  color: #374151;
  min-height: 20px;
  word-break: break-word;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
