<template>
  <div class="postpartum-project-table-container">
    <el-card class="table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        @row-click="handleRowClick"
        class="project-table"
      >
        <el-table-column prop="name" label="项目名称" min-width="150" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <span class="font-medium">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="项目描述" min-width="200" align="center">
          <template #default="{ row }">
            <div class="text-gray-600">
              {{ row.description || '-' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="auto" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ row.status_label }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="auto" align="center">
          <template #default="{ row }">
            <div class="text-gray-600">
              <div>{{ formatDate(row.created_at) }}</div>
              <div class="text-xs text-gray-400">{{ formatTime(row.created_at) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="updated_at" label="更新时间" width="auto" align="center">
          <template #default="{ row }">
            <div class="text-gray-600">
              <div>{{ formatDate(row.updated_at) }}</div>
              <div class="text-xs text-gray-400">{{ formatTime(row.updated_at) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template #default="{ row }">
            <div class="flex justify-center gap-2">
              <el-button
                type="primary"
                size="small"
                @click.stop="handleEdit(row)"
                class="bg-blue-500 hover:bg-blue-600 border-blue-500"
              >
                编辑
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-end"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Promotion } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get, del } from '@/utils/request.js'
import { getPostpartumProjectStatusTagType } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const emit = defineEmits(['edit', 'row-click'])

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get('customer-service/postpartum-project/list/', requestParams)
    tableData.value = data.list || []
    totalCount.value = data.total_count || 0
  } catch (error) {
    console.error('获取产后康复项目列表失败:', error)
    ElMessage.error('获取产后康复项目列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 监听过滤条件变化
watch(
  () => props.filters,
  () => {
    currentPage.value = 1
    loadData()
  },
  { deep: true },
)

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})

// 暴露刷新方法
const refresh = () => {
  loadData()
}

defineExpose({
  refresh,
  loadData,
})

// 状态相关方法
const getStatusTagType = (status) => {
  return getPostpartumProjectStatusTagType(status)
}

// 时间格式化
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'HH:mm')
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 删除项目
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除项目"${row.name}"吗？删除后无法恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    )

    const deleteUrl = `customer-service/postpartum-project/delete/${row.rid}/`
    await del(deleteUrl)

    ElMessage.success('删除成功')

    // 重新加载数据
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      showErrorTip(error)
    }
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}
</script>

<style scoped>
.postpartum-project-table-container {
  margin-bottom: 24px;
}

.table-card {
  border-radius: 8px;
  overflow: hidden;
}

.project-table {
  --el-table-header-bg-color: #f8fafc;
  --el-table-header-text-color: #374151;
}

.project-table :deep(.el-table__header-wrapper) {
  background-color: var(--el-table-header-bg-color);
}

.project-table :deep(.el-table__header th) {
  background-color: var(--el-table-header-bg-color);
  color: var(--el-table-header-text-color);
  font-weight: 600;
}

.project-table :deep(.el-table__row) {
  cursor: pointer;
}

.project-table :deep(.el-table__row:hover) {
  background-color: #f9fafb;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}
</style>
