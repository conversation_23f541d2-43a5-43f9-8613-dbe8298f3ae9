<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="postpartum-project-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="postpartum-project-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="项目名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入项目名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="项目描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                placeholder="请输入项目描述"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="状态" prop="status">
              <el-select
                v-model="form.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option
                  v-for="option in statusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ submitting ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { POSTPARTUM_PROJECT_STATUS_OPTIONS } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
  itemData: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const submitting = ref(false)
const loading = ref(false)
const formRef = ref()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'edit' ? '编辑产后康复项目' : '新建产后康复项目'
})

// 状态选项
const statusOptions = POSTPARTUM_PROJECT_STATUS_OPTIONS

// 表单数据
const form = reactive({
  name: '',
  description: '',
  status: 'ENABLED',
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 1, max: 100, message: '项目名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  description: [
    { max: 500, message: '项目描述长度不能超过 500 个字符', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        resetForm()
        if (props.mode === 'edit' && props.itemData) {
          loadProjectData()
        }
      })
    }
  },
)

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    description: '',
    status: 'ENABLED',
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 加载项目数据（编辑模式）
const loadProjectData = async () => {
  if (!props.itemData) return

  // 从父组件传递的数据中获取
  Object.assign(form, {
    name: props.itemData.name || '',
    description: props.itemData.description || '',
    status: props.itemData.status || 'ENABLED',
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch (error) {
    console.log('表单验证失败:', error)
    return
  }

  submitting.value = true
  try {
    const submitData = {
      name: form.name,
      description: form.description,
      status: form.status,
    }

    if (props.mode === 'edit' && props.itemId) {
      await put(`customer-service/postpartum-project/update/${props.itemId}/`, submitData)
      ElMessage.success('项目更新成功')
    } else {
      await post('customer-service/postpartum-project/create/', submitData)
      ElMessage.success('项目创建成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (submitting.value) return
  visible.value = false
}
</script>

<style scoped>
.postpartum-project-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #ec4899;
  margin-right: 10px;
  border-radius: 2px;
}

.postpartum-project-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
