<template>
  <div class="postpartum-project-management-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">产后康复 - 项目管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理产后康复项目，维护康复项目信息</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新建项目
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <PostpartumProjectTable
      ref="projectTableRef"
      :filters="currentFilters"
      @edit="handleEdit"
      @row-click="handleRowClick"
    />

    <!-- 表单对话框 -->
    <PostpartumProjectFormDialog
      v-model="showFormDialog"
      :mode="formMode"
      :item-id="currentRow?.rid"
      :item-data="currentRow"
      @success="handleSubmitSuccess"
    />

    <!-- 详情查看对话框 -->
    <PostpartumProjectDetailDialog
      v-model="showDetailDialog"
      :item-id="currentRow?.rid"
      :item-data="currentRow"
      @edit="handleEditFromDetail"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import PostpartumProjectTable from './components/PostpartumProjectTable.vue'
import PostpartumProjectFormDialog from './components/PostpartumProjectFormDialog.vue'
import PostpartumProjectDetailDialog from './components/PostpartumProjectDetailDialog.vue'

// 响应式数据
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref(null)
const formMode = ref('add') // 'add' | 'edit'

// 获取 table 组件引用
const projectTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
  status: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入项目名称',
  },
  {
    key: 'status',
    type: 'select',
    label: '状态',
    placeholder: '请选择状态',
    options: [
      { label: '启用', value: 'ENABLED' },
      { label: '禁用', value: 'DISABLED' },
    ],
    clearable: true,
  },
]

// 新增项目
const handleCreate = () => {
  formMode.value = 'add'
  currentRow.value = null
  showFormDialog.value = true
}

// 编辑项目
const handleEdit = (row) => {
  formMode.value = 'edit'
  currentRow.value = row
  showFormDialog.value = true
}

// 查看详情
const handleRowClick = (row) => {
  currentRow.value = row
  showDetailDialog.value = true
}

// 从详情页面编辑
const handleEditFromDetail = (row) => {
  showDetailDialog.value = false
  setTimeout(() => {
    handleEdit(row)
  }, 100)
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentRow.value = null
}

// 搜索处理
const handleSearch = (filters) => {
  Object.assign(currentFilters, filters)
}

// 提交成功处理
const handleSubmitSuccess = () => {
  showFormDialog.value = false
  currentRow.value = null
  // 刷新表格数据
  if (projectTableRef.value) {
    projectTableRef.value.loadData()
  }
}
</script>

<style scoped>
.postpartum-project-management-container {
  padding: 24px;
}

.page-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
