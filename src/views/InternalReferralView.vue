<template>
  <div class="internal-referral-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">院内转诊管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理产妇和新生儿院内转诊申请流程</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleAddReferral"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增转诊申请
          </el-button>
          <el-button
            type="default"
            @click="handleExportPDF"
            class="border-pink-300 text-pink-500 hover:bg-pink-50"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            导出列表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="mb-6">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="" type="border-card">
        <el-tab-pane label="产妇院内转诊" name="maternal">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <User />
              </el-icon>
              产妇院内转诊
            </div>
          </template>
          <!-- 筛选搜索组件 -->
          <FilterPanel
            :fields="maternalFilterFields"
            :filters="maternalFilters"
            @search="handleMaternalSearch"
            class="mb-6"
          />

          <!-- 产妇转诊申请列表 -->
          <MaternalReferralTable
            ref="maternalTableRef"
            :filters="maternalFilters"
            @edit="handleEditReferral"
            @view="handleViewReferral"
            @update-status="handleUpdateStatus"
            @row-click="handleRowClick"
          />
        </el-tab-pane>

        <el-tab-pane label="新生儿院内转诊" name="newborn">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Avatar />
              </el-icon>
              新生儿院内转诊
            </div>
          </template>
          <!-- 筛选搜索组件 -->
          <FilterPanel
            :fields="newbornFilterFields"
            :filters="newbornFilters"
            @search="handleNewbornSearch"
            class="mb-6"
          />

          <!-- 新生儿转诊申请列表 -->
          <NewbornReferralTable
            ref="newbornTableRef"
            :filters="newbornFilters"
            @edit="handleEditReferral"
            @view="handleViewReferral"
            @update-status="handleUpdateStatus"
            @row-click="handleRowClick"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 转诊申请表单弹窗 -->
    <ReferralFormDialog
      v-model="formVisible"
      :referral-id="currentReferral?.rid"
      :referral-type="activeTab"
      @success="handleSaveReferral"
    />

    <!-- 转诊详情查看弹窗 -->
    <ReferralDetailDialog v-model:visible="detailVisible" :referral-data="currentReferral" />

    <!-- 状态更新弹窗 -->
    <ReferralProcessDialog
      v-model:visible="statusVisible"
      :referral-data="currentReferral"
      :referral-type="activeTab"
      @save="handleSaveStatus"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElButton, ElMessage, ElIcon, ElTabs, ElTabPane } from 'element-plus'
import { Plus, Download, User, Avatar } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import MaternalReferralTable from '@/components/internal-referral/MaternalReferralTable.vue'
import NewbornReferralTable from '@/components/internal-referral/NewbornReferralTable.vue'
import ReferralFormDialog from '@/components/internal-referral/ReferralFormDialog.vue'
import ReferralDetailDialog from '@/components/internal-referral/ReferralDetailDialog.vue'
import ReferralProcessDialog from '@/components/internal-referral/ReferralProcessDialog.vue'
import { MEDICAL_REFERRAL_STATUS_OPTIONS } from '@/utils/constants.js'

// 响应式数据
const activeTab = ref('maternal')
const formVisible = ref(false)
const detailVisible = ref(false)
const statusVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentReferral = ref(null)

// 获取 table 组件引用
const maternalTableRef = ref(null)
const newbornTableRef = ref(null)

// 产妇转诊筛选条件
const maternalFilters = reactive({
  sk: '',
  aps: '',
  rd: '',
  apt: '',
})

// 产妇转诊过滤器字段配置
const maternalFilterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键词',
    placeholder: '产妇姓名或申请单号',
  },
  {
    key: 'aps',
    type: 'select',
    label: '转诊状态',
    placeholder: '请选择转诊状态',
    options: MEDICAL_REFERRAL_STATUS_OPTIONS,
  },
  {
    key: 'rd',
    type: 'input',
    label: '转诊科室',
    placeholder: '请输入转诊科室',
  },
  {
    key: 'apt',
    type: 'date',
    label: '申请日期',
    placeholder: '申请日期',
  },
]

// 新生儿转诊筛选条件
const newbornFilters = reactive({
  sk: '',
  aps: '',
  rd: '',
  apt: '',
})

// 新生儿转诊过滤器字段配置
const newbornFilterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键词',
    placeholder: '产妇姓名、婴儿姓名或申请单号',
  },
  {
    key: 'aps',
    type: 'select',
    label: '转诊状态',
    placeholder: '请选择转诊状态',
    options: MEDICAL_REFERRAL_STATUS_OPTIONS,
  },
  {
    key: 'rd',
    type: 'input',
    label: '转诊科室',
    placeholder: '请输入转诊科室',
  },
  {
    key: 'apt',
    type: 'date',
    label: '申请日期',
    placeholder: '申请日期',
  },
]

// 方法
const handleAddReferral = () => {
  currentReferral.value = null
  formMode.value = 'add'
  formVisible.value = true
}

const handleEditReferral = (referral) => {
  currentReferral.value = { ...referral }
  formMode.value = 'edit'
  formVisible.value = true
}

const handleViewReferral = (referral) => {
  currentReferral.value = { ...referral }
  detailVisible.value = true
}

const handleUpdateStatus = (referral) => {
  currentReferral.value = { ...referral }
  statusVisible.value = true
}

const handleRowClick = (referral) => {
  currentReferral.value = { ...referral }
  detailVisible.value = true
}

const handleMaternalSearch = () => {
  // 重置到第一页并重新加载数据
  maternalTableRef.value?.resetPagination()
}

const handleNewbornSearch = () => {
  // 重置到第一页并重新加载数据
  newbornTableRef.value?.resetPagination()
}

const handleSaveReferral = () => {
  formVisible.value = false
  // 刷新当前活跃tab的表格数据
  if (activeTab.value === 'maternal') {
    maternalTableRef.value?.refresh()
  } else {
    newbornTableRef.value?.refresh()
  }
}

const handleSaveStatus = () => {
  statusVisible.value = false
  // 刷新当前活跃tab的表格数据
  if (activeTab.value === 'maternal') {
    maternalTableRef.value?.refresh()
  } else {
    newbornTableRef.value?.refresh()
  }
}

const handleExportPDF = () => {
  ElMessage.info('导出功能开发中...')
}

const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)
}
</script>

<style scoped>
.internal-referral-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

:deep(.referral-tabs .el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

:deep(.referral-tabs .el-tabs__content) {
  padding: 0;
}

:deep(.referral-tabs .el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-weight: 500;
}

:deep(.referral-tabs .el-tabs__item:hover) {
  color: #ec4899;
}
</style>
