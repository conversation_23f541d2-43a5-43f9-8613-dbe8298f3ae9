<template>
  <div class="details-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">客户档案详情 - {{ customerName }}</h1>
            <p class="text-sm text-gray-600 mt-1">查看和管理客户的完整健康记录</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户信息头部 -->
    <ClientHeader :client-data="clientData" />

    <!-- 仪表板卡片 -->
    <DashboardCards :client-data="clientData" @reload="loadClientData" />

    <!-- 标签页内容 -->
    <RecordTabs
      v-model:active-tab="activeTab"
      :tabs="tabs"
      :customer-id="route.params.id"
      :baby-list="babyList"
    />

    <!-- 操作按钮 -->
    <!-- <ActionButtons
      @add-record="handleAddRecord"
      @print="handlePrint"
      @export="handleExport"
      @settings="handleSettings"
    /> -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request'
import ClientHeader from './components/ClientHeader.vue'
import DashboardCards from './components/DashboardCards.vue'
import RecordTabs from './components/RecordTabs.vue'

const route = useRoute()

// 当前激活的标签
const activeTab = ref('maternal-admission')

// 当前客户名称
const customerName = ref(route.query.name || '客户')

// 婴儿列表数据
const babyList = ref([])

// 客户数据
const clientData = ref({})

// 标签页配置
const tabs = ref([
  { key: 'maternal-admission', title: '产妇入住评估', icon: 'UserFilled' },
  { key: 'newborn-admission', title: '新生儿入住评估', icon: 'Avatar' },
  { key: 'maternal-daily-required', title: '产妇每日必填记录', icon: 'DocumentCopy' },
  { key: 'maternal-recovery', title: '产妇康复评估', icon: 'TrendCharts' },
  { key: 'maternal-daily-care', title: '产妇每日生理护理', icon: 'Monitor' },
  { key: 'maternal-dietary', title: '产妇膳食计划', icon: 'KnifeFork' },
  { key: 'newborn-care-one', title: '新生儿护理记录单（1）', icon: 'Document' },
  { key: 'newborn-care-two', title: '新生儿护理记录单（2）', icon: 'List' },
  { key: 'newborn-care-operation', title: '新生儿护理操作记录', icon: 'Setting' },
  { key: 'newborn-daily-required', title: '新生儿每日必填记录', icon: 'Calendar' },
  { key: 'newborn-feeding', title: '新生儿喂养记录', icon: 'Bowl' },
  { key: 'newborn-fullmoon', title: '新生儿满月评估', icon: 'Trophy' },
  // { key: 'personalized-care', title: '个性化护理计划', icon: 'Reading' },
  // { key: 'health-education', title: '健康宣教记录', icon: 'ChatLineRound' },
])

// 初始化
onMounted(() => {
  loadClientData()
})

const loadClientData = async () => {
  try {
    const clientId = route.params.id
    const response = await get(`customer-service/maternity-admission/customer/detail/${clientId}/`)

    // 直接使用原始数据
    customerName.value = response.maternity_name
    clientData.value = response
    babyList.value = response.babies_data || []

    console.log(`已加载客户 ${clientId} 的档案信息`)
  } catch (error) {
    console.error('加载客户数据失败:', error)
    ElMessage.error('加载客户数据失败')
  }
}
</script>

<style scoped>
.details-view-container {
  padding: 0;
}

.page-header {
  transition: all 0.3s ease;
}
</style>
