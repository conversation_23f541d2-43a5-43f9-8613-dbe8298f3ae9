<template>
  <div class="customers-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">母婴核心记录 - 客户列表</h1>
            <p class="text-sm text-gray-600 mt-1">管理产妇和新生儿信息，跟踪住院状态</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button @click="toggleView" class="border-pink-500 text-pink-500 hover:bg-pink-50">
            <el-icon class="mr-2">
              <List v-if="viewMode === 'card'" />
              <Grid v-else />
            </el-icon>
            {{ viewMode === 'card' ? '表格视图' : '卡片视图' }}
          </el-button>
          <!-- <el-button @click="handleExport" class="border-pink-300 text-pink-600 hover:bg-pink-50">
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            导出数据
          </el-button>
          <el-button
            type="primary"
            @click="showCreateModal = true"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增客户
          </el-button> -->
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <!-- <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
              <el-icon class="text-pink-600">
                <User />
              </el-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">在住客户</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.inpatient }}</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <el-icon class="text-blue-600">
                <Calendar />
              </el-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日入住</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.todayCheckin }}</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <el-icon class="text-green-600">
                <Check />
              </el-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已出院</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.discharged }}</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg p-6 shadow-sm">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <el-icon class="text-orange-600">
                <Warning />
              </el-icon>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">需关注</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.needAttention }}</p>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 卡片视图 -->
    <CustomerCards
      v-if="viewMode === 'card'"
      ref="customerCardsRef"
      :filters="currentFilters"
      @view-profile="handleViewProfile"
      @add-record="handleAddRecord"
      @export-customer="handleExportCustomer"
      @row-click="handleRowClick"
    />

    <!-- 表格视图 -->
    <CustomerTable
      v-else
      ref="customerTableRef"
      :filters="currentFilters"
      @view-profile="handleViewProfile"
      @add-record="handleAddRecord"
      @export-customer="handleExportCustomer"
      @row-click="handleRowClick"
    />

    <!-- 新增客户模态框 -->
    <CustomerFormDialog v-model="showCreateModal" @success="handleCreateSuccess" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElButton, ElMessage, ElIcon } from 'element-plus'
import { Grid, List } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import CustomerFormDialog from './components/CustomerFormDialog.vue'
import FilterPanel from '@/components/FilterPanel.vue'
import CustomerCards from './components/CustomerCards.vue'
import CustomerTable from './components/CustomerTable.vue'
import { CHECK_IN_STATUS_OPTIONS } from '@/utils/constants.js'

const router = useRouter()

// 响应式数据
const showCreateModal = ref(false)
const viewMode = ref('card') // 'card' 或 'table'

// 获取 table/cards 组件引用
const customerTableRef = ref(null)
const customerCardsRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '', // 搜索关键字
  cs: '', // 入住状态
  cid: '', // 入住日期
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '产妇、房间号或新生儿',
  },
  {
    key: 'cis',
    type: 'select',
    label: '状态',
    placeholder: '请选择入住状态',
    options: CHECK_IN_STATUS_OPTIONS,
  },
  {
    key: ['acid_start', 'acid_end'], // 直接使用数组作为key
    label: '入住日期范围',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
]

// 方法定义

// 方法
const toggleView = () => {
  viewMode.value = viewMode.value === 'card' ? 'table' : 'card'
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  const currentRef = viewMode.value === 'card' ? customerCardsRef.value : customerTableRef.value
  currentRef?.resetPagination()
}

// 查看档案
const handleViewProfile = (customer) => {
  // 跳转到客户详情页面
  if (!customer?.aid) {
    ElMessage.error('客户信息不完整，无法跳转')
    return
  }

  router
    .push({
      name: 'maternal-details',
      params: { id: customer.aid },
      query: { name: customer.customerName || '未知客户' },
    })
    .catch((error) => {
      console.error('路由跳转失败:', error)
      ElMessage.error('页面跳转失败')
    })
}

// 新增记录
const handleAddRecord = (customer) => {
  ElMessage.info(`为 ${customer.customerName} 新增记录`)
  // 这里可以打开新增记录的模态框
}

// 导出客户数据
const handleExportCustomer = (customer) => {
  ElMessage.info(`导出 ${customer.customerName} 的档案`)
  // 这里可以实现导出功能
}

// 行点击 - 查看详情
const handleRowClick = () => {
  // 这里可以跳转到客户详情页面或打开详情对话框
}

// 新增客户成功
const handleCreateSuccess = () => {
  ElMessage.success('客户信息已保存')
  // 刷新当前视图数据
  const currentRef = viewMode.value === 'card' ? customerCardsRef.value : customerTableRef.value
  currentRef?.refresh()
}
</script>

<style scoped>
.customers-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

:deep(.el-table) {
  --el-table-border-color: #f0f0f0;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-pagination) {
  --el-pagination-button-bg-color: transparent;
  --el-pagination-hover-color: #ec4899;
}
</style>
