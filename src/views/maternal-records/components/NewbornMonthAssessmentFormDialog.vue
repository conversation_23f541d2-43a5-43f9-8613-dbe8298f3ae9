<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="1200px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="160px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="评估时间" prop="assessment_date">
              <el-date-picker
                v-model="form.assessment_date"
                type="datetime"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="体温 (℃)" prop="temperature">
              <el-input-number
                v-model="form.temperature"
                :precision="1"
                :min="0"
                :max="50"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="体重 (g)" prop="weight">
              <el-input-number
                v-model="form.weight"
                :precision="0"
                :min="0"
                :max="10000"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="营养发育状况" prop="nutrition_development">
              <el-select v-model="form.nutrition_development" placeholder="选择营养发育状况">
                <el-option
                  v-for="option in MONTH_NUTRITION_DEVELOPMENT_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 体征表现 -->
        <div class="form-section mb-6">
          <h4 class="section-title">体征表现</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="面色" prop="complexion">
              <el-select v-model="form.complexion" placeholder="选择面色">
                <el-option
                  v-for="option in MONTH_SKIN_COLOR_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="哭声" prop="cry">
              <el-select v-model="form.cry" placeholder="选择哭声">
                <el-option
                  v-for="option in MONTH_CRY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="反应" prop="reaction">
              <el-select v-model="form.reaction" placeholder="选择反应">
                <el-option
                  v-for="option in MONTH_REACTION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="皮肤状况" prop="skin">
              <el-select v-model="form.skin" multiple placeholder="选择皮肤状况">
                <el-option
                  v-for="option in MONTH_SKIN_STATUS_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="space-y-4">
            <el-form-item label="破损部位" prop="damaged_part" v-if="form.skin.includes('DAMAGED')">
              <el-input
                v-model="form.damaged_part"
                type="textarea"
                :rows="2"
                placeholder="请描述破损部位"
              />
            </el-form-item>
            <el-form-item label="皮疹部位" prop="rash_part" v-if="form.skin.includes('RASH')">
              <el-input
                v-model="form.rash_part"
                type="textarea"
                :rows="2"
                placeholder="请描述皮疹部位"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 喂养情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">喂养情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="喂养方式" prop="feeding_situation">
              <el-select v-model="form.feeding_situation" placeholder="选择喂养方式">
                <el-option
                  v-for="option in MONTH_FEEDING_SITUATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="奶粉品牌"
              prop="formula_brand"
              v-if="form.feeding_situation !== 'BREASTFEEDING'"
            >
              <el-input v-model="form.formula_brand" placeholder="请输入奶粉品牌" />
            </el-form-item>
          </div>
        </div>

        <!-- 排泄情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">排泄情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="排尿是否正常">
              <el-switch v-model="form.urine_normal" />
            </el-form-item>
            <el-form-item label="排尿次数/天" prop="urine_times">
              <el-input-number v-model="form.urine_times" :min="0" class="w-full" />
            </el-form-item>
            <el-form-item label="大便是否正常">
              <el-switch v-model="form.bowel_movement_normal" />
            </el-form-item>
            <el-form-item label="排便习惯次数/天" prop="bowel_movement_usual_times">
              <el-input-number v-model="form.bowel_movement_usual_times" :min="0" class="w-full" />
            </el-form-item>
            <el-form-item label="便秘次数" prop="constipations_times">
              <el-input-number v-model="form.constipations_times" :min="0" class="w-full" />
            </el-form-item>
            <el-form-item label="腹泻次数" prop="diarrhea_times">
              <el-input-number v-model="form.diarrhea_times" :min="0" class="w-full" />
            </el-form-item>
          </div>
        </div>

        <!-- 肌张力及活动 -->
        <div class="form-section mb-6">
          <h4 class="section-title">肌张力及活动</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="肌张力及活动" prop="muscle_tone_and_activity">
              <el-select v-model="form.muscle_tone_and_activity" placeholder="选择肌张力及活动状况">
                <el-option
                  v-for="option in MUSCLE_TONE_ACTIVITY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="space-y-4">
            <el-form-item
              label="受限部位"
              prop="muscle_tone_and_activity_limited_part"
              v-if="form.muscle_tone_and_activity === 'LIMITED'"
            >
              <el-input
                v-model="form.muscle_tone_and_activity_limited_part"
                type="textarea"
                :rows="2"
                placeholder="请描述受限部位"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 护理问题 -->
        <div class="form-section mb-6">
          <h4 class="section-title">护理问题</h4>
          <div class="space-y-4">
            <el-form-item label="是否存在护理问题">
              <el-switch v-model="form.current_nursing_problems" />
            </el-form-item>
            <el-form-item
              label="现存护理问题描述"
              prop="current_nursing_problems_description"
              v-if="form.current_nursing_problems"
            >
              <el-input
                v-model="form.current_nursing_problems_description"
                type="textarea"
                :rows="3"
                placeholder="请详细描述现存护理问题"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 指导建议 -->
        <div class="form-section mb-6">
          <h4 class="section-title">指导建议</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="喂养指导" prop="feeding_guidance">
              <el-select v-model="form.feeding_guidance" placeholder="选择喂养指导">
                <el-option
                  v-for="option in MONTH_FEEDING_GUIDANCE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="预防感冒措施" prop="prevent_cold">
              <el-select v-model="form.prevent_cold" placeholder="选择预防感冒措施">
                <el-option
                  v-for="option in MONTH_PREVENT_COLD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div class="space-y-4">
            <el-form-item
              label="喂养指导其他描述"
              prop="feeding_guidance_other_description"
              v-if="form.feeding_guidance === 'OTHER'"
            >
              <el-input
                v-model="form.feeding_guidance_other_description"
                type="textarea"
                :rows="2"
                placeholder="请描述其他喂养指导"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '提交' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils.js'
import { getCurrentTime, processDateFields } from '@/utils/dateUtils.js'
import {
  MONTH_NUTRITION_DEVELOPMENT_OPTIONS,
  MONTH_SKIN_COLOR_OPTIONS,
  MONTH_CRY_OPTIONS,
  MONTH_REACTION_OPTIONS,
  MONTH_SKIN_STATUS_OPTIONS,
  MONTH_FEEDING_SITUATION_OPTIONS,
  MUSCLE_TONE_ACTIVITY_OPTIONS,
  MONTH_FEEDING_GUIDANCE_OPTIONS,
  MONTH_PREVENT_COLD_OPTIONS,
} from '@/utils/constants.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  babyId: { type: [String, Number], default: null },
  detail: {
    type: Object,
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  assessment_date: '',
  temperature: null,
  weight: null,
  nutrition_development: '',
  complexion: '',
  cry: '',
  reaction: '',
  skin: [],
  damaged_part: '',
  rash_part: '',
  feeding_situation: '',
  formula_brand: '',
  urine_normal: true,
  urine_times: null,
  bowel_movement_normal: true,
  bowel_movement_usual_times: null,
  constipations_times: null,
  diarrhea_times: null,
  muscle_tone_and_activity: '',
  muscle_tone_and_activity_limited_part: '',
  current_nursing_problems: false,
  current_nursing_problems_description: '',
  feeding_guidance: '',
  feeding_guidance_other_description: '',
  prevent_cold: '',
})

// 日期字段配置
const DATE_FIELDS = ['assessment_date']

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return isEdit.value ? '更新满月评估记录' : '新建满月评估记录'
})

const isEdit = computed(() => !!props.detail)

// 表单验证规则
const rules = computed(() => ({
  assessment_date: [{ required: true, message: '请选择评估时间', trigger: 'change' }],
}))

// 监听父级字段变化，清空相关子字段
watch(
  () => form.skin,
  (newValue) => {
    if (!newValue.includes('DAMAGED')) {
      form.damaged_part = ''
    }
    if (!newValue.includes('RASH')) {
      form.rash_part = ''
    }
  },
)

watch(
  () => form.feeding_situation,
  (newValue) => {
    if (newValue === 'BREASTFEEDING') {
      form.formula_brand = ''
    }
  },
)

watch(
  () => form.muscle_tone_and_activity,
  (newValue) => {
    if (newValue !== 'LIMITED') {
      form.muscle_tone_and_activity_limited_part = ''
    }
  },
)

watch(
  () => form.current_nursing_problems,
  (newValue) => {
    if (!newValue) {
      form.current_nursing_problems_description = ''
    }
  },
)

watch(
  () => form.feeding_guidance,
  (newValue) => {
    if (newValue !== 'OTHER') {
      form.feeding_guidance_other_description = ''
    }
  },
)

// 监听对话框显示
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      if (props.detail) {
        // 编辑模式：使用传入的详情数据
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)

        // 处理日期字段
        const processedData = processDateFields(props.detail, 'toInput', DATE_FIELDS)
        Object.assign(form, processedData)
        console.log('Form data after assignment:', { ...form })
      } else {
        // 新建模式：初始化默认值
        resetForm()
        await nextTick()
        const now = getCurrentTime('input')
        form.assessment_date = now
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (['urine_normal', 'bowel_movement_normal', 'current_nursing_problems'].includes(key)) {
      form[key] = key === 'current_nursing_problems' ? false : true
    } else if (
      [
        'temperature',
        'weight',
        'urine_times',
        'bowel_movement_usual_times',
        'constipations_times',
        'diarrhea_times',
      ].includes(key)
    ) {
      form[key] = null
    } else if (key === 'skin') {
      form[key] = []
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 处理提交数据的日期格式
    const submitData = processDateFields(form, 'toAPI', DATE_FIELDS)
    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      // 验证babyId
      if (!props.babyId) {
        ElMessage.error('未指定宝宝ID，无法提交记录')
        return
      }
      res = await post(
        `customer-service/newborn-month-assessment/create/${props.babyId}/`,
        submitData,
      )
    } else {
      res = await put(
        `customer-service/newborn-month-assessment/update/${props.babyId}/`,
        submitData,
      )
    }

    ElMessage.success(isEdit.value ? '新生儿满月评估更新成功' : '新生儿满月评估创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
