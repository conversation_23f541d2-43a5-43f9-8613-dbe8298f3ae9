<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="记录日期" prop="record_date" required>
              <el-date-picker
                v-model="form.record_date"
                type="date"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择记录日期"
              />
            </el-form-item>
            <el-form-item label="黄疸值 (mg/dL)" prop="jaundice_value">
              <el-input-number
                v-model="form.jaundice_value"
                :min="0"
                :max="50"
                :precision="1"
                class="w-full"
                placeholder="请输入黄疸值"
              />
            </el-form-item>
            <el-form-item label="护理人员签名" prop="caregiver_signature" class="col-span-full">
              <el-input v-model="form.caregiver_signature" placeholder="请输入护理人员签名" />
            </el-form-item>
          </div>
        </div>

        <!-- 皮肤检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">皮肤检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="黄疸情况" prop="skin_jaundice">
              <el-input v-model="form.skin_jaundice" placeholder="如：轻度黄疸、无黄疸等" />
            </el-form-item>
            <el-form-item label="红润程度" prop="skin_ruddy">
              <el-input v-model="form.skin_ruddy" placeholder="如：红润正常、苍白等" />
            </el-form-item>
            <el-form-item label="红斑情况" prop="skin_erythema">
              <el-input v-model="form.skin_erythema" placeholder="如：无红斑、轻微红斑等" />
            </el-form-item>
            <el-form-item label="糜烂情况" prop="skin_erosion">
              <el-input v-model="form.skin_erosion" placeholder="如：无糜烂、轻微糜烂等" />
            </el-form-item>
            <el-form-item label="脓疱疹情况" prop="skin_pustule">
              <el-input v-model="form.skin_pustule" placeholder="如：无脓疱疹、少量脓疱疹等" />
            </el-form-item>
            <el-form-item label="湿疹情况" prop="skin_eczema">
              <el-input v-model="form.skin_eczema" placeholder="如：无湿疹、轻微湿疹等" />
            </el-form-item>
            <el-form-item label="尿布疹情况" prop="skin_diaper_rash">
              <el-input v-model="form.skin_diaper_rash" placeholder="如：无尿布疹、轻微尿布疹等" />
            </el-form-item>
          </div>
        </div>

        <!-- 眼部检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">眼部检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="眼睛正常情况" prop="eyes_normal">
              <el-input v-model="form.eyes_normal" placeholder="如：双眼正常、单眼异常等" />
            </el-form-item>
            <el-form-item label="分泌物情况" prop="eyes_discharge">
              <el-input v-model="form.eyes_discharge" placeholder="如：无分泌物、少量分泌物等" />
            </el-form-item>
          </div>
        </div>

        <!-- 口腔检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">口腔检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="口腔光滑情况" prop="oral_smooth">
              <el-input v-model="form.oral_smooth" placeholder="如：口腔光滑、粗糙等" />
            </el-form-item>
            <el-form-item label="破溃情况" prop="oral_ulcer">
              <el-input v-model="form.oral_ulcer" placeholder="如：无破溃、轻微破溃等" />
            </el-form-item>
            <el-form-item label="鹅口疮情况" prop="oral_thrush">
              <el-input v-model="form.oral_thrush" placeholder="如：无鹅口疮、轻微鹅口疮等" />
            </el-form-item>
          </div>
        </div>

        <!-- 脐带检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">脐带检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="干燥情况" prop="umbilical_dry">
              <el-input v-model="form.umbilical_dry" placeholder="如：脐带干燥、湿润等" />
            </el-form-item>
            <el-form-item label="出血情况" prop="umbilical_bleeding">
              <el-input v-model="form.umbilical_bleeding" placeholder="如：无出血、少量出血等" />
            </el-form-item>
            <el-form-item label="脱落情况" prop="umbilical_detached">
              <el-input v-model="form.umbilical_detached" placeholder="如：已脱落、未脱落等" />
            </el-form-item>
          </div>
        </div>

        <!-- 其他检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">其他检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="呕吐情况" prop="vomiting">
              <el-input v-model="form.vomiting" placeholder="如：无呕吐、偶有呕吐等" />
            </el-form-item>
            <el-form-item label="四肢张力" prop="limb_tone">
              <el-input v-model="form.limb_tone" placeholder="如：张力正常、张力偏低等" />
            </el-form-item>
            <el-form-item label="产伤情况" prop="birth_injury">
              <el-input v-model="form.birth_injury" placeholder="如：无产伤、轻微产伤等" />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  babyId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const currentItemId = ref(null)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!currentItemId.value)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑新生儿护理记录单（2）' : '新建新生儿护理记录单（2）'
})

const form = reactive({
  record_date: '',
  skin_jaundice: '',
  skin_ruddy: '',
  skin_erythema: '',
  skin_erosion: '',
  skin_pustule: '',
  skin_eczema: '',
  skin_diaper_rash: '',
  eyes_normal: '',
  eyes_discharge: '',
  oral_smooth: '',
  oral_ulcer: '',
  oral_thrush: '',
  umbilical_dry: '',
  umbilical_bleeding: '',
  umbilical_detached: '',
  vomiting: '',
  limb_tone: '',
  birth_injury: '',
  jaundice_value: null,
  caregiver_signature: '',
})

const rules = {
  record_date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  jaundice_value: [
    { type: 'number', min: 0, max: 50, message: '黄疸值应在0-50mg/dL之间', trigger: 'change' },
  ],
}

const { scrollToTop } = useDialogScrollToTop()

// 重置表单
const resetForm = () => {
  const numberFields = ['jaundice_value']
  const stringFields = [
    'record_date',
    'skin_jaundice',
    'skin_ruddy',
    'skin_erythema',
    'skin_erosion',
    'skin_pustule',
    'skin_eczema',
    'skin_diaper_rash',
    'eyes_normal',
    'eyes_discharge',
    'oral_smooth',
    'oral_ulcer',
    'oral_thrush',
    'umbilical_dry',
    'umbilical_bleeding',
    'umbilical_detached',
    'vomiting',
    'limb_tone',
    'birth_injury',
    'caregiver_signature',
  ]

  numberFields.forEach((key) => {
    form[key] = null
  })

  stringFields.forEach((key) => {
    form[key] = ''
  })

  nextTick(() => formRef.value?.clearValidate())
}

// 设置表单数据
const setFormData = (data) => {
  if (!data) return

  // 安全地设置表单数据，确保类型正确
  Object.keys(form).forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      form[key] = data[key]
    }
  })
}

// 获取详情数据
const fetchDetail = async (recordId) => {
  if (!recordId || loading.value) return

  loading.value = true
  try {
    const response = await get(`customer-service/newborn-care-two/detail/${recordId}/`)
    if (response) {
      setFormData(response)
      console.log('Form data loaded from API:', { ...form })
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    const errorMsg = error.response?.status === 404 ? '记录不存在或已被删除' : '获取详情失败'
    ElMessage.error(errorMsg)
  } finally {
    loading.value = false
  }
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (isVisible) => {
    scrollToTop()
    if (isVisible) {
      // 设置 currentItemId
      currentItemId.value = props.detail?.record_id || null

      if (props.detail && isEdit.value) {
        // 编辑模式：如果传入了详情数据，直接使用
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)
        setFormData(props.detail)
        console.log('Form data after assignment:', { ...form })
      } else if (currentItemId.value) {
        // 编辑模式：从API获取数据
        resetForm()
        await fetchDetail(currentItemId.value)
      } else {
        // 新建模式：初始化默认值
        resetForm()
        await nextTick()
        form.record_date = getCurrentTime('date')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 验证babyId
const validateBabyId = () => {
  if (!props.babyId) {
    ElMessage.error('未指定宝宝ID，无法提交记录')
    return false
  }
  return true
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    // 验证babyId（仅在新建时需要）
    if (!isEdit.value && !validateBabyId()) {
      return
    }

    submitting.value = true

    // 处理提交数据 - 确保数据类型正确
    const submitData = { ...form }

    // 清理空字符串，数字字段保持null
    Object.keys(submitData).forEach((key) => {
      if (typeof submitData[key] === 'string' && submitData[key].trim() === '') {
        submitData[key] = ''
      }
    })

    console.log('Submit data:', submitData)

    let res
    if (!currentItemId.value) {
      // 创建记录
      res = await post(`customer-service/newborn-care-two/create/${props.babyId}/`, submitData)
      ElMessage.success('新生儿护理记录创建成功！')
    } else {
      // 更新记录
      res = await put(
        `customer-service/newborn-care-two/update/${currentItemId.value}/`,
        submitData,
      )
      ElMessage.success('新生儿护理记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.col-span-full {
  grid-column: 1 / -1;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
