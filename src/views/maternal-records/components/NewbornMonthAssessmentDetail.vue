<template>
  <div class="newborn-month-assessment-detail">
    <div class="detail-header mb-6 flex justify-between items-center">
      <div class="flex-1">
        <div class="flex items-center gap-4 mb-2">
          <h3 class="text-lg font-semibold text-gray-800">新生儿满月评估记录</h3>
          <!-- Baby切换Tab -->
          <div v-if="props.babyList?.length > 1" class="flex bg-gray-100 rounded-lg p-1">
            <button
              v-for="baby in props.babyList"
              :key="baby.baby_info.nid"
              @click="handleBabyChange(baby.baby_info)"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-all cursor-pointer',
                currentBaby?.nid === baby.baby_info.nid
                  ? 'bg-white text-pink-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800',
              ]"
            >
              {{ baby.baby_info.name || `宝宝${baby.baby_info.nid}` }}
            </button>
          </div>
        </div>
        <p class="text-sm text-gray-600">
          查看新生儿满月时的健康状况和发育情况
          <span v-if="babyList.length > 1 && currentBaby" class="ml-2 text-pink-600">
            当前：{{ currentBaby.name || `宝宝${currentBaby.nid}` }}
          </span>
        </p>
      </div>
      <div class="flex gap-2">
        <el-button
          v-if="detailData"
          type="primary"
          @click="editRecord"
          :disabled="!currentBaby"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          更新记录
        </el-button>
        <el-button
          v-if="detailData"
          :disabled="true"
          @click="exportPDF"
          :loading="pdfLoading"
          class="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
        >
          <template #icon>
            <Download />
          </template>
          导出PDF
        </el-button>
      </div>
    </div>

    <!-- 无Baby提示 -->
    <div v-if="!currentBaby" class="text-center py-8">
      <el-empty description="暂无宝宝信息，无法查看满月评估记录" />
    </div>

    <div v-else v-loading="loading" class="detail-content">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>评估时间：</label>
              <span>{{ formatDate(detailData.assessment_date) }}</span>
            </div>
            <div class="detail-item">
              <label>体温 (℃)：</label>
              <span>{{ formatNumber(detailData.temperature, '℃') }}</span>
            </div>
            <div class="detail-item">
              <label>体重 (g)：</label>
              <span>{{ formatNumber(detailData.weight, 'g') }}</span>
            </div>
            <div class="detail-item">
              <label>营养发育状况：</label>
              <span>{{ getMonthNutritionDevelopmentText(detailData.nutrition_development) }}</span>
            </div>
          </div>
        </div>

        <!-- 体征表现 -->
        <div class="detail-section">
          <h3 class="section-title">体征表现</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>面色：</label>
              <span>{{ getMonthSkinColorText(detailData.complexion) }}</span>
            </div>
            <div class="detail-item">
              <label>哭声：</label>
              <span>{{ getMonthCryText(detailData.cry) }}</span>
            </div>
            <div class="detail-item">
              <label>反应：</label>
              <span>{{ getMonthReactionText(detailData.reaction) }}</span>
            </div>
            <div class="detail-item">
              <label>皮肤状况：</label>
              <span>{{ getMonthSkinStatusText(detailData.skin) }}</span>
            </div>
          </div>
          <div v-if="shouldShowSkinDetails(detailData)" class="space-y-4 mt-4">
            <div v-if="showDamagedPart(detailData)" class="detail-item">
              <label>破损部位：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.damaged_part) }}</p>
            </div>
            <div v-if="showRashPart(detailData)" class="detail-item">
              <label>皮疹部位：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.rash_part) }}</p>
            </div>
          </div>
        </div>

        <!-- 喂养情况 -->
        <div class="detail-section">
          <h3 class="section-title">喂养情况</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>喂养方式：</label>
              <span>{{ getMonthFeedingSituationText(detailData.feeding_situation) }}</span>
            </div>
            <div v-if="detailData.formula_brand" class="detail-item">
              <label>奶粉品牌：</label>
              <span>{{ formatText(detailData.formula_brand) }}</span>
            </div>
          </div>
        </div>

        <!-- 排泄情况 -->
        <div class="detail-section">
          <h3 class="section-title">排泄情况</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="detail-item">
              <label>排尿是否正常：</label>
              <span>{{ formatBoolean(detailData.urine_normal) }}</span>
            </div>
            <div class="detail-item">
              <label>排尿次数/天：</label>
              <span>{{ formatNumber(detailData.urine_times) }}次</span>
            </div>
            <div class="detail-item">
              <label>大便是否正常：</label>
              <span>{{ formatBoolean(detailData.bowel_movement_normal) }}</span>
            </div>
            <div class="detail-item">
              <label>排便习惯次数/天：</label>
              <span>{{ formatNumber(detailData.bowel_movement_usual_times) }}次</span>
            </div>
            <div class="detail-item">
              <label>便秘次数：</label>
              <span>{{ formatNumber(detailData.constipations_times) }}次</span>
            </div>
            <div class="detail-item">
              <label>腹泻次数：</label>
              <span>{{ formatNumber(detailData.diarrhea_times) }}次</span>
            </div>
          </div>
        </div>

        <!-- 肌张力及活动 -->
        <div class="detail-section">
          <h3 class="section-title">肌张力及活动</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>肌张力及活动：</label>
              <span>{{ getMuscleToneActivityText(detailData.muscle_tone_and_activity) }}</span>
            </div>
            <div v-if="detailData.muscle_tone_and_activity_limited_part" class="detail-item">
              <label>受限部位：</label>
              <span>{{ formatText(detailData.muscle_tone_and_activity_limited_part) }}</span>
            </div>
          </div>
        </div>

        <!-- 护理问题 -->
        <div class="detail-section">
          <h3 class="section-title">护理问题</h3>
          <div class="space-y-4">
            <div class="detail-item">
              <label>是否存在护理问题：</label>
              <span>{{ formatBoolean(detailData.current_nursing_problems) }}</span>
            </div>
            <div v-if="detailData.current_nursing_problems_description" class="detail-item">
              <label>现存护理问题描述：</label>
              <p class="mt-1 text-gray-600">
                {{ formatText(detailData.current_nursing_problems_description) }}
              </p>
            </div>
          </div>
        </div>

        <!-- 指导建议 -->
        <div class="detail-section">
          <h3 class="section-title">指导建议</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>喂养指导：</label>
              <span>{{ getMonthFeedingGuidanceText(detailData.feeding_guidance) }}</span>
            </div>
            <div class="detail-item">
              <label>预防感冒措施：</label>
              <span>{{ getMonthPreventColdText(detailData.prevent_cold) }}</span>
            </div>
          </div>
          <div v-if="detailData.feeding_guidance_other_description" class="space-y-4 mt-4">
            <div class="detail-item">
              <label>喂养指导其他描述：</label>
              <p class="mt-1 text-gray-600">
                {{ formatText(detailData.feeding_guidance_other_description) }}
              </p>
            </div>
          </div>
        </div>

        <!-- 记录信息 -->
        <div class="detail-section">
          <h3 class="section-title">记录信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>评估ID：</label>
              <span>{{ formatText(detailData.assessment_id) }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDate(detailData.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDate(detailData.updated_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暂无数据 -->
      <div v-else class="text-center py-16">
        <el-empty description="暂无满月评估记录">
          <el-button
            type="primary"
            @click="createRecord"
            :disabled="!currentBaby"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            创建记录
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 表单对话框 -->
    <NewbornMonthAssessmentFormDialog
      v-model="showFormDialog"
      :baby-id="currentBaby?.nid"
      :detail="dialogDetail"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { get } from '@/utils/request.js'
import { formatNumber, formatText, formatBoolean } from '@/utils/utils.js'
import { formatDate } from '@/utils/dateUtils.js'
import {
  getMonthNutritionDevelopmentText,
  getMonthSkinColorText,
  getMonthCryText,
  getMonthReactionText,
  getMonthSkinStatusText,
  getMonthFeedingSituationText,
  getMonthFeedingGuidanceText,
  getMonthPreventColdText,
  getMuscleToneActivityText,
} from '@/utils/constants.js'
import NewbornMonthAssessmentFormDialog from './NewbornMonthAssessmentFormDialog.vue'

// Props
const props = defineProps({
  babyList: {
    type: Array,
    default: () => [],
  },
})

// 响应式数据
const loading = ref(false)
const pdfLoading = ref(false)
const detailData = ref(null)
const currentBaby = ref(null)
const showFormDialog = ref(false)
const dialogDetail = ref(null)

// 获取详情数据
const fetchDetail = async () => {
  if (!currentBaby.value?.nid) return

  loading.value = true
  try {
    const response = await get(
      `customer-service/newborn-month-assessment/detail/${currentBaby.value.nid}/`,
    )
    if (response) {
      detailData.value = response
    } else {
      detailData.value = null
    }
  } catch (error) {
    console.error('获取满月评估详情失败:', error)
    if (error.response?.status === 404) {
      // 404错误表示暂无记录，这是正常情况
      detailData.value = null
    } else {
      ElMessage.error('获取满月评估详情失败')
    }
    detailData.value = null
  } finally {
    loading.value = false
  }
}

// 处理baby切换
const handleBabyChange = (babyInfo) => {
  if (currentBaby.value?.nid === babyInfo.nid) return

  currentBaby.value = babyInfo
  detailData.value = null // 清空当前详情
}

// 监听babyList变化，设置默认选中的baby
watch(
  () => props.babyList,
  (newBabyList) => {
    if (newBabyList?.length > 0) {
      // 如果当前没有选中的baby，或者当前baby不在新列表中，则重新初始化
      const currentBabyExists = newBabyList.some(
        (item) => item.baby_info.nid === currentBaby.value?.nid,
      )
      if (!currentBaby.value || !currentBabyExists) {
        currentBaby.value = newBabyList[0].baby_info
        detailData.value = null // 清空当前详情
        fetchDetail() // 重新获取详情
      }
    } else {
      currentBaby.value = null
      detailData.value = null
    }
  },
  { immediate: true },
)

// 监听currentBaby变化，获取详情数据
watch(
  () => currentBaby.value?.nid,
  (newBabyId) => {
    if (newBabyId) {
      fetchDetail()
    } else {
      detailData.value = null
    }
  },
)

// 创建记录
const createRecord = () => {
  if (!currentBaby.value) {
    ElMessage.error('请先选择宝宝')
    return
  }
  dialogDetail.value = null
  showFormDialog.value = true
}

// 编辑记录
const editRecord = () => {
  if (!detailData.value) {
    ElMessage.error('暂无记录可编辑')
    return
  }
  dialogDetail.value = detailData.value
  showFormDialog.value = true
}

// 处理表单成功
const handleFormSuccess = () => {
  showFormDialog.value = false
  fetchDetail()
}

// 导出PDF (暂时禁用)
const exportPDF = async () => {
  if (!detailData.value?.assessment_id) {
    ElMessage.error('暂无评估记录可导出')
    return
  }

  // 暂时提示功能未开放
  ElMessage.info('PDF导出功能暂未开放')
}

// 刷新数据
const refresh = () => {
  fetchDetail()
}

// 判断是否显示皮肤详情
const shouldShowSkinDetails = (data) => {
  return showDamagedPart(data) || showRashPart(data)
}

// 判断是否显示破损部位
const showDamagedPart = (data) => {
  if (!data?.skin || !data?.damaged_part) return false
  const skinArray = Array.isArray(data.skin) ? data.skin : [data.skin]
  return skinArray.includes('DAMAGED')
}

// 判断是否显示皮疹部位
const showRashPart = (data) => {
  if (!data?.skin || !data?.rash_part) return false
  const skinArray = Array.isArray(data.skin) ? data.skin : [data.skin]
  return skinArray.includes('RASH')
}

// 暴露方法给父组件
defineExpose({
  refresh,
  exportPDF,
})
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
}

.detail-item span {
  color: #374151;
}

.detail-item p {
  color: #6b7280;
  line-height: 1.5;
}

.grid .detail-item {
  flex-direction: row;
  align-items: center;
}

.grid .detail-item label {
  margin-bottom: 0;
  margin-right: 0.5rem;
  min-width: 8rem;
}
</style>
