export { default as ActionButtons } from './ActionButtons.vue'
export { default as BabyCard } from './BabyCard.vue'
export { default as BabyFormDialog } from './BabyFormDialog.vue'
export { default as ClientHeader } from './ClientHeader.vue'
export { default as CustomerCards } from './CustomerCards.vue'
export { default as CustomerFormDialog } from './CustomerFormDialog.vue'
export { default as CustomerTable } from './CustomerTable.vue'
export { default as DashboardCards } from './DashboardCards.vue'
export { default as MaternalAdmissionDetail } from './MaternalAdmissionDetail.vue'
export { default as MaternalAdmissionFormDialog } from './MaternalAdmissionFormDialog.vue'
export { default as MaternalDailyCareDetail } from './MaternalDailyCareDetail.vue'
export { default as MaternalDailyCareFormDialog } from './MaternalDailyCareFormDialog.vue'
export { default as MaternalDailyRequiredDetail } from './MaternalDailyRequiredDetail.vue'
export { default as MaternalDailyRequiredFormDialog } from './MaternalDailyRequiredFormDialog.vue'
export { default as MaternalDietaryDetail } from './MaternalDietaryDetail.vue'
export { default as MaternalDietaryFormDialog } from './MaternalDietaryFormDialog.vue'
export { default as MaternalRecoveryDetail } from './MaternalRecoveryDetail.vue'
export { default as MaternalRecoveryFormDialog } from './MaternalRecoveryFormDialog.vue'
export { default as NewbornAdmissionDetail } from './NewbornAdmissionDetail.vue'
export { default as NewbornAdmissionFormDialog } from './NewbornAdmissionFormDialog.vue'
export { default as NewbornCareOneDetail } from './NewbornCareOneDetail.vue'
export { default as NewbornCareOneFormDialog } from './NewbornCareOneFormDialog.vue'
export { default as NewbornCareOperationDetail } from './NewbornCareOperationDetail.vue'
export { default as NewbornCareOperationFormDialog } from './NewbornCareOperationFormDialog.vue'
export { default as NewbornDailyRequiredDetail } from './NewbornDailyRequiredDetail.vue'
export { default as NewbornDailyRequiredFormDialog } from './NewbornDailyRequiredFormDialog.vue'
export { default as NewbornFeedingDetail } from './NewbornFeedingDetail.vue'
export { default as NewbornFeedingFormDialog } from './NewbornFeedingFormDialog.vue'
export { default as NewbornMonthAssessmentDetail } from './NewbornMonthAssessmentDetail.vue'
export { default as NewbornMonthAssessmentFormDialog } from './NewbornMonthAssessmentFormDialog.vue'
export { default as PersonalizedCareForm } from './PersonalizedCareForm.vue'
export { default as RecordTabs } from './RecordTabs.vue'
