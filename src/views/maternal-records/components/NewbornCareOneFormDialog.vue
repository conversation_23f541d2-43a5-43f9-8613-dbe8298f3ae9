<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="记录日期" prop="record_date" required>
              <el-date-picker
                v-model="form.record_date"
                type="date"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择记录日期"
              />
            </el-form-item>
            <el-form-item label="出生后天数" prop="days_after_birth" required>
              <el-input-number
                v-model="form.days_after_birth"
                :min="0"
                :max="365"
                :precision="0"
                class="w-full"
                placeholder="请输入出生后天数"
              />
            </el-form-item>
            <el-form-item label="护理人员签名" prop="caregiver_signature" class="col-span-full">
              <el-input v-model="form.caregiver_signature" placeholder="请输入护理人员签名" />
            </el-form-item>
          </div>
        </div>

        <!-- 生理指标 -->
        <div class="form-section mb-6">
          <h4 class="section-title">生理指标</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="体重 (克)" prop="weight">
              <el-input-number
                v-model="form.weight"
                :min="0"
                :max="10000"
                :precision="1"
                class="w-full"
                placeholder="请输入新生儿体重"
              />
            </el-form-item>
            <el-form-item label="身长 (厘米)" prop="length">
              <el-input-number
                v-model="form.length"
                :min="0"
                :max="100"
                :precision="1"
                class="w-full"
                placeholder="请输入新生儿身长"
              />
            </el-form-item>
            <el-form-item label="体温 (℃)" prop="temperature">
              <el-input-number
                v-model="form.temperature"
                :min="35"
                :max="42"
                :precision="1"
                class="w-full"
                placeholder="请输入新生儿体温"
              />
            </el-form-item>
            <el-form-item label="前囟情况" prop="anterior_fontanelle">
              <el-input v-model="form.anterior_fontanelle" placeholder="如：平软、饱满等" />
            </el-form-item>
          </div>
        </div>

        <!-- 行为表现 -->
        <div class="form-section mb-6">
          <h4 class="section-title">行为表现</h4>
          <div class="space-y-4">
            <el-form-item label="哭声情况" prop="cry">
              <el-input
                v-model="form.cry"
                type="textarea"
                :rows="3"
                placeholder="请输入新生儿哭声情况，如：响亮有力、微弱等"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 排泄情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">排泄情况</h4>
          <div class="space-y-4">
            <el-form-item label="小便情况" prop="urine">
              <el-input
                v-model="form.urine"
                type="textarea"
                :rows="2"
                placeholder="请输入小便情况，如：正常，淡黄色"
              />
            </el-form-item>
            <el-form-item label="大便次数" prop="bowel_movement_frequency">
              <el-input-number
                v-model="form.bowel_movement_frequency"
                :min="0"
                :max="50"
                :precision="0"
                class="w-full"
                placeholder="请输入大便次数"
              />
            </el-form-item>
            <el-form-item label="大便颜色" prop="bowel_movement_color">
              <el-input
                v-model="form.bowel_movement_color"
                placeholder="请输入大便颜色，如：黄色、绿色等"
              />
            </el-form-item>
            <el-form-item label="大便性质" prop="bowel_movement_consistency">
              <el-input
                v-model="form.bowel_movement_consistency"
                type="textarea"
                :rows="2"
                placeholder="请输入大便性质，如：软便、糊状等"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 护理指导 -->
        <div class="form-section mb-6">
          <h4 class="section-title">护理指导</h4>
          <el-form-item label="护理指导意见" prop="guidance">
            <el-input
              v-model="form.guidance"
              type="textarea"
              :rows="4"
              placeholder="请输入护理指导意见，如：继续观察体重增长情况，注意保暖，按需喂养等"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  babyId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const currentItemId = ref(null)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!currentItemId.value)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑新生儿护理记录单（1）' : '新建新生儿护理记录单（1）'
})

const form = reactive({
  record_date: '',
  days_after_birth: null,
  weight: null,
  length: null,
  cry: '',
  temperature: null,
  urine: '',
  bowel_movement_frequency: null,
  bowel_movement_color: '',
  bowel_movement_consistency: '',
  anterior_fontanelle: '',
  guidance: '',
  caregiver_signature: '',
})

const rules = {
  record_date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  days_after_birth: [
    { required: true, message: '请输入出生后天数', trigger: 'change' },
    { type: 'number', min: 0, max: 365, message: '出生后天数应在0-365天之间', trigger: 'change' },
  ],
  weight: [
    { type: 'number', min: 0, max: 10000, message: '体重应在0-10000克之间', trigger: 'change' },
  ],
  length: [
    { type: 'number', min: 0, max: 100, message: '身长应在0-100厘米之间', trigger: 'change' },
  ],
  temperature: [
    { type: 'number', min: 35, max: 42, message: '体温应在35-42℃之间', trigger: 'change' },
  ],
  bowel_movement_frequency: [
    { type: 'number', min: 0, max: 50, message: '大便次数应在0-50次之间', trigger: 'change' },
  ],
}

const { scrollToTop } = useDialogScrollToTop()

// 重置表单
const resetForm = () => {
  const numberFields = [
    'days_after_birth',
    'weight',
    'length',
    'temperature',
    'bowel_movement_frequency',
  ]
  const stringFields = [
    'record_date',
    'cry',
    'urine',
    'bowel_movement_color',
    'bowel_movement_consistency',
    'anterior_fontanelle',
    'guidance',
    'caregiver_signature',
  ]

  numberFields.forEach((key) => {
    form[key] = null
  })

  stringFields.forEach((key) => {
    form[key] = ''
  })

  nextTick(() => formRef.value?.clearValidate())
}

// 设置表单数据
const setFormData = (data) => {
  if (!data) return

  // 安全地设置表单数据，确保类型正确
  Object.keys(form).forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      form[key] = data[key]
    }
  })
}

// 获取详情数据
const fetchDetail = async (recordId) => {
  if (!recordId || loading.value) return

  loading.value = true
  try {
    const response = await get(`customer-service/newborn-care-one/detail/${recordId}/`)
    if (response) {
      setFormData(response)
      console.log('Form data loaded from API:', { ...form })
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    const errorMsg = error.response?.status === 404 ? '记录不存在或已被删除' : '获取详情失败'
    ElMessage.error(errorMsg)
  } finally {
    loading.value = false
  }
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (isVisible) => {
    scrollToTop()
    if (isVisible) {
      // 设置 currentItemId
      currentItemId.value = props.detail?.record_id || null

      if (props.detail && isEdit.value) {
        // 编辑模式：如果传入了详情数据，直接使用
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)
        setFormData(props.detail)
        console.log('Form data after assignment:', { ...form })
      } else if (currentItemId.value) {
        // 编辑模式：从API获取数据
        resetForm()
        await fetchDetail(currentItemId.value)
      } else {
        // 新建模式：初始化默认值
        resetForm()
        await nextTick()
        form.record_date = getCurrentTime('date')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 验证babyId
const validateBabyId = () => {
  if (!props.babyId) {
    ElMessage.error('未指定宝宝ID，无法提交记录')
    return false
  }
  return true
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    // 验证babyId（仅在新建时需要）
    if (!isEdit.value && !validateBabyId()) {
      return
    }

    submitting.value = true

    // 处理提交数据 - 确保数据类型正确
    const submitData = { ...form }

    // 清理空字符串，数字字段保持null
    Object.keys(submitData).forEach((key) => {
      if (typeof submitData[key] === 'string' && submitData[key].trim() === '') {
        submitData[key] = ''
      }
    })

    console.log('Submit data:', submitData)

    let res
    if (!currentItemId.value) {
      // 创建记录
      res = await post(`customer-service/newborn-care-one/create/${props.babyId}/`, submitData)
      ElMessage.success('新生儿护理记录创建成功！')
    } else {
      // 更新记录
      res = await put(
        `customer-service/newborn-care-one/update/${currentItemId.value}/`,
        submitData,
      )
      ElMessage.success('新生儿护理记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.col-span-full {
  grid-column: 1 / -1;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
