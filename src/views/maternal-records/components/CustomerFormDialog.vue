<template>
  <el-dialog v-model="visible" title="新增客户" width="800px" :before-close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <el-form-item label="产妇姓名" prop="motherName">
          <el-input v-model="form.motherName" placeholder="请输入产妇姓名" />
        </el-form-item>
        <el-form-item label="房间号" prop="roomNumber">
          <el-input v-model="form.roomNumber" placeholder="请输入房间号" />
        </el-form-item>
        <el-form-item label="入住日期" prop="checkinDate">
          <el-date-picker
            v-model="form.checkinDate"
            type="date"
            placeholder="选择入住日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="预计退房日期" prop="checkoutDate">
          <el-date-picker
            v-model="form.checkoutDate"
            type="date"
            placeholder="选择预计退房日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="新生儿信息" prop="babyInfo">
          <el-input v-model="form.babyInfo" placeholder="如：小宝(男)" />
        </el-form-item>
        <el-form-item label="主责护理" prop="nurse">
          <el-input v-model="form.nurse" placeholder="如：刘护士 / 张医生" />
        </el-form-item>
      </div>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElDatePicker,
  ElButton,
  ElMessage,
} from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(props.modelValue)
const loading = ref(false)
const formRef = ref()

const form = reactive({
  motherName: '',
  roomNumber: '',
  checkinDate: '',
  checkoutDate: '',
  babyInfo: '',
  nurse: '',
  remark: '',
})

const rules = {
  motherName: [{ required: true, message: '请输入产妇姓名', trigger: 'blur' }],
  roomNumber: [{ required: true, message: '请输入房间号', trigger: 'blur' }],
  checkinDate: [{ required: true, message: '请选择入住日期', trigger: 'change' }],
  checkoutDate: [{ required: true, message: '请选择预计退房日期', trigger: 'change' }],
  babyInfo: [{ required: true, message: '请输入新生儿信息', trigger: 'blur' }],
  nurse: [{ required: true, message: '请输入主责护理', trigger: 'blur' }],
}

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
  },
)

watch(visible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    resetForm()
  }
})

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    motherName: '',
    roomNumber: '',
    checkinDate: '',
    checkoutDate: '',
    babyInfo: '',
    nurse: '',
    remark: '',
  })
}

const handleClose = () => {
  visible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    emit('success', { ...form })
    visible.value = false
    ElMessage.success('客户信息保存成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}
</script>
