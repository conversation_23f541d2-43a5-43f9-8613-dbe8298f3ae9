<template>
  <div class="maternal-daily-care-detail">
    <div class="detail-header mb-6 flex justify-between items-center">
      <div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">产妇每日生理护理记录</h3>
        <p class="text-sm text-gray-600">查看产妇每日生理护理的记录和详情</p>
      </div>
      <div class="flex gap-2">
        <el-button
          type="primary"
          @click="createRecord"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          添加记录
        </el-button>
        <el-button
          v-if="detail"
          disabled
          @click="exportPDF"
          :loading="pdfLoading"
          class="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
        >
          <template #icon>
            <Download />
          </template>
          导出PDF
        </el-button>
      </div>
    </div>

    <!-- 记录列表区域 -->
    <div class="mb-6">
      <RecordListView
        ref="recordListRef"
        :api-url="`customer-service/mdpc-record/list/${props.customerId}/`"
        :selected-id="detail?.record_id"
        @select="handleSelectRecord"
      />
    </div>

    <!-- 详情内容区域 -->
    <div v-if="detail" class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div v-loading="detailLoading" class="p-6">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>记录日期：</label>
                <span>{{ formatDate(detail.record_date) }}</span>
              </div>
              <div class="detail-item">
                <label>创建人：</label>
                <span>{{ formatText(detail.creator_name) }}</span>
              </div>
              <div class="detail-item">
                <label>签名：</label>
                <span>{{ formatText(detail.signature) }}</span>
              </div>
            </div>
          </div>

          <!-- 饮食情况 -->
          <div class="detail-section">
            <h3 class="section-title">饮食情况</h3>
            <div class="space-y-3">
              <div class="detail-item">
                <label>正常饮食：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.normal_diet) }}</p>
              </div>
              <div class="detail-item">
                <label>特殊饮食：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.special_diet) }}</p>
              </div>
            </div>
          </div>

          <!-- 睡眠与排泄 -->
          <div class="detail-section">
            <h3 class="section-title">睡眠与排泄</h3>
            <div class="grid grid-cols-1 gap-4">
              <div class="detail-item">
                <label>睡眠时长：</label>
                <span>{{ formatNumber(detail.sleep_hours) }}小时</span>
              </div>
              <div class="detail-item">
                <label>小便情况：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.urination) }}</p>
              </div>
              <div class="detail-item">
                <label>大便情况：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.defecation) }}</p>
              </div>
            </div>
          </div>

          <!-- 个人卫生护理 -->
          <div class="detail-section">
            <h3 class="section-title">个人卫生护理</h3>
            <div class="space-y-3">
              <div class="detail-item">
                <label>刷牙：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.brushing_teeth) }}</p>
              </div>
              <div class="detail-item">
                <label>洗头：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.wash_hair) }}</p>
              </div>
              <div class="detail-item">
                <label>洗澡：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.bathe) }}</p>
              </div>
              <div class="detail-item">
                <label>会阴清洁：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.perineal_cleaning) }}</p>
              </div>
            </div>
          </div>

          <!-- 产后运动 -->
          <div class="detail-section">
            <h3 class="section-title">产后运动</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="detail-item">
                <label>运动时间：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.exercise_time) }}</p>
              </div>
              <div class="detail-item">
                <label>运动方式：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.exercise_method) }}</p>
              </div>
              <div class="detail-item">
                <label>运动耐受性：</label>
                <span>{{ getExerciseText(detail.exercise_tolerance) }}</span>
              </div>
              <div class="detail-item">
                <label>运动理解程度：</label>
                <span>{{ getExerciseText(detail.exercise_understanding) }}</span>
              </div>
            </div>
          </div>

          <!-- 生命体征 -->
          <div class="detail-section">
            <h3 class="section-title">生命体征</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>心率：</label>
                <span>{{ formatNumber(detail.heart_rate) }}次/分</span>
              </div>
              <div class="detail-item">
                <label>血压：</label>
                <span>{{ formatText(detail.blood_pressure) }}mmHg</span>
              </div>
            </div>
          </div>

          <!-- 其他情况 -->
          <div class="detail-section">
            <h3 class="section-title">其他情况</h3>
            <div class="detail-item">
              <label>其他情况：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detail.other_situation) }}</p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end gap-2 p-4">
          <el-button
            @click="editRecord"
            class="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            @click="deleteRecord"
            :loading="deleteLoading"
            class="hover:bg-red-50"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表单对话框 -->
    <MaternalDailyCareFormDialog
      v-model="showFormDialog"
      :detail="dialogDetail"
      :customer-id="props.customerId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { get, del } from '@/utils/request.js'
import { formatDate } from '@/utils/dateUtils.js'
import { formatNumber, formatText, downloadFile } from '@/utils/utils.js'
import { getExerciseAfterEvaluationText } from '@/utils/constants.js'
import MaternalDailyCareFormDialog from './MaternalDailyCareFormDialog.vue'
import RecordListView from '@/components/RecordListView.vue'

// 接收父组件传入的参数
const props = defineProps({
  customerId: {
    type: [String, Number],
    required: true,
  },
})

const detailLoading = ref(false)
const pdfLoading = ref(false)
const deleteLoading = ref(false)
const showFormDialog = ref(false)

// 组件引用
const recordListRef = ref()

// 选中的记录
const detail = ref(null)
const dialogDetail = ref(null)

// 获取详情数据
const fetchDetail = async (recordId) => {
  if (!recordId || detailLoading.value) return

  detailLoading.value = true
  try {
    const response = await get(`customer-service/mdpc-record/detail/${recordId}/`)
    detail.value = response
  } catch (error) {
    console.error('获取记录详情失败:', error)
    ElMessage.error('获取记录详情失败')
    detail.value = null
  } finally {
    detailLoading.value = false
  }
}

// 选择记录
const handleSelectRecord = (record) => {
  fetchDetail(record.record_id)
}

// 获取运动相关文本（使用 constants.js 中的函数，兼容旧数据）
const getExerciseText = (value) => {
  // 兼容性映射：FAIR -> AVERAGE
  const compatibleValue = value === 'FAIR' ? 'AVERAGE' : value
  return getExerciseAfterEvaluationText(compatibleValue)
}

const createRecord = () => {
  dialogDetail.value = null
  showFormDialog.value = true
}

// 编辑记录
const editRecord = () => {
  if (!detail.value) {
    ElMessage.error('暂无记录可编辑')
    return
  }
  dialogDetail.value = detail.value
  showFormDialog.value = true
}

// 删除记录
const deleteRecord = async () => {
  if (!detail.value?.record_id) {
    ElMessage.error('暂无记录可删除')
    return
  }

  try {
    await ElMessageBox.confirm('确定要删除这条产妇每日生理护理记录吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    deleteLoading.value = true
    await del(`customer-service/mdpc-record/delete/${detail.value.record_id}/`)

    ElMessage.success('记录删除成功')
    detail.value = null
    recordListRef.value?.refresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除记录失败:', error)
      ElMessage.error('删除记录失败，请重试')
    }
  } finally {
    deleteLoading.value = false
  }
}

// 处理表单成功
const handleFormSuccess = (result) => {
  showFormDialog.value = false
  recordListRef.value?.refresh()

  // 如果是编辑模式，刷新当前详情数据
  if (dialogDetail.value && result?.record_id) {
    fetchDetail(result.record_id)
  }
}

// 导出PDF
const exportPDF = async () => {
  if (!detail.value?.record_id) {
    ElMessage.error('暂无记录可导出')
    return
  }

  pdfLoading.value = true
  try {
    const response = await get(`customer-service/mdpc-record/pdf/${detail.value.record_id}/`)

    if (response.pdf_url && response.filename) {
      const fullUrl = response.pdf_url.startsWith('http')
        ? response.pdf_url
        : `${window.location.origin}${response.pdf_url}`

      downloadFile(fullUrl, response.filename)

      const expiresHours = Math.floor((response.expires_in || 3600) / 3600)
      ElMessage.success({
        message: `PDF导出成功！文件链接有效期为${expiresHours}小时`,
        duration: 5000,
      })
    } else {
      throw new Error('PDF文件信息不完整')
    }
  } catch (error) {
    console.error('导出PDF失败:', error)
    if (error.response?.status === 404) {
      ElMessage.error('记录不存在或已被删除')
    } else if (error.response?.status === 403) {
      ElMessage.error('没有权限导出此PDF文件')
    } else {
      ElMessage.error('PDF导出失败，请重试')
    }
  } finally {
    pdfLoading.value = false
  }
}

// 刷新数据
const refresh = () => {
  recordListRef.value?.refresh()
}

// 暴露方法给父组件
defineExpose({
  refresh,
  exportPDF,
})
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
}

.detail-item span {
  color: #374151;
}

.detail-item p {
  color: #6b7280;
  line-height: 1.5;
}

.grid .detail-item {
  flex-direction: row;
  align-items: center;
}

.grid .detail-item label {
  margin-bottom: 0;
  margin-right: 0.5rem;
  min-width: 6rem;
}
</style>
