<template>
  <div class="h-full space-y-4 flex flex-col">
    <!-- 产妇标题 -->
    <div class="flex items-center gap-3 mb-4">
      <CirclePlus class="w-5 h-5 text-pink-500" />
      <span class="text-lg font-semibold text-gray-800">产妇恢复情况</span>
    </div>

    <div v-if="maternalData && maternalData.last_record" class="space-y-4 flex flex-col flex-1">
      <!-- 产妇信息卡片 -->
      <div
        class="maternal-card bg-white rounded-xl p-6 border border-pink-100 shadow-sm transition-all duration-300"
      >
        <!-- 核心指标 -->
        <div class="stat-grid grid grid-cols-3 gap-3 mb-4">
          <!-- 体重 -->
          <div
            class="stat-item bg-gradient-to-br from-pink-50 to-rose-50 rounded-lg p-3 text-center border border-pink-100 shadow-sm"
          >
            <div class="stat-value text-xl font-bold text-pink-600 mb-1">
              {{ maternalData.last_record.weight }}
              <span class="text-sm font-normal">kg</span>
            </div>
            <div class="stat-label text-xs text-gray-500">体重</div>
            <div
              v-if="weightChange !== undefined"
              class="text-xs mt-1"
              :class="weightChange >= 0 ? 'text-green-600' : 'text-red-600'"
            >
              {{ weightChange >= 0 ? '+' : '' }}{{ weightChange }}kg
            </div>
          </div>

          <!-- 体温 -->
          <div
            class="stat-item bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-3 text-center border border-emerald-100 shadow-sm"
          >
            <div class="stat-value text-xl font-bold text-emerald-600 mb-1">
              {{ maternalData.last_record.temperature }}
              <span class="text-sm font-normal">°C</span>
            </div>
            <div class="stat-label text-xs text-gray-500">体温</div>
            <div class="text-xs text-gray-400 mt-1">
              {{ getTemperatureStatus(maternalData.last_record.temperature) }}
            </div>
          </div>

          <!-- 血压 -->
          <div
            class="stat-item bg-gradient-to-br from-red-50 to-rose-50 rounded-lg p-3 text-center border border-red-100 shadow-sm"
          >
            <div class="stat-value text-xl font-bold text-red-600 mb-1">
              {{ maternalData.last_record.blood_pressure }}
            </div>
            <div class="stat-label text-xs text-gray-500">血压</div>
            <div class="text-xs text-gray-400 mt-1">
              {{ getBloodPressureStatus(maternalData.last_record.blood_pressure) }}
            </div>
          </div>
        </div>

        <!-- 记录信息 -->
        <div
          class="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-3 text-center border border-gray-100"
        >
          <div class="text-sm text-gray-600">最后记录时间</div>
          <div class="text-sm font-medium text-pink-600 mt-1">
            {{ maternalData.last_record_date || '暂无记录' }}
          </div>
        </div>
      </div>

      <!-- 趋势图部分 -->
      <div
        v-if="hasTrendData"
        class="trend-chart bg-white rounded-xl p-6 border border-pink-100 shadow-sm flex-1 flex flex-col"
      >
        <!-- 图表切换标签 -->
        <div class="flex items-center justify-end mb-4">
          <div
            class="flex bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-1 border border-gray-100"
          >
            <button
              v-for="(option, key) in trendOptions"
              :key="key"
              @click="selectedTrendType = key"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-all duration-200',
                selectedTrendType === key
                  ? 'bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-pink-500 hover:bg-white',
              ]"
            >
              {{ option.label }}
            </button>
          </div>
        </div>

        <!-- ECharts 图表 -->
        <div
          class="chart-container bg-gradient-to-br from-gray-50 to-slate-50 rounded-lg border border-gray-100 h-full min-h-56"
        >
          <v-chart :option="chartOption" :autoresize="true" style="height: 100%; width: 100%" />
        </div>

        <!-- 时间范围提示 -->
        <div
          class="text-center text-xs text-gray-500 mt-3 py-2 bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg"
        >
          数据时间范围: {{ formatDateRange() }}
        </div>
      </div>

      <!-- 无趋势数据时的提示 -->
      <div v-else class="trend-chart bg-white rounded-xl p-6 border border-pink-100 shadow-sm">
        <div class="text-center py-8">
          <div
            class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center border border-gray-100"
          >
            <TrendCharts class="w-8 h-8 text-gray-400" />
          </div>
          <div class="text-sm font-medium text-gray-600 mb-2">暂无趋势数据</div>
          <div
            class="text-xs text-gray-400 bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg py-2 px-4 inline-block"
          >
            需要至少2条记录才能生成趋势图
          </div>
        </div>
      </div>
    </div>

    <div v-else class="bg-white rounded-xl p-8 border border-pink-100 shadow-sm">
      <div class="text-center py-8">
        <div
          class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center border border-gray-100"
        >
          <CirclePlus class="w-10 h-10 text-gray-400" />
        </div>
        <div class="text-lg font-medium text-gray-600 mb-4">暂无产妇记录数据</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { CirclePlus, TrendCharts } from '@element-plus/icons-vue'
// ECharts imports
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'

// 注册ECharts组件
use([CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

// 默认趋势图类型
const DEFAULT_TREND_TYPE = 'weight_trend'

const props = defineProps({
  aid: {
    type: String,
    default: '',
  },
  maternalData: {
    type: Object,
    default: () => null,
  },
})

// 趋势图相关状态
const selectedTrendType = ref(DEFAULT_TREND_TYPE)

// 趋势图配置选项
const trendOptions = {
  weight_trend: {
    label: '体重',
    unit: 'kg',
    color: '#ec4899',
    yAxisName: '体重 (kg)',
  },
  temperature_trend: {
    label: '体温',
    unit: '°C',
    color: '#10b981',
    yAxisName: '体温 (°C)',
  },
}

// 检查是否有趋势数据
const hasTrendData = computed(() => {
  return (
    props.maternalData?.trend_data &&
    Object.keys(props.maternalData.trend_data).some(
      (key) =>
        key !== 'date_range' &&
        key !== 'total_records' &&
        Array.isArray(props.maternalData.trend_data[key]) &&
        props.maternalData.trend_data[key].length > 1,
    )
  )
})

// 获取当前选中的趋势数据
const getCurrentTrendData = () => {
  if (!props.maternalData?.trend_data) return []
  return props.maternalData.trend_data[selectedTrendType.value] || []
}

// 格式化日期范围
const formatDateRange = () => {
  if (!props.maternalData?.trend_data?.date_range) return ''
  const { start_date, end_date } = props.maternalData.trend_data.date_range
  return `${start_date} 至 ${end_date}`
}

// ECharts 图表配置
const chartOption = computed(() => {
  const data = getCurrentTrendData()
  const config = trendOptions[selectedTrendType.value]

  if (!data.length) return {}

  return {
    title: {
      text: `${config.label}趋势图`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#374151',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: config.color,
      borderWidth: 1,
      textStyle: {
        color: '#374151',
      },
      formatter: (params) => {
        const point = params[0]
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${point.name}</div>
            <div style="color: ${config.color};">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${config.color}; border-radius: 50%; margin-right: 8px;"></span>
              ${config.label}: ${point.value}${config.unit}
            </div>
          </div>
        `
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map((item) => item.display_date),
      axisLine: {
        lineStyle: {
          color: '#e5e7eb',
        },
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
      },
    },
    yAxis: {
      type: 'value',
      name: config.yAxisName,
      nameTextStyle: {
        color: '#6b7280',
        fontSize: 12,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: config.label,
        type: 'line',
        data: data.map((item) => item.value),
        smooth: true,
        lineStyle: {
          color: config.color,
          width: 3,
        },
        itemStyle: {
          color: config.color,
          borderWidth: 2,
          borderColor: '#fff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: `${config.color}20`,
              },
              {
                offset: 1,
                color: `${config.color}05`,
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            borderWidth: 4,
            shadowBlur: 10,
            shadowColor: config.color,
          },
        },
      },
    ],
  }
})

// 获取体温状态
const getTemperatureStatus = (value) => {
  if (!value) return '未记录'
  if (value < 36) return '偏低'
  if (value <= 37.5) return '正常'
  if (value <= 38.5) return '轻度发热'
  return '高热'
}

// 获取血压状态
const getBloodPressureStatus = (value) => {
  if (!value) return '未记录'
  const parts = value.split('/')
  if (parts.length !== 2) return '格式错误'

  const systolic = parseInt(parts[0])
  const diastolic = parseInt(parts[1])

  if (systolic < 90 || diastolic < 60) return '偏低'
  if (systolic <= 120 && diastolic <= 80) return '正常'
  if (systolic <= 140 && diastolic <= 90) return '偏高'
  return '高血压'
}

// 计算体重变化
const weightChange = computed(() => {
  const trendData = props.maternalData?.trend_data?.weight_trend
  if (!trendData || trendData.length < 2) return undefined

  const latest = trendData[trendData.length - 1]?.value
  const previous = trendData[trendData.length - 2]?.value

  if (latest !== undefined && previous !== undefined) {
    return parseFloat((latest - previous).toFixed(1))
  }
  return undefined
})
</script>

<style scoped>
.maternal-card {
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease-out;
}

.maternal-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
  border-color: rgba(236, 72, 153, 0.3);
}

.stat-item {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transition: left 0.6s ease;
}

.stat-item:hover::before {
  left: 100%;
}

.trend-chart {
  transition: all 0.3s ease;
}

.trend-chart:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
  border-color: rgba(236, 72, 153, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 自定义按钮样式，避免使用蓝色 */
.action-buttons :deep(.el-button--primary) {
  --el-color-primary: #ec4899;
  --el-color-primary-light-3: #f472b6;
  --el-color-primary-light-5: #f9a8d4;
  --el-color-primary-light-7: #fbbf24;
  --el-color-primary-light-8: #fde68a;
  --el-color-primary-light-9: #fef3c7;
  --el-color-primary-dark-2: #db2777;
}

.action-buttons :deep(.el-button--success) {
  --el-color-success: #10b981;
  --el-color-success-light-3: #34d399;
  --el-color-success-light-5: #6ee7b7;
  --el-color-success-light-7: #a7f3d0;
  --el-color-success-light-8: #d1fae5;
  --el-color-success-light-9: #ecfdf5;
  --el-color-success-dark-2: #059669;
}

/* 趋势图样式 */
.chart-container {
  padding: 12px;
  transition: all 0.3s ease;
}

.chart-container:hover {
  background: linear-gradient(to bottom right, #f8fafc, #f1f5f9) !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .stat-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .chart-container {
    height: 250px !important;
  }
}
</style>
