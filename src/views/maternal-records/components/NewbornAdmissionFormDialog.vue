<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="1200px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="160px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="入住时间" prop="admission_time">
              <el-date-picker
                v-model="form.admission_time"
                type="datetime"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="评估时间" prop="assessment_time">
              <el-date-picker
                v-model="form.assessment_time"
                type="datetime"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="血型" prop="blood_type">
              <el-select v-model="form.blood_type" placeholder="选择血型">
                <el-option
                  v-for="option in BLOOD_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="Apgar评分" prop="apgar_score">
              <el-input-number v-model="form.apgar_score" :min="0" :max="10" class="w-full" />
            </el-form-item>
          </div>
        </div>

        <!-- 病史与用药情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">病史与用药情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="有无出院小结">
              <el-switch v-model="form.discharge_summary" />
            </el-form-item>
            <el-form-item label="住院治疗史">
              <el-switch v-model="form.hospitalization_history" />
            </el-form-item>
            <el-form-item label="现在是否用药">
              <el-switch v-model="form.current_medication" />
            </el-form-item>
          </div>
          <div class="space-y-4">
            <el-form-item
              label="疾病名称"
              prop="hospitalization_disease"
              v-if="form.hospitalization_history"
            >
              <el-input v-model="form.hospitalization_disease" placeholder="请输入疾病名称" />
            </el-form-item>
            <el-form-item label="药物名称" prop="medication_name" v-if="form.current_medication">
              <el-input v-model="form.medication_name" placeholder="请输入药物名称" />
            </el-form-item>
            <el-form-item label="过敏史" prop="allergy_history">
              <el-input
                v-model="form.allergy_history"
                type="textarea"
                :rows="2"
                placeholder="请详细描述过敏史"
              />
            </el-form-item>
            <el-form-item label="新生儿特殊情况" prop="special_conditions">
              <el-input
                v-model="form.special_conditions"
                type="textarea"
                :rows="2"
                placeholder="请描述新生儿特殊情况"
              />
            </el-form-item>
            <el-form-item label="其他特殊情况" prop="other_special_conditions">
              <el-input
                v-model="form.other_special_conditions"
                type="textarea"
                :rows="2"
                placeholder="其他需要注意的情况"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 黄疸监测 -->
        <div class="form-section mb-6">
          <h4 class="section-title">黄疸监测</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="出院黄疸指数 (mg/dL)" prop="discharge_jaundice">
              <el-input-number
                v-model="form.discharge_jaundice"
                :precision="1"
                :min="0"
                :max="50"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="入住黄疸指数 (mg/dL)" prop="admission_jaundice">
              <el-input-number
                v-model="form.admission_jaundice"
                :precision="1"
                :min="0"
                :max="50"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="测量部位" prop="location">
              <el-select v-model="form.location" placeholder="选择测量部位">
                <el-option
                  v-for="option in LOCATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="是否需要光疗">
              <el-switch v-model="form.need_phototherapy" />
            </el-form-item>
            <el-form-item label="巩膜黄染">
              <el-switch v-model="form.scleral_jaundice" />
            </el-form-item>
          </div>
        </div>

        <!-- 喂养情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">喂养情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="喂养方式" prop="feeding_type">
              <el-select v-model="form.feeding_type" placeholder="选择喂养方式">
                <el-option
                  v-for="option in FEEDING_METHOD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="奶粉品牌"
              prop="formula_brand"
              v-if="form.feeding_type !== 'BREAST_FEEDING'"
            >
              <el-input v-model="form.formula_brand" placeholder="请输入奶粉品牌" />
            </el-form-item>
            <el-form-item
              label="配方奶与母乳量比例"
              prop="formula_breastmilk_ratio"
              v-if="form.feeding_type === 'MIXED_FEEDING'"
            >
              <el-input v-model="form.formula_breastmilk_ratio" placeholder="如：1:1" />
            </el-form-item>
            <el-form-item label="吸吮情况" prop="sucking_ability">
              <el-select v-model="form.sucking_ability" placeholder="选择吸吮情况">
                <el-option
                  v-for="option in SUCKING_ABILITY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 排泄情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">排泄情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="排尿情况" prop="urination_status">
              <el-select v-model="form.urination_status" placeholder="选择排尿情况">
                <el-option
                  v-for="option in NEWBORN_URINATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="排尿次数/天" prop="normal_urination_times">
              <el-input-number v-model="form.normal_urination_times" :min="0" class="w-full" />
            </el-form-item>
            <el-form-item label="惯用尿布品牌" prop="usual_diaper_brand">
              <el-input v-model="form.usual_diaper_brand" placeholder="请输入尿布品牌" />
            </el-form-item>
            <el-form-item label="排便情况" prop="bowel_movement_status">
              <el-select v-model="form.bowel_movement_status" placeholder="选择排便情况">
                <el-option
                  v-for="option in NEWBORN_BOWEL_MOVEMENT_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="排便次数/天" prop="bowel_times">
              <el-input-number v-model="form.bowel_times" :min="0" class="w-full" />
            </el-form-item>
            <el-form-item label="臀部情况" prop="buttocks">
              <el-select v-model="form.buttocks" placeholder="选择臀部情况">
                <el-option
                  v-for="option in BUTTOCKS_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item
            label="臀部异常情况"
            prop="buttocks_abnormality"
            v-if="form.buttocks === 'ABNORMAL'"
          >
            <el-select v-model="form.buttocks_abnormality" placeholder="选择异常情况">
              <el-option
                v-for="option in BUTTOCKS_ABNORMALITY_OPTIONS"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </div>

        <!-- 生命体征 -->
        <div class="form-section mb-6">
          <h4 class="section-title">生命体征</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="体温 (°C)" prop="temperature">
              <el-input-number
                v-model="form.temperature"
                :precision="1"
                :min="35"
                :max="42"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="心率 (次/分)" prop="heart_rate">
              <el-input-number v-model="form.heart_rate" :min="50" :max="200" class="w-full" />
            </el-form-item>
            <el-form-item label="呼吸 (次/分)" prop="respiration">
              <el-input-number v-model="form.respiration" :min="12" :max="80" class="w-full" />
            </el-form-item>
            <el-form-item label="体重 (g)" prop="weight">
              <el-input-number
                v-model="form.weight"
                :precision="1"
                :min="500"
                :max="6000"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="血氧 (%)" prop="spo2">
              <el-input-number
                v-model="form.spo2"
                :precision="1"
                :min="85"
                :max="100"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 体格检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">体格检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="面色" prop="complexion">
              <el-select v-model="form.complexion" placeholder="选择面色">
                <el-option
                  v-for="option in COMPLEXION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="哭声" prop="cry">
              <el-select v-model="form.cry" placeholder="选择哭声">
                <el-option
                  v-for="option in CRY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="反应" prop="reaction">
              <el-select v-model="form.reaction" placeholder="选择反应">
                <el-option
                  v-for="option in REACTION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="四肢" prop="extremities">
              <el-select v-model="form.extremities" placeholder="选择四肢状况">
                <el-option
                  v-for="option in EXTREMITIES_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="四肢张力及活动" prop="extremity_tone">
              <el-select v-model="form.extremity_tone" placeholder="选择张力状况">
                <el-option
                  v-for="option in EXTREMITY_TONE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="产伤">
              <el-switch v-model="form.birth_injury" />
            </el-form-item>
          </div>
          <el-form-item
            label="受限部位"
            prop="extremity_restricted_area"
            v-if="form.extremity_tone === 'LIMITED'"
          >
            <el-input v-model="form.extremity_restricted_area" placeholder="请描述受限部位" />
          </el-form-item>
          <div v-if="form.birth_injury" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="产伤类型" prop="birth_injury_type">
              <el-select v-model="form.birth_injury_type" placeholder="选择产伤类型">
                <el-option
                  v-for="option in BIRTH_INJURY_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="皮损部位"
              prop="birth_injury_skin_lesion_location"
              v-if="form.birth_injury_type === 'SKIN_LESIONS'"
            >
              <el-input
                v-model="form.birth_injury_skin_lesion_location"
                placeholder="请描述皮损部位"
              />
            </el-form-item>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="心脏杂音">
              <el-switch v-model="form.heart_murmur" />
            </el-form-item>
            <el-form-item label="神经反射" prop="reflexes">
              <el-select v-model="form.reflexes" placeholder="选择神经反射">
                <el-option
                  v-for="option in REFLEXES_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="营养发育" prop="nutrition_development">
              <el-select v-model="form.nutrition_development" placeholder="选择营养发育">
                <el-option
                  v-for="option in NUTRITION_DEVELOPMENT_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item
            label="发育滞后百分位数"
            prop="nutrition_delayed_percentile"
            v-if="form.nutrition_development === 'DELAYED'"
          >
            <el-input-number
              v-model="form.nutrition_delayed_percentile"
              :min="0"
              :max="100"
              class="w-full"
            />
          </el-form-item>
        </div>

        <!-- 皮肤状况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">皮肤状况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="全身皮肤" prop="skin_status">
              <el-select v-model="form.skin_status" placeholder="选择皮肤状况">
                <el-option
                  v-for="option in SKIN_STATUS_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="皮肤异常"
              prop="skin_abnormality"
              v-if="form.skin_status === 'ABNORMAL'"
            >
              <el-select v-model="form.skin_abnormality" placeholder="选择异常情况">
                <el-option
                  v-for="option in SKIN_ABNORMALITY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div v-if="form.skin_status === 'ABNORMAL'" class="space-y-4">
            <el-form-item
              label="皮损部位"
              prop="skin_lesion_location"
              v-if="form.skin_abnormality === 'LESION'"
            >
              <el-input v-model="form.skin_lesion_location" placeholder="请描述皮损部位" />
            </el-form-item>
            <el-form-item
              label="红斑部位"
              prop="skin_erythema_location"
              v-if="form.skin_abnormality === 'ERYTHEMA'"
            >
              <el-input v-model="form.skin_erythema_location" placeholder="请描述红斑部位" />
            </el-form-item>
            <el-form-item
              label="皮疹部位"
              prop="skin_rash_location"
              v-if="form.skin_abnormality === 'RASH'"
            >
              <el-input v-model="form.skin_rash_location" placeholder="请描述皮疹部位" />
            </el-form-item>
            <el-form-item
              label="水肿部位"
              prop="skin_edema_location"
              v-if="form.skin_abnormality === 'EDEMA'"
            >
              <el-input v-model="form.skin_edema_location" placeholder="请描述水肿部位" />
            </el-form-item>
            <el-form-item
              label="其他部位"
              prop="skin_other_location"
              v-if="form.skin_abnormality === 'OTHER'"
            >
              <el-input v-model="form.skin_other_location" placeholder="请描述其他情况" />
            </el-form-item>
          </div>
        </div>

        <!-- 头部检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">头部检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="头围 (cm)" prop="head_circumference">
              <el-input-number
                v-model="form.head_circumference"
                :precision="1"
                :min="25"
                :max="45"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="前囟" prop="anterior_fontanelle">
              <el-select v-model="form.anterior_fontanelle" placeholder="选择前囟状况">
                <el-option
                  v-for="option in ANTERIOR_FONTANELLE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="前囟大小" prop="fontanelle_size">
              <el-input v-model="form.fontanelle_size" placeholder="如：2cm×2cm" />
            </el-form-item>
            <el-form-item label="口腔黏膜" prop="oral_mucosa">
              <el-select v-model="form.oral_mucosa" placeholder="选择口腔黏膜">
                <el-option
                  v-for="option in ORAL_MUCOSA_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="口腔黏膜异常"
              prop="oral_mucosa_abnormality"
              v-if="form.oral_mucosa === 'ABNORMAL'"
            >
              <el-select v-model="form.oral_mucosa_abnormality" placeholder="选择异常情况">
                <el-option
                  v-for="option in ORAL_MUCOSA_ABNORMALITY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="眼结膜充血和分泌物">
              <el-switch v-model="form.conjunctiva_status" />
            </el-form-item>
          </div>
        </div>

        <!-- 畸形与特殊检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">畸形与特殊检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="先天性畸形">
              <el-switch v-model="form.congenital_anomaly" />
            </el-form-item>
            <el-form-item label="假性月经">
              <el-switch v-model="form.pseudomenstruation" />
            </el-form-item>
          </div>
          <div v-if="form.congenital_anomaly" class="space-y-4">
            <el-form-item label="畸形类型" prop="anomaly_type">
              <el-select v-model="form.anomaly_type" placeholder="选择畸形类型">
                <el-option
                  v-for="option in ANOMALY_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="其他畸形"
              prop="anomaly_type_other"
              v-if="form.anomaly_type === 'OTHER'"
            >
              <el-input v-model="form.anomaly_type_other" placeholder="请描述其他畸形情况" />
            </el-form-item>
          </div>
        </div>

        <!-- 脐部情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">脐部情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="脐部" prop="umbilical_cord">
              <el-select v-model="form.umbilical_cord" placeholder="选择脐部情况">
                <el-option
                  v-for="option in UMBILICAL_CORD_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="脐部异常"
              prop="umbilical_cord_abnormality"
              v-if="form.umbilical_cord === 'ABNORMAL'"
            >
              <el-select v-model="form.umbilical_cord_abnormality" placeholder="选择异常情况">
                <el-option
                  v-for="option in UMBILICAL_CORD_ABNORMALITY_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="轮脐红肿">
              <el-switch v-model="form.umbilical_cord_red_and_swollen" />
            </el-form-item>
            <el-form-item label="脐部脱落">
              <el-switch v-model="form.umbilical_cord_fall_off" />
            </el-form-item>
          </div>
        </div>

        <!-- 预防接种 -->
        <div class="form-section mb-6">
          <h4 class="section-title">预防接种</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="预防接种" prop="vaccine_injection">
              <el-select v-model="form.vaccine_injection" placeholder="选择接种疫苗">
                <el-option
                  v-for="option in VACCINE_INJECTION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="其他接种"
              prop="vaccine_injection_other"
              v-if="form.vaccine_injection === 'OTHER'"
            >
              <el-input v-model="form.vaccine_injection_other" placeholder="请输入其他接种疫苗" />
            </el-form-item>
          </div>
        </div>

        <!-- 护理与签名 -->
        <div class="form-section mb-6">
          <h4 class="section-title">护理与签名</h4>
          <div class="space-y-4">
            <el-form-item label="婴儿护理要点" prop="baby_care_points">
              <el-input
                v-model="form.baby_care_points"
                type="textarea"
                :rows="3"
                placeholder="请填写婴儿护理要点"
              />
            </el-form-item>
            <el-form-item label="签名" prop="signature">
              <el-input v-model="form.signature" placeholder="评估人员签名" />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '保存修改' : '提交评估' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { processDateFields, getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import {
  BLOOD_TYPE_OPTIONS,
  LOCATION_OPTIONS,
  FEEDING_METHOD_OPTIONS,
  SUCKING_ABILITY_OPTIONS,
  NEWBORN_URINATION_OPTIONS,
  NEWBORN_BOWEL_MOVEMENT_OPTIONS,
  BUTTOCKS_OPTIONS,
  BUTTOCKS_ABNORMALITY_OPTIONS,
  COMPLEXION_OPTIONS,
  CRY_OPTIONS,
  REACTION_OPTIONS,
  EXTREMITIES_OPTIONS,
  EXTREMITY_TONE_OPTIONS,
  BIRTH_INJURY_TYPE_OPTIONS,
  REFLEXES_OPTIONS,
  NUTRITION_DEVELOPMENT_OPTIONS,
  SKIN_STATUS_OPTIONS,
  SKIN_ABNORMALITY_OPTIONS,
  ANTERIOR_FONTANELLE_OPTIONS,
  ORAL_MUCOSA_OPTIONS,
  ORAL_MUCOSA_ABNORMALITY_OPTIONS,
  ANOMALY_TYPE_OPTIONS,
  UMBILICAL_CORD_OPTIONS,
  UMBILICAL_CORD_ABNORMALITY_OPTIONS,
  VACCINE_INJECTION_OPTIONS,
} from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils.js'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  detail: { type: Object, default: null },
  babyId: { type: [String, Number], default: null },
})

const emit = defineEmits(['update:modelValue', 'success'])

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const isEdit = computed(() => !!props.detail)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑新生儿入住评估' : '新增新生儿入住评估'
})

// 使用导入的选项数组

// 日期字段定义
const DATE_FIELDS = ['admission_time', 'assessment_time']

const form = reactive({
  admission_time: '',
  assessment_time: '',
  blood_type: '',
  apgar_score: null,
  discharge_summary: false,
  hospitalization_history: false,
  hospitalization_disease: '',
  current_medication: false,
  medication_name: '',
  allergy_history: '',
  special_conditions: '',
  discharge_jaundice: null,
  feeding_type: '',
  formula_brand: '',
  formula_breastmilk_ratio: '',
  urination_status: '',
  normal_urination_times: null,
  usual_diaper_brand: '',
  bowel_movement_status: '',
  bowel_times: null,
  temperature: null,
  heart_rate: null,
  respiration: null,
  weight: null,
  spo2: null,
  complexion: '',
  cry: '',
  reaction: '',
  extremities: '',
  extremity_tone: '',
  extremity_restricted_area: '',
  birth_injury: false,
  birth_injury_type: '',
  birth_injury_skin_lesion_location: '',
  heart_murmur: false,
  reflexes: '',
  nutrition_development: '',
  nutrition_delayed_percentile: null,
  skin_status: '',
  skin_abnormality: '',
  skin_lesion_location: '',
  skin_erythema_location: '',
  skin_rash_location: '',
  skin_edema_location: '',
  skin_other_location: '',
  head_circumference: null,
  anterior_fontanelle: '',
  fontanelle_size: '',
  oral_mucosa: '',
  oral_mucosa_abnormality: '',
  conjunctiva_status: false,
  scleral_jaundice: false,
  congenital_anomaly: false,
  anomaly_type: '',
  anomaly_type_other: '',
  umbilical_cord: '',
  umbilical_cord_abnormality: '',
  umbilical_cord_red_and_swollen: false,
  umbilical_cord_fall_off: false,
  sucking_ability: '',
  buttocks: '',
  buttocks_abnormality: '',
  pseudomenstruation: false,
  vaccine_injection: '',
  vaccine_injection_other: '',
  admission_jaundice: null,
  location: '',
  need_phototherapy: false,
  other_special_conditions: '',
  baby_care_points: '',
  signature: '',
})

const rules = computed(() => ({
  admission_time: [{ required: true, message: '请选择入住时间', trigger: 'change' }],
  assessment_time: [{ required: true, message: '请选择评估时间', trigger: 'change' }],
  apgar_score: [{ required: true, message: '请填写Apgar评分', trigger: 'blur' }],
  temperature: [{ required: true, message: '请填写体温', trigger: 'blur' }],
  heart_rate: [{ required: true, message: '请填写心率', trigger: 'blur' }],
  respiration: [{ required: true, message: '请填写呼吸频率', trigger: 'blur' }],
  weight: [{ required: true, message: '请填写体重', trigger: 'blur' }],
  signature: [{ required: true, message: '请填写签名', trigger: 'blur' }],
}))

const { scrollToTop } = useDialogScrollToTop()

// 监听父级字段变化，清空相关子字段
watch(
  () => form.hospitalization_history,
  (newValue) => {
    if (!newValue) {
      form.hospitalization_disease = ''
    }
  },
)

watch(
  () => form.current_medication,
  (newValue) => {
    if (!newValue) {
      form.medication_name = ''
    }
  },
)

watch(
  () => form.feeding_type,
  (newValue) => {
    // 当喂养方式改为纯母乳时，清空奶粉品牌
    if (newValue === 'BREAST_FEEDING') {
      form.formula_brand = ''
    }
    // 当喂养方式不是混合喂养时，清空比例
    if (newValue !== 'MIXED_FEEDING') {
      form.formula_breastmilk_ratio = ''
    }
  },
)

watch(
  () => form.buttocks,
  (newValue) => {
    if (newValue !== 'ABNORMAL') {
      form.buttocks_abnormality = ''
    }
  },
)

watch(
  () => form.extremity_tone,
  (newValue) => {
    if (newValue !== 'LIMITED') {
      form.extremity_restricted_area = ''
    }
  },
)

watch(
  () => form.birth_injury,
  (newValue) => {
    if (!newValue) {
      form.birth_injury_type = ''
      form.birth_injury_skin_lesion_location = ''
    }
  },
)

watch(
  () => form.birth_injury_type,
  (newValue) => {
    if (newValue !== 'SKIN_LESIONS') {
      form.birth_injury_skin_lesion_location = ''
    }
  },
)

watch(
  () => form.nutrition_development,
  (newValue) => {
    if (newValue !== 'DELAYED') {
      form.nutrition_delayed_percentile = null
    }
  },
)

watch(
  () => form.skin_status,
  (newValue) => {
    if (newValue !== 'ABNORMAL') {
      form.skin_abnormality = ''
      form.skin_lesion_location = ''
      form.skin_erythema_location = ''
      form.skin_rash_location = ''
      form.skin_edema_location = ''
      form.skin_other_location = ''
    }
  },
)

watch(
  () => form.skin_abnormality,
  () => {
    // 清空所有位置字段，只保留当前选择的类型对应的字段
    form.skin_lesion_location = ''
    form.skin_erythema_location = ''
    form.skin_rash_location = ''
    form.skin_edema_location = ''
    form.skin_other_location = ''
  },
)

watch(
  () => form.oral_mucosa,
  (newValue) => {
    if (newValue !== 'ABNORMAL') {
      form.oral_mucosa_abnormality = ''
    }
  },
)

watch(
  () => form.congenital_anomaly,
  (newValue) => {
    if (!newValue) {
      form.anomaly_type = ''
      form.anomaly_type_other = ''
    }
  },
)

watch(
  () => form.anomaly_type,
  (newValue) => {
    if (newValue !== 'OTHER') {
      form.anomaly_type_other = ''
    }
  },
)

watch(
  () => form.umbilical_cord,
  (newValue) => {
    if (newValue !== 'ABNORMAL') {
      form.umbilical_cord_abnormality = ''
    }
  },
)

watch(
  () => form.vaccine_injection,
  (newValue) => {
    if (newValue !== 'OTHER') {
      form.vaccine_injection_other = ''
    }
  },
)

watch(
  () => props.modelValue,
  async (visible) => {
    scrollToTop()
    if (visible) {
      // 设置 currentItemId

      if (props.detail) {
        // 使用传入的详情数据
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)

        // 处理日期字段
        const processedData = processDateFields(props.detail, 'toInput', DATE_FIELDS)
        Object.assign(form, processedData)
        console.log('Form data after assignment:', { ...form })
      }
      //  else if (currentItemId.value) {
      //   resetForm()
      //   await fetchAdmissionDetail(currentItemId.value)
      // }
      else {
        // 初始化默认值
        resetForm()
        await nextTick()
        const now = getCurrentTime('input')
        form.admission_time = now
        form.assessment_time = now
      }
    }
  },
)

// const fetchAdmissionDetail = async (admissionId) => {
//   if (!admissionId) return
//   loading.value = true
//   try {
//     const response = await get(
//       `customer-service/newborn-check-in-assessment/detail/${admissionId}/`,
//     )
//     if (response) {
//       // 处理日期字段
//       const processedData = processDateFields(response, 'toInput', DATE_FIELDS)
//       Object.assign(form, processedData)
//       console.log('Form data from API:', { ...form })
//     }
//   } catch (error) {
//     console.error('获取详情失败:', error)
//     ElMessage.error('获取详情失败')
//   } finally {
//     loading.value = false
//   }
// }

const handleClose = () => {
  visible.value = false
}

const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (
      [
        'discharge_summary',
        'hospitalization_history',
        'current_medication',
        'birth_injury',
        'heart_murmur',
        'conjunctiva_status',
        'scleral_jaundice',
        'congenital_anomaly',
        'umbilical_cord_red_and_swollen',
        'umbilical_cord_fall_off',
        'pseudomenstruation',
        'need_phototherapy',
      ].includes(key)
    ) {
      form[key] = false
    } else if (
      [
        'apgar_score',
        'discharge_jaundice',
        'normal_urination_times',
        'bowel_times',
        'temperature',
        'heart_rate',
        'respiration',
        'weight',
        'spo2',
        'nutrition_delayed_percentile',
        'head_circumference',
        'admission_jaundice',
      ].includes(key)
    ) {
      form[key] = null
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 处理提交数据的日期格式
    const submitData = processDateFields(form, 'toAPI', DATE_FIELDS)
    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      // 验证babyId
      if (!props.babyId) {
        ElMessage.error('未指定宝宝ID，无法提交记录')
        return
      }
      res = await post(
        `customer-service/newborn-check-in-assessment/create/${props.babyId}/`,
        submitData,
      )
    } else {
      res = await put(
        `customer-service/newborn-check-in-assessment/update/${props.babyId}/`,
        submitData,
      )
    }

    ElMessage.success(isEdit.value ? '新生儿入住评估更新成功' : '新生儿入住评估提交成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
