<template>
  <div class="newborn-admission-detail">
    <div class="detail-header mb-6 flex justify-between items-center">
      <div class="flex-1">
        <div class="flex items-center gap-4 mb-2">
          <h3 class="text-lg font-semibold text-gray-800">新生儿入住评估记录</h3>
          <!-- Baby切换Tab -->
          <div v-if="props.babyList?.length > 1" class="flex bg-gray-100 rounded-lg p-1">
            <button
              v-for="baby in props.babyList"
              :key="baby.baby_info.nid"
              @click="handleBabyChange(baby.baby_info)"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-all cursor-pointer',
                currentBaby?.nid === baby.baby_info.nid
                  ? 'bg-white text-pink-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800',
              ]"
            >
              {{ baby.baby_info.name || `宝宝${baby.baby_info.nid}` }}
            </button>
          </div>
        </div>
        <p class="text-sm text-gray-600">
          查看新生儿入住时的健康状况和基本信息
          <span v-if="babyList.length > 1 && currentBaby" class="ml-2 text-pink-600">
            当前：{{ currentBaby.name || `宝宝${currentBaby.nid}` }}
          </span>
        </p>
      </div>
      <div class="flex gap-2">
        <el-button
          v-if="detailData"
          type="primary"
          @click="editRecord"
          :disabled="!currentBaby"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          更新记录
        </el-button>
        <el-button
          v-if="detailData"
          :disabled="true"
          @click="exportPDF"
          :loading="pdfLoading"
          class="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
        >
          <template #icon>
            <Download />
          </template>
          导出PDF
        </el-button>
      </div>
    </div>

    <!-- 无Baby提示 -->
    <div v-if="!currentBaby" class="text-center py-8">
      <el-empty description="暂无宝宝信息，无法查看入住评估记录" />
    </div>

    <div v-else v-loading="loading" class="detail-content">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>入住时间：</label>
              <span>{{ formatDateTime(detailData.admission_time) }}</span>
            </div>
            <div class="detail-item">
              <label>评估时间：</label>
              <span>{{ formatDateTime(detailData.assessment_time) }}</span>
            </div>
            <div class="detail-item">
              <label>血型：</label>
              <span>{{ getBloodTypeText(detailData.blood_type) }}</span>
            </div>
            <div class="detail-item">
              <label>Apgar评分：</label>
              <span>{{ formatNumber(detailData.apgar_score) }}</span>
            </div>
            <div class="detail-item">
              <label>有无出院小结：</label>
              <span>{{ formatBoolean(detailData.discharge_summary) }}</span>
            </div>
            <div class="detail-item">
              <label>现在是否用药：</label>
              <span>{{ formatBoolean(detailData.current_medication) }}</span>
            </div>
          </div>
          <div v-if="detailData.medication_name" class="space-y-4 mt-4">
            <div class="detail-item">
              <label>药物名称：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.medication_name) }}</p>
            </div>
          </div>
        </div>

        <!-- 病史与特殊情况 -->
        <div class="detail-section">
          <h3 class="section-title">病史与特殊情况</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>住院治疗史：</label>
              <span>{{ formatBoolean(detailData.hospitalization_history) }}</span>
            </div>
            <div v-if="detailData.hospitalization_history" class="detail-item">
              <label>疾病名称：</label>
              <span>{{ formatText(detailData.hospitalization_disease) }}</span>
            </div>
          </div>
          <div class="space-y-4 mt-4">
            <div class="detail-item">
              <label>过敏史：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.allergy_history) }}</p>
            </div>
            <div class="detail-item">
              <label>新生儿特殊情况：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.special_conditions) }}</p>
            </div>
            <div class="detail-item">
              <label>其他特殊情况：</label>
              <p class="mt-1 text-gray-600">
                {{ formatText(detailData.other_special_conditions) }}
              </p>
            </div>
          </div>
        </div>

        <!-- 黄疸指数 -->
        <div class="detail-section">
          <h3 class="section-title">黄疸指数</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="detail-item">
              <label>出院黄疸指数：</label>
              <span>{{ formatNumber(detailData.discharge_jaundice, 'mg/dL') }}</span>
            </div>
            <div class="detail-item">
              <label>入住黄疸指数：</label>
              <span>{{ formatNumber(detailData.admission_jaundice, 'mg/dL') }}</span>
            </div>
            <div class="detail-item">
              <label>测量部位：</label>
              <span>{{ getLocationText(detailData.location) }}</span>
            </div>
            <div class="detail-item">
              <label>是否需要光疗：</label>
              <span>{{ formatBoolean(detailData.need_phototherapy) }}</span>
            </div>
            <div class="detail-item">
              <label>巩膜黄染：</label>
              <span>{{ formatBoolean(detailData.scleral_jaundice) }}</span>
            </div>
          </div>
        </div>

        <!-- 喂养情况 -->
        <div class="detail-section">
          <h3 class="section-title">喂养情况</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>喂养方式：</label>
              <span>{{ getFeedingMethodText(detailData.feeding_type) }}</span>
            </div>
            <div v-if="detailData.formula_brand" class="detail-item">
              <label>奶粉品牌：</label>
              <span>{{ formatText(detailData.formula_brand) }}</span>
            </div>
            <div v-if="detailData.formula_breastmilk_ratio" class="detail-item">
              <label>母乳与配方奶比例：</label>
              <span>{{ formatText(detailData.formula_breastmilk_ratio) }}</span>
            </div>
            <div class="detail-item">
              <label>吸吮情况：</label>
              <span>{{ getSuckingAbilityText(detailData.sucking_ability) }}</span>
            </div>
          </div>
        </div>

        <!-- 排泄情况 -->
        <div class="detail-section">
          <h3 class="section-title">排泄情况</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="detail-item">
              <label>排尿情况：</label>
              <span>{{ getNewbornUrinationText(detailData.urination_status) }}</span>
            </div>
            <div class="detail-item">
              <label>排尿次数：</label>
              <span>{{ formatNumber(detailData.normal_urination_times) }}次/日</span>
            </div>
            <div class="detail-item">
              <label>惯用尿布品牌：</label>
              <span>{{ formatText(detailData.usual_diaper_brand) }}</span>
            </div>
            <div class="detail-item">
              <label>排便情况：</label>
              <span>{{ getNewbornBowelMovementText(detailData.bowel_movement_status) }}</span>
            </div>
            <div class="detail-item">
              <label>排便次数：</label>
              <span>{{ formatNumber(detailData.bowel_times) }}次/日</span>
            </div>
            <div class="detail-item">
              <label>臀部情况：</label>
              <span>{{ getButtocksText(detailData.buttocks) }}</span>
            </div>
          </div>
          <div
            v-if="detailData.buttocks === 'ABNORMAL' && detailData.buttocks_abnormality"
            class="mt-4"
          >
            <div class="detail-item">
              <label>臀部异常情况：</label>
              <span>{{ getButtocksAbnormalityText(detailData.buttocks_abnormality) }}</span>
            </div>
          </div>
        </div>

        <!-- 生命体征 -->
        <div class="detail-section">
          <h3 class="section-title">生命体征</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="detail-item">
              <label>体温 (°C)：</label>
              <span>{{ formatNumber(detailData.temperature) }}</span>
            </div>
            <div class="detail-item">
              <label>心率 (次/分)：</label>
              <span>{{ formatNumber(detailData.heart_rate) }}</span>
            </div>
            <div class="detail-item">
              <label>呼吸 (次/分)：</label>
              <span>{{ formatNumber(detailData.respiration) }}</span>
            </div>
            <div class="detail-item">
              <label>体重 (g)：</label>
              <span>{{ formatNumber(detailData.weight, 'g') }}</span>
            </div>
            <div class="detail-item">
              <label>血氧 (%)：</label>
              <span>{{ formatNumber(detailData.spo2, '%') }}</span>
            </div>
          </div>
        </div>

        <!-- 体格检查 -->
        <div class="detail-section">
          <h3 class="section-title">体格检查</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="detail-item">
              <label>面色：</label>
              <span>{{ getComplexionText(detailData.complexion) }}</span>
            </div>
            <div class="detail-item">
              <label>哭声：</label>
              <span>{{ getCryText(detailData.cry) }}</span>
            </div>
            <div class="detail-item">
              <label>反应：</label>
              <span>{{ getReactionText(detailData.reaction) }}</span>
            </div>
            <div class="detail-item">
              <label>四肢：</label>
              <span>{{ getExtremitiesText(detailData.extremities) }}</span>
            </div>
            <div class="detail-item">
              <label>四肢张力及活动：</label>
              <span>{{ getExtremityToneText(detailData.extremity_tone) }}</span>
            </div>
            <div v-if="detailData.extremity_tone === 'LIMITED'" class="detail-item">
              <label>受限部位：</label>
              <span>{{ formatText(detailData.extremity_restricted_area) }}</span>
            </div>
            <div class="detail-item">
              <label>产伤：</label>
              <span>{{ formatBoolean(detailData.birth_injury) }}</span>
            </div>
            <div v-if="detailData.birth_injury" class="detail-item">
              <label>产伤类型：</label>
              <span>{{ getBirthInjuryTypeText(detailData.birth_injury_type) }}</span>
            </div>
            <div v-if="detailData.birth_injury_skin_lesion_location" class="detail-item">
              <label>皮损部位：</label>
              <span>{{ formatText(detailData.birth_injury_skin_lesion_location) }}</span>
            </div>
            <div class="detail-item">
              <label>心脏杂音：</label>
              <span>{{ formatBoolean(detailData.heart_murmur) }}</span>
            </div>
            <div class="detail-item">
              <label>神经反射：</label>
              <span>{{ getReflexesText(detailData.reflexes) }}</span>
            </div>
            <div class="detail-item">
              <label>营养发育：</label>
              <span>{{ getNutritionDevelopmentText(detailData.nutrition_development) }}</span>
            </div>
            <div v-if="detailData.nutrition_delayed_percentile" class="detail-item">
              <label>发育滞后百分位数：</label>
              <span>{{ formatNumber(detailData.nutrition_delayed_percentile) }}%</span>
            </div>
          </div>
        </div>

        <!-- 皮肤状况 -->
        <div class="detail-section">
          <h3 class="section-title">皮肤状况</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>全身皮肤：</label>
              <span>{{ getSkinStatusText(detailData.skin_status) }}</span>
            </div>
            <div v-if="detailData.skin_status === 'ABNORMAL'" class="detail-item">
              <label>皮肤异常：</label>
              <span>{{ getSkinAbnormalityText(detailData.skin_abnormality) }}</span>
            </div>
          </div>
          <div v-if="detailData.skin_status === 'ABNORMAL'" class="space-y-4 mt-4">
            <div v-if="detailData.skin_lesion_location" class="detail-item">
              <label>皮损部位：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.skin_lesion_location) }}</p>
            </div>
            <div v-if="detailData.skin_erythema_location" class="detail-item">
              <label>红斑部位：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.skin_erythema_location) }}</p>
            </div>
            <div v-if="detailData.skin_rash_location" class="detail-item">
              <label>皮疹部位：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.skin_rash_location) }}</p>
            </div>
            <div v-if="detailData.skin_edema_location" class="detail-item">
              <label>水肿部位：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.skin_edema_location) }}</p>
            </div>
            <div v-if="detailData.skin_other_location" class="detail-item">
              <label>其他部位：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.skin_other_location) }}</p>
            </div>
          </div>
        </div>

        <!-- 头部检查 -->
        <div class="detail-section">
          <h3 class="section-title">头部检查</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="detail-item">
              <label>头围 (cm)：</label>
              <span>{{ formatNumber(detailData.head_circumference) }}</span>
            </div>
            <div class="detail-item">
              <label>前囟：</label>
              <span>{{ getAnteriorFontanelleText(detailData.anterior_fontanelle) }}</span>
            </div>
            <div class="detail-item">
              <label>前囟大小：</label>
              <span>{{ formatText(detailData.fontanelle_size) }}</span>
            </div>
            <div class="detail-item">
              <label>口腔黏膜：</label>
              <span>{{ getOralMucosaText(detailData.oral_mucosa) }}</span>
            </div>
            <div v-if="detailData.oral_mucosa === 'ABNORMAL'" class="detail-item">
              <label>口腔黏膜异常：</label>
              <span>{{ getOralMucosaAbnormalityText(detailData.oral_mucosa_abnormality) }}</span>
            </div>
            <div class="detail-item">
              <label>眼结膜充血和分泌物：</label>
              <span>{{ formatBoolean(detailData.conjunctiva_status) }}</span>
            </div>
          </div>
        </div>

        <!-- 畸形与特殊检查 -->
        <div class="detail-section">
          <h3 class="section-title">畸形与特殊检查</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>先天性畸形：</label>
              <span>{{ formatBoolean(detailData.congenital_anomaly) }}</span>
            </div>
            <div v-if="detailData.congenital_anomaly" class="detail-item">
              <label>畸形类型：</label>
              <span>{{ getAnomalyTypeText(detailData.anomaly_type) }}</span>
            </div>
            <div v-if="detailData.anomaly_type_other" class="detail-item">
              <label>其他畸形：</label>
              <span>{{ formatText(detailData.anomaly_type_other) }}</span>
            </div>
            <div class="detail-item">
              <label>假性月经：</label>
              <span>{{ formatBoolean(detailData.pseudomenstruation) }}</span>
            </div>
          </div>
        </div>

        <!-- 脐部情况 -->
        <div class="detail-section">
          <h3 class="section-title">脐部情况</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>脐部：</label>
              <span>{{ getUmbilicalCordText(detailData.umbilical_cord) }}</span>
            </div>
            <div v-if="detailData.umbilical_cord === 'ABNORMAL'" class="detail-item">
              <label>脐部异常：</label>
              <span>{{
                getUmbilicalCordAbnormalityText(detailData.umbilical_cord_abnormality)
              }}</span>
            </div>
            <div class="detail-item">
              <label>轮脐红肿：</label>
              <span>{{ formatBoolean(detailData.umbilical_cord_red_and_swollen) }}</span>
            </div>
            <div class="detail-item">
              <label>脐部脱落：</label>
              <span>{{ formatBoolean(detailData.umbilical_cord_fall_off) }}</span>
            </div>
          </div>
        </div>

        <!-- 预防接种 -->
        <div class="detail-section">
          <h3 class="section-title">预防接种</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>预防接种：</label>
              <span>{{ getVaccineInjectionText(detailData.vaccine_injection) }}</span>
            </div>
            <div v-if="detailData.vaccine_injection_other" class="detail-item">
              <label>其他接种：</label>
              <span>{{ formatText(detailData.vaccine_injection_other) }}</span>
            </div>
          </div>
        </div>

        <!-- 护理与签名 -->
        <div class="detail-section">
          <h3 class="section-title">护理与签名</h3>
          <div class="space-y-4">
            <div class="detail-item">
              <label>婴儿护理要点：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.baby_care_points) }}</p>
            </div>
            <div class="detail-item">
              <label>签名：</label>
              <span>{{ formatText(detailData.signature) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暂无数据状态 -->
      <el-empty v-else-if="!loading && currentBaby" class="text-center py-12">
        <el-button
          type="primary"
          @click="createRecord"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          立即创建
        </el-button>
      </el-empty>
    </div>

    <!-- 表单对话框 -->
    <NewbornAdmissionFormDialog
      v-model="showFormDialog"
      :detail="dialogDetail"
      :baby-id="currentBaby?.nid"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage, ElEmpty } from 'element-plus'
import { get } from '@/utils/request.js'
import { formatDateTime } from '@/utils/dateUtils.js'
import { formatBoolean, formatNumber, formatText, downloadFile } from '@/utils/utils.js'
import {
  getBloodTypeText,
  getFeedingMethodText,
  getNewbornUrinationText,
  getNewbornBowelMovementText,
  getComplexionText,
  getCryText,
  getReactionText,
  getExtremitiesText,
  getExtremityToneText,
  getBirthInjuryTypeText,
  getReflexesText,
  getNutritionDevelopmentText,
  getSkinStatusText,
  getSkinAbnormalityText,
  getAnteriorFontanelleText,
  getOralMucosaText,
  getOralMucosaAbnormalityText,
  getAnomalyTypeText,
  getUmbilicalCordText,
  getUmbilicalCordAbnormalityText,
  getSuckingAbilityText,
  getButtocksText,
  getButtocksAbnormalityText,
  getVaccineInjectionText,
  getLocationText,
} from '@/utils/constants.js'
import NewbornAdmissionFormDialog from './NewbornAdmissionFormDialog.vue'

// 接收父组件传入的参数
const props = defineProps({
  babyList: {
    type: Array,
    default: () => [],
  },
})

const loading = ref(false)
const pdfLoading = ref(false)
const detailData = ref(null)
const showFormDialog = ref(false)
const dialogDetail = ref(null)

// 当前选中的baby
const currentBaby = ref(null)

// 获取详情数据
const fetchDetail = async () => {
  if (!currentBaby.value?.nid || loading.value) return

  loading.value = true
  try {
    const response = await get(
      `customer-service/newborn-check-in-assessment/detail/${currentBaby.value.nid}/`,
    )
    detailData.value = response
  } catch (error) {
    console.log(error)
    if (error.response?.status !== 404) {
      const errorMsg = error.response?.status === 403 ? '没有权限查看此记录' : '获取评估记录失败'
      ElMessage.error(errorMsg)
    }
    detailData.value = null
  } finally {
    loading.value = false
  }
}

// 处理baby切换
const handleBabyChange = (babyInfo) => {
  if (currentBaby.value?.nid === babyInfo.nid) return

  currentBaby.value = babyInfo
  detailData.value = null // 清空当前详情
}

// 监听babyList变化，设置默认选中的baby
watch(
  () => props.babyList,
  (newBabyList) => {
    if (newBabyList?.length > 0) {
      // 如果当前没有选中的baby，或者当前baby不在新列表中，则重新初始化
      const currentBabyExists = newBabyList.some(
        (item) => item.baby_info.nid === currentBaby.value?.nid,
      )
      if (!currentBaby.value || !currentBabyExists) {
        currentBaby.value = newBabyList[0].baby_info
        detailData.value = null // 清空当前详情
        fetchDetail() // 重新获取详情
      }
    } else {
      currentBaby.value = null
      detailData.value = null
    }
  },
  { immediate: true },
)

// 监听currentBaby变化，获取详情数据
watch(
  () => currentBaby.value?.nid,
  (newBabyId) => {
    if (newBabyId) {
      fetchDetail()
    } else {
      detailData.value = null
    }
  },
)

// 创建记录
const createRecord = () => {
  if (!currentBaby.value) {
    ElMessage.error('请先选择宝宝')
    return
  }
  dialogDetail.value = null
  showFormDialog.value = true
}

// 编辑记录
const editRecord = () => {
  if (!detailData.value) {
    ElMessage.error('暂无记录可编辑')
    return
  }
  dialogDetail.value = detailData.value
  showFormDialog.value = true
}

// 处理表单成功
const handleFormSuccess = () => {
  showFormDialog.value = false
  fetchDetail()
}

// 导出PDF
const exportPDF = async () => {
  if (!detailData.value?.id) {
    ElMessage.error('暂无评估记录可导出')
    return
  }

  pdfLoading.value = true
  try {
    const response = await get(
      `customer-service/newborn-check-in-assessment/pdf/${currentBaby.value.nid}/`,
    )

    if (response.pdf_url && response.filename) {
      // 获取完整的文件URL
      const fullUrl = response.pdf_url.startsWith('http')
        ? response.pdf_url
        : `${window.location.origin}${response.pdf_url}`

      // 下载文件
      downloadFile(fullUrl, response.filename)

      // 显示成功消息，包含过期时间提示
      const expiresHours = Math.floor((response.expires_in || 3600) / 3600)
      ElMessage.success({
        message: `PDF导出成功！文件链接有效期为${expiresHours}小时`,
        duration: 5000,
      })
    } else {
      throw new Error('PDF文件信息不完整')
    }
  } catch (error) {
    console.error('导出PDF失败:', error)

    // 提供更详细的错误信息
    if (error.response?.status === 404) {
      ElMessage.error('评估记录不存在或已被删除')
    } else if (error.response?.status === 403) {
      ElMessage.error('没有权限导出此PDF文件')
    } else if (error.message?.includes('网络')) {
      ElMessage.error('网络连接失败，请检查网络后重试')
    } else {
      ElMessage.error('PDF导出失败，请重试')
    }
  } finally {
    pdfLoading.value = false
  }
}

// 刷新数据
const refresh = () => {
  fetchDetail()
}

// 暴露方法给父组件
defineExpose({
  refresh,
  exportPDF,
})
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
}

.detail-item span {
  color: #374151;
}

.detail-item p {
  color: #6b7280;
  line-height: 1.5;
}

.grid .detail-item {
  flex-direction: row;
  align-items: center;
}

.grid .detail-item label {
  margin-bottom: 0;
  margin-right: 0.5rem;
  min-width: 6rem;
}
</style>
