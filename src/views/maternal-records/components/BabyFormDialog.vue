<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="新生儿姓名" prop="name" required>
              <el-input
                v-model="form.name"
                placeholder="请输入新生儿姓名"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="性别" prop="gender" required>
              <el-radio-group v-model="form.gender">
                <el-radio
                  v-for="option in GENDER_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="出生时间" prop="birth_time" required>
              <el-date-picker
                v-model="form.birth_time"
                type="datetime"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择出生时间"
              />
            </el-form-item>
            <el-form-item label="手牌号" prop="hand_card_number" required>
              <el-input
                v-model="form.hand_card_number"
                placeholder="请输入手牌号"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 出生信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">出生信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="出生体重 (克)" prop="birth_weight" required>
              <el-input-number
                v-model.number="form.birth_weight"
                :min="500"
                :max="10000"
                :precision="1"
                class="w-full"
                placeholder="请输入出生体重"
              />
            </el-form-item>
            <el-form-item label="身长 (厘米)" prop="birth_length" required>
              <el-input-number
                v-model.number="form.birth_length"
                :min="20"
                :max="80"
                :precision="1"
                class="w-full"
                placeholder="请输入出生身长"
              />
            </el-form-item>
            <el-form-item label="孕周" prop="birth_week" required>
              <el-input v-model="form.birth_week" placeholder="请输入孕周，如：34" maxlength="10" />
            </el-form-item>
            <el-form-item label="过敏史" prop="allergy_history">
              <el-input v-model="form.allergy_history" placeholder="请输入过敏史" maxlength="200" />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { GENDER_OPTIONS } from '@/utils/constants.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  babyId: {
    type: [String, Number],
    default: null,
  },
  aid: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const currentItemId = ref(null)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!currentItemId.value)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑新生儿信息' : '新建新生儿信息'
})

const form = reactive({
  name: '',
  gender: null,
  birth_time: '',
  birth_weight: null,
  birth_length: null,
  allergy_history: '',
  birth_week: '',
  hand_card_number: '',
})

const rules = {
  name: [{ required: true, message: '请输入新生儿姓名', trigger: 'blur' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  birth_time: [{ required: true, message: '请选择出生时间', trigger: 'change' }],
  birth_weight: [{ required: true, message: '请输入出生体重', trigger: 'change' }],
  birth_length: [{ required: true, message: '请输入出生身长', trigger: 'change' }],
  birth_week: [{ required: true, message: '请输入孕周', trigger: 'blur' }],
  hand_card_number: [{ required: true, message: '请输入手牌号', trigger: 'blur' }],
}

const { scrollToTop } = useDialogScrollToTop()

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (['gender', 'birth_weight', 'birth_length'].includes(key)) {
      form[key] = null
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

// 获取详情数据
const fetchDetail = async (babyId) => {
  if (!babyId || loading.value) return

  loading.value = true
  try {
    const response = await get(`customer-service/newborn/detail/${babyId}/`)
    if (response) {
      Object.assign(form, {
        ...response,
        birth_weight: response.birth_weight ? Number(response.birth_weight) : null,
        birth_length: response.birth_length ? Number(response.birth_length) : null,
        gender: response.gender ? Number(response.gender) : null,
      })
      console.log('Form data from API:', { ...form })
    }
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (visible) => {
    scrollToTop()
    if (visible) {
      // 设置 currentItemId
      currentItemId.value = props.babyId || props.detail?.nid || null

      if (props.detail) {
        // 使用传入的详情数据
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)
        Object.assign(form, {
          ...props.detail,
          birth_weight: props.detail.birth_weight ? Number(props.detail.birth_weight) : null,
          birth_length: props.detail.birth_length ? Number(props.detail.birth_length) : null,
          gender: props.detail.gender ? Number(props.detail.gender) : null,
        })
        console.log('Form data after assignment:', { ...form })
      } else if (currentItemId.value) {
        resetForm()
        await fetchDetail(currentItemId.value)
      } else {
        // 初始化默认值
        resetForm()
        await nextTick()
        form.birth_time = getCurrentTime('datetime')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 处理提交数据
    const submitData = { ...form }

    // 确保数字字段正确处理
    if (submitData.birth_weight) {
      submitData.birth_weight = Number(submitData.birth_weight)
    }
    if (submitData.birth_length) {
      submitData.birth_length = Number(submitData.birth_length)
    }
    if (submitData.gender) {
      submitData.gender = Number(submitData.gender)
    }

    console.log('Submit data:', submitData)

    let res
    if (!currentItemId.value) {
      // 创建记录
      res = await post(`customer-service/newborn/create/${props.aid}/`, submitData)
      ElMessage.success('新生儿信息创建成功！')
    } else {
      // 更新记录
      res = await put(`customer-service/newborn/update/${currentItemId.value}/`, submitData)
      ElMessage.success('新生儿信息更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    let errorMsg = ''
    if (error.message || error.msg) {
      errorMsg = error.message || error.msg
    } else if (error.data) {
      errorMsg = Object.values(error.data).flat()[0]
    } else {
      errorMsg = Object.values(error).flat()[0]
    }
    ElMessage.error(errorMsg || '操作失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-select__wrapper.is-focused) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
