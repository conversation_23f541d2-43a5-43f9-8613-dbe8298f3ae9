<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="space-y-4">
            <el-form-item label="记录日期" prop="record_date" required>
              <el-date-picker
                v-model="form.record_date"
                type="date"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择记录日期"
              />
            </el-form-item>
            <el-form-item label="出生天数" prop="days_after_birth">
              <el-input-number
                v-model="form.days_after_birth"
                :min="1"
                :max="100"
                class="w-full"
                placeholder="请输入出生天数"
              />
            </el-form-item>
            <el-form-item label="操作者" prop="operator">
              <el-input
                v-model="form.operator"
                placeholder="请输入操作者姓名"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 生理指标 -->
        <div class="form-section mb-6">
          <h4 class="section-title">生理指标</h4>
          <div class="space-y-4">
            <el-form-item label="体温 (℃)" prop="temperature">
              <el-input-number
                v-model="form.temperature"
                :min="35"
                :max="42"
                :precision="1"
                class="w-full"
                placeholder="请输入新生儿体温"
              />
            </el-form-item>
            <el-form-item label="体重 (g)" prop="weight">
              <el-input-number
                v-model="form.weight"
                :min="0"
                :max="10000"
                :precision="1"
                class="w-full"
                placeholder="请输入新生儿体重"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 护理操作 -->
        <div class="form-section mb-6">
          <h4 class="section-title">护理操作</h4>
          <div class="space-y-4">
            <el-form-item label="脐护情况" prop="umbilical_care">
              <el-input
                v-model="form.umbilical_care"
                type="textarea"
                :rows="3"
                placeholder="请描述脐护情况"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="洗澡情况" prop="bath">
              <el-input
                v-model="form.bath"
                type="textarea"
                :rows="3"
                placeholder="请描述洗澡情况"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="游泳情况" prop="swimming">
              <el-input
                v-model="form.swimming"
                type="textarea"
                :rows="3"
                placeholder="请描述游泳情况"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="抚触情况" prop="massage">
              <el-input
                v-model="form.massage"
                type="textarea"
                :rows="3"
                placeholder="请描述抚触情况"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="推拿情况" prop="acupressure">
              <el-input
                v-model="form.acupressure"
                type="textarea"
                :rows="3"
                placeholder="请描述推拿情况"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="SPA情况" prop="spa">
              <el-input
                v-model="form.spa"
                type="textarea"
                :rows="4"
                placeholder="请详细描述SPA情况"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="其他情况" prop="other_situation">
              <el-input
                v-model="form.other_situation"
                type="textarea"
                :rows="4"
                placeholder="请描述其他情况说明"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  babyId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!props.detail)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑新生儿护理操作记录' : '新建新生儿护理操作记录'
})

const form = reactive({
  record_date: '',
  days_after_birth: null,
  temperature: null,
  weight: null,
  umbilical_care: '',
  bath: '',
  swimming: '',
  massage: '',
  acupressure: '',
  spa: '',
  other_situation: '',
  operator: '',
})

const rules = {
  record_date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  days_after_birth: [
    { type: 'number', min: 1, max: 100, message: '出生天数应在1-100天之间', trigger: 'change' },
  ],
  temperature: [
    { type: 'number', min: 35, max: 42, message: '体温应在35-42℃之间', trigger: 'change' },
  ],
  weight: [
    { type: 'number', min: 0, max: 10000, message: '体重应在0-10000g之间', trigger: 'change' },
  ],
  operator: [
    { max: 50, message: '操作者姓名不能超过50个字符', trigger: 'blur' },
  ],
  umbilical_care: [
    { max: 500, message: '脐护情况描述不能超过500个字符', trigger: 'blur' },
  ],
  bath: [
    { max: 500, message: '洗澡情况描述不能超过500个字符', trigger: 'blur' },
  ],
  swimming: [
    { max: 500, message: '游泳情况描述不能超过500个字符', trigger: 'blur' },
  ],
  massage: [
    { max: 500, message: '抚触情况描述不能超过500个字符', trigger: 'blur' },
  ],
  acupressure: [
    { max: 500, message: '推拿情况描述不能超过500个字符', trigger: 'blur' },
  ],
  spa: [
    { max: 1000, message: 'SPA情况描述不能超过1000个字符', trigger: 'blur' },
  ],
  other_situation: [
    { max: 1000, message: '其他情况说明不能超过1000个字符', trigger: 'blur' },
  ],
}

const { scrollToTop } = useDialogScrollToTop()

// 重置表单
const resetForm = () => {
  const numberFields = ['days_after_birth', 'temperature', 'weight']
  const stringFields = ['record_date', 'umbilical_care', 'bath', 'swimming', 'massage', 'acupressure', 'spa', 'other_situation', 'operator']

  numberFields.forEach((key) => {
    form[key] = null
  })

  stringFields.forEach((key) => {
    form[key] = ''
  })

  nextTick(() => formRef.value?.clearValidate())
}

// 设置表单数据
const setFormData = (data) => {
  if (!data) return

  // 安全地设置表单数据，确保类型正确
  Object.keys(form).forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      form[key] = data[key]
    }
  })
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (isVisible) => {
    scrollToTop()
    if (isVisible) {
      if (props.detail && isEdit.value) {
        // 编辑模式：如果传入了详情数据，直接使用
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)
        setFormData(props.detail)
        console.log('Form data after assignment:', { ...form })
      } else {
        // 新建模式：初始化默认值
        resetForm()
        await nextTick()
        form.record_date = getCurrentTime('date')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 验证babyId
const validateBabyId = () => {
  if (!props.babyId) {
    ElMessage.error('未指定宝宝ID，无法提交记录')
    return false
  }
  return true
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    // 验证babyId（仅在新建时需要）
    if (!isEdit.value && !validateBabyId()) {
      return
    }

    submitting.value = true

    // 处理提交数据 - 确保数据类型正确
    const submitData = { ...form }

    // 清理空字符串，数字字段保持null
    Object.keys(submitData).forEach((key) => {
      if (typeof submitData[key] === 'string' && submitData[key].trim() === '') {
        submitData[key] = ''
      }
    })

    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      // 创建记录
      res = await post(`customer-service/newborn-care-operation/create/${props.babyId}/`, submitData)
      ElMessage.success('新生儿护理操作记录创建成功！')
    } else {
      // 更新记录
      res = await put(`customer-service/newborn-care-operation/update/${props.detail.record_id}/`, submitData)
      ElMessage.success('新生儿护理操作记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
