<template>
  <div class="action-buttons bg-white rounded-lg p-4 shadow-sm">
    <div class="flex flex-wrap gap-3">
      <el-button
        type="success"
        @click="$emit('add-record')"
        class="flex items-center gap-2 bg-green-500 hover:bg-green-600 border-green-500"
      >
        <Plus class="w-4 h-4" />
        新增记录
      </el-button>

      <el-button
        @click="$emit('print')"
        class="flex items-center gap-2 border-pink-300 text-pink-600 hover:bg-pink-50"
      >
        <Printer class="w-4 h-4" />
        打印客户档案
      </el-button>

      <el-button
        @click="$emit('export')"
        class="flex items-center gap-2 border-pink-300 text-pink-600 hover:bg-pink-50"
      >
        <Download class="w-4 h-4" />
        导出健康记录
      </el-button>

      <el-button
        @click="$emit('settings')"
        class="flex items-center gap-2 border-gray-300 text-gray-600 hover:bg-gray-50"
      >
        <Setting class="w-4 h-4" />
        设置提醒
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { Plus, Printer, Download, Setting } from '@element-plus/icons-vue'

defineEmits(['add-record', 'print', 'export', 'settings'])
</script>

<style scoped>
.action-buttons {
  transition: all 0.3s ease;
}

.action-buttons:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}
</style>
