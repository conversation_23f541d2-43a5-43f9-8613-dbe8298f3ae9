<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="记录日期" prop="record_date">
              <el-date-picker
                v-model="form.record_date"
                type="date"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择记录日期"
              />
            </el-form-item>
            <el-form-item label="签名" prop="signature">
              <el-input v-model="form.signature" placeholder="请输入护理人员签名" />
            </el-form-item>
          </div>
        </div>

        <!-- 饮食情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">饮食情况</h4>
          <div class="space-y-4">
            <el-form-item label="正常饮食" prop="normal_diet">
              <el-input
                v-model="form.normal_diet"
                type="textarea"
                :rows="3"
                placeholder="请输入正常饮食情况，如：清淡易消化，营养均衡，少食多餐"
              />
            </el-form-item>
            <el-form-item label="特殊饮食" prop="special_diet">
              <el-input
                v-model="form.special_diet"
                type="textarea"
                :rows="3"
                placeholder="请输入特殊饮食情况，如：低盐低脂，补充蛋白质和维生素"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 睡眠与排泄 -->
        <div class="form-section mb-6">
          <h4 class="section-title">睡眠与排泄</h4>
          <div class="space-y-4">
            <el-form-item label="睡眠时长 (小时)" prop="sleep_hours">
              <el-input-number
                v-model="form.sleep_hours"
                :min="0"
                :max="24"
                :precision="0"
                class="w-full"
                placeholder="请输入睡眠小时数"
              />
            </el-form-item>
            <el-form-item label="小便情况" prop="urination">
              <el-input
                v-model="form.urination"
                type="textarea"
                :rows="2"
                placeholder="请输入小便情况，如：正常，6-8次/日，无异常"
              />
            </el-form-item>
            <el-form-item label="大便情况" prop="defecation">
              <el-input
                v-model="form.defecation"
                type="textarea"
                :rows="2"
                placeholder="请输入大便情况，如：正常，1次/日，成形"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 个人卫生护理 -->
        <div class="form-section mb-6">
          <h4 class="section-title">个人卫生护理</h4>
          <div class="space-y-4">
            <el-form-item label="刷牙" prop="brushing_teeth">
              <el-input
                v-model="form.brushing_teeth"
                type="textarea"
                :rows="2"
                placeholder="请输入刷牙情况，如：早晚各1次，使用软毛牙刷"
              />
            </el-form-item>
            <el-form-item label="洗头" prop="wash_hair">
              <el-input
                v-model="form.wash_hair"
                type="textarea"
                :rows="2"
                placeholder="请输入洗头情况，如：每3天1次，温水清洗"
              />
            </el-form-item>
            <el-form-item label="洗澡" prop="bathe">
              <el-input
                v-model="form.bathe"
                type="textarea"
                :rows="2"
                placeholder="请输入洗澡情况，如：每日温水擦浴，保持清洁"
              />
            </el-form-item>
            <el-form-item label="会阴清洁" prop="perineal_cleaning">
              <el-input
                v-model="form.perineal_cleaning"
                type="textarea"
                :rows="2"
                placeholder="请输入会阴清洁情况，如：每日2次，温开水清洗，前后分开"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 产后运动 -->
        <div class="form-section mb-6">
          <h4 class="section-title">产后运动</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="运动时间" prop="exercise_time" class="col-span-full">
              <el-input
                v-model="form.exercise_time"
                type="textarea"
                :rows="2"
                placeholder="请输入运动时间安排，如：上午30分钟，下午20分钟产后康复操"
              />
            </el-form-item>
            <el-form-item label="运动方式" prop="exercise_method" class="col-span-full">
              <el-input
                v-model="form.exercise_method"
                type="textarea"
                :rows="2"
                placeholder="请输入运动方式，如：产后康复操、腹式呼吸、产褥期体操等"
              />
            </el-form-item>
            <el-form-item label="运动耐受性" prop="exercise_tolerance">
              <el-select v-model="form.exercise_tolerance" placeholder="选择运动耐受性">
                <el-option
                  v-for="option in EXERCISE_AFTER_EVALUATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="运动理解程度" prop="exercise_understanding">
              <el-select v-model="form.exercise_understanding" placeholder="选择运动理解程度">
                <el-option
                  v-for="option in EXERCISE_AFTER_EVALUATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 生命体征 -->
        <div class="form-section mb-6">
          <h4 class="section-title">生命体征</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="心率 (次/分)" prop="heart_rate">
              <el-input-number
                v-model="form.heart_rate"
                :min="40"
                :max="200"
                :precision="0"
                class="w-full"
                placeholder="请输入心率"
              />
            </el-form-item>
            <el-form-item label="血压 (mmHg)" prop="blood_pressure">
              <el-input v-model="form.blood_pressure" placeholder="如：120/80" />
            </el-form-item>
          </div>
        </div>

        <!-- 其他情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">其他情况</h4>
          <el-form-item label="其他情况" prop="other_situation">
            <el-input
              v-model="form.other_situation"
              type="textarea"
              :rows="4"
              placeholder="请输入其他需要记录的情况，如：精神状态良好，食欲正常，无特殊不适"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { EXERCISE_AFTER_EVALUATION_OPTIONS } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  customerId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!props.detail)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑产妇每日生理护理记录' : '新建产妇每日生理护理记录'
})

const form = reactive({
  record_date: '',
  normal_diet: '',
  special_diet: '',
  sleep_hours: null,
  urination: '',
  defecation: '',
  brushing_teeth: '',
  wash_hair: '',
  bathe: '',
  perineal_cleaning: '',
  exercise_time: '',
  exercise_method: '',
  exercise_tolerance: '',
  exercise_understanding: '',
  heart_rate: null,
  blood_pressure: '',
  other_situation: '',
  signature: '',
})

const rules = {
  record_date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  // 其他字段都是可选的，根据API文档只有record_date是必填
}

const { scrollToTop } = useDialogScrollToTop()

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (['sleep_hours', 'heart_rate'].includes(key)) {
      form[key] = null
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (visible) => {
    scrollToTop()
    if (visible) {
      if (props.detail) {
        // 使用传入的详情数据
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)

        // 兼容性处理：将旧的 FAIR 值转换为 AVERAGE
        const processedDetail = { ...props.detail }
        if (processedDetail.exercise_tolerance === 'FAIR') {
          processedDetail.exercise_tolerance = 'AVERAGE'
        }
        if (processedDetail.exercise_understanding === 'FAIR') {
          processedDetail.exercise_understanding = 'AVERAGE'
        }

        Object.assign(form, processedDetail)
        console.log('Form data after assignment:', { ...form })
      } else {
        // 初始化默认值
        resetForm()
        await nextTick()
        form.record_date = getCurrentTime('date')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 处理提交数据 - 清理空字符串，保留null值用于数字字段
    const submitData = { ...form }

    // 对于字符串字段，将空字符串转换为null或者保持为空字符串（根据后端要求）
    Object.keys(submitData).forEach((key) => {
      if (typeof submitData[key] === 'string' && submitData[key].trim() === '') {
        submitData[key] = '' // 保持空字符串，符合API要求
      }
    })

    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      // 创建记录
      res = await post(`customer-service/mdpc-record/create/${props.customerId}/`, submitData)
      ElMessage.success('产妇每日生理护理记录创建成功！')
    } else {
      // 更新记录
      res = await put(`customer-service/mdpc-record/update/${props.detail.record_id}/`, submitData)
      ElMessage.success('产妇每日生理护理记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.col-span-full {
  grid-column: 1 / -1;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
