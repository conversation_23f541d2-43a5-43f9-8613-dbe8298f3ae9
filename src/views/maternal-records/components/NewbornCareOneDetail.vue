<template>
  <div class="newborn-care-one-detail">
    <div class="detail-header mb-6 flex justify-between items-center">
      <div class="flex-1">
        <div class="flex items-center gap-4 mb-2">
          <h3 class="text-lg font-semibold text-gray-800">新生儿护理记录单（1）</h3>
          <!-- Baby切换Tab -->
          <div v-if="props.babyList?.length > 1" class="flex bg-gray-100 rounded-lg p-1">
            <button
              v-for="baby in props.babyList"
              :key="baby.baby_info.nid"
              @click="handleBabyChange(baby.baby_info)"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-all cursor-pointer',
                currentBaby?.nid === baby.baby_info.nid
                  ? 'bg-white text-pink-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800',
              ]"
            >
              {{ baby.baby_info.name || `宝宝${baby.baby_info.nid}` }}
            </button>
          </div>
        </div>
        <p class="text-sm text-gray-600">
          查看新生儿护理记录的详细信息
          <span v-if="babyList.length > 1 && currentBaby" class="ml-2 text-pink-600">
            当前：{{ currentBaby.name || `宝宝${currentBaby.nid}` }}
          </span>
        </p>
      </div>
      <div class="flex gap-2">
        <el-button
          type="primary"
          @click="createRecord"
          :disabled="!currentBaby"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          添加记录
        </el-button>
        <el-button
          v-if="detail"
          disabled
          @click="exportPDF"
          :loading="pdfLoading"
          class="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
        >
          <template #icon>
            <Download />
          </template>
          导出PDF
        </el-button>
      </div>
    </div>

    <!-- 无Baby提示 -->
    <div v-if="!currentBaby" class="text-center py-8">
      <el-empty description="暂无宝宝信息，无法查看护理记录" />
    </div>

    <!-- 记录列表区域 -->
    <div v-else class="mb-6">
      <RecordListView
        ref="recordListRef"
        :api-url="apiUrl"
        :selected-id="detail?.record_id"
        @select="handleSelectRecord"
        :key="currentBaby.nid"
      />
    </div>

    <!-- 详情内容区域 -->
    <div v-if="detail" class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div v-loading="detailLoading" class="p-6">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>记录日期：</label>
                <span>{{ formatDate(detail.record_date) }}</span>
              </div>
              <div class="detail-item">
                <label>出生后天数：</label>
                <span>{{ formatNumber(detail.days_after_birth) }}天</span>
              </div>
              <div class="detail-item">
                <label>护理人员签名：</label>
                <span>{{ formatText(detail.caregiver_signature) }}</span>
              </div>
            </div>
          </div>

          <!-- 生理指标 -->
          <div class="detail-section">
            <h3 class="section-title">生理指标</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>体重：</label>
                <span>{{ formatNumber(detail.weight) }}克</span>
              </div>
              <div class="detail-item">
                <label>身长：</label>
                <span>{{ formatNumber(detail.length) }}厘米</span>
              </div>
              <div class="detail-item">
                <label>体温：</label>
                <span>{{ formatNumber(detail.temperature) }}℃</span>
              </div>
              <div class="detail-item">
                <label>前囟情况：</label>
                <span>{{ formatText(detail.anterior_fontanelle) }}</span>
              </div>
            </div>
          </div>

          <!-- 行为表现 -->
          <div class="detail-section">
            <h3 class="section-title">行为表现</h3>
            <div class="space-y-3">
              <div class="detail-item">
                <label>哭声情况：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.cry) }}</p>
              </div>
            </div>
          </div>

          <!-- 排泄情况 -->
          <div class="detail-section">
            <h3 class="section-title">排泄情况</h3>
            <div class="space-y-3">
              <div class="detail-item">
                <label>小便情况：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.urine) }}</p>
              </div>
              <div class="detail-item">
                <label>大便次数：</label>
                <span>{{ formatNumber(detail.bowel_movement_frequency) }}次</span>
              </div>
              <div class="detail-item">
                <label>大便颜色：</label>
                <p class="mt-1 text-gray-600">{{ formatText(detail.bowel_movement_color) }}</p>
              </div>
              <div class="detail-item">
                <label>大便性质：</label>
                <p class="mt-1 text-gray-600">
                  {{ formatText(detail.bowel_movement_consistency) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 护理指导 -->
          <div class="detail-section">
            <h3 class="section-title">护理指导</h3>
            <div class="detail-item">
              <label>护理指导意见：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detail.guidance) }}</p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-end gap-2 p-4">
          <el-button
            @click="editRecord"
            class="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            @click="deleteRecord"
            :loading="deleteLoading"
            class="hover:bg-red-50"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表单对话框 -->
    <NewbornCareOneFormDialog
      v-model="showFormDialog"
      :detail="dialogDetail"
      :baby-id="currentBaby?.nid"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed, nextTick } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElEmpty } from 'element-plus'
import { get, del } from '@/utils/request.js'
import { formatDate } from '@/utils/dateUtils.js'
import { formatNumber, formatText, downloadFile } from '@/utils/utils.js'
import NewbornCareOneFormDialog from './NewbornCareOneFormDialog.vue'
import RecordListView from '@/components/RecordListView.vue'

// 接收父组件传入的参数
const props = defineProps({
  babyList: {
    type: Array,
    default: () => [],
  },
})

const detailLoading = ref(false)
const pdfLoading = ref(false)
const deleteLoading = ref(false)
const showFormDialog = ref(false)

// 组件引用
const recordListRef = ref()

// 当前选中的baby
const currentBaby = ref(null)

// 选中的记录
const detail = ref(null)
const dialogDetail = ref(null)

// 计算 API URL
const apiUrl = computed(() => {
  if (!currentBaby.value?.nid) {
    return ''
  }
  return `customer-service/newborn-care-one/list/${currentBaby.value.nid}/`
})

// 监听babyList变化，设置默认选中的baby
watch(
  () => props.babyList,
  (newBabyList) => {
    if (newBabyList?.length > 0) {
      // 如果当前没有选中的baby，或者当前baby不在新列表中，则重新初始化
      const currentBabyExists = newBabyList.some(
        (item) => item.baby_info.nid === currentBaby.value?.nid,
      )
      if (!currentBaby.value || !currentBabyExists) {
        currentBaby.value = newBabyList[0].baby_info
        detail.value = null // 清空当前详情
      }
    } else {
      currentBaby.value = null
      detail.value = null
    }
  },
  { immediate: true },
)

// 监听apiUrl变化，刷新记录列表
watch(apiUrl, (newUrl) => {
  if (newUrl && recordListRef.value) {
    nextTick(() => {
      recordListRef.value.refresh()
    })
  }
})

// 处理baby切换
const handleBabyChange = (babyInfo) => {
  if (currentBaby.value?.nid === babyInfo.nid) return

  currentBaby.value = babyInfo
  detail.value = null // 清空当前详情
}

// 获取详情数据
const fetchDetail = async (recordId) => {
  if (!recordId || detailLoading.value) return

  detailLoading.value = true
  try {
    const response = await get(`customer-service/newborn-care-one/detail/${recordId}/`)
    detail.value = response
  } catch (error) {
    console.error('获取记录详情失败:', error)
    const errorMsg = error.response?.status === 404 ? '记录不存在或已被删除' : '获取记录详情失败'
    ElMessage.error(errorMsg)
    detail.value = null
  } finally {
    detailLoading.value = false
  }
}

// 选择记录
const handleSelectRecord = (record) => {
  fetchDetail(record.record_id)
}

const createRecord = () => {
  if (!currentBaby.value) {
    ElMessage.error('请先选择宝宝')
    return
  }
  dialogDetail.value = null
  showFormDialog.value = true
}

// 编辑记录
const editRecord = () => {
  if (!detail.value) {
    ElMessage.error('暂无记录可编辑')
    return
  }
  dialogDetail.value = detail.value
  showFormDialog.value = true
}

// 删除记录
const deleteRecord = async () => {
  if (!detail.value?.record_id) {
    ElMessage.error('暂无记录可删除')
    return
  }

  try {
    await ElMessageBox.confirm('确定要删除这条新生儿护理记录吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    deleteLoading.value = true
    await del(`customer-service/newborn-care-one/delete/${detail.value.record_id}/`)

    ElMessage.success('记录删除成功')
    detail.value = null
    recordListRef.value?.refresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除记录失败:', error)
      const errorMsg =
        error.response?.status === 404 ? '记录不存在或已被删除' : '删除记录失败，请重试'
      ElMessage.error(errorMsg)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 处理表单成功
const handleFormSuccess = (result) => {
  showFormDialog.value = false
  recordListRef.value?.refresh()

  // 如果是编辑模式，刷新当前详情数据
  if (dialogDetail.value && result?.record_id) {
    fetchDetail(result.record_id)
  }
}

// 导出PDF
const exportPDF = async () => {
  if (!detail.value?.record_id) {
    ElMessage.error('暂无记录可导出')
    return
  }

  pdfLoading.value = true
  try {
    const response = await get(`customer-service/newborn-care-one/pdf/${detail.value.record_id}/`)

    if (response.pdf_url && response.filename) {
      const fullUrl = response.pdf_url.startsWith('http')
        ? response.pdf_url
        : `${window.location.origin}${response.pdf_url}`

      downloadFile(fullUrl, response.filename)

      const expiresHours = Math.floor((response.expires_in || 3600) / 3600)
      ElMessage.success({
        message: `PDF导出成功！文件链接有效期为${expiresHours}小时`,
        duration: 5000,
      })
    } else {
      throw new Error('PDF文件信息不完整')
    }
  } catch (error) {
    console.error('导出PDF失败:', error)
    let errorMsg = 'PDF导出失败，请重试'
    if (error.response?.status === 404) {
      errorMsg = '记录不存在或已被删除'
    } else if (error.response?.status === 403) {
      errorMsg = '没有权限导出此PDF文件'
    }
    ElMessage.error(errorMsg)
  } finally {
    pdfLoading.value = false
  }
}

// 刷新数据
const refresh = () => {
  recordListRef.value?.refresh()
}

// 暴露方法给父组件
defineExpose({
  refresh,
  exportPDF,
})
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
}

.detail-item span {
  color: #374151;
}

.detail-item p {
  color: #6b7280;
  line-height: 1.5;
}

.grid .detail-item {
  flex-direction: row;
  align-items: center;
}

.grid .detail-item label {
  margin-bottom: 0;
  margin-right: 0.5rem;
  min-width: 6rem;
}
</style>
