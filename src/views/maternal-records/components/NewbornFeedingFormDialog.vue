<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="space-y-4">
            <el-form-item label="记录时间" prop="record_time" required>
              <el-date-picker
                v-model="form.record_time"
                type="datetime"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择记录时间"
              />
            </el-form-item>
            <el-form-item label="喂养方式" prop="feeding_method" required>
              <el-select
                v-model="form.feeding_method"
                placeholder="请选择喂养方式"
                class="w-full"
                @change="handleFeedingMethodChange"
              >
                <el-option
                  v-for="option in FEEDING_METHOD_CHOICE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="护理员签名" prop="caregiver_signature">
              <el-input
                v-model="form.caregiver_signature"
                placeholder="请输入护理员签名"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 母乳喂养 -->
        <div v-if="showBreastFeeding" class="form-section mb-6">
          <h4 class="section-title">母乳喂养</h4>
          <div class="space-y-4">
            <el-form-item label="左侧时间 (分钟)" prop="breast_feeding_left_time">
              <el-input-number
                v-model="form.breast_feeding_left_time"
                :min="0"
                :max="120"
                :precision="0"
                class="w-full"
                placeholder="请输入左侧喂养时间"
              />
            </el-form-item>
            <el-form-item label="右侧时间 (分钟)" prop="breast_feeding_right_time">
              <el-input-number
                v-model="form.breast_feeding_right_time"
                :min="0"
                :max="120"
                :precision="0"
                class="w-full"
                placeholder="请输入右侧喂养时间"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 人工喂养 -->
        <div v-if="showArtificialFeeding" class="form-section mb-6">
          <h4 class="section-title">人工喂养</h4>
          <div class="space-y-4">
            <el-form-item label="人工喂养指征" prop="artificial_feeding_indicators">
              <el-input
                v-model="form.artificial_feeding_indicators"
                type="textarea"
                :rows="3"
                placeholder="请输入人工喂养指征，如：母乳不足、其他医疗原因等"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="喂养量 (毫升)" prop="artificial_feeding_amount">
              <el-input-number
                v-model="form.artificial_feeding_amount"
                :min="0"
                :max="500"
                :precision="1"
                class="w-full"
                placeholder="请输入人工喂养量"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 混合喂养 -->
        <div v-if="showMixedFeeding" class="form-section mb-6">
          <h4 class="section-title">混合喂养</h4>
          <div class="space-y-4">
            <el-form-item label="自喂时间 (分钟)" prop="mixed_feeding_self_feeding_time">
              <el-input-number
                v-model="form.mixed_feeding_self_feeding_time"
                :min="0"
                :max="120"
                :precision="0"
                class="w-full"
                placeholder="请输入自喂时间"
              />
            </el-form-item>
            <el-form-item label="人喂量 (毫升)" prop="mixed_feeding_human_feeding_ml">
              <el-input-number
                v-model="form.mixed_feeding_human_feeding_ml"
                :min="0"
                :max="500"
                :precision="1"
                class="w-full"
                placeholder="请输入人喂量"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 其他喂养 -->
        <div class="form-section mb-6">
          <h4 class="section-title">其他喂养</h4>
          <div class="space-y-4">
            <el-form-item label="" prop="other_feeding">
              <el-input
                v-model="form.other_feeding"
                type="textarea"
                :rows="3"
                placeholder="请输入其他喂养方式描述，如：添加维生素D滴剂等"
                maxlength="300"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils.js'
import { FEEDING_METHOD_CHOICE_OPTIONS } from '@/utils/constants.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  babyId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!props.detail)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑新生儿喂养记录' : '新建新生儿喂养记录'
})

const form = reactive({
  record_time: '',
  feeding_method: '',
  breast_feeding_left_time: null,
  breast_feeding_right_time: null,
  artificial_feeding_indicators: '',
  artificial_feeding_amount: null,
  mixed_feeding_self_feeding_time: null,
  mixed_feeding_human_feeding_ml: null,
  other_feeding: '',
  caregiver_signature: '',
})

// 根据喂养方式显示对应的输入字段
const showBreastFeeding = computed(() => {
  return form.feeding_method === 'BREAST_FEEDING'
})

const showArtificialFeeding = computed(() => {
  return form.feeding_method === 'ARTIFICIAL_FEEDING'
})

const showMixedFeeding = computed(() => {
  return form.feeding_method === 'MIXED_FEEDING'
})

const rules = {
  record_time: [{ required: true, message: '请选择记录时间', trigger: 'change' }],
  feeding_method: [{ required: true, message: '请选择喂养方式', trigger: 'change' }],
  breast_feeding_left_time: [
    {
      type: 'number',
      min: 0,
      max: 120,
      message: '左侧喂养时间应在0-120分钟之间',
      trigger: 'change',
    },
  ],
  breast_feeding_right_time: [
    {
      type: 'number',
      min: 0,
      max: 120,
      message: '右侧喂养时间应在0-120分钟之间',
      trigger: 'change',
    },
  ],
  artificial_feeding_amount: [
    { type: 'number', min: 0, max: 500, message: '人工喂养量应在0-500毫升之间', trigger: 'change' },
  ],
  mixed_feeding_self_feeding_time: [
    { type: 'number', min: 0, max: 120, message: '自喂时间应在0-120分钟之间', trigger: 'change' },
  ],
  mixed_feeding_human_feeding_ml: [
    { type: 'number', min: 0, max: 500, message: '人喂量应在0-500毫升之间', trigger: 'change' },
  ],
}

const { scrollToTop } = useDialogScrollToTop()

// 处理喂养方式变化
const handleFeedingMethodChange = (value) => {
  // 清空不相关的字段
  if (value !== 'BREAST_FEEDING') {
    form.breast_feeding_left_time = null
    form.breast_feeding_right_time = null
  }

  if (value !== 'ARTIFICIAL_FEEDING') {
    form.artificial_feeding_indicators = ''
    form.artificial_feeding_amount = null
  }

  if (value !== 'MIXED_FEEDING') {
    form.mixed_feeding_self_feeding_time = null
    form.mixed_feeding_human_feeding_ml = null
  }
}

// 重置表单
const resetForm = () => {
  const numberFields = [
    'breast_feeding_left_time',
    'breast_feeding_right_time',
    'artificial_feeding_amount',
    'mixed_feeding_self_feeding_time',
    'mixed_feeding_human_feeding_ml',
  ]
  const stringFields = [
    'record_time',
    'feeding_method',
    'artificial_feeding_indicators',
    'other_feeding',
    'caregiver_signature',
  ]

  numberFields.forEach((key) => {
    form[key] = null
  })

  stringFields.forEach((key) => {
    form[key] = ''
  })

  nextTick(() => formRef.value?.clearValidate())
}

// 设置表单数据
const setFormData = (data) => {
  if (!data) return

  // 安全地设置表单数据，确保类型正确
  Object.keys(form).forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      form[key] = data[key]
    }
  })
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (isVisible) => {
    scrollToTop()
    if (isVisible) {
      if (props.detail && isEdit.value) {
        // 编辑模式：如果传入了详情数据，直接使用
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)
        setFormData(props.detail)
        console.log('Form data after assignment:', { ...form })
      } else {
        // 新建模式：初始化默认值
        resetForm()
        await nextTick()
        form.record_time = getCurrentTime('datetime')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 验证babyId
const validateBabyId = () => {
  if (!props.babyId) {
    ElMessage.error('未指定宝宝ID，无法提交记录')
    return false
  }
  return true
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    // 验证babyId（仅在新建时需要）
    if (!isEdit.value && !validateBabyId()) {
      return
    }

    submitting.value = true

    // 处理提交数据 - 确保数据类型正确
    const submitData = { ...form }

    // 清理空字符串，数字字段保持null
    Object.keys(submitData).forEach((key) => {
      if (typeof submitData[key] === 'string' && submitData[key].trim() === '') {
        submitData[key] = ''
      }
    })

    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      // 创建记录
      res = await post(`customer-service/newborn-feeding/create/${props.babyId}/`, submitData)
      ElMessage.success('新生儿喂养记录创建成功！')
    } else {
      // 更新记录
      res = await put(
        `customer-service/newborn-feeding/update/${props.detail.record_id}/`,
        submitData,
      )
      ElMessage.success('新生儿喂养记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
