<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="1000px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="160px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="入所时间" prop="admission_time">
              <el-date-picker
                v-model="form.admission_time"
                type="datetime"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item label="评估时间" prop="assessment_time">
              <el-date-picker
                v-model="form.assessment_time"
                type="datetime"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 病史及诊断 -->
        <div class="form-section mb-6">
          <h4 class="section-title">病史及诊断</h4>
          <div class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="有无出院小结">
                <el-switch v-model="form.has_discharge_summary" />
              </el-form-item>
              <el-form-item label="现在是否用药">
                <el-switch v-model="form.is_now_medication" />
              </el-form-item>
              <el-form-item label="是否急性疾病治疗期">
                <el-switch v-model="form.is_acute_disease_treatment_period" />
              </el-form-item>
              <el-form-item label="是否传染病传染期">
                <el-switch v-model="form.is_infectious_disease_period" />
              </el-form-item>
              <el-form-item label="有无精神疾病史">
                <el-switch v-model="form.has_psychiatric_disease_history" />
              </el-form-item>
              <el-form-item label="有无心理疾病">
                <el-switch v-model="form.has_psychological_disease" />
              </el-form-item>
            </div>
            <el-form-item label="出院诊断" prop="discharge_diagnosis">
              <el-input v-model="form.discharge_diagnosis" type="textarea" :rows="2" />
            </el-form-item>
            <el-form-item label="孕期并发症合并症" prop="pregnancy_complications_and_comorbidities">
              <el-input
                v-model="form.pregnancy_complications_and_comorbidities"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
            <el-form-item label="分娩特殊情况" prop="delivery_special_situation">
              <el-input v-model="form.delivery_special_situation" type="textarea" :rows="2" />
            </el-form-item>
            <el-form-item label="既往孕产史" prop="previous_pregnancy_history">
              <el-input v-model="form.previous_pregnancy_history" type="textarea" :rows="2" />
            </el-form-item>
          </div>
        </div>

        <!-- 过敏史 -->
        <div class="form-section mb-6">
          <h4 class="section-title">过敏史</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="是否有过敏食物">
              <el-switch v-model="form.has_allergic_food" />
            </el-form-item>
            <el-form-item label="是否有过敏药物">
              <el-switch v-model="form.has_allergic_drug" />
            </el-form-item>
          </div>
          <div class="space-y-4">
            <el-form-item label="过敏食物" prop="allergic_food" v-if="form.has_allergic_food">
              <el-input v-model="form.allergic_food" placeholder="请描述过敏食物" />
            </el-form-item>
            <el-form-item
              label="食物过敏反应"
              prop="food_allergic_reaction"
              v-if="form.has_allergic_food"
            >
              <el-input v-model="form.food_allergic_reaction" type="textarea" :rows="2" />
            </el-form-item>
            <el-form-item
              label="药物过敏反应"
              prop="drug_allergic_reaction"
              v-if="form.has_allergic_drug"
            >
              <el-input v-model="form.drug_allergic_reaction" type="textarea" :rows="2" />
            </el-form-item>
          </div>
        </div>

        <!-- 心理及饮食 -->
        <div class="form-section mb-6">
          <h4 class="section-title">心理及饮食状况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="情绪" prop="emotion">
              <el-select v-model="form.emotion" multiple placeholder="选择情绪状态">
                <el-option label="稳定" value="STABLE" />
                <el-option label="兴奋" value="EXCITED" />
                <el-option label="抑郁" value="DEPRESSED" />
                <el-option label="焦虑" value="ANXIETY" />
                <el-option label="恐惧" value="FEAR" />
              </el-select>
            </el-form-item>
            <el-form-item label="食欲" prop="appetite">
              <el-select v-model="form.appetite" placeholder="选择食欲状况">
                <el-option label="正常" value="NORMAL" />
                <el-option label="差" value="POOR" />
                <el-option label="厌食" value="ANTIAE" />
              </el-select>
            </el-form-item>
            <el-form-item label="饮食要求" prop="dietary_requirements">
              <el-select v-model="form.dietary_requirements" placeholder="选择饮食要求">
                <el-option label="普通" value="ORDINARY" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
            <el-form-item label="是否有饮食嗜好">
              <el-switch v-model="form.has_food_preference" />
            </el-form-item>
          </div>
          <div class="space-y-4">
            <el-form-item
              label="饮食要求其他描述"
              prop="dro_description"
              v-if="form.dietary_requirements === 'OTHER'"
            >
              <el-input v-model="form.dro_description" type="textarea" :rows="2" />
            </el-form-item>
            <el-form-item
              label="饮食嗜好描述"
              prop="food_preference_description"
              v-if="form.has_food_preference"
            >
              <el-input v-model="form.food_preference_description" type="textarea" :rows="2" />
            </el-form-item>
          </div>
        </div>

        <!-- 排便排尿 -->
        <div class="form-section mb-6">
          <h4 class="section-title">排便排尿情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="排尿" prop="urination">
              <el-select v-model="form.urination" placeholder="选择排尿状况">
                <el-option label="正常" value="NORMAL" />
                <el-option label="频繁" value="FREQUENT" />
              </el-select>
            </el-form-item>
            <el-form-item label="排尿次数/天" prop="urination_times">
              <el-input-number v-model="form.urination_times" :min="0" class="w-full" />
            </el-form-item>
            <el-form-item label="有无尿痛">
              <el-switch v-model="form.has_urination_pain" />
            </el-form-item>
            <el-form-item label="排便" prop="defecation">
              <el-select v-model="form.defecation" placeholder="选择排便状况">
                <el-option label="正常" value="NORMAL" />
                <el-option label="便秘" value="CONSTIPATION" />
                <el-option label="腹泻" value="DIARRHEA" />
              </el-select>
            </el-form-item>
            <el-form-item label="排便次数/天" prop="defecation_times">
              <el-input-number v-model="form.defecation_times" :min="0" class="w-full" />
            </el-form-item>
          </div>
        </div>

        <!-- 教育及家庭支持 -->
        <div class="form-section mb-6">
          <h4 class="section-title">教育及家庭支持</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="康复知识熟悉程度" prop="knowledge_of_rehabilitation">
              <el-select v-model="form.knowledge_of_rehabilitation" placeholder="选择熟悉程度">
                <el-option label="熟悉" value="FAMILIAR" />
                <el-option label="了解" value="KNOWLEDGE" />
                <el-option label="不了解" value="UNKNOWLEDGE" />
              </el-select>
            </el-form-item>
            <el-form-item label="参加孕期教育情况" prop="participation_in_pregnancy_education">
              <el-select
                v-model="form.participation_in_pregnancy_education"
                placeholder="选择参加情况"
              >
                <el-option label="夫妻共同" value="TOGETHER" />
                <el-option label="个人" value="PERSONAL" />
                <el-option label="家属" value="FAMILY" />
              </el-select>
            </el-form-item>
            <el-form-item label="家属对产妇的态度" prop="family_attitude_to_patient">
              <el-select v-model="form.family_attitude_to_patient" placeholder="选择家属态度">
                <el-option label="关心适度" value="APPROPRIATE_CARE" />
                <el-option label="不关心" value="NOT_CARE" />
                <el-option label="过度关心" value="TOO_CARE" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item
            label="家属态度其他描述"
            prop="fatp_pther_description"
            v-if="form.family_attitude_to_patient === 'OTHER'"
          >
            <el-input v-model="form.fatp_pther_description" type="textarea" :rows="2" />
          </el-form-item>
        </div>

        <!-- 生命体征 -->
        <div class="form-section mb-6">
          <h4 class="section-title">生命体征及体格检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="体温 (°C)" prop="temperature">
              <el-input-number
                v-model="form.temperature"
                :precision="1"
                :min="35"
                :max="42"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="血压 (mmHg)" prop="blood_pressure">
              <el-input v-model="form.blood_pressure" placeholder="如 120/80" />
            </el-form-item>
            <el-form-item label="脉搏 (次/分)" prop="pulse">
              <el-input-number v-model="form.pulse" :min="50" :max="150" class="w-full" />
            </el-form-item>
            <el-form-item label="呼吸 (次/分)" prop="respiration">
              <el-input-number v-model="form.respiration" :min="12" :max="30" class="w-full" />
            </el-form-item>
            <el-form-item label="体重 (kg)" prop="weight">
              <el-input-number
                v-model="form.weight"
                :precision="1"
                :min="40"
                :max="150"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="身高 (cm)" prop="height">
              <el-input-number
                v-model="form.height"
                :precision="1"
                :min="140"
                :max="200"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="面色" prop="face_color">
              <el-select v-model="form.face_color" placeholder="选择面色">
                <el-option label="红润" value="RED" />
                <el-option label="苍白" value="WHITE" />
                <el-option label="发黄" value="YELLOW" />
              </el-select>
            </el-form-item>
            <el-form-item label="口腔黏膜" prop="oral_mucosa">
              <el-select v-model="form.oral_mucosa" placeholder="选择口腔状况">
                <el-option label="完整" value="INTACT" />
                <el-option label="溃疡" value="ULCER" />
              </el-select>
            </el-form-item>
            <el-form-item label="活动" prop="activity">
              <el-select v-model="form.activity" placeholder="选择活动状况">
                <el-option label="正常" value="NORMAL" />
                <el-option label="受限" value="LIMITED" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item
            label="活动受限部位"
            prop="activity_limited_part"
            v-if="form.activity === 'LIMITED'"
          >
            <el-input v-model="form.activity_limited_part" />
          </el-form-item>
        </div>

        <!-- 产科检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">产科检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="恶露颜色" prop="lochia_color">
              <el-input v-model="form.lochia_color" />
            </el-form-item>
            <el-form-item label="恶露量" prop="lochia_amount">
              <el-input v-model="form.lochia_amount" />
            </el-form-item>
            <el-form-item label="恶露气味" prop="lochia_smell">
              <el-input v-model="form.lochia_smell" />
            </el-form-item>
            <el-form-item label="宫底高度" prop="uterus_height">
              <el-input v-model="form.uterus_height" />
            </el-form-item>
            <el-form-item label="切口位置" prop="incision_position">
              <el-select v-model="form.incision_position" placeholder="选择切口位置">
                <el-option label="腹壁切口" value="ABDOMINAL_WALL" />
                <el-option label="会阴侧切" value="PERINEAL_INCISION" />
                <el-option label="会阴正中切" value="PERINEAL_MIDDLE" />
                <el-option label="未知" value="UNKNOWN" />
              </el-select>
            </el-form-item>
            <el-form-item label="切口情况" prop="incision_situation">
              <el-select v-model="form.incision_situation" placeholder="选择切口情况">
                <el-option label="正常" value="NORMAL" />
                <el-option label="异常" value="ABNORMAL" />
                <el-option label="未知" value="UNKNOWN" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item
            label="切口异常情况"
            prop="incision_abnormal"
            v-if="form.incision_situation === 'ABNORMAL'"
          >
            <el-checkbox-group v-model="form.incision_abnormal">
              <el-checkbox value="RED">红</el-checkbox>
              <el-checkbox value="SWOLLEN">肿</el-checkbox>
              <el-checkbox value="PAIN">痛</el-checkbox>
              <el-checkbox value="SECRETION">有分泌物</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <!-- 乳房检查 -->
        <div class="form-section mb-6">
          <h4 class="section-title">乳房检查</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="乳房情况" prop="breast_situation">
              <el-checkbox-group v-model="form.breast_situation">
                <el-checkbox value="NORMAL">正常</el-checkbox>
                <el-checkbox value="FUL">充盈</el-checkbox>
                <el-checkbox value="SWOLLEN">肿胀</el-checkbox>
                <el-checkbox value="SECONDARY_BREASTS">副乳</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="乳头情况" prop="nipple_situation">
              <el-select v-model="form.nipple_situation" placeholder="选择乳头情况">
                <el-option label="正常" value="NORMAL" />
                <el-option label="异常" value="ABNORMAL" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item
            label="乳头异常情况"
            prop="nipple_abnormal"
            v-if="form.nipple_situation === 'ABNORMAL'"
          >
            <el-checkbox-group v-model="form.nipple_abnormal">
              <el-checkbox value="INVAGINATED">内陷</el-checkbox>
              <el-checkbox value="CRACKED">皲裂</el-checkbox>
              <el-checkbox value="BLISTER">起泡</el-checkbox>
              <el-checkbox value="DEFORMITY">畸形</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="乳汁情况" prop="milk_situation">
              <el-select v-model="form.milk_situation" placeholder="选择乳汁情况">
                <el-option label="多" value="MORE" />
                <el-option label="中等" value="MIDDLE" />
                <el-option label="少" value="LESS" />
                <el-option label="无" value="NONE" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item label="乳房其他情况" prop="breast_other_situation">
            <el-input v-model="form.breast_other_situation" type="textarea" :rows="2" />
          </el-form-item>
        </div>

        <!-- 其他信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">其他信息</h4>
          <div class="space-y-4">
            <el-form-item label="特殊发现" prop="special_findings">
              <el-input v-model="form.special_findings" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="产妇护理要点" prop="maternal_nursing_points">
              <el-input v-model="form.maternal_nursing_points" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="签名" prop="signature">
              <el-input v-model="form.signature" />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <!-- <el-button @click="handleReset" :disabled="submitting || loading">重置</el-button> -->
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '保存修改' : '提交评估' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { processDateFields, getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  detail: { type: Object, default: null },
  customerId: { type: [String, Number], default: null },
})

const emit = defineEmits(['update:modelValue', 'success'])

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)
const isEdit = computed(() => !!props.detail)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑产妇入住评估' : '新增产妇入住评估'
})

// 日期字段定义
const DATE_FIELDS = ['admission_time', 'assessment_time']

const form = reactive({
  admission_time: '',
  has_discharge_summary: false,
  is_now_medication: false,
  has_allergic_food: false,
  food_allergic_reaction: '',
  has_allergic_drug: false,
  drug_allergic_reaction: '',
  discharge_diagnosis: '',
  pregnancy_complications_and_comorbidities: '',
  delivery_special_situation: '',
  is_acute_disease_treatment_period: false,
  is_infectious_disease_period: false,
  has_psychiatric_disease_history: false,
  has_psychological_disease: false,
  emotion: [],
  appetite: '',
  dietary_requirements: '',
  dro_description: '',
  has_food_preference: false,
  food_preference_description: '',
  allergic_food: '',
  urination: '',
  urination_times: null,
  has_urination_pain: false,
  defecation: '',
  defecation_times: null,
  knowledge_of_rehabilitation: '',
  participation_in_pregnancy_education: '',
  family_attitude_to_patient: '',
  fatp_pther_description: '',
  temperature: null,
  blood_pressure: '',
  pulse: null,
  respiration: null,
  weight: null,
  height: null,
  face_color: '',
  oral_mucosa: '',
  activity: '',
  activity_limited_part: '',
  lochia_color: '',
  lochia_amount: '',
  lochia_smell: '',
  uterus_height: '',
  incision_position: '',
  incision_situation: '',
  incision_abnormal: [],
  breast_situation: [],
  nipple_situation: '',
  nipple_abnormal: [],
  milk_situation: '',
  breast_other_situation: '',
  special_findings: '',
  previous_pregnancy_history: '',
  maternal_nursing_points: '',
  signature: '',
  assessment_time: '',
})

const rules = computed(() => ({
  admission_time: [{ required: true, message: '请选择入所时间', trigger: 'change' }],
  assessment_time: [{ required: true, message: '请选择评估时间', trigger: 'change' }],
  temperature: [{ required: true, message: '请填写体温', trigger: 'blur' }],
  blood_pressure: [{ required: true, message: '请填写血压', trigger: 'blur' }],
  pulse: [{ required: true, message: '请填写脉搏', trigger: 'blur' }],
  respiration: [{ required: true, message: '请填写呼吸频率', trigger: 'blur' }],
  signature: [{ required: true, message: '请填写签名', trigger: 'blur' }],
}))

const { scrollToTop } = useDialogScrollToTop()

// 监听父级字段变化，清空相关子字段
watch(
  () => form.has_allergic_food,
  (newValue) => {
    if (!newValue) {
      form.allergic_food = ''
      form.food_allergic_reaction = ''
    }
  },
)

watch(
  () => form.has_allergic_drug,
  (newValue) => {
    if (!newValue) {
      form.drug_allergic_reaction = ''
    }
  },
)

watch(
  () => form.dietary_requirements,
  (newValue) => {
    if (newValue !== 'OTHER') {
      form.dro_description = ''
    }
  },
)

watch(
  () => form.has_food_preference,
  (newValue) => {
    if (!newValue) {
      form.food_preference_description = ''
    }
  },
)

watch(
  () => form.family_attitude_to_patient,
  (newValue) => {
    if (newValue !== 'OTHER') {
      form.fatp_pther_description = ''
    }
  },
)

watch(
  () => form.activity,
  (newValue) => {
    if (newValue !== 'LIMITED') {
      form.activity_limited_part = ''
    }
  },
)

watch(
  () => form.incision_situation,
  (newValue) => {
    if (newValue !== 'ABNORMAL') {
      form.incision_abnormal = []
    }
  },
)

watch(
  () => form.nipple_situation,
  (newValue) => {
    if (newValue !== 'ABNORMAL') {
      form.nipple_abnormal = []
    }
  },
)

watch(
  () => props.modelValue,
  async (visible) => {
    scrollToTop()
    if (visible) {
      if (props.detail) {
        // 使用传入的详情数据
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)

        // 处理日期字段
        const processedData = processDateFields(props.detail, 'toInput', DATE_FIELDS)
        Object.assign(form, processedData)
        console.log('Form data after assignment:', { ...form })
      } else {
        // 初始化默认值
        resetForm()
        await nextTick()
        const now = getCurrentTime('input')
        form.admission_time = now
        form.assessment_time = now
      }
    }
  },
)

const handleClose = () => {
  visible.value = false
}

const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (['emotion', 'incision_abnormal', 'breast_situation', 'nipple_abnormal'].includes(key)) {
      form[key] = []
    } else if (
      [
        'has_discharge_summary',
        'is_now_medication',
        'has_allergic_food',
        'has_allergic_drug',
        'is_acute_disease_treatment_period',
        'is_infectious_disease_period',
        'has_psychiatric_disease_history',
        'has_psychological_disease',
        'has_urination_pain',
        'has_food_preference',
      ].includes(key)
    ) {
      form[key] = false
    } else if (
      [
        'temperature',
        'pulse',
        'respiration',
        'weight',
        'height',
        'urination_times',
        'defecation_times',
      ].includes(key)
    ) {
      form[key] = null
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 处理提交数据的日期格式
    const submitData = processDateFields(form, 'toAPI', DATE_FIELDS)
    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      res = await post(`customer-service/mcia-record/create/${props.customerId}/`, submitData)
    } else {
      res = await put(`customer-service/mcia-record/update/${props.customerId}/`, submitData)
    }

    ElMessage.success(isEdit.value ? '产妇入住评估更新成功' : '产妇入住评估提交成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    let errorMsg = ''
    if (error.message || error.msg) {
      errorMsg = error.message || error.msg
    } else if (error.data) {
      errorMsg = Object.values(error.data).flat()[0]
    } else {
      errorMsg = Object.values(error).flat()[0]
    }
    ElMessage.error(errorMsg || '操作失败，请重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
