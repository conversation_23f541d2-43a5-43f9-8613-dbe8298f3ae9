<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="maternal-form-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="maternal-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-form-item label="姓名" prop="name" required>
              <el-input
                v-model="formData.name"
                placeholder="请输入姓名"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="联系电话" prop="phone" required>
              <el-input v-model="formData.phone" placeholder="请输入联系电话" maxlength="11" />
            </el-form-item>

            <el-form-item label="出生日期" prop="birth_date" required>
              <el-date-picker
                v-model="formData.birth_date"
                type="date"
                placeholder="请选择出生日期"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>

            <el-form-item label="性别" prop="gender" required>
              <el-radio-group v-model="formData.gender">
                <el-radio
                  v-for="option in GENDER_OPTIONS"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="血型" prop="blood_type">
              <el-select
                v-model="formData.blood_type"
                placeholder="请选择血型"
                class="w-full"
                clearable
              >
                <el-option
                  v-for="option in BLOOD_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="身份证号" prop="identity_number" required>
              <el-input
                v-model="formData.identity_number"
                placeholder="请输入身份证号"
                maxlength="18"
              />
            </el-form-item>

            <el-form-item label="民族" prop="ethnicity">
              <el-input v-model="formData.ethnicity" placeholder="请输入民族" maxlength="20" />
            </el-form-item>

            <el-form-item label="籍贯" prop="native_place">
              <el-input v-model="formData.native_place" placeholder="请输入籍贯" maxlength="50" />
            </el-form-item>
          </div>

          <!-- 家庭地址单独一行，占满整行 -->
          <el-form-item label="家庭地址" prop="home_address">
            <el-input
              v-model="formData.home_address"
              type="textarea"
              :rows="3"
              placeholder="请输入详细家庭地址"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 紧急联系人信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">紧急联系人信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-form-item label="紧急联系人" prop="emergency_contact" required>
              <el-input
                v-model="formData.emergency_contact"
                placeholder="请输入紧急联系人姓名"
                maxlength="50"
              />
            </el-form-item>

            <el-form-item label="联系电话" prop="emergency_contact_phone" required>
              <el-input
                v-model="formData.emergency_contact_phone"
                placeholder="请输入紧急联系人电话"
                maxlength="11"
              />
            </el-form-item>

            <el-form-item label="关系" prop="emergency_contact_relation" required>
              <el-select
                v-model="formData.emergency_contact_relation"
                placeholder="请选择关系"
                class="w-full"
              >
                <el-option
                  v-for="option in EMERGENCY_CONTACT_RELATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '新增' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'
import {
  GENDER_OPTIONS,
  BLOOD_TYPE_OPTIONS,
  EMERGENCY_CONTACT_RELATION_OPTIONS,
} from '@/utils/constants.js'

const emit = defineEmits(['update:modelValue', 'success'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
  rowData: {
    type: Object,
    default: null,
  },
})

// 响应式数据
const formRef = ref(null)
const submitLoading = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新增孕产妇档案' : '编辑孕产妇档案'
})

// 表单数据
const defaultFormData = {
  name: '',
  phone: '',
  birth_date: '',
  gender: 2, // 默认女性
  blood_type: '',
  identity_number: '',
  ethnicity: '',
  native_place: '',
  home_address: '',
  emergency_contact: '',
  emergency_contact_phone: '',
  emergency_contact_relation: '',
}

const formData = reactive({ ...defaultFormData })

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  birth_date: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
  gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
  identity_number: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    {
      pattern:
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: '请输入正确的身份证号',
      trigger: 'blur',
    },
  ],
  emergency_contact: [
    { required: true, message: '请输入紧急联系人', trigger: 'blur' },
    { min: 2, max: 50, message: '紧急联系人姓名长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  emergency_contact_phone: [
    { required: true, message: '请输入紧急联系人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  emergency_contact_relation: [{ required: true, message: '请选择关系', trigger: 'change' }],
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, defaultFormData)
  formRef.value?.resetFields()
}

// 加载数据（编辑模式）
const loadData = () => {
  if (props.mode === 'edit' && props.rowData) {
    // 直接使用传入的行数据填充表单
    Object.assign(formData, {
      name: props.rowData.name || '',
      phone: props.rowData.phone || '',
      birth_date: props.rowData.birth_date || '',
      gender: props.rowData.gender || 2,
      blood_type: props.rowData.blood_type || '',
      identity_number: props.rowData.identity_number || '',
      ethnicity: props.rowData.ethnicity || '',
      native_place: props.rowData.native_place || '',
      home_address: props.rowData.home_address || '',
      emergency_contact: props.rowData.emergency_contact || '',
      emergency_contact_phone: props.rowData.emergency_contact_phone || '',
      emergency_contact_relation: props.rowData.emergency_contact_relation || '',
    })
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    submitLoading.value = true

    // 准备提交数据，排除不需要的字段
    const submitData = {
      name: formData.name,
      phone: formData.phone,
      birth_date: formData.birth_date,
      ethnicity: formData.ethnicity,
      native_place: formData.native_place,
      gender: formData.gender,
      blood_type: formData.blood_type,
      home_address: formData.home_address,
      identity_number: formData.identity_number,
      emergency_contact: formData.emergency_contact,
      emergency_contact_phone: formData.emergency_contact_phone,
      emergency_contact_relation: formData.emergency_contact_relation,
    }

    if (props.mode === 'add') {
      await post('user/maternity/create/', submitData)
      ElMessage.success('新增孕产妇档案成功')
    } else {
      await put(`user/maternity/update/${props.rowData.uid}/`, submitData)
      ElMessage.success('更新孕产妇档案成功')
    }

    emit('success')
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  dialogVisible.value = false
}

// 监听对话框显示
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      if (props.mode === 'edit') {
        loadData()
      } else {
        resetForm()
      }
    }
  },
)
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #ec4899;
  background-color: #ec4899;
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: #ec4899;
}
</style>
