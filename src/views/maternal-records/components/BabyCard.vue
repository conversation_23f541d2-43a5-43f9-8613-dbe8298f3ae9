<template>
  <div class="baby-card-container space-y-4">
    <!-- 婴儿标题和切换控制 -->
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-3">
        <User class="w-5 h-5 text-pink-500" />
        <span class="text-lg font-semibold text-gray-800">新生儿状态追踪</span>
      </div>
      <!-- 婴儿切换控制 -->
      <div v-if="hasMultipleBabies" class="flex items-center gap-2">
        <button
          @click="previousBaby"
          :disabled="!canShowPrevious"
          class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
        >
          <ArrowLeft class="w-4 h-4 text-gray-600" />
        </button>
        <span class="text-sm text-gray-500 px-2">
          {{ currentBabyIndex + 1 }} / {{ babies.length }}
        </span>
        <button
          @click="nextBaby"
          :disabled="!canShowNext"
          class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
        >
          <ArrowRight class="w-4 h-4 text-gray-600" />
        </button>
      </div>
    </div>

    <div v-if="currentBaby" class="space-y-4">
      <!-- 婴儿信息卡片 -->
      <div
        class="baby-card bg-white rounded-xl p-6 border border-pink-100 shadow-sm transition-all duration-300"
      >
        <div class="flex justify-between items-start mb-4">
          <div>
            <h4 class="text-xl font-bold text-gray-800 mb-1">{{ currentBaby.baby_info.name }}</h4>
            <div class="text-sm text-gray-600 space-y-1">
              <div>
                {{ currentBaby.baby_info.gender_display }} · 出生{{
                  currentBaby.baby_info.days_since_birth
                }}天
              </div>
              <div>出生时间: {{ formatBirthTime(currentBaby.baby_info.birth_time) }}</div>
            </div>
          </div>
          <div v-if="hasMultipleBabies" class="text-right">
            <span
              class="inline-block text-xs bg-gradient-to-r from-pink-100 to-purple-100 text-pink-700 px-3 py-1 rounded-full mb-2 font-medium"
            >
              {{ currentBaby.baby_info.hand_card_number }}
            </span>
          </div>
        </div>

        <!-- 核心指标 -->
        <div class="stat-grid grid grid-cols-3 gap-3 mb-4">
          <!-- 黄疸值 -->
          <div
            class="stat-item bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-3 text-center border border-amber-100 shadow-sm"
          >
            <div class="stat-value text-xl font-bold text-amber-600 mb-1">
              {{ currentBaby.last_record?.jaundice || '-' }}
              <span v-if="currentBaby.last_record?.jaundice" class="text-sm font-normal"
                >mg/dL</span
              >
            </div>
            <div class="stat-label text-xs text-gray-500">黄疸值</div>
            <div class="text-xs text-gray-400 mt-1">
              {{ getJaundiceStatus(currentBaby.last_record?.jaundice) }}
            </div>
          </div>

          <!-- 体重 -->
          <div
            class="stat-item bg-gradient-to-br from-pink-50 to-rose-50 rounded-lg p-3 text-center border border-pink-100 shadow-sm"
          >
            <div class="stat-value text-xl font-bold text-pink-600 mb-1">
              {{ currentBaby.last_record?.weight || currentBaby.baby_info.birth_weight }}
              <span class="text-sm font-normal">g</span>
            </div>
            <div class="stat-label text-xs text-gray-500">体重</div>
            <div
              v-if="currentBaby.weight_growth !== undefined"
              class="text-xs mt-1"
              :class="currentBaby.weight_growth >= 0 ? 'text-green-600' : 'text-red-600'"
            >
              {{ currentBaby.weight_growth >= 0 ? '+' : '' }}{{ currentBaby.weight_growth }}g
            </div>
          </div>

          <!-- 体温 -->
          <div
            class="stat-item bg-gradient-to-br from-emerald-50 to-teal-50 rounded-lg p-3 text-center border border-emerald-100 shadow-sm"
          >
            <div class="stat-value text-xl font-bold text-emerald-600 mb-1">
              {{ currentBaby.last_record?.temperature || '-' }}
              <span v-if="currentBaby.last_record?.temperature" class="text-sm font-normal"
                >°C</span
              >
            </div>
            <div class="stat-label text-xs text-gray-500">体温</div>
            <div class="text-xs text-gray-400 mt-1">
              {{ getTemperatureStatus(currentBaby.last_record?.temperature) }}
            </div>
          </div>
        </div>

        <!-- 最后记录信息 -->
        <div
          class="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-3 text-center border border-gray-100"
        >
          <div class="text-sm text-gray-600">最后记录时间</div>
          <div class="text-sm font-medium text-pink-600 mt-1">
            {{ currentBaby.last_record_date || '暂无记录' }}
          </div>
        </div>
      </div>

      <!-- 趋势图部分 -->
      <div
        v-if="hasTrendData"
        class="trend-chart bg-white rounded-xl p-6 border border-pink-100 shadow-sm"
      >
        <!-- 图表切换标签 -->
        <div class="flex items-center justify-end mb-4">
          <div
            class="flex bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-1 border border-gray-100"
          >
            <button
              v-for="(option, key) in trendOptions"
              :key="key"
              @click="selectedTrendType = key"
              :class="[
                'px-3 py-1 text-sm font-medium rounded-md transition-all duration-200',
                selectedTrendType === key
                  ? 'bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-pink-500 hover:bg-white',
              ]"
            >
              {{ option.label }}
            </button>
          </div>
        </div>

        <!-- ECharts 图表 -->
        <div
          class="chart-container bg-gradient-to-br from-gray-50 to-slate-50 rounded-lg border border-gray-100"
          style="height: 300px"
        >
          <v-chart :option="chartOption" :autoresize="true" style="height: 100%; width: 100%" />
        </div>

        <!-- 时间范围提示 -->
        <div
          class="text-center text-xs text-gray-500 mt-3 py-2 bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg"
        >
          数据时间范围: {{ formatDateRange() }}
        </div>
      </div>

      <!-- 无趋势数据时的提示 -->
      <div v-else class="trend-chart bg-white rounded-xl p-6 border border-pink-100 shadow-sm">
        <div class="text-center py-8">
          <div
            class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center border border-gray-100"
          >
            <Monitor class="w-8 h-8 text-gray-400" />
          </div>
          <div class="text-sm font-medium text-gray-600 mb-2">暂无趋势数据</div>
          <div
            class="text-xs text-gray-400 bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg py-2 px-4 inline-block"
          >
            需要至少2条记录才能生成趋势图
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons p-4">
        <div class="flex gap-3">
          <el-button
            type="primary"
            size="default"
            @click="handleAdd"
            class="flex-1 bg-gradient-to-r from-pink-500 to-pink-600 border-pink-500 hover:from-pink-600 hover:to-pink-700 shadow-md hover:shadow-lg transition-all duration-300"
          >
            <Plus class="w-4 h-4 mr-2" />
            添加新生儿
          </el-button>
          <el-button
            type="success"
            size="default"
            @click="handleUpdate"
            class="flex-1 bg-gradient-to-r from-emerald-500 to-emerald-600 border-emerald-500 hover:from-emerald-600 hover:to-emerald-700 shadow-md hover:shadow-lg transition-all duration-300"
          >
            <Edit class="w-4 h-4 mr-2" />
            更新信息
          </el-button>
          <el-button
            type="danger"
            size="default"
            @click="handleDelete"
            :loading="deleteLoading"
            class="flex-1 bg-gradient-to-r from-red-500 to-red-600 border-red-500 hover:from-red-600 hover:to-red-700 shadow-md hover:shadow-lg transition-all duration-300"
          >
            <Delete class="w-4 h-4 mr-2" />
            删除
          </el-button>
        </div>
      </div>
    </div>

    <div v-else class="bg-white rounded-xl p-8 border border-pink-100 shadow-sm">
      <div class="text-center py-8">
        <div
          class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center border border-gray-100"
        >
          <User class="w-10 h-10 text-gray-400" />
        </div>
        <div class="text-lg font-medium text-gray-600 mb-4">暂无新生儿数据</div>
        <el-button
          type="primary"
          size="default"
          @click="handleAdd"
          class="bg-gradient-to-r from-pink-500 to-pink-600 border-pink-500 hover:from-pink-600 hover:to-pink-700 shadow-lg hover:shadow-xl transition-all duration-300"
        >
          <Plus class="w-4 h-4 mr-2" />
          添加新生儿
        </el-button>
      </div>
    </div>

    <!-- 新生儿信息表单对话框 -->
    <BabyFormDialog
      v-model="showBabyDialog"
      :baby-id="currentBabyId"
      :aid="aid"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Monitor, Plus, Edit, Delete, User, ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { del } from '@/utils/request.js'
import BabyFormDialog from './BabyFormDialog.vue'
import { format } from 'date-fns'
// ECharts imports
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'

// 注册ECharts组件
use([CanvasRenderer, LineChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent])

// 健康指标阈值常量
const HEALTH_THRESHOLDS = {
  jaundice: {
    normal: 5,
    mild: 12,
    moderate: 15,
  },
  temperature: {
    low: 36,
    normal: 37.5,
    fever: 38.5,
  },
}

// 默认趋势图类型
const DEFAULT_TREND_TYPE = 'weight_trend'

const props = defineProps({
  aid: {
    type: String,
    default: '',
  },
  babies: {
    type: Array,
    default: () => [],
    validator: (value) => Array.isArray(value),
  },
})

const emit = defineEmits(['add', 'update', 'delete', 'add-record', 'reload'])

// 格式化出生时间
const formatBirthTime = (birthTime) => {
  if (!birthTime) return '-'
  try {
    return format(new Date(birthTime), 'yyyy-MM-dd HH:mm')
  } catch {
    return birthTime
  }
}

// 获取黄疸值状态
const getJaundiceStatus = (value) => {
  if (!value || isNaN(Number(value))) return '未记录'
  const numValue = Number(value)
  if (numValue < HEALTH_THRESHOLDS.jaundice.normal) return '正常'
  if (numValue <= HEALTH_THRESHOLDS.jaundice.mild) return '轻度'
  if (numValue <= HEALTH_THRESHOLDS.jaundice.moderate) return '中度'
  return '重度'
}

// 获取体温状态
const getTemperatureStatus = (value) => {
  if (!value || isNaN(Number(value))) return '未记录'
  const numValue = Number(value)
  if (numValue < HEALTH_THRESHOLDS.temperature.low) return '偏低'
  if (numValue <= HEALTH_THRESHOLDS.temperature.normal) return '正常'
  if (numValue <= HEALTH_THRESHOLDS.temperature.fever) return '轻度发热'
  return '高热'
}

// 当前显示的婴儿索引
const currentBabyIndex = ref(0)

// 当前显示的婴儿
const currentBaby = computed(() => {
  const babies = props.babies
  if (!babies || babies.length === 0) return null
  return babies[currentBabyIndex.value] || null
})

// 检查是否有趋势数据
const hasTrendData = computed(() => {
  return (
    currentBaby.value?.trend_data &&
    Object.keys(currentBaby.value.trend_data).some(
      (key) =>
        key !== 'date_range' &&
        Array.isArray(currentBaby.value.trend_data[key]) &&
        currentBaby.value.trend_data[key].length > 0,
    )
  )
})

// 是否可以显示上一个婴儿
const canShowPrevious = computed(() => currentBabyIndex.value > 0)

// 是否可以显示下一个婴儿
const canShowNext = computed(() => currentBabyIndex.value < props.babies.length - 1)

// 是否有多个婴儿
const hasMultipleBabies = computed(() => props.babies && props.babies.length > 1)

// 切换到上一个婴儿
const previousBaby = () => {
  if (currentBabyIndex.value > 0) {
    currentBabyIndex.value--
  }
}

// 切换到下一个婴儿
const nextBaby = () => {
  if (currentBabyIndex.value < props.babies.length - 1) {
    currentBabyIndex.value++
  }
}

// 对话框状态
const showBabyDialog = ref(false)
const currentBabyId = ref(null)
const deleteLoading = ref(false)

// 趋势图相关状态
const selectedTrendType = ref(DEFAULT_TREND_TYPE)

// 趋势图配置选项
const trendOptions = {
  weight_trend: {
    label: '体重',
    unit: 'g',
    color: '#ec4899',
    yAxisName: '体重 (g)',
  },
  temperature_trend: {
    label: '体温',
    unit: '°C',
    color: '#10b981',
    yAxisName: '体温 (°C)',
  },
  jaundice_trend: {
    label: '黄疸',
    unit: 'mg/dL',
    color: '#f59e0b',
    yAxisName: '黄疸值 (mg/dL)',
  },
}

// 获取当前选中的趋势数据
const getCurrentTrendData = () => {
  if (!currentBaby.value?.trend_data) return []
  return currentBaby.value.trend_data[selectedTrendType.value] || []
}

// 格式化日期范围
const formatDateRange = () => {
  if (!currentBaby.value?.trend_data?.date_range) return ''
  const { start_date, end_date } = currentBaby.value.trend_data.date_range
  return `${start_date} 至 ${end_date}`
}

// ECharts 图表配置
const chartOption = computed(() => {
  const data = getCurrentTrendData()
  const config = trendOptions[selectedTrendType.value]

  if (!data.length) return {}

  return {
    title: {
      text: `${config.label}趋势图`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#374151',
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: config.color,
      borderWidth: 1,
      textStyle: {
        color: '#374151',
      },
      formatter: (params) => {
        const point = params[0]
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${point.name}</div>
            <div style="color: ${config.color};">
              <span style="display: inline-block; width: 10px; height: 10px; background-color: ${config.color}; border-radius: 50%; margin-right: 8px;"></span>
              ${config.label}: ${point.value}${config.unit}
            </div>
          </div>
        `
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map((item) => item.display_date),
      axisLine: {
        lineStyle: {
          color: '#e5e7eb',
        },
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
      },
    },
    yAxis: {
      type: 'value',
      name: config.yAxisName,
      nameTextStyle: {
        color: '#6b7280',
        fontSize: 12,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#6b7280',
        fontSize: 11,
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: config.label,
        type: 'line',
        data: data.map((item) => item.value),
        smooth: true,
        lineStyle: {
          color: config.color,
          width: 3,
        },
        itemStyle: {
          color: config.color,
          borderWidth: 2,
          borderColor: '#fff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: `${config.color}20`,
              },
              {
                offset: 1,
                color: `${config.color}05`,
              },
            ],
          },
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            borderWidth: 4,
            shadowBlur: 10,
            shadowColor: config.color,
          },
        },
      },
    ],
  }
})

const handleAdd = () => {
  currentBabyId.value = null
  showBabyDialog.value = true
}

const handleUpdate = () => {
  currentBabyId.value = currentBaby.value.baby_info.nid
  showBabyDialog.value = true
}

const handleDelete = async () => {
  if (!currentBaby.value?.baby_info?.nid) {
    ElMessage.error('暂无新生儿可删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除新生儿"${currentBaby.value.baby_info.name}"吗？删除后将无法恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    )

    deleteLoading.value = true
    await del(`customer-service/newborn/delete/${currentBaby.value.baby_info.nid}/`)

    ElMessage.success('新生儿删除成功')

    // 处理删除后的索引变化
    const totalBabies = props.babies.length

    if (totalBabies <= 1) {
      // 如果只有一个新生儿，删除后重置索引为0
      currentBabyIndex.value = 0
    } else if (currentBabyIndex.value >= totalBabies - 1) {
      // 如果删除的是最后一个，索引向前移动
      currentBabyIndex.value = totalBabies - 2
    }
    // 如果删除的不是最后一个，保持当前索引不变（这样会自动显示下一个）

    emit('reload')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除新生儿失败:', error)
      const errorMessage =
        error?.response?.data?.message || error?.message || '删除新生儿失败，请稍后重试'
      ElMessage.error(errorMessage)
    }
  } finally {
    deleteLoading.value = false
  }
}

const handleDialogSuccess = () => {
  showBabyDialog.value = false
  emit('reload')
}

// 监听babies数组变化，校验和调整currentBabyIndex
watch(
  () => props.babies,
  (newBabies) => {
    if (!newBabies || newBabies.length === 0) {
      currentBabyIndex.value = 0
    } else if (currentBabyIndex.value >= newBabies.length) {
      // 如果当前索引超出范围，调整到最后一个
      currentBabyIndex.value = newBabies.length - 1
    }
  },
  { immediate: true },
)

// 监听当前婴儿变化，重置趋势图选择
watch(
  () => currentBaby.value,
  () => {
    // 当切换婴儿时，默认显示体重趋势
    selectedTrendType.value = DEFAULT_TREND_TYPE
  },
)
</script>

<style scoped>
.baby-card {
  transition: all 0.3s ease;
  animation: fadeIn 0.3s ease-out;
}

.baby-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
  border-color: rgba(236, 72, 153, 0.3);
}

.stat-item {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  transition: left 0.6s ease;
}

.stat-item:hover::before {
  left: 100%;
}

.trend-chart {
  transition: all 0.3s ease;
}

.trend-chart:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(236, 72, 153, 0.15);
  border-color: rgba(236, 72, 153, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 自定义按钮样式，避免使用蓝色 */
.action-buttons :deep(.el-button--primary) {
  --el-color-primary: #ec4899;
  --el-color-primary-light-3: #f472b6;
  --el-color-primary-light-5: #f9a8d4;
  --el-color-primary-light-7: #fbbf24;
  --el-color-primary-light-8: #fde68a;
  --el-color-primary-light-9: #fef3c7;
  --el-color-primary-dark-2: #db2777;
}

.action-buttons :deep(.el-button--success) {
  --el-color-success: #10b981;
  --el-color-success-light-3: #34d399;
  --el-color-success-light-5: #6ee7b7;
  --el-color-success-light-7: #a7f3d0;
  --el-color-success-light-8: #d1fae5;
  --el-color-success-light-9: #ecfdf5;
  --el-color-success-dark-2: #059669;
}

/* 切换按钮样式 */
.baby-card-container button {
  transition: all 0.2s ease;
}

.baby-card-container button:hover:not(:disabled) {
  transform: scale(1.05);
}

.baby-card-container button:active:not(:disabled) {
  transform: scale(0.95);
}

/* 趋势图样式 */
.chart-container {
  padding: 12px;
  transition: all 0.3s ease;
}

.chart-container:hover {
  background: linear-gradient(to bottom right, #f8fafc, #f1f5f9) !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .action-buttons {
    flex-direction: column;
    gap: 8px;
  }

  .stat-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .baby-card-container > div:first-child {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .baby-card-container > div:first-child > div:last-child {
    width: 100%;
    justify-content: center;
  }

  .chart-container {
    height: 250px !important;
  }

  .trend-chart .grid {
    grid-template-columns: 1fr;
  }
}
</style>
