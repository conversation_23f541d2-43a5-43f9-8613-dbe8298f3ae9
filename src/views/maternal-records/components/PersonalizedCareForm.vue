<template>
  <div class="personalized-care-form">
    <div class="form-header mb-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-2">个性化母婴护理计划</h3>
      <p class="text-sm text-gray-600">根据客户具体情况制定的个性化护理方案</p>
    </div>

    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="space-y-6">
      <!-- 客户基本信息 -->
      <div class="form-section bg-gray-50 p-4 rounded-lg">
        <h4 class="text-md font-medium text-gray-700 mb-4 flex items-center gap-2">
          <InfoFilled as Info />
          客户基本信息 (自动带入)
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <el-form-item label="产妇姓名">
            <el-input v-model="formData.motherName" readonly class="readonly-input" />
          </el-form-item>
          <el-form-item label="新生儿姓名">
            <el-input v-model="formData.babyName" readonly class="readonly-input" />
          </el-form-item>
          <el-form-item label="计划制定日期" prop="planDate">
            <el-date-picker
              v-model="formData.planDate"
              type="date"
              placeholder="请选择日期"
              class="w-full"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 产妇护理计划 -->
      <div class="form-section bg-gray-50 p-4 rounded-lg">
        <h4 class="text-md font-medium text-gray-700 mb-4 flex items-center gap-2">
          <User />
          产妇护理计划
        </h4>

        <!-- 产后恢复 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">产后恢复</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="恢复目标">
              <el-input
                v-model="formData.maternalCare.recovery.goals"
                type="textarea"
                :rows="3"
                placeholder="如：伤口愈合良好，恶露正常，子宫复旧顺利"
              />
            </el-form-item>
            <el-form-item label="护理措施">
              <el-input
                v-model="formData.maternalCare.recovery.measures"
                type="textarea"
                :rows="3"
                placeholder="如：每日伤口护理，观察恶露，指导产后运动"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 乳房护理与母乳喂养 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">乳房护理与母乳喂养</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标">
              <el-input
                v-model="formData.maternalCare.breastfeeding.goals"
                type="textarea"
                :rows="3"
                placeholder="如：成功实现纯母乳喂养，预防乳腺炎"
              />
            </el-form-item>
            <el-form-item label="措施">
              <el-input
                v-model="formData.maternalCare.breastfeeding.measures"
                type="textarea"
                :rows="3"
                placeholder="如：指导正确哺乳姿势，按需哺乳，乳房按摩"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 营养膳食 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">营养膳食</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标">
              <el-input
                v-model="formData.maternalCare.nutrition.goals"
                type="textarea"
                :rows="3"
                placeholder="如：均衡营养，促进泌乳和恢复"
              />
            </el-form-item>
            <el-form-item label="特殊需求/禁忌">
              <el-input
                v-model="formData.maternalCare.nutrition.notes"
                type="textarea"
                :rows="3"
                placeholder="如：对海鲜过敏，少油少盐"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 心理调适与健康教育 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">心理调适与健康教育</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标">
              <el-input
                v-model="formData.maternalCare.psychology.goals"
                type="textarea"
                :rows="3"
                placeholder="如：保持良好情绪，掌握育儿知识"
              />
            </el-form-item>
            <el-form-item label="措施">
              <el-input
                v-model="formData.maternalCare.psychology.measures"
                type="textarea"
                :rows="3"
                placeholder="如：提供心理支持，安排健康宣教课程"
              />
            </el-form-item>
          </div>
        </div>
      </div>

      <!-- 新生儿护理计划 -->
      <div class="form-section bg-gray-50 p-4 rounded-lg">
        <h4 class="text-md font-medium text-gray-700 mb-4 flex items-center gap-2">
          <User as Baby />
          新生儿护理计划
        </h4>

        <!-- 喂养 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">喂养</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标">
              <el-input
                v-model="formData.babyCare.feeding.goals"
                type="textarea"
                :rows="3"
                placeholder="如：按需喂养，体重稳定增长"
              />
            </el-form-item>
            <el-form-item label="措施">
              <el-input
                v-model="formData.babyCare.feeding.measures"
                type="textarea"
                :rows="3"
                placeholder="如：观察喂养情况，记录奶量"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 日常护理 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">日常护理 (皮肤、脐部、臀部)</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标">
              <el-input
                v-model="formData.babyCare.dailyCare.goals"
                type="textarea"
                :rows="3"
                placeholder="如：皮肤清洁干燥，脐部护理得当，预防红臀"
              />
            </el-form-item>
            <el-form-item label="措施">
              <el-input
                v-model="formData.babyCare.dailyCare.measures"
                type="textarea"
                :rows="3"
                placeholder="如：每日沐浴，脐部消毒，及时更换尿布"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 黄疸监测与护理 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">黄疸监测与护理</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标">
              <el-input
                v-model="formData.babyCare.jaundice.goals"
                type="textarea"
                :rows="3"
                placeholder="如：监测黄疸指数，预防高胆红素血症"
              />
            </el-form-item>
            <el-form-item label="措施">
              <el-input
                v-model="formData.babyCare.jaundice.measures"
                type="textarea"
                :rows="3"
                placeholder="如：每日监测经皮胆红素，必要时日光浴或蓝光治疗指导"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 早期启蒙与互动 -->
        <div class="plan-item mb-6 p-4 bg-white rounded-lg">
          <h5 class="text-sm font-medium text-pink-600 mb-3">早期启蒙与互动</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="目标">
              <el-input
                v-model="formData.babyCare.stimulation.goals"
                type="textarea"
                :rows="3"
                placeholder="如：促进感官发育"
              />
            </el-form-item>
            <el-form-item label="措施">
              <el-input
                v-model="formData.babyCare.stimulation.measures"
                type="textarea"
                :rows="3"
                placeholder="如：抚触，听音乐，视觉追踪练习"
              />
            </el-form-item>
          </div>
        </div>
      </div>

      <!-- 计划执行与评估 -->
      <div class="form-section bg-gray-50 p-4 rounded-lg">
        <h4 class="text-md font-medium text-gray-700 mb-4 flex items-center gap-2">
          <Document as FileText />
          计划执行与评估
        </h4>
        <div class="grid grid-cols-1 gap-4">
          <el-form-item label="执行情况记录与调整说明">
            <el-input
              v-model="formData.executionNotes"
              type="textarea"
              :rows="4"
              placeholder="记录计划的执行情况，遇到的问题，以及根据实际情况对计划的调整"
            />
          </el-form-item>
          <el-form-item label="下次评估/更新日期">
            <el-date-picker
              v-model="formData.nextReviewDate"
              type="date"
              placeholder="请选择日期"
              class="w-full"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions flex justify-end gap-3 pt-4">
        <el-button @click="savePlan">保存计划</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button
          type="primary"
          @click="submitForm"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          提交/更新计划
        </el-button>
        <el-button @click="printPlan">打印计划</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  InfoFilled as Info,
  User,
  User as Baby,
  Document as FileText,
} from '@element-plus/icons-vue'

const formRef = ref()

const formData = reactive({
  motherName: '王芳',
  babyName: '小宝',
  planDate: new Date(),
  maternalCare: {
    recovery: {
      goals: '',
      measures: '',
    },
    breastfeeding: {
      goals: '',
      measures: '',
    },
    nutrition: {
      goals: '',
      notes: '',
    },
    psychology: {
      goals: '',
      measures: '',
    },
  },
  babyCare: {
    feeding: {
      goals: '',
      measures: '',
    },
    dailyCare: {
      goals: '',
      measures: '',
    },
    jaundice: {
      goals: '',
      measures: '',
    },
    stimulation: {
      goals: '',
      measures: '',
    },
  },
  executionNotes: '',
  nextReviewDate: null,
})

const rules = {
  planDate: [{ required: true, message: '请选择计划制定日期', trigger: 'change' }],
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    ElMessage.success('个性化护理计划提交成功！')
    console.log('护理计划数据:', formData)
  } catch (error) {
    ElMessage.error('请检查表单信息是否完整')
  }
}

const savePlan = () => {
  ElMessage.success('护理计划保存成功！')
  console.log('保存护理计划:', formData)
}

const resetForm = () => {
  formRef.value?.resetFields()
  // 保留基本信息，重置其他字段
  Object.keys(formData).forEach((key) => {
    if (!['motherName', 'babyName'].includes(key)) {
      if (key === 'planDate') {
        formData[key] = new Date()
      } else if (key === 'nextReviewDate') {
        formData[key] = null
      } else if (typeof formData[key] === 'object') {
        resetNestedObject(formData[key])
      } else {
        formData[key] = ''
      }
    }
  })
}

const resetNestedObject = (obj) => {
  Object.keys(obj).forEach((key) => {
    if (typeof obj[key] === 'object') {
      resetNestedObject(obj[key])
    } else {
      obj[key] = ''
    }
  })
}

const printPlan = () => {
  ElMessage.info('正在准备打印护理计划...')
}
</script>

<style scoped>
.form-section {
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.form-section:hover {
  border-color: #f3a0c0;
  background-color: #fdf7f0;
}

.plan-item {
  border-bottom: 1px dotted #f3a0c0;
}

.plan-item:last-child {
  border-bottom: none;
}

.form-actions {
  border-top: 1px solid #e5e7eb;
}

.readonly-input :deep(.el-input__inner) {
  background-color: #f0f0f0;
  cursor: not-allowed;
}
</style>
