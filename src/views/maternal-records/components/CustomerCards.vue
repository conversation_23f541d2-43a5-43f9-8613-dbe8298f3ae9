<template>
  <div class="customer-cards-container">
    <!-- 卡片网格 -->
    <div
      v-loading="loading"
      element-loading-text="加载中..."
      class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 min-h-[400px]"
    >
      <div
        v-for="customer in tableData"
        :key="customer.id"
        class="rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer flex flex-col"
        :class="{
          'bg-gradient-to-br from-pink-50 to-white border border-pink-200 shadow-pink-100':
            customer.checkInStatus === '已入住',
          'bg-gradient-to-br from-blue-50 to-white border border-blue-200':
            customer.checkInStatus === '已预定',
          'bg-gradient-to-br from-gray-50 to-white border border-gray-200':
            customer.checkInStatus === '已出院',
        }"
        @click="handleRowClick(customer)"
      >
        <!-- 客户头部信息 -->
        <div class="flex items-center justify-between mb-4 pb-4 border-b border-gray-200">
          <div class="flex items-center">
            <h3 class="text-lg font-semibold text-gray-900">{{ customer.customerName }}</h3>
            <el-tag v-if="customer.needAttention" type="danger" size="small" class="ml-2">
              需关注
            </el-tag>
            <el-tag v-if="customer.isMultiple" type="info" size="small" class="ml-2">多胞胎</el-tag>
          </div>
          <el-tag :type="getStatusType(customer.checkInStatus)" class="font-medium">
            {{ customer.checkInStatus }}
          </el-tag>
        </div>

        <!-- 客户详细信息 -->
        <div class="grid grid-cols-2 gap-4 mb-4 text-sm flex-1">
          <div>
            <span class="text-gray-600">新生儿：</span>
            <span class="text-gray-900">
              <div v-for="baby in customer.newborns" :key="baby" class="text-gray-700">
                {{ baby }}
              </div>
            </span>
          </div>
          <div>
            <span class="text-gray-600">房间号：</span>
            <span class="text-gray-900">{{ customer.roomNumber }}</span>
          </div>
          <div>
            <span class="text-gray-600">预计入住：</span>
            <span class="text-gray-900">{{ formatDate(customer.expectedCheckInDate) }}</span>
          </div>
          <div>
            <span class="text-gray-600">预计退房：</span>
            <span class="text-gray-900">{{ formatDate(customer.expectedCheckOutDate) }}</span>
          </div>
          <div v-if="customer.actualCheckInDate" class="col-span-2">
            <span class="text-gray-600">实际入住：</span>
            <span class="text-green-600">{{ formatDate(customer.actualCheckInDate) }}</span>
          </div>
          <div class="col-span-2">
            <span class="text-gray-600">主责护理：</span>
            <span class="text-gray-900">{{ customer.mainNurse }}</span>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="text-center bg-gray-50 rounded-lg p-3">
            <div class="text-lg font-bold text-gray-900">{{ customer.stayDays }}</div>
            <div class="text-xs text-gray-600">住院天数</div>
          </div>
          <div class="text-center bg-gray-50 rounded-lg p-3">
            <div class="text-lg font-bold text-gray-900">{{ customer.recordsCount }}</div>
            <div class="text-xs text-gray-600">记录表单</div>
          </div>
          <div
            class="text-center bg-gray-50 rounded-lg p-3 flex flex-col items-center justify-center"
          >
            <div class="text-xs font-medium text-gray-900">{{ customer.lastUpdateTime }}</div>
            <div class="text-xs text-gray-600">最后更新</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2">
          <el-button
            type="primary"
            @click.stop="handleViewProfile(customer)"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500 flex-1"
          >
            <el-icon class="mr-1">
              <FolderOpened />
            </el-icon>
            查看档案
          </el-button>
          <!-- <el-button
            v-if="customer.checkInStatus === '已入住'"
            type="primary"
            @click.stop="handleAddRecord(customer)"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500 flex-1"
          >
            <el-icon class="mr-1">
              <Plus />
            </el-icon>
            新增记录
          </el-button>
          <el-button
            size="small"
            @click.stop="handleExportCustomer(customer)"
            class="border-gray-300 text-gray-600 hover:bg-gray-50"
          >
            <el-icon>
              <Download />
            </el-icon>
          </el-button> -->
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container mt-6 flex justify-center">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[12, 24, 36, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        class="bg-white rounded-lg px-4 py-2 shadow-sm"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { FolderOpened } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'
import { getCheckInStatusTagType } from '@/utils/constants.js'

const emit = defineEmits(['view-profile', 'add-record', 'export-customer', 'row-click'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/maternity-admission/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(12) // 卡片视图默认更多条数
const total = computed(() => totalCount.value)

// 转换API数据格式
const transformCustomerData = (apiData) => {
  return apiData.map((item) => washCustomerData(item))
}

const washCustomerData = (item) => {
  return {
    id: item.id,
    aid: item.aid,
    customerName: item.maternity,
    phone: item.phone,
    isMultiple: item.is_multiple_birth,
    needAttention: item.need_attention,
    checkInStatus: item.check_in_status,
    newborns: item.newborns || [],
    roomNumber: item.room_number,
    expectedCheckInDate: item.expected_check_in_date,
    actualCheckInDate: item.actual_check_in_date,
    expectedCheckOutDate: item.expected_check_out_date,
    actualCheckOutDate: item.actual_check_out_date,
    mainNurse: item.main_nurse,
    stayDays: item.stay_days,
    recordsCount: item.records_count,
    lastUpdateTime: item.last_update_time,
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的日期字段
    const processedFilters = { ...props.filters }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = transformCustomerData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取客户列表失败:', error)
    ElMessage.error('获取客户列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 状态相关方法 - 使用全局常量工具函数
const getStatusType = (status) => {
  return getCheckInStatusTagType(status)
}

// 时间格式化
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'MM-dd')
}

// 事件处理
const handleViewProfile = (row) => {
  emit('view-profile', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>
