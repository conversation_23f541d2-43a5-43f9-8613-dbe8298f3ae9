<template>
  <div class="record-tabs mb-6">
    <!-- 标签页导航 -->
    <div class="tabs-nav bg-white rounded-lg p-2 shadow-sm mb-4">
      <div class="flex flex-wrap gap-2">
        <button
          v-for="tab in props.tabs"
          :key="tab.key"
          @click="$emit('update:activeTab', tab.key)"
          :class="[
            'tab-button px-4 py-2 text-sm font-medium rounded-lg transition-all flex items-center gap-2',
            props.activeTab === tab.key
              ? 'bg-pink-500 text-white shadow-md'
              : 'text-gray-600 hover:bg-pink-50 hover:text-pink-600',
          ]"
        >
          <component :is="getTabIcon(tab.icon)" class="w-4 h-4" />
          {{ tab.title }}
        </button>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content bg-white rounded-lg shadow-sm min-h-96 p-5">
      <div class="">
        <!-- 根据当前活跃标签显示对应内容 -->
        <component
          :is="getTabComponent(props.activeTab)"
          :customer-id="props.customerId"
          :baby-list="props.babyList"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  UserFilled,
  Avatar,
  DocumentCopy,
  TrendCharts,
  Monitor,
  KnifeFork,
  Document,
  List,
  Setting,
  Calendar,
  Bowl,
  Trophy,
  Reading,
  ChatLineRound,
} from '@element-plus/icons-vue'

// 导入各个表单组件（这些组件需要单独创建）
import MaternalAdmissionDetail from './MaternalAdmissionDetail.vue'
import NewbornAdmissionDetail from './NewbornAdmissionDetail.vue'
import MaternalRecoveryDetail from './MaternalRecoveryDetail.vue'
import MaternalDailyCareDetail from './MaternalDailyCareDetail.vue'
import MaternalDietaryDetail from './MaternalDietaryDetail.vue'
import NewbornCareOneDetail from './NewbornCareOneDetail.vue'
import NewbornCareTwoDetail from './NewbornCareTwoDetail.vue'
import NewbornCareOperationDetail from './NewbornCareOperationDetail.vue'
import NewbornDailyRequiredDetail from './NewbornDailyRequiredDetail.vue'
import NewbornFeedingDetail from './NewbornFeedingDetail.vue'
import NewbornMonthAssessmentDetail from './NewbornMonthAssessmentDetail.vue'
import PersonalizedCareForm from './PersonalizedCareForm.vue'
import EducationRecordDialog from '@/components/HealthEducation.vue'
import MaternalDailyRequiredDetail from './MaternalDailyRequiredDetail.vue'

const props = defineProps({
  activeTab: {
    type: String,
    required: true,
  },
  tabs: {
    type: Array,
    required: true,
  },
  customerId: {
    type: [String, Number],
    required: true,
  },
  babyList: {
    type: Array,
    default: () => [],
  },
})

defineEmits(['update:activeTab'])

const getTabIcon = (icon) => {
  const icons = {
    UserFilled: UserFilled,
    Avatar: Avatar,
    DocumentCopy: DocumentCopy,
    TrendCharts: TrendCharts,
    Monitor: Monitor,
    KnifeFork: KnifeFork,
    Document: Document,
    List: List,
    Setting: Setting,
    Calendar: Calendar,
    Bowl: Bowl,
    Trophy: Trophy,
    Reading: Reading,
    ChatLineRound: ChatLineRound,
  }
  return icons[icon] || Document
}

const getTabComponent = (tabKey) => {
  const components = {
    'maternal-admission': MaternalAdmissionDetail,
    'newborn-admission': NewbornAdmissionDetail,
    'maternal-recovery': MaternalRecoveryDetail,
    'maternal-daily-care': MaternalDailyCareDetail,
    'maternal-dietary': MaternalDietaryDetail,
    'newborn-care-one': NewbornCareOneDetail,
    'newborn-care-two': NewbornCareTwoDetail,
    'newborn-care-operation': NewbornCareOperationDetail,
    'newborn-daily-required': NewbornDailyRequiredDetail,
    'newborn-feeding': NewbornFeedingDetail,
    'newborn-fullmoon': NewbornMonthAssessmentDetail,
    'personalized-care': PersonalizedCareForm,
    'health-education': EducationRecordDialog,
    'maternal-daily-required': MaternalDailyRequiredDetail,
  }
  return components[tabKey] || MaternalAdmissionDetail
}
</script>

<style scoped>
.tab-button {
  transition: all 0.2s ease;
}

.tab-button:hover {
  transform: translateY(-1px);
}

.tab-content {
  transition: all 0.3s ease;
}

.tab-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}
</style>
