<template>
  <div class="customer-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <FolderOpened />
          </el-icon>
          客户档案列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="customerName" label="产妇姓名" width="150" fixed="left">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.customerName.charAt(0) }}
            </el-avatar>
            <div>
              <div class="font-medium">{{ row.customerName }}</div>
              <div class="flex gap-1 mt-1">
                <el-tag v-if="row.needAttention" type="danger" size="small">需关注</el-tag>
                <el-tag v-if="row.isMultiple" type="info" size="small">多胞胎</el-tag>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="newborns" label="新生儿信息" width="180">
        <template #default="{ row }">
          <div class="text-sm">
            <div v-for="baby in row.newborns" :key="baby" class="text-gray-700 mb-1">
              {{ baby }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="roomNumber" label="房间号" width="100">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.roomNumber }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="checkInStatus" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.checkInStatus)" size="small">
            {{ row.checkInStatus }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="入住信息" width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>预计: {{ formatDate(row.expectedCheckInDate) }}</div>
            <div v-if="row.actualCheckInDate" class="text-green-600">
              实际: {{ formatDate(row.actualCheckInDate) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="退房信息" width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>预计: {{ formatDate(row.expectedCheckOutDate) }}</div>
            <div v-if="row.actualCheckOutDate" class="text-red-600">
              实际: {{ formatDate(row.actualCheckOutDate) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="mainNurse" label="主责护理" width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.mainNurse }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="stayDays" label="住院天数" width="100">
        <template #default="{ row }">
          <span class="font-medium text-gray-800">{{ row.stayDays }}天</span>
        </template>
      </el-table-column>

      <el-table-column prop="recordsCount" label="记录表单" width="100">
        <template #default="{ row }">
          <span class="font-medium text-pink-600">{{ row.recordsCount }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="lastUpdateTime" label="最后更新" width="100">
        <template #default="{ row }">
          <span class="text-gray-500 text-sm">{{ row.lastUpdateTime }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="240" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              @click.stop="handleViewProfile(row)"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              查看档案
            </el-button>
            <!-- <el-button
              v-if="row.checkInStatus === '已入住'"
              @click.stop="handleAddRecord(row)"
              type="default"
              size="small"
            >
              新增记录
            </el-button> -->
            <!-- <el-button
              @click.stop="handleExportCustomer(row)"
              size="small"
              class="border-gray-300 text-gray-600 hover:bg-gray-50"
            >
              导出
            </el-button> -->
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { FolderOpened } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'
import { getCheckInStatusTagType } from '@/utils/constants.js'

const emit = defineEmits(['view-profile', 'add-record', 'export-customer', 'row-click'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/maternity-admission/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 转换API数据格式
const transformCustomerData = (apiData) => {
  return apiData.map((item) => washCustomerData(item))
}

const washCustomerData = (item) => {
  return {
    id: item.id,
    aid: item.aid,
    customerName: item.maternity,
    phone: item.phone,
    isMultiple: item.is_multiple_birth,
    needAttention: item.need_attention,
    checkInStatus: item.check_in_status,
    newborns: item.newborns || [],
    roomNumber: item.room_number,
    expectedCheckInDate: item.expected_check_in_date,
    actualCheckInDate: item.actual_check_in_date,
    expectedCheckOutDate: item.expected_check_out_date,
    actualCheckOutDate: item.actual_check_out_date,
    mainNurse: item.main_nurse,
    stayDays: item.stay_days,
    recordsCount: item.records_count,
    lastUpdateTime: item.last_update_time,
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的日期字段
    const processedFilters = { ...props.filters }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = transformCustomerData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取客户列表失败:', error)
    ElMessage.error('获取客户列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 状态相关方法 - 使用全局常量工具函数
const getStatusType = (status) => {
  return getCheckInStatusTagType(status)
}

// 时间格式化
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'MM-dd')
}

// 事件处理
const handleViewProfile = (row) => {
  emit('view-profile', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.customer-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.customer-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
