<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="记录日期" prop="record_date">
              <el-date-picker
                v-model="form.record_date"
                type="date"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择记录日期"
              />
            </el-form-item>
            <el-form-item label="产后天数" prop="postpartum_days">
              <el-input-number v-model="form.postpartum_days" :min="1" :max="100" class="w-full" />
            </el-form-item>
            <el-form-item label="签名" prop="signature">
              <el-input v-model="form.signature" placeholder="请输入评估人员签名" />
            </el-form-item>
          </div>
        </div>

        <!-- 生命体征 -->
        <div class="form-section mb-6">
          <h4 class="section-title">生命体征</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="体温 (°C)" prop="temperature">
              <el-input-number
                v-model="form.temperature"
                :precision="1"
                :min="35"
                :max="42"
                class="w-full"
                placeholder="36.5"
              />
            </el-form-item>
            <el-form-item label="脉搏 (次/分)" prop="pulse">
              <el-input-number v-model="form.pulse" :min="40" :max="150" class="w-full" />
            </el-form-item>
            <el-form-item label="体重 (kg)" prop="weight">
              <el-input-number
                v-model="form.weight"
                :precision="1"
                :min="30"
                :max="150"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 子宫恢复情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">子宫恢复情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="宫底高度" prop="uterus_height">
              <el-input v-model="form.uterus_height" placeholder="如：脐下3指" />
            </el-form-item>
            <el-form-item label="切口情况" prop="incision_situation">
              <el-select v-model="form.incision_situation" multiple placeholder="选择切口情况">
                <el-option
                  v-for="option in RECOVERY_INCISION_SITUATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 恶露情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">恶露情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="恶露量" prop="lochia_amount">
              <el-input v-model="form.lochia_amount" placeholder="请输入恶露量" />
            </el-form-item>
            <el-form-item label="恶露颜色" prop="lochia_color">
              <el-input v-model="form.lochia_color" placeholder="请输入恶露颜色" />
            </el-form-item>
            <el-form-item label="恶露气味" prop="lochia_smell">
              <el-input v-model="form.lochia_smell" placeholder="请输入恶露气味" />
            </el-form-item>
          </div>
        </div>

        <!-- 乳房情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">乳房情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="乳汁量" prop="milk_amount">
              <el-input v-model="form.milk_amount" placeholder="请输入乳汁量" />
            </el-form-item>
            <el-form-item label="乳房疼痛" prop="breast_pain">
              <el-input v-model="form.breast_pain" placeholder="请输入疼痛程度" />
            </el-form-item>
            <el-form-item label="乳头皲裂" prop="nipple_cracked">
              <el-input v-model="form.nipple_cracked" placeholder="请输入皲裂情况" />
            </el-form-item>
          </div>
        </div>

        <!-- 指导意见 -->
        <div class="form-section mb-6">
          <h4 class="section-title">指导意见</h4>
          <el-form-item label="指导意见" prop="guidance_opinion">
            <el-input
              v-model="form.guidance_opinion"
              type="textarea"
              :rows="4"
              placeholder="请输入护理指导意见和建议"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { RECOVERY_INCISION_SITUATION_OPTIONS } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  customerId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const isEdit = computed(() => !!props.detail)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑产妇康复护理评估记录' : '新建产妇康复护理评估记录'
})

// 日期字段定义 - record_date是纯日期，不需要时间处理

const form = reactive({
  record_date: '',
  postpartum_days: null,
  temperature: null,
  pulse: null,
  weight: null,
  uterus_height: '',
  incision_situation: [],
  lochia_amount: '',
  lochia_color: '',
  lochia_smell: '',
  milk_amount: '',
  breast_pain: '',
  nipple_cracked: '',
  guidance_opinion: '',
  signature: '',
})

const rules = {
  record_date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  postpartum_days: [{ required: true, message: '请输入产后天数', trigger: 'blur' }],
  signature: [{ required: true, message: '请输入签名', trigger: 'blur' }],
}

const { scrollToTop } = useDialogScrollToTop()

// 重置表单
const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (['postpartum_days', 'temperature', 'pulse', 'weight'].includes(key)) {
      form[key] = null
    } else if (key === 'incision_situation') {
      form[key] = []
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

// 监听 visible 变化
watch(
  () => props.modelValue,
  async (visible) => {
    scrollToTop()
    if (visible) {
      if (props.detail) {
        // 使用传入的详情数据
        resetForm()
        await nextTick()
        console.log('Detail data received:', props.detail)
        Object.assign(form, props.detail)
        console.log('Form data after assignment:', { ...form })
      } else {
        // 初始化默认值
        resetForm()
        await nextTick()
        form.record_date = getCurrentTime('date')
      }
    }
  },
)

// 处理关闭
const handleClose = () => {
  visible.value = false
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 处理提交数据的日期格式
    const submitData = form
    console.log('Submit data:', submitData)

    let res
    if (!isEdit.value) {
      // 创建记录
      res = await post(`customer-service/mra-record/create/${props.customerId}/`, submitData)
      ElMessage.success('产妇康复护理评估记录创建成功！')
    } else {
      // 更新记录
      res = await put(`customer-service/mra-record/update/${props.detail.record_id}/`, submitData)
      ElMessage.success('产妇康复护理评估记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
