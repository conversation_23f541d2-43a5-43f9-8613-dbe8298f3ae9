<template>
  <div class="maternal-admission-detail">
    <div class="detail-header mb-6 flex justify-between items-center">
      <div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">产妇入住评估记录</h3>
        <p class="text-sm text-gray-600">查看产妇入住时的健康状况和基本信息</p>
      </div>
      <div class="flex gap-2">
        <el-button
          v-if="detailData"
          type="primary"
          @click="showFormDialog = true"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          更新记录
        </el-button>
        <el-button
          v-if="detailData"
          :disabled="true"
          @click="exportPDF"
          :loading="pdfLoading"
          class="hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
        >
          <template #icon>
            <Download />
          </template>
          导出PDF
        </el-button>
      </div>
    </div>

    <div v-loading="loading" class="detail-content">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>入住时间：</label>
              <span>{{ formatDateTime(detailData.admission_time) }}</span>
            </div>
            <div class="detail-item">
              <label>评估时间：</label>
              <span>{{ formatDateTime(detailData.assessment_time) }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(detailData.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDateTime(detailData.updated_at) }}</span>
            </div>
            <div class="detail-item">
              <label>是否有出院小结：</label>
              <span>{{ formatBoolean(detailData.has_discharge_summary) }}</span>
            </div>
            <div class="detail-item">
              <label>是否正在用药：</label>
              <span>{{ formatBoolean(detailData.is_now_medication) }}</span>
            </div>
          </div>
        </div>

        <!-- 体征检查 -->
        <div class="detail-section">
          <h3 class="section-title">体征检查</h3>
          <div class="grid grid-cols-3 gap-4">
            <div class="detail-item">
              <label>体温 (°C)：</label>
              <span>{{ formatNumber(detailData.temperature) }}</span>
            </div>
            <div class="detail-item">
              <label>血压 (mmHg)：</label>
              <span>{{ formatNumber(detailData.blood_pressure) }}</span>
            </div>
            <div class="detail-item">
              <label>脉搏 (次/分)：</label>
              <span>{{ formatNumber(detailData.pulse) }}</span>
            </div>
            <div class="detail-item">
              <label>呼吸 (次/分)：</label>
              <span>{{ formatNumber(detailData.respiration) }}</span>
            </div>
            <div class="detail-item">
              <label>体重 (kg)：</label>
              <span>{{ formatNumber(detailData.weight, 'kg') }}</span>
            </div>
            <div class="detail-item">
              <label>身高 (cm)：</label>
              <span>{{ formatNumber(detailData.height) }}</span>
            </div>
            <div class="detail-item">
              <label>面色：</label>
              <span>{{ getFaceColorText(detailData.face_color) }}</span>
            </div>
            <div class="detail-item">
              <label>口腔黏膜：</label>
              <span>{{ getOralMucosaText(detailData.oral_mucosa) }}</span>
            </div>
            <div class="detail-item">
              <label>活动能力：</label>
              <span>{{ getActivityText(detailData.activity) }}</span>
            </div>
            <div v-if="detailData.activity_limited_part" class="detail-item">
              <label>活动受限部位：</label>
              <span>{{ detailData.activity_limited_part }}</span>
            </div>
          </div>
        </div>

        <!-- 过敏史 -->
        <div class="detail-section">
          <h3 class="section-title">过敏史</h3>
          <div class="grid grid-cols-1 gap-4">
            <div class="detail-item">
              <label>食物过敏：</label>
              <span>{{ formatBoolean(detailData.has_allergic_food) }}</span>
            </div>
            <div v-if="detailData.has_allergic_food" class="detail-item">
              <label>过敏食物：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.allergic_food) }}</p>
            </div>
            <div v-if="detailData.has_allergic_food" class="detail-item">
              <label>食物过敏反应：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.food_allergic_reaction) }}</p>
            </div>
            <div class="detail-item">
              <label>药物过敏：</label>
              <span>{{ formatBoolean(detailData.has_allergic_drug) }}</span>
            </div>
            <div v-if="detailData.has_allergic_drug" class="detail-item">
              <label>药物过敏反应：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.drug_allergic_reaction) }}</p>
            </div>
          </div>
        </div>

        <!-- 诊断与分娩情况 -->
        <div class="detail-section">
          <h3 class="section-title">诊断与分娩情况</h3>
          <div class="space-y-4">
            <div class="detail-item">
              <label>出院诊断：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.discharge_diagnosis) }}</p>
            </div>
            <div class="detail-item">
              <label>妊娠并发症及合并症：</label>
              <p class="mt-1 text-gray-600">
                {{ formatText(detailData.pregnancy_complications_and_comorbidities) }}
              </p>
            </div>
            <div class="detail-item">
              <label>分娩特殊情况：</label>
              <p class="mt-1 text-gray-600">
                {{ formatText(detailData.delivery_special_situation) }}
              </p>
            </div>
            <div class="detail-item">
              <label>既往妊娠史：</label>
              <p class="mt-1 text-gray-600">
                {{ formatText(detailData.previous_pregnancy_history) }}
              </p>
            </div>
          </div>
        </div>

        <!-- 健康状态评估 -->
        <div class="detail-section">
          <h3 class="section-title">健康状态评估</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>是否急性疾病治疗期：</label>
              <span>{{ formatBoolean(detailData.is_acute_disease_treatment_period) }}</span>
            </div>
            <div class="detail-item">
              <label>是否传染病期：</label>
              <span>{{ formatBoolean(detailData.is_infectious_disease_period) }}</span>
            </div>
            <div class="detail-item">
              <label>是否有精神疾病史：</label>
              <span>{{ formatBoolean(detailData.has_psychiatric_disease_history) }}</span>
            </div>
            <div class="detail-item">
              <label>是否有心理疾病：</label>
              <span>{{ formatBoolean(detailData.has_psychological_disease) }}</span>
            </div>
          </div>
        </div>

        <!-- 恶露与乳房情况 -->
        <div class="detail-section">
          <h3 class="section-title">恶露与乳房情况</h3>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="detail-item">
              <label>恶露颜色：</label>
              <span>{{ formatText(detailData.lochia_color) }}</span>
            </div>
            <div class="detail-item">
              <label>恶露量：</label>
              <span>{{ formatText(detailData.lochia_amount) }}</span>
            </div>
            <div class="detail-item">
              <label>恶露气味：</label>
              <span>{{ formatText(detailData.lochia_smell) }}</span>
            </div>
            <div class="detail-item">
              <label>子宫高度：</label>
              <span>{{ formatText(detailData.uterus_height) }}</span>
            </div>
            <div class="detail-item">
              <label>乳头情况：</label>
              <span>{{ getNippleSituationText(detailData.nipple_situation) }}</span>
            </div>
            <div class="detail-item">
              <label>乳汁分泌：</label>
              <span>{{ getMilkSituationText(detailData.milk_situation) }}</span>
            </div>
          </div>
          <div class="space-y-4">
            <div class="detail-item">
              <label>切口位置：</label>
              <span>{{ getIncisionPositionText(detailData.incision_position) }}</span>
            </div>
            <div class="detail-item">
              <label>切口情况：</label>
              <span>{{ getIncisionSituationText(detailData.incision_situation) }}</span>
            </div>
            <div
              v-if="detailData.incision_abnormal && detailData.incision_abnormal.length > 0"
              class="detail-item"
            >
              <label>切口异常情况：</label>
              <span>{{ getIncisionAbnormalText(detailData.incision_abnormal) }}</span>
            </div>
            <div
              v-if="detailData.breast_situation && detailData.breast_situation.length > 0"
              class="detail-item"
            >
              <label>乳房情况：</label>
              <span>{{ getBreastSituationText(detailData.breast_situation) }}</span>
            </div>
            <div
              v-if="detailData.nipple_abnormal && detailData.nipple_abnormal.length > 0"
              class="detail-item"
            >
              <label>乳头异常情况：</label>
              <span>{{ getNippleAbnormalText(detailData.nipple_abnormal) }}</span>
            </div>
            <div v-if="detailData.breast_other_situation" class="detail-item">
              <label>乳房其他情况：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.breast_other_situation) }}</p>
            </div>
          </div>
        </div>

        <!-- 心理与生活状态 -->
        <div class="detail-section">
          <h3 class="section-title">心理与生活状态</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>情绪状态：</label>
              <span>{{ getEmotionText(detailData.emotion) }}</span>
            </div>
            <div class="detail-item">
              <label>食欲：</label>
              <span>{{ getAppetiteText(detailData.appetite) }}</span>
            </div>
            <div class="detail-item">
              <label>饮食要求：</label>
              <span>{{ getDietaryRequirementsText(detailData.dietary_requirements) }}</span>
            </div>
            <div class="detail-item">
              <label>排尿：</label>
              <span>{{ getUrinationText(detailData.urination) }}</span>
            </div>
            <div class="detail-item">
              <label>排尿次数：</label>
              <span>{{ formatNumber(detailData.urination_times) }}次/日</span>
            </div>
            <div class="detail-item">
              <label>排尿疼痛：</label>
              <span>{{ formatBoolean(detailData.has_urination_pain) }}</span>
            </div>
            <div class="detail-item">
              <label>排便：</label>
              <span>{{ getDefecationText(detailData.defecation) }}</span>
            </div>
            <div class="detail-item">
              <label>排便次数：</label>
              <span>{{ formatNumber(detailData.defecation_times) }}次/日</span>
            </div>
            <div class="detail-item">
              <label>康复知识了解：</label>
              <span>{{
                getKnowledgeOfRehabilitationText(detailData.knowledge_of_rehabilitation)
              }}</span>
            </div>
          </div>
          <div class="space-y-4 mt-4">
            <div v-if="detailData.dro_description" class="detail-item">
              <label>饮食要求描述：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.dro_description) }}</p>
            </div>
            <div v-if="detailData.has_food_preference" class="detail-item">
              <label>饮食偏好：</label>
              <p class="mt-1 text-gray-600">
                {{ formatText(detailData.food_preference_description) }}
              </p>
            </div>
          </div>
        </div>

        <!-- 社会支持评估 -->
        <div class="detail-section">
          <h3 class="section-title">社会支持评估</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>参与孕期教育：</label>
              <span>{{
                getParticipationInPregnancyEducationText(
                  detailData.participation_in_pregnancy_education,
                )
              }}</span>
            </div>
            <div class="detail-item">
              <label>家庭对患者态度：</label>
              <span>{{
                getFamilyAttitudeToPatientText(detailData.family_attitude_to_patient)
              }}</span>
            </div>
          </div>
          <div v-if="detailData.fatp_pther_description" class="space-y-4 mt-4">
            <div class="detail-item">
              <label>家庭态度其他描述：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.fatp_pther_description) }}</p>
            </div>
          </div>
        </div>

        <!-- 护理要点与特殊发现 -->
        <div class="detail-section">
          <h3 class="section-title">护理要点与特殊发现</h3>
          <div class="space-y-4">
            <div class="detail-item">
              <label>产妇护理要点：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.maternal_nursing_points) }}</p>
            </div>
            <div class="detail-item">
              <label>特殊发现：</label>
              <p class="mt-1 text-gray-600">{{ formatText(detailData.special_findings) }}</p>
            </div>
            <div class="detail-item">
              <label>签名：</label>
              <span>{{ formatText(detailData.signature) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 暂无数据状态 -->
      <el-empty v-else-if="!loading" class="text-center py-12">
        <el-button
          type="primary"
          @click="showFormDialog = true"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          立即创建
        </el-button>
      </el-empty>
    </div>

    <!-- 表单对话框 -->
    <MaternalAdmissionFormDialog
      v-model="showFormDialog"
      :detail="detailData"
      :customer-id="customerId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'
import { formatDateTime } from '@/utils/dateUtils.js'
import { formatBoolean, formatNumber, formatText, downloadFile } from '@/utils/utils.js'
import {
  getFaceColorText,
  getOralMucosaText,
  getActivityText,
  getEmotionText,
  getAppetiteText,
  getDietaryRequirementsText,
  getUrinationText,
  getDefecationText,
  getKnowledgeOfRehabilitationText,
  getParticipationInPregnancyEducationText,
  getFamilyAttitudeToPatientText,
  getIncisionPositionText,
  getIncisionSituationText,
  getIncisionAbnormalText,
  getBreastSituationText,
  getNippleSituationText,
  getNippleAbnormalText,
  getMilkSituationText,
} from '@/utils/constants.js'
import MaternalAdmissionFormDialog from './MaternalAdmissionFormDialog.vue'

// 接收父组件传入的参数
const props = defineProps({
  customerId: {
    type: [String, Number],
    required: true,
  },
})

const loading = ref(false)
const pdfLoading = ref(false)
const detailData = ref(null)
const showFormDialog = ref(false)

// 获取详情数据
const fetchDetail = async () => {
  if (loading.value) return // 防止重复请求

  loading.value = true
  try {
    const response = await get(`customer-service/mcia-record/detail/${props.customerId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取产妇入住评估记录失败')
  } finally {
    loading.value = false
  }
}

// 处理表单成功
const handleFormSuccess = () => {
  showFormDialog.value = false
  fetchDetail()
}

// 导出PDF
const exportPDF = async () => {
  if (!detailData.value?.id) {
    ElMessage.error('暂无评估记录可导出')
    return
  }

  pdfLoading.value = true
  try {
    const response = await get(
      `customer-service/maternal-admiss-assessment/pdf/${props.customerId}/`,
    )

    if (response.pdf_url && response.filename) {
      // 获取完整的文件URL
      const fullUrl = response.pdf_url.startsWith('http')
        ? response.pdf_url
        : `${window.location.origin}${response.pdf_url}`

      // 下载文件
      downloadFile(fullUrl, response.filename)

      // 显示成功消息，包含过期时间提示
      const expiresHours = Math.floor((response.expires_in || 3600) / 3600)
      ElMessage.success({
        message: `PDF导出成功！文件链接有效期为${expiresHours}小时`,
        duration: 5000,
      })
    } else {
      throw new Error('PDF文件信息不完整')
    }
  } catch (error) {
    console.error('导出PDF失败:', error)

    // 提供更详细的错误信息
    if (error.response?.status === 404) {
      ElMessage.error('评估记录不存在或已被删除')
    } else if (error.response?.status === 403) {
      ElMessage.error('没有权限导出此PDF文件')
    } else if (error.message?.includes('网络')) {
      ElMessage.error('网络连接失败，请检查网络后重试')
    } else {
      ElMessage.error('PDF导出失败，请重试')
    }
  } finally {
    pdfLoading.value = false
  }
}

// 刷新数据
const refresh = () => {
  fetchDetail()
}

// 暴露方法给父组件
defineExpose({
  refresh,
  exportPDF,
})

onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.25rem;
  flex-shrink: 0;
}

.detail-item span {
  color: #374151;
}

.detail-item p {
  color: #6b7280;
  line-height: 1.5;
}

.grid .detail-item {
  flex-direction: row;
  align-items: center;
}

.grid .detail-item label {
  margin-bottom: 0;
  margin-right: 0.5rem;
  min-width: 6rem;
}
</style>
