<template>
  <div class="dashboard-cards grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- 新生儿状态追踪 -->
    <div class="dashboard-card bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all">
      <div class="card-content">
        <!-- 使用BabyCard组件 -->
        <BabyCard
          :aid="clientData.aid"
          :babies="clientData.babies_data || []"
          @reload="handleReload"
        />
      </div>
    </div>

    <!-- 产妇恢复情况 -->
    <div class="dashboard-card bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all">
      <div class="card-content h-full">
        <!-- 使用MaternalCard组件 -->
        <MaternalCard
          :aid="clientData.aid"
          :maternal-data="clientData.maternity_data || null"
          @reload="handleReload"
        />
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="dashboard-card bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all">
      <h3 class="flex items-center gap-3 text-lg font-semibold text-gray-800 mb-4">
        <List class="w-5 h-5 text-pink-500" />
        最近活动
      </h3>
      <div class="card-content space-y-3">
        <div
          v-for="(activity, index) in formattedRecentActivities"
          :key="index"
          class="activity-item flex gap-3 py-2"
        >
          <div
            class="activity-icon w-9 h-9 bg-pink-100 rounded-full flex items-center justify-center"
          >
            <component :is="getActivityIcon(activity?.icon)" class="w-4 h-4 text-pink-600" />
          </div>
          <div class="activity-details flex-1">
            <div class="activity-title text-sm font-medium text-gray-800 mb-1">
              {{ activity?.title || '未知活动' }}
            </div>
            <div class="activity-time text-xs text-gray-500">
              {{ activity?.time || '未知时间' }} - {{ activity?.staff || '未知人员' }}
            </div>
          </div>
        </div>
        <div v-if="formattedRecentActivities.length === 0" class="text-center text-gray-500 py-4">
          暂无最近活动记录
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Monitor, KnifeFork, List, User } from '@element-plus/icons-vue'
import BabyCard from './BabyCard.vue'
import MaternalCard from './MaternalCard.vue'

const props = defineProps({
  clientData: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['reload'])

// 操作按钮处理方法
const handleReload = () => {
  emit('reload')
}

const getActivityIcon = (icon) => {
  const icons = {
    stethoscope: Monitor,
    baby: User,
    utensils: KnifeFork,
  }
  return icons[icon] || List
}

// 格式化最近活动数据
const formattedRecentActivities = computed(() => {
  const recentActivity = props.clientData.recent_activity
  if (!recentActivity || !Array.isArray(recentActivity)) return []

  return recentActivity.map((activity) => ({
    title: activity.activity_type,
    time: new Date(activity.activity_time).toLocaleString('zh-CN'),
    staff: activity.creator,
    icon: 'stethoscope',
  }))
})
</script>

<style scoped>
.dashboard-card {
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
}

/* 保留活动卡片样式 */

.activity-item {
  transition: all 0.2s ease;
  border-radius: 8px;
  padding: 8px;
}

.activity-item:hover {
  background-color: #f9fafb;
}
</style>
