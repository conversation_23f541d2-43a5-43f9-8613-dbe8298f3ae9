<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="记录日期" prop="record_date">
              <el-date-picker
                v-model="form.record_date"
                type="date"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                placeholder="请选择记录日期"
              />
            </el-form-item>
            <el-form-item label="餐数" prop="meal_number">
              <el-input-number
                v-model="form.meal_number"
                :min="1"
                :max="10"
                :precision="0"
                class="w-full"
                placeholder="请输入餐数"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 饮食情况 -->
        <div class="form-section mb-6">
          <h4 class="section-title">饮食情况</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="进食情况" prop="eating_situation">
              <el-select v-model="form.eating_situation" placeholder="选择进食情况" class="w-full">
                <el-option
                  v-for="option in EATING_SITUATION_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="特殊饮食特点" prop="special_diet_features">
              <el-select
                v-model="form.special_diet_features"
                placeholder="选择特殊饮食特点"
                class="w-full"
              >
                <el-option
                  v-for="option in SPECIAL_DIET_FEATURES_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="普通饮食特点" prop="normal_diet_features" class="col-span-full">
              <el-input
                v-model="form.normal_diet_features"
                type="textarea"
                :rows="3"
                placeholder="请输入普通饮食特点，如：清淡易消化，营养均衡，少食多餐"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 营养摄入详情 -->
        <div class="form-section mb-6">
          <h4 class="section-title">营养摄入详情</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="碳水化合物 (g)" prop="carbohydrate">
              <el-input-number
                v-model="form.carbohydrate"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入碳水化合物"
              />
            </el-form-item>
            <el-form-item label="蔬菜类 (g)" prop="vegetables_class">
              <el-input-number
                v-model="form.vegetables_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入蔬菜类"
              />
            </el-form-item>
            <el-form-item label="水果类 (g)" prop="fruit_class">
              <el-input-number
                v-model="form.fruit_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入水果类"
              />
            </el-form-item>
            <el-form-item label="肉类 (g)" prop="meat_class">
              <el-input-number
                v-model="form.meat_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入肉类"
              />
            </el-form-item>
            <el-form-item label="禽类 (g)" prop="poultry_class">
              <el-input-number
                v-model="form.poultry_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入禽类"
              />
            </el-form-item>
            <el-form-item label="鱼类 (g)" prop="fish_class">
              <el-input-number
                v-model="form.fish_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入鱼类"
              />
            </el-form-item>
            <el-form-item label="蛋类 (g)" prop="egg_class">
              <el-input-number
                v-model="form.egg_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入蛋类"
              />
            </el-form-item>
            <el-form-item label="豆制品类 (g)" prop="soy_products_class">
              <el-input-number
                v-model="form.soy_products_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入豆制品类"
              />
            </el-form-item>
            <el-form-item label="牛奶 (ml)" prop="milk">
              <el-input-number
                v-model="form.milk"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入牛奶"
              />
            </el-form-item>
            <el-form-item label="奶制品类 (g)" prop="dairy_products_class">
              <el-input-number
                v-model="form.dairy_products_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入奶制品类"
              />
            </el-form-item>
            <el-form-item label="油类 (g)" prop="oil_class">
              <el-input-number
                v-model="form.oil_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入油类"
              />
            </el-form-item>
            <el-form-item label="中药材 (g)" prop="chinese_medicines_class">
              <el-input-number
                v-model="form.chinese_medicines_class"
                :min="0"
                :precision="1"
                class="w-full"
                placeholder="请输入中药材"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :disabled="submitting">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { getCurrentTime } from '@/utils/dateUtils.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { EATING_SITUATION_MAP, SPECIAL_DIET_FEATURES_MAP } from '@/utils/constants.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: null,
  },
  customerId: {
    type: [String, Number],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const formRef = ref()
const loading = ref(false)
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val),
})

const isEdit = computed(() => !!props.detail)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑产妇膳食记录' : '新建产妇膳食记录'
})

// 枚举选项
const EATING_SITUATION_OPTIONS = Object.values(EATING_SITUATION_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

const SPECIAL_DIET_FEATURES_OPTIONS = Object.values(SPECIAL_DIET_FEATURES_MAP)
  .filter((item) => item.value !== 'UNKNOWN')
  .map((item) => ({ label: item.text, value: item.value }))

const form = reactive({
  record_date: '',
  meal_number: null,
  eating_situation: '',
  normal_diet_features: '',
  special_diet_features: '',
  carbohydrate: null,
  vegetables_class: null,
  fruit_class: null,
  meat_class: null,
  poultry_class: null,
  fish_class: null,
  egg_class: null,
  soy_products_class: null,
  milk: null,
  dairy_products_class: null,
  oil_class: null,
  chinese_medicines_class: null,
})

const rules = {
  record_date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
}

const { scrollToTop } = useDialogScrollToTop()

const resetForm = () => {
  Object.keys(form).forEach((key) => {
    if (
      [
        'meal_number',
        'carbohydrate',
        'vegetables_class',
        'fruit_class',
        'meat_class',
        'poultry_class',
        'fish_class',
        'egg_class',
        'soy_products_class',
        'milk',
        'dairy_products_class',
        'oil_class',
        'chinese_medicines_class',
      ].includes(key)
    ) {
      form[key] = null
    } else {
      form[key] = ''
    }
  })
  nextTick(() => formRef.value?.clearValidate())
}

watch(
  () => props.modelValue,
  async (visible) => {
    scrollToTop()
    if (visible) {
      if (props.detail) {
        resetForm()
        await nextTick()
        Object.assign(form, props.detail)
      } else {
        resetForm()
        await nextTick()
        form.record_date = getCurrentTime('date')
      }
    }
  },
)

const handleClose = () => {
  visible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = { ...form }

    let res
    if (!isEdit.value) {
      res = await post(`customer-service/md-record/create/${props.customerId}/`, submitData)
      ElMessage.success('产妇膳食记录创建成功！')
    } else {
      res = await put(`customer-service/md-record/update/${props.detail.record_id}/`, submitData)
      ElMessage.success('产妇膳食记录更新成功！')
    }

    emit('success', res)
    visible.value = false
  } catch (error) {
    let errorMsg = '操作失败，请重试'
    if (error.message || error.msg) {
      errorMsg = error.message || error.msg
    } else if (error.data) {
      errorMsg = Object.values(error.data).flat()[0]
    }
    ElMessage.error(errorMsg)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.col-span-full {
  grid-column: 1 / -1;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
