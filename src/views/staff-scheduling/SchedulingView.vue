<template>
  <div class="scheduling-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">人员与排班管理 - 排班管理</h1>
            <p class="text-sm text-gray-600 mt-1">制定和管理员工排班计划，确保人员配置合理</p>
          </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreateSchedule"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            制定新排班
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 视图切换和内容 -->
    <div class="mb-6">
      <el-tabs v-model="currentView" @tab-change="handleTabChange" class="" type="border-card">
        <el-tab-pane label="日视图" name="daily">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Calendar />
              </el-icon>
              日视图
            </div>
          </template>
          <!-- 日历视图 -->
          <ScheduleCalendarView
            v-if="currentView === 'daily'"
            ref="calendarViewRef"
            :view-type="currentView"
            :filters="filters"
            @edit-schedule="handleEditSchedule"
            @quick-assign="handleQuickAssign"
          />
        </el-tab-pane>

        <el-tab-pane label="周视图" name="weekly">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Calendar />
              </el-icon>
              周视图
            </div>
          </template>
          <!-- 日历视图 -->
          <ScheduleCalendarView
            v-if="currentView === 'weekly'"
            ref="calendarViewRef"
            :view-type="currentView"
            :filters="filters"
            @edit-schedule="handleEditSchedule"
          />
        </el-tab-pane>

        <el-tab-pane label="月视图" name="monthly">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Calendar />
              </el-icon>
              月视图
            </div>
          </template>
          <!-- 日历视图 -->
          <ScheduleCalendarView
            v-if="currentView === 'monthly'"
            ref="calendarViewRef"
            :view-type="currentView"
            :filters="filters"
            @edit-schedule="handleEditSchedule"
          />
        </el-tab-pane>

        <el-tab-pane label="列表视图" name="list">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <List />
              </el-icon>
              列表视图
            </div>
          </template>
          <!-- 列表视图 -->
          <ScheduleListView
            ref="listViewRef"
            :filters="filters"
            @view="handleViewSchedule"
            @edit="handleEditSchedule"
            @delete="handleDeleteSchedule"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 排班表单弹窗 -->
    <ScheduleFormDialog
      v-model:visible="formVisible"
      :schedule-data="currentSchedule"
      :mode="formMode"
      @save="handleSaveSchedule"
    />

    <!-- 排班详情对话框 -->
    <SDScheduleDetailDialog
      v-model="detailDialogVisible"
      :item-id="selectedScheduleId"
      @edit="handleShowEditDialog"
      @delete="handleDeleteSchedule"
      @close="handleCloseDetailDialog"
    />

    <!-- 排班编辑对话框 -->
    <SDScheduleFormDialog
      v-model="editDialogVisible"
      :item-id="selectedScheduleId"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElButton, ElMessage, ElIcon, ElTabs, ElTabPane } from 'element-plus'
import { Plus, Calendar, List } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import ScheduleCalendarView from './components/ScheduleCalendarView.vue'
import ScheduleListView from './components/ScheduleListView.vue'
import ScheduleFormDialog from './components/ScheduleFormDialog.vue'
import SDScheduleDetailDialog from './components/SDScheduleDetailDialog.vue'
import SDScheduleFormDialog from './components/SDScheduleFormDialog.vue'
import { useBaseDataStore } from '@/stores/baseData.js'
import { get } from '@/utils/request.js'
import { SHIFT_TYPE_OPTIONS } from '@/utils/constants.js'
import { getTodayString } from '@/utils/utils.js'

// 响应式数据
const formVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentSchedule = ref(null)
const currentView = ref('daily') // 'daily' | 'weekly' | 'monthly' | 'list'

// 新增对话框状态
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const selectedScheduleId = ref(null)

// 子组件引用
const calendarViewRef = ref(null)
const listViewRef = ref(null)

// 基础数据store
const baseDataStore = useBaseDataStore()

// 员工选项和加载状态
const staffs = ref([])
const staffsLoading = ref(false)
const staffSearchOptions = ref([]) // 搜索结果选项
const staffSearchLoading = ref(false)

// 筛选条件
const filters = reactive({
  department_rid: '',
  staff_sid: '',
  start_date: getTodayString(),
  end_date: '',
  shift_type: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'department_rid',
    type: 'select',
    label: '部门筛选',
    placeholder: '选择部门',
    options: baseDataStore.departments.getOptions(),
    loading: baseDataStore.departments.isLoading() || baseDataStore.departments.searchLoading.value,
  },
  {
    key: 'staff_sid',
    type: 'select',
    label: '员工筛选',
    placeholder: '输入员工姓名搜索',
    filterable: true,
    remote: true,
    remoteMethod: searchStaff,
    options:
      staffSearchOptions.value.length > 0
        ? staffSearchOptions.value
        : staffs.value.map((staff) => ({
            label: staff.name,
            value: staff.sid,
          })),
    loading: staffSearchLoading.value || staffsLoading.value,
  },
  {
    key: 'start_date',
    type: 'date',
    label: '开始日期',
    placeholder: '选择开始日期',
  },
  {
    key: 'end_date',
    type: 'date',
    label: '结束日期',
    placeholder: '选择结束日期',
  },
  {
    key: 'shift_type',
    type: 'select',
    label: '班次筛选',
    placeholder: '选择班次',
    options: SHIFT_TYPE_OPTIONS,
  },
])

// 方法
const handleTabChange = (tabName) => {
  currentView.value = tabName
}

const handleCreateSchedule = () => {
  currentSchedule.value = null
  formMode.value = 'add'
  formVisible.value = true
}

const handleEditSchedule = (schedule) => {
  selectedScheduleId.value = schedule.rid
  detailDialogVisible.value = true
}

const handleViewSchedule = (schedule) => {
  selectedScheduleId.value = schedule.rid
  detailDialogVisible.value = true
}

const handleQuickAssign = (data) => {
  // 快速分配逻辑
  ElMessage.success(`快速分配 ${data.employee || '员工'} 到 ${data.shift}`)
}

const handleDeleteSchedule = () => {
  handleSearch()
}

const handleSearch = () => {
  // 触发子组件刷新数据
  if (currentView.value === 'list') {
    // 列表视图：重置分页并刷新
    if (listViewRef.value) {
      listViewRef.value.resetPagination()
    }
  } else {
    // 日历视图：直接刷新数据
    if (calendarViewRef.value) {
      calendarViewRef.value.refreshData()
    }
  }
}

const handleSaveSchedule = () => {
  handleSearch()
  formVisible.value = false
}

// 新增对话框相关方法
const handleShowEditDialog = () => {
  detailDialogVisible.value = false
  editDialogVisible.value = true
}

const handleCloseDetailDialog = () => {
  detailDialogVisible.value = false
  selectedScheduleId.value = null
}

const handleEditSuccess = () => {
  editDialogVisible.value = false
  selectedScheduleId.value = null
  // 刷新数据
  handleSearch()
}

// 获取员工列表（支持搜索）
const fetchStaffs = async (options = {}) => {
  const { department, query, isSearch = false } = options

  const loading = isSearch ? staffSearchLoading : staffsLoading
  loading.value = true

  try {
    const params = {
      page: 1,
      page_size: isSearch ? 50 : 100,
    }

    // 如果指定了部门，添加部门参数
    if (department) {
      params.department = department
    }

    // 如果是搜索模式，添加搜索关键词
    if (query && query.trim()) {
      params.sk = query.trim()
    }

    const response = await get('organizational-management/staff/list/', params)

    if (response?.list) {
      if (isSearch) {
        // 搜索模式：更新搜索选项
        staffSearchOptions.value = response.list.map((staff) => ({
          label: staff.name,
          value: staff.sid,
        }))
      } else {
        // 普通模式：更新员工列表
        staffs.value = response.list
      }
    } else {
      if (isSearch) {
        staffSearchOptions.value = []
      } else {
        staffs.value = []
      }
    }
  } catch (error) {
    console.error(isSearch ? '搜索员工失败:' : '获取员工列表失败:', error)
    if (!isSearch) {
      ElMessage.error('获取员工列表失败')
    }

    if (isSearch) {
      staffSearchOptions.value = []
    } else {
      staffs.value = []
    }
  } finally {
    loading.value = false
  }
}

// 搜索员工（使用统一的 fetchStaffs 函数）
const searchStaff = async (query) => {
  if (!query || query.trim() === '') {
    staffSearchOptions.value = []
    return
  }

  await fetchStaffs({
    department: filters.department_rid,
    query: query,
    isSearch: true,
  })
}

// 监听部门选择变化
watch(
  () => filters.department_rid,
  (newDepartment) => {
    // 清空员工选择和搜索结果
    filters.staff_sid = ''
    staffSearchOptions.value = []

    // 获取员工列表
    fetchStaffs({ department: newDepartment })
  },
)

onMounted(() => {
  // 初始化基础数据
  baseDataStore.departments.fetch()

  // 初始加载所有员工
  fetchStaffs()
})
</script>

<style scoped>
.scheduling-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.schedule-view-container {
  transition: all 0.3s ease;
}

/* 自定义 tabs 样式 */
:deep(.el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

:deep(.el-tabs__content) {
  padding: 24px;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
}

:deep(.el-tabs__item:hover) {
  color: #ec4899;
}
</style>
