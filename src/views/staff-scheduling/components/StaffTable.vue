<template>
  <div class="staff-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <User />
          </el-icon>
          员工列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 名员工</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="staff_number" label="员工号" min-width="120" fixed="left">
        <template #default="{ row }">
          <span class="font-mono">{{ row.staff_number }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="name" label="姓名" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.name.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="gender_display" label="性别" min-width="80">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.gender_display }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="department_display" label="所属部门" min-width="120">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.department_display }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="position_display" label="职位" min-width="120">
        <template #default="{ row }">
          <span class="font-medium text-gray-700">{{ row.position_display }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="role_display" label="角色" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.role_display }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="phone" label="联系电话" min-width="140">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.phone }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="hire_date" label="入职日期" min-width="120">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.hire_date) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="is_active_display" label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.is_active_display)" size="small">
            {{ row.is_active_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleEdit(row)"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              编辑
            </el-button>
            <el-button
              @click.stop="handleResignation(row)"
              type="warning"
              size="small"
              class="text-orange-600 border-orange-200 hover:bg-orange-50"
              :disabled="row.is_active_display === '离职'"
            >
              标记离职
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get, put } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'

const emit = defineEmits([
  'edit',
  'view-detail',
  'manage-training',
  'manage-assessment',
  'manage-health',
  'resignation',
  'row-click',
])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'organizational-management/staff/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = data.list || []
    totalCount.value = data.total_count || 0
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 状态相关方法
const getStatusType = (status) => {
  const statusMap = {
    在职: 'success',
    试用期: 'warning',
    离职: 'info',
    停职: 'danger',
  }
  return statusMap[status] || 'info'
}

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 标记离职
const handleResignation = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要标记员工 "${row.name}" 为离职状态吗？`, '离职确认', {
      confirmButtonText: '确定离职',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--warning',
    })

    const resignationUrl = `organizational-management/staff/resignation/${row.sid}/`
    await put(resignationUrl)

    ElMessage.success('员工已标记为离职状态')

    // 重新加载数据
    loadData()
  } catch (error) {
    showErrorTip(error)
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.staff-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.staff-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
