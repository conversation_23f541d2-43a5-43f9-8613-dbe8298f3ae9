<template>
  <el-dialog v-model="visible" title="健康检查记录管理" width="900px" class="health-dialog">
    <div class="mb-4 flex justify-between items-center">
      <h4 class="text-lg font-semibold text-gray-800">{{ staffData?.name }} - 健康检查记录</h4>
      <el-button
        type="primary"
        @click="showAddForm"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon><Plus /></el-icon>
        添加体检
      </el-button>
    </div>

    <el-table :data="healthRecords" stripe class="w-full">
      <el-table-column prop="checkDate" label="体检日期" width="120" />
      <el-table-column prop="type" label="体检类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getHealthTypeColor(row.type)">{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="hospital" label="体检机构" min-width="150" />
      <el-table-column prop="result" label="体检结果" width="100">
        <template #default="{ row }">
          <el-tag :type="getResultType(row.result)">{{ row.result }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="height" label="身高(cm)" width="100" />
      <el-table-column prop="weight" label="体重(kg)" width="100" />
      <el-table-column prop="bmi" label="BMI" width="80">
        <template #default="{ row }">
          <span
            :class="{
              'text-green-600': row.bmi >= 18.5 && row.bmi < 24,
              'text-yellow-600': row.bmi >= 24 && row.bmi < 28,
              'text-red-500': row.bmi >= 28,
            }"
          >
            {{ row.bmi }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="nextDate" label="下次体检" width="120">
        <template #default="{ row }">
          <span :class="{ 'text-red-500': isExpiringSoon(row.nextDate) }">
            {{ row.nextDate }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button size="small" @click="editHealth(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteHealth(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 健康检查表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'add' ? '添加体检记录' : '编辑体检记录'"
      width="600px"
      append-to-body
    >
      <el-form ref="healthFormRef" :model="healthForm" :rules="healthRules" label-width="100px">
        <el-form-item label="体检日期" prop="checkDate">
          <el-date-picker
            v-model="healthForm.checkDate"
            type="date"
            placeholder="选择体检日期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="体检类型" prop="type">
          <el-select v-model="healthForm.type" placeholder="请选择体检类型" class="w-full">
            <el-option label="入职体检" value="入职体检" />
            <el-option label="年度体检" value="年度体检" />
            <el-option label="专项体检" value="专项体检" />
            <el-option label="复查体检" value="复查体检" />
          </el-select>
        </el-form-item>
        <el-form-item label="体检机构" prop="hospital">
          <el-input v-model="healthForm.hospital" placeholder="请输入体检机构" />
        </el-form-item>
        <el-form-item label="体检结果" prop="result">
          <el-select v-model="healthForm.result" placeholder="请选择体检结果" class="w-full">
            <el-option label="合格" value="合格" />
            <el-option label="基本合格" value="基本合格" />
            <el-option label="不合格" value="不合格" />
            <el-option label="待复查" value="待复查" />
          </el-select>
        </el-form-item>
        <div class="grid grid-cols-2 gap-4">
          <el-form-item label="身高(cm)" prop="height">
            <el-input-number
              v-model="healthForm.height"
              :min="100"
              :max="250"
              placeholder="身高"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="体重(kg)" prop="weight">
            <el-input-number
              v-model="healthForm.weight"
              :min="30"
              :max="200"
              :precision="1"
              placeholder="体重"
              class="w-full"
            />
          </el-form-item>
        </div>
        <el-form-item label="下次体检" prop="nextDate">
          <el-date-picker
            v-model="healthForm.nextDate"
            type="date"
            placeholder="选择下次体检日期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="体检说明" prop="remarks">
          <el-input
            v-model="healthForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入体检说明或异常情况"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="formVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="saveHealth"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存更改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import {
  ElDialog,
  ElTable,
  ElTableColumn,
  ElButton,
  ElIcon,
  ElTag,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElInputNumber,
  ElMessage,
  ElMessageBox,
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  staffData: { type: Object, default: null },
})

const emit = defineEmits(['update:visible', 'save'])

const formVisible = ref(false)
const formMode = ref('add')
const healthFormRef = ref()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const healthRecords = ref([
  {
    id: 1,
    checkDate: '2024-01-01',
    type: '年度体检',
    hospital: '市人民医院',
    result: '合格',
    height: 165,
    weight: 58.5,
    bmi: 21.5,
    nextDate: '2025-01-01',
    remarks: '',
  },
  {
    id: 2,
    checkDate: '2023-01-15',
    type: '入职体检',
    hospital: '健康体检中心',
    result: '合格',
    height: 165,
    weight: 60.0,
    bmi: 22.0,
    nextDate: '2024-01-15',
    remarks: '轻微近视',
  },
])

const healthForm = reactive({
  id: '',
  checkDate: '',
  type: '',
  hospital: '',
  result: '',
  height: 0,
  weight: 0,
  nextDate: '',
  remarks: '',
})

const healthRules = {
  checkDate: [{ required: true, message: '请选择体检日期', trigger: 'change' }],
  type: [{ required: true, message: '请选择体检类型', trigger: 'change' }],
  hospital: [{ required: true, message: '请输入体检机构', trigger: 'blur' }],
  result: [{ required: true, message: '请选择体检结果', trigger: 'change' }],
  height: [{ required: true, message: '请输入身高', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入体重', trigger: 'blur' }],
  nextDate: [{ required: true, message: '请选择下次体检日期', trigger: 'change' }],
}

const getHealthTypeColor = (type) => {
  const colorMap = {
    入职体检: 'primary',
    年度体检: 'success',
    专项体检: 'warning',
    复查体检: 'danger',
  }
  return colorMap[type] || ''
}

const getResultType = (result) => {
  const resultMap = { 合格: 'success', 基本合格: 'warning', 不合格: 'danger', 待复查: 'info' }
  return resultMap[result] || 'info'
}

const isExpiringSoon = (nextDate) => {
  const next = new Date(nextDate)
  const now = new Date()
  const thirtyDaysLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  return next <= thirtyDaysLater
}

// 计算BMI
const calculateBMI = () => {
  if (healthForm.height && healthForm.weight) {
    const heightInM = healthForm.height / 100
    return Math.round((healthForm.weight / (heightInM * heightInM)) * 10) / 10
  }
  return 0
}

// 监听身高体重变化，自动计算BMI
watch([() => healthForm.height, () => healthForm.weight], () => {
  const bmi = calculateBMI()
  if (bmi > 0) {
    healthForm.bmi = bmi
  }
})

const showAddForm = () => {
  formMode.value = 'add'
  Object.assign(healthForm, {
    id: '',
    checkDate: '',
    type: '',
    hospital: '',
    result: '',
    height: 0,
    weight: 0,
    nextDate: '',
    remarks: '',
  })
  formVisible.value = true
}

const editHealth = (health) => {
  formMode.value = 'edit'
  Object.assign(healthForm, health)
  formVisible.value = true
}

const saveHealth = async () => {
  try {
    await healthFormRef.value.validate()
    const bmi = calculateBMI()

    if (formMode.value === 'add') {
      healthRecords.value.push({ ...healthForm, id: Date.now(), bmi })
      ElMessage.success('体检记录添加成功')
    } else {
      const index = healthRecords.value.findIndex((h) => h.id === healthForm.id)
      if (index !== -1) {
        healthRecords.value[index] = { ...healthForm, bmi }
        ElMessage.success('体检记录更新成功')
      }
    }
    formVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const deleteHealth = async (health) => {
  try {
    await ElMessageBox.confirm(`确定要删除${health.checkDate}的体检记录吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })
    healthRecords.value = healthRecords.value.filter((h) => h.id !== health.id)
    ElMessage.success('体检记录删除成功')
  } catch {
    // 用户取消
  }
}

const handleSave = () => {
  emit('save', healthRecords.value)
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.health-dialog {
  --el-dialog-border-radius: 12px;
}
</style>
