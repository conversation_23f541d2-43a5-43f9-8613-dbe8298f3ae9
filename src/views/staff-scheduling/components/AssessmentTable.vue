<template>
  <div class="detail-section mr-4">
    <div class="flex items-center justify-between mb-4">
      <h3 class="section-title mb-0">考核记录</h3>
      <div class="flex gap-2">
        <el-button
          size="small"
          type="primary"
          @click="handleAdd"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-1"><Plus /></el-icon>
          新增考核
        </el-button>
      </div>
    </div>
    <div v-loading="loading" class="assessment-table-container">
      <el-table :data="tableData" stripe class="w-full" size="small" @row-click="handleView">
        <el-table-column prop="assessment_name" label="考核名称" min-width="100" align="center" />
        <el-table-column prop="assessment_date" label="考核日期" min-width="100" align="center" />
        <el-table-column prop="assessment_result" label="考核结果" min-width="70" align="center">
          <template #default="{ row }">
            <el-tag :type="getResultTagType(row.assessment_result)" size="small">
              {{ row.assessment_result }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assessment_comment" label="考核评语" min-width="160" align="center">
          <template #default="{ row }">
            <el-tooltip
              :content="row.assessment_comment"
              placement="top"
              :disabled="row.assessment_comment.length <= 20"
            >
              <span class="text-gray-700">
                {{
                  row.assessment_comment.length > 20
                    ? row.assessment_comment.substring(0, 20) + '...'
                    : row.assessment_comment
                }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click.stop="handleEdit(row)"> 编辑 </el-button>
            <el-button size="small" type="danger" @click.stop="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 详情对话框 -->
    <AssessmentDetailDialog
      v-model="detailDialogVisible"
      :assessment-data="currentAssessment"
      @close="detailDialogVisible = false"
    />

    <!-- 表单对话框 -->
    <AssessmentFormDialog
      v-model="formDialogVisible"
      :assessment-data="currentAssessment"
      :staff-data="staffData"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'
import AssessmentDetailDialog from './AssessmentDetailDialog.vue'
import AssessmentFormDialog from './AssessmentFormDialog.vue'

const props = defineProps({
  staffId: {
    type: [String, Number],
    required: true,
  },
  staffData: {
    type: Object,
    required: true,
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const currentAssessment = ref(null)

// 对话框控制
const detailDialogVisible = ref(false)
const formDialogVisible = ref(false)

// 获取考核结果标签类型
const getResultTagType = (result) => {
  const resultMap = {
    合格: 'success',
    不合格: 'danger',
    优秀: 'warning',
    良好: 'info',
  }
  return resultMap[result] || 'info'
}

// 加载数据
const loadData = async () => {
  if (!props.staffId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/sar/list/${props.staffId}/`)
    tableData.value = response.list || response
  } catch (error) {
    console.error('获取考核记录失败:', error)
    ElMessage.error('获取考核记录失败')
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 事件处理
const handleView = (row) => {
  currentAssessment.value = row
  detailDialogVisible.value = true
}

const handleAdd = () => {
  currentAssessment.value = null
  formDialogVisible.value = true
}

const handleEdit = (row) => {
  currentAssessment.value = row
  formDialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除考核记录"${row.assessment_name}"吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await del(`organizational-management/sar/delete/${row.rid}/`)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      showErrorTip(error)
    }
  }
}

const handleFormSuccess = () => {
  loadData()
}

// 监听staffId变化，重新加载数据
watch(
  () => props.staffId,
  (newVal) => {
    if (newVal) {
      loadData()
    }
  },
  { immediate: true },
)

// 组件挂载后自动加载数据
onMounted(() => {
  if (props.staffId) {
    loadData()
  }
})

// 提供给外部刷新
defineExpose({ loadData })
</script>

<style scoped>
.assessment-table-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.section-title.mb-0 {
  margin-bottom: 0;
}

:deep(.el-table) {
  --el-table-border-color: #e5e7eb;
}

:deep(.el-table th) {
  background-color: #f9fafb !important;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table__fixed-header-wrapper th) {
  background-color: #f9fafb !important;
}

:deep(.el-table td) {
  padding: 0.5rem;
}
</style>
