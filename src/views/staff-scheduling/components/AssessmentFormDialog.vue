<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="assessment-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="assessment-form"
      >
        <!-- 考核信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">考核信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="考核名称" prop="assessment_name">
              <el-input
                v-model="form.assessment_name"
                placeholder="请输入考核名称"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="考核日期" prop="assessment_date">
              <el-date-picker
                v-model="form.assessment_date"
                type="date"
                placeholder="请选择考核日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="考核结果" prop="assessment_result">
              <el-input
                v-model="form.assessment_result"
                placeholder="请输入考核结果"
                class="w-full"
              />
            </el-form-item>
          </div>

          <el-form-item label="考核评语" prop="assessment_comment">
            <el-input
              v-model="form.assessment_comment"
              type="textarea"
              :rows="4"
              placeholder="请输入考核评语"
              class="w-full"
            />
          </el-form-item>

          <el-form-item label="考核附件" prop="attachment">
            <FileUpload
              :file-types="['jpg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"
              :max-size="20"
              :multiple="true"
              :limit="5"
              v-model="form.attachment"
              :urls="form.attachment_urls"
              action="file/sar/upload/"
              field="staff_assessment_record_attachment"
              upload-text="上传考核附件"
              custom-tip-text="支持JPG、PNG、PDF、DOC、DOCX、XLS、XLSX格式，最多上传5个文件，每个文件不超过20MB"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.assessmentData ? '保存修改' : '提交' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElMessage,
  ElDatePicker,
} from 'element-plus'
import { post, put } from '@/utils/request.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import FileUpload from '@/components/FileUpload.vue'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  assessmentData: {
    type: Object,
    default: null,
  },
  staffData: {
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.assessmentData ? '编辑考核记录' : '新增考核记录'
})

// 表单数据
const form = reactive({
  rid: '',
  assessment_name: '',
  assessment_date: '',
  assessment_result: '',
  assessment_comment: '',
  attachment: [],
  attachment_urls: [],
})

// 表单验证规则
const rules = {
  assessment_name: [{ required: true, message: '请输入考核名称', trigger: 'blur' }],
  assessment_date: [{ required: true, message: '请选择考核日期', trigger: 'change' }],
  assessment_result: [{ required: true, message: '请输入考核结果', trigger: 'blur' }],
  assessment_comment: [{ required: true, message: '请输入考核评语', trigger: 'blur' }],
}

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      setTimeout(() => {
        resetForm()
        if (props.assessmentData) {
          // 编辑模式：使用传入的考核数据
          const processedData = transformAPIDataToForm(props.assessmentData)
          Object.assign(form, processedData)
        }
      }, 5)

      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    rid: apiData.rid,
    assessment_name: apiData.assessment_name || '',
    assessment_date: apiData.assessment_date || '',
    assessment_result: apiData.assessment_result || '',
    assessment_comment: apiData.assessment_comment || '',
    attachment: apiData.attachment || [],
    attachment_urls: apiData.attachment_urls || [],
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    rid: '',
    assessment_name: '',
    assessment_date: '',
    assessment_result: '',
    assessment_comment: '',
    attachment: [],
    attachment_urls: [],
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      assessment_name: form.assessment_name,
      assessment_date: form.assessment_date,
      assessment_result: form.assessment_result,
      assessment_comment: form.assessment_comment,
      attachment: form.attachment,
    }

    let res
    if (!props.assessmentData) {
      // 新增模式
      res = await post(`organizational-management/sar/create/${props.staffData.sid}/`, submitData)
    } else {
      // 编辑模式
      res = await put(`organizational-management/sar/update/${form.rid}/`, submitData)
    }

    ElMessage.success(props.assessmentData ? '考核记录更新成功' : '考核记录创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-picker) {
  width: 100%;
}

:deep(.el-date-picker .el-input__wrapper) {
  border-color: #dcdfe6;
}

:deep(.el-date-picker:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-picker.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
