<template>
  <el-dialog
    v-model="visible"
    title="部门管理"
    width="800px"
    class="department-dialog"
    :close-on-click-modal="false"
    :destroy-on-close="false"
  >
    <div class="mb-4 flex justify-between items-center">
      <el-button
        type="primary"
        @click="showAddForm"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon><Plus /></el-icon>
        新增部门
      </el-button>
    </div>

    <!-- 部门列表表格 -->
    <div class="table-container">
      <el-table 
        :data="departments" 
        stripe 
        class="w-full"
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <el-table-column prop="name" label="部门名称" min-width="200" />
        <el-table-column prop="created_at" label="创建时间" width="180" align="center" />
        <el-table-column prop="updated_at" label="更新时间" width="180" align="center" />
        <el-table-column label="操作" width="150" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="editDepartment(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="deleteDepartment(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 部门表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'add' ? '新增部门' : '编辑部门'"
      width="500px"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="departmentForm"
        :rules="formRules"
        label-width="80px"
        class="department-form"
      >
        <el-form-item label="部门名称" prop="name">
          <el-input
            v-model="departmentForm.name"
            placeholder="请输入部门名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="saveDepartment"
            :loading="submitting"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            {{ formMode === 'add' ? '新增' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { get, post, put, del } from '@/utils/request.js'

// Props
const props = defineProps({
  visible: { type: Boolean, default: false },
})

const emit = defineEmits(['update:visible', 'save'])

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const departments = ref([])
const loading = ref(false)
const formVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const submitting = ref(false)
const formRef = ref(null)

// 部门表单数据
const departmentForm = reactive({
  rid: '',
  name: '',
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入部门名称', trigger: 'blur' },
    { min: 2, max: 50, message: '部门名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadDepartments()
  }
})

// 加载部门列表
const loadDepartments = async () => {
  try {
    loading.value = true
    const response = await get('maternity-center/department/list/')
    departments.value = response || []
  } catch (error) {
    console.error('加载部门列表失败:', error)
    ElMessage.error('加载部门列表失败')
  } finally {
    loading.value = false
  }
}

// 显示新增表单
const showAddForm = () => {
  formMode.value = 'add'
  Object.assign(departmentForm, {
    rid: '',
    name: '',
  })
  formVisible.value = true
}

// 编辑部门
const editDepartment = (dept) => {
  formMode.value = 'edit'
  Object.assign(departmentForm, {
    rid: dept.rid,
    name: dept.name,
  })
  formVisible.value = true
}

// 保存部门
const saveDepartment = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      name: departmentForm.name,
    }

    if (formMode.value === 'add') {
      await post('maternity-center/department/create/', submitData)
      ElMessage.success('新增部门成功')
    } else {
      await put(`maternity-center/department/update/${departmentForm.rid}/`, submitData)
      ElMessage.success('更新部门成功')
    }

    formVisible.value = false
    // 重新请求列表数据
    await loadDepartments()
    emit('save')
  } catch (error) {
    console.error('保存部门失败:', error)
    ElMessage.error('保存部门失败：' + (error.msg || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 删除部门
const deleteDepartment = async (dept) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除部门"${dept.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await del(`maternity-center/department/delete/${dept.rid}/`)
    ElMessage.success('删除部门成功')

    // 重新请求列表数据
    await loadDepartments()
    emit('save')
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除部门失败:', error)
    ElMessage.error('删除部门失败：' + (error.msg || '未知错误'))
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.department-dialog {
  --el-dialog-border-radius: 12px;
}

.table-container {
  max-height: 400px;
  overflow-y: auto;
}

.department-form {
  padding: 0 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
