<template>
  <el-dialog
    v-model="visible"
    title="健康检查记录详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div class="detail-content max-h-[60vh] overflow-y-auto">
      <div v-if="healthCheckData" class="space-y-6">
        <!-- 健康检查基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>体检项目：</label>
              <span>{{ healthCheckData.health_check_project }}</span>
            </div>
            <div class="detail-item">
              <label>体检日期：</label>
              <span>{{ healthCheckData.health_check_date }}</span>
            </div>
            <div class="detail-item">
              <label>体检结果：</label>
              <span>{{ healthCheckData.health_check_result }}</span>
            </div>
          </div>
        </div>
        <!-- 附件信息 -->
        <div
          class="detail-section"
          v-if="healthCheckData.attachment_urls && healthCheckData.attachment_urls.length"
        >
          <h3 class="section-title">附件</h3>
          <FileDisplayList :file-list="healthCheckData.attachment_urls" max-height="200px" />
        </div>
        <!-- 创建/更新时间 -->
        <div class="detail-section">
          <h3 class="section-title">操作信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ healthCheckData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ healthCheckData.updated_at }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElDialog, ElButton } from 'element-plus'
import FileDisplayList from '@/components/FileDisplayList.vue'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  healthCheckData: { required: true },
})
const emit = defineEmits(['update:modelValue', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}
.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}
.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}
.detail-item {
  display: flex;
  align-items: center;
}
.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}
:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
