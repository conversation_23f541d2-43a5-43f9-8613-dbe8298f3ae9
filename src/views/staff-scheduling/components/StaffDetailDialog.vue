<template>
  <el-dialog
    v-model="visible"
    title="员工详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>员工姓名：</label>
              <span>{{ detailData.name }}</span>
            </div>
            <div class="detail-item">
              <label>员工工号：</label>
              <span>{{ detailData.staff_number }}</span>
            </div>
            <div class="detail-item">
              <label>系统ID：</label>
              <span class="text-sm text-gray-500">{{ detailData.sid }}</span>
            </div>
            <div class="detail-item">
              <label>联系电话：</label>
              <span>{{ detailData.phone }}</span>
            </div>
            <div class="detail-item">
              <label>性别：</label>
              <span>{{ detailData.gender_display }}</span>
            </div>
            <div class="detail-item">
              <label>出生日期：</label>
              <span>{{ detailData.birth_date }}</span>
            </div>
            <div class="detail-item">
              <label>身份证号：</label>
              <span>{{ detailData.identity_number || '未填写' }}</span>
            </div>
            <div class="detail-item">
              <label>民族：</label>
              <span>{{ detailData.ethnicity }}</span>
            </div>
            <div class="detail-item">
              <label>籍贯：</label>
              <span>{{ detailData.native_place }}</span>
            </div>
            <div class="detail-item">
              <label>血型：</label>
              <span>{{ detailData.blood_type_display }}</span>
            </div>
            <div class="detail-item">
              <label>家庭住址：</label>
              <span>{{ detailData.home_address }}</span>
            </div>
            <div class="detail-item">
              <label>员工状态：</label>
              <el-tag :type="getStatusType(detailData.is_active_display)">
                {{ detailData.is_active_display }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 紧急联系人信息 -->
        <div class="detail-section">
          <h3 class="section-title">紧急联系人信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>紧急联系人：</label>
              <span>{{ detailData.emergency_contact }}</span>
            </div>
            <div class="detail-item">
              <label>联系电话：</label>
              <span>{{ detailData.emergency_contact_phone }}</span>
            </div>
            <div class="detail-item">
              <label>关系：</label>
              <el-tag type="info">{{ detailData.emergency_contact_relation_display }}</el-tag>
            </div>
          </div>
        </div>

        <!-- 工作信息 -->
        <div class="detail-section">
          <h3 class="section-title">工作信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>所属部门：</label>
              <el-tag type="info">{{ detailData.department?.name }}</el-tag>
            </div>
            <div class="detail-item">
              <label>职位：</label>
              <span>{{ detailData.position || '未填写' }}</span>
            </div>
            <div class="detail-item">
              <label>角色：</label>
              <el-tag type="warning">{{ detailData.role?.name }}</el-tag>
            </div>
            <div class="detail-item">
              <label>入职日期：</label>
              <span>{{ detailData.hire_date }}</span>
            </div>
            <div class="detail-item">
              <label>合同号：</label>
              <span>{{ detailData.contract_number }}</span>
            </div>
            <div class="detail-item">
              <label>合同有效期：</label>
              <span>{{ detailData.contract_validity_date }}</span>
            </div>
            <div class="detail-item">
              <label>最后登录：</label>
              <span>{{ detailData.last_login || '从未登录' }}</span>
            </div>
          </div>
        </div>

        <!-- 所属机构信息 -->
        <div v-if="detailData.maternity_center" class="detail-section">
          <h3 class="section-title">所属机构信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>机构名称：</label>
              <span>{{ detailData.maternity_center.name }}</span>
            </div>
            <div class="detail-item">
              <label>机构地址：</label>
              <span>{{ detailData.maternity_center.address }}</span>
            </div>
            <div class="detail-item">
              <label>联系人：</label>
              <span>{{ detailData.maternity_center.contact }}</span>
            </div>
            <div class="detail-item">
              <label>机构容量：</label>
              <span>{{ detailData.maternity_center.capacity }}人</span>
            </div>
            <div class="detail-item">
              <label>机构状态：</label>
              <el-tag :type="detailData.maternity_center.status === 1 ? 'success' : 'danger'">
                {{ detailData.maternity_center.status === 1 ? '正常' : '停用' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>机构标识：</label>
              <span class="text-sm text-gray-500">{{
                detailData.maternity_center.file_identifier
              }}</span>
            </div>
          </div>
        </div>

        <!-- 角色权限信息 -->
        <div v-if="detailData.role?.permissions?.length" class="detail-section">
          <h3 class="section-title">角色权限</h3>
          <div class="mt-4">
            <div class="detail-item mb-3">
              <label>角色描述：</label>
              <span>{{ detailData.role.description }}</span>
            </div>
            <div class="detail-item">
              <label>权限列表：</label>
            </div>
            <div class="mt-2 flex flex-wrap gap-2">
              <el-tag
                v-for="permission in detailData.role.permissions"
                :key="permission"
                size="small"
                effect="plain"
                type="success"
              >
                {{ permission }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 合同文件列表 -->
        <ContractTable :staff-id="props.itemId" :staff-data="detailData" />

        <!-- 资质证书列表 -->
        <QualificationTable :staff-id="props.itemId" :staff-data="detailData" />
        <!-- 培训记录列表 -->
        <TrainingTable :staff-id="props.itemId" :staff-data="detailData" />

        <!-- 健康检查记录列表 -->
        <HealthCheckTable :staff-id="props.itemId" :staff-data="detailData" />

        <!-- 考核记录列表 -->
        <AssessmentTable :staff-id="props.itemId" :staff-data="detailData" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleEdit"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑员工
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { ElDialog, ElButton, ElTag, ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'
import TrainingTable from './TrainingTable.vue'
import HealthCheckTable from './HealthCheckTable.vue'
import AssessmentTable from './AssessmentTable.vue'
import ContractTable from './ContractTable.vue'
import QualificationTable from './QualificationTable.vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  itemId: { type: [String, Number], default: null },
})

const emit = defineEmits(['update:visible', 'edit', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/staff/detail/${props.itemId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取员工详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 获取员工状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    在职: 'success',
    离职: 'info',
    停职: 'danger',
    试用期: 'warning',
  }
  return statusMap[status] || 'info'
}

const handleClose = () => {
  emit('close')
  emit('update:visible', false)
  detailData.value = null
}

const handleEdit = () => {
  emit('edit', detailData.value)
  visible.value = false
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
