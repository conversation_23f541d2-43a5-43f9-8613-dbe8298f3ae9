<template>
  <el-dialog
    v-model="visible"
    title="考核记录详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div class="detail-content max-h-[60vh] overflow-y-auto">
      <div v-if="assessmentData" class="space-y-6">
        <!-- 考核基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>考核名称：</label>
              <span>{{ assessmentData.assessment_name }}</span>
            </div>
            <div class="detail-item">
              <label>考核日期：</label>
              <span>{{ assessmentData.assessment_date }}</span>
            </div>
            <div class="detail-item">
              <label>考核结果：</label>
              <span>{{ assessmentData.assessment_result }}</span>
            </div>
          </div>
          <div class="detail-item mt-4">
            <label>考核评语：</label>
            <span class="text-gray-700">{{ assessmentData.assessment_comment }}</span>
          </div>
        </div>

        <!-- 附件信息 -->
        <div
          class="detail-section"
          v-if="assessmentData.attachment_urls && assessmentData.attachment_urls.length"
        >
          <h3 class="section-title">附件</h3>
          <FileDisplayList :file-list="assessmentData.attachment_urls" max-height="200px" />
        </div>

        <!-- 创建/更新时间 -->
        <div class="detail-section">
          <h3 class="section-title">操作信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ assessmentData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ assessmentData.updated_at }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElDialog, ElButton } from 'element-plus'
import FileDisplayList from '@/components/FileDisplayList.vue'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  assessmentData: { required: true },
})
const emit = defineEmits(['update:modelValue', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 删除获取考核结果标签类型的函数
// const getAssessmentResultTagType = (result) => {
//   const resultMap = {
//     合格: 'success',
//     不合格: 'danger',
//     优秀: 'warning',
//     良好: 'info',
//   }
//   return resultMap[result] || 'info'
// }

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}
.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}
.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}
.detail-item {
  display: flex;
  align-items: flex-start;
}
.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}
:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
