<template>
  <el-dialog
    v-model="visible"
    title="合同文件详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div class="detail-content max-h-[60vh] overflow-y-auto">
      <div v-if="contractData" class="space-y-6">
        <!-- 合同基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>合同号：</label>
              <span>{{ contractData.contract_number }}</span>
            </div>
            <div class="detail-item">
              <label>合同有效期：</label>
              <span>{{ contractData.contract_validity_period }}</span>
            </div>
          </div>
        </div>

        <!-- 合同文件 -->
        <div class="detail-section" v-if="contractData.file_url && contractData.file_url.length">
          <h3 class="section-title">附件</h3>
          <FileDisplayList :file-list="contractData.file_url" max-height="200px" />
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElDialog, ElButton } from 'element-plus'
import FileDisplayList from '@/components/FileDisplayList.vue'

const props = defineProps({
  modelValue: { type: Boolean, default: false },
  contractData: { required: true },
})
const emit = defineEmits(['update:modelValue', 'close'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}
.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}
.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}
.detail-item {
  display: flex;
  align-items: center;
}
.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}
.file-preview-section {
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}
.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.file-info {
  display: flex;
  align-items: center;
}
.file-name {
  font-size: 0.875rem;
  color: #374151;
}
.file-actions {
  display: flex;
  gap: 0.5rem;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}
:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}
:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
