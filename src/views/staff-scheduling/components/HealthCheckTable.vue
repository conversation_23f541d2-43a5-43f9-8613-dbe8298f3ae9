<template>
  <div class="health-check-section mr-4">
    <div class="flex items-center justify-between mb-4">
      <h3 class="section-title mb-0">健康检查记录</h3>
      <div class="flex gap-2">
        <el-button
          size="small"
          type="primary"
          @click="handleAddHealthCheck"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-1"><Plus /></el-icon>
          新增健康检查
        </el-button>
      </div>
    </div>
    <div v-loading="loading" class="health-check-table-container">
      <el-table
        :data="records"
        stripe
        class="w-full"
        size="small"
        @row-click="handleHealthCheckRowClick"
      >
        <el-table-column
          prop="health_check_project"
          label="体检项目"
          min-width="120"
          align="center"
        />
        <el-table-column prop="health_check_date" label="体检日期" min-width="110" align="center" />
        <el-table-column
          prop="health_check_result"
          label="体检结果"
          min-width="80"
          align="center"
        />
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click.stop="handleEditHealthCheck(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click.stop="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 健康检查记录表单对话框 -->
    <HealthCheckFormDialog
      v-model="healthCheckFormVisible"
      :health-check-data="currentHealthCheck"
      :staff-data="staffData"
      @success="handleHealthCheckSuccess"
    />

    <!-- 健康检查记录详情对话框 -->
    <HealthCheckDetailDialog
      v-model="healthCheckDetailVisible"
      :health-check-data="selectedHealthCheckDetail"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElButton, ElTable, ElTableColumn, ElIcon, ElMessage, ElMessageBox } from 'element-plus'
import { get, del } from '@/utils/request.js'
import HealthCheckFormDialog from './HealthCheckFormDialog.vue'
import HealthCheckDetailDialog from './HealthCheckDetailDialog.vue'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  staffId: { type: [String, Number], required: true },
  staffData: { type: Object, default: null },
})

const loading = ref(false)
const records = ref([])
const healthCheckFormVisible = ref(false)
const currentHealthCheck = ref(null)
const healthCheckDetailVisible = ref(false)
const selectedHealthCheckDetail = ref(null)

const fetchRecords = async () => {
  if (!props.staffId) return
  loading.value = true
  try {
    const response = await get(`organizational-management/shr/list/${props.staffId}/`)
    records.value = response || []
  } catch (error) {
    showErrorTip(error)
    records.value = []
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除健康检查记录"${row.health_check_project}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )
    await del(`organizational-management/shr/delete/${row.rid}/`)
    ElMessage.success('健康检查记录删除成功')
    fetchRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除健康检查记录失败')
    }
  }
}

const handleAddHealthCheck = () => {
  currentHealthCheck.value = null
  healthCheckFormVisible.value = true
}

const handleEditHealthCheck = (healthCheck) => {
  currentHealthCheck.value = healthCheck
  healthCheckFormVisible.value = true
}

const handleHealthCheckRowClick = (row) => {
  selectedHealthCheckDetail.value = row
  healthCheckDetailVisible.value = true
}

const handleHealthCheckSuccess = () => {
  fetchRecords() // 重新加载健康检查记录
}

watch(
  () => props.staffId,
  () => {
    fetchRecords()
  },
  { immediate: true },
)

onMounted(() => {
  fetchRecords()
})

// 提供给外部刷新
defineExpose({ fetchRecords })
</script>

<style scoped>
.health-check-table-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.section-title.mb-0 {
  margin-bottom: 0;
}

:deep(.el-table) {
  --el-table-border-color: #e5e7eb;
}

:deep(.el-table th) {
  background-color: #f9fafb !important;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table__fixed-header-wrapper th) {
  background-color: #f9fafb !important;
}

:deep(.el-table td) {
  padding: 0.5rem;
}
</style>
