<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="health-check-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="health-check-form">
        <!-- 健康检查信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">健康检查信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="体检项目" prop="health_check_project">
              <el-input
                v-model="form.health_check_project"
                placeholder="请输入体检项目"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="体检日期" prop="health_check_date">
              <el-date-picker
                v-model="form.health_check_date"
                type="date"
                placeholder="请选择体检日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="体检结果" prop="health_check_result">
              <el-input
                v-model="form.health_check_result"
                placeholder="请输入体检结果"
                class="w-full"
              />
            </el-form-item>
          </div>

          <el-form-item label="体检附件" prop="attachment">
            <FileUpload
              :file-types="['jpg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']"
              :max-size="20"
              :multiple="true"
              :limit="5"
              v-model="form.attachment"
              :urls="form.attachment_urls"
              action="file/shr/upload/"
              field="staff_health_check_record_attachment"
              upload-text="上传体检附件"
              custom-tip-text="支持JPG、PNG、PDF、DOC、DOCX、XLS、XLSX格式，最多上传5个文件，每个文件不超过20MB"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.healthCheckData ? '保存修改' : '提交' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElMessage,
  ElDatePicker,
} from 'element-plus'
import { post, put } from '@/utils/request.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'

import FileUpload from '@/components/FileUpload.vue'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  healthCheckData: {
    type: Object,
    default: null,
  },
  staffData: {
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.healthCheckData ? '编辑健康检查记录' : '新增健康检查记录'
})

// 表单数据
const form = reactive({
  rid: '',
  health_check_project: '',
  health_check_date: '',
  health_check_result: '',
  attachment: [],
  attachment_urls: [],
})

// 表单验证规则
const rules = {
  health_check_project: [{ required: true, message: '请输入体检项目', trigger: 'blur' }],
  health_check_date: [{ required: true, message: '请选择体检日期', trigger: 'change' }],
  health_check_result: [{ required: true, message: '请选择体检结果', trigger: 'change' }],
}

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      setTimeout(() => {
        resetForm()
        if (props.healthCheckData) {
          // 编辑模式：使用传入的健康检查数据
          const processedData = transformAPIDataToForm(props.healthCheckData)
          console.log('processedData', processedData)
          Object.assign(form, processedData)
          console.log('form', form)
        }
      }, 5)

      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    rid: apiData.rid,
    health_check_project: apiData.health_check_project || '',
    health_check_date: apiData.health_check_date || '',
    health_check_result: apiData.health_check_result || '',
    attachment: apiData.attachment || [],
    attachment_urls: apiData.attachment_urls || [],
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    rid: '',
    health_check_project: '',
    health_check_date: '',
    health_check_result: '',
    attachment: [],
    attachment_urls: [],
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      health_check_project: form.health_check_project,
      health_check_date: form.health_check_date,
      health_check_result: form.health_check_result,
      attachment: form.attachment,
    }

    let res
    if (!props.healthCheckData) {
      // 新增模式
      res = await post(`organizational-management/shr/create/${props.staffData.sid}/`, submitData)
    } else {
      // 编辑模式
      res = await put(`organizational-management/shr/update/${form.rid}/`, submitData)
    }

    ElMessage.success(props.healthCheckData ? '健康检查记录更新成功' : '健康检查记录创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-picker) {
  width: 100%;
}

:deep(.el-date-picker .el-input__wrapper) {
  border-color: #dcdfe6;
}

:deep(.el-date-picker:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-picker.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style> 