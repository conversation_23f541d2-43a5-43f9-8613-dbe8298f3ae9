<template>
  <el-dialog v-model="visible" title="资质证书管理" width="900px" class="certificate-dialog">
    <div class="mb-4 flex justify-between items-center">
      <h4 class="text-lg font-semibold text-gray-800">{{ staffData?.name }} - 证书管理</h4>
      <el-button
        type="primary"
        @click="showAddForm"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon><Plus /></el-icon>
        添加证书
      </el-button>
    </div>

    <el-table :data="certificates" stripe class="w-full">
      <el-table-column prop="name" label="证书名称" min-width="150" />
      <el-table-column prop="issueOrg" label="颁发机构" min-width="120" />
      <el-table-column prop="issueDate" label="颁发日期" width="120" />
      <el-table-column prop="expiryDate" label="有效期至" width="120">
        <template #default="{ row }">
          <span :class="{ 'text-red-500': isExpiringSoon(row.expiryDate) }">
            {{ row.expiryDate }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getCertStatusType(row.status)">{{ row.statusText }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button size="small" @click="editCertificate(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteCertificate(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 证书表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'add' ? '添加证书' : '编辑证书'"
      width="600px"
      append-to-body
    >
      <el-form ref="certFormRef" :model="certForm" :rules="certRules" label-width="100px">
        <el-form-item label="证书名称" prop="name">
          <el-input v-model="certForm.name" placeholder="请输入证书名称" />
        </el-form-item>
        <el-form-item label="颁发机构" prop="issueOrg">
          <el-input v-model="certForm.issueOrg" placeholder="请输入颁发机构" />
        </el-form-item>
        <el-form-item label="颁发日期" prop="issueDate">
          <el-date-picker
            v-model="certForm.issueDate"
            type="date"
            placeholder="选择颁发日期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="有效期至" prop="expiryDate">
          <el-date-picker
            v-model="certForm.expiryDate"
            type="date"
            placeholder="选择有效期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="证书状态" prop="status">
          <el-select v-model="certForm.status" placeholder="请选择状态" class="w-full">
            <el-option label="有效" value="valid" />
            <el-option label="即将到期" value="expiring" />
            <el-option label="已过期" value="expired" />
            <el-option label="已吊销" value="revoked" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="certForm.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="formVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="saveCertificate"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存更改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import {
  ElDialog,
  ElTable,
  ElTableColumn,
  ElButton,
  ElIcon,
  ElTag,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElMessage,
  ElMessageBox,
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  staffData: { type: Object, default: null },
})

const emit = defineEmits(['update:visible', 'save'])

const formVisible = ref(false)
const formMode = ref('add')
const certFormRef = ref()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const certificates = ref([
  {
    id: 1,
    name: '护士执业证',
    issueOrg: '卫健委',
    issueDate: '2020-05-15',
    expiryDate: '2025-05-15',
    status: 'valid',
    statusText: '有效',
    remarks: '',
  },
  {
    id: 2,
    name: '护师资格证',
    issueOrg: '人社部',
    issueDate: '2021-08-20',
    expiryDate: '2024-08-20',
    status: 'expiring',
    statusText: '即将到期',
    remarks: '需要续期',
  },
])

const certForm = reactive({
  id: '',
  name: '',
  issueOrg: '',
  issueDate: '',
  expiryDate: '',
  status: 'valid',
  remarks: '',
})

const certRules = {
  name: [{ required: true, message: '请输入证书名称', trigger: 'blur' }],
  issueOrg: [{ required: true, message: '请输入颁发机构', trigger: 'blur' }],
  issueDate: [{ required: true, message: '请选择颁发日期', trigger: 'change' }],
  expiryDate: [{ required: true, message: '请选择有效期', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

const getCertStatusType = (status) => {
  const statusMap = { valid: 'success', expiring: 'warning', expired: 'danger', revoked: 'info' }
  return statusMap[status] || 'info'
}

const isExpiringSoon = (expiryDate) => {
  const expiry = new Date(expiryDate)
  const now = new Date()
  const thirtyDaysLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  return expiry <= thirtyDaysLater
}

const showAddForm = () => {
  formMode.value = 'add'
  Object.assign(certForm, {
    id: '',
    name: '',
    issueOrg: '',
    issueDate: '',
    expiryDate: '',
    status: 'valid',
    remarks: '',
  })
  formVisible.value = true
}

const editCertificate = (cert) => {
  formMode.value = 'edit'
  Object.assign(certForm, cert)
  formVisible.value = true
}

const saveCertificate = async () => {
  try {
    await certFormRef.value.validate()
    if (formMode.value === 'add') {
      certificates.value.push({ ...certForm, id: Date.now() })
      ElMessage.success('证书添加成功')
    } else {
      const index = certificates.value.findIndex((c) => c.id === certForm.id)
      if (index !== -1) {
        certificates.value[index] = { ...certForm }
        ElMessage.success('证书更新成功')
      }
    }
    formVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const deleteCertificate = async (cert) => {
  try {
    await ElMessageBox.confirm(`确定要删除证书"${cert.name}"吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })
    certificates.value = certificates.value.filter((c) => c.id !== cert.id)
    ElMessage.success('证书删除成功')
  } catch {
    // 用户取消
  }
}

const handleSave = () => {
  emit('save', certificates.value)
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.certificate-dialog {
  --el-dialog-border-radius: 12px;
}
</style>
