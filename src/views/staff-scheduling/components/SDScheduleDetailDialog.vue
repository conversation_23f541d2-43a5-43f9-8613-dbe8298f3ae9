<template>
  <el-dialog
    v-model="visible"
    title="排班详情"
    width="600px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>员工姓名：</label>
              <span>{{ detailData.staff_name }}</span>
            </div>
            <div class="detail-item">
              <label>职位：</label>
              <span>{{ detailData.position }}</span>
            </div>
            <div class="detail-item">
              <label>排班状态：</label>
              <el-tag :type="getOrgScheduleStatusTagType(detailData.status)">
                {{ detailData.status_display }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 排班信息 -->
        <div class="detail-section">
          <h3 class="section-title">排班信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>排班日期：</label>
              <span>{{ detailData.schedule_date }}</span>
            </div>
            <div class="detail-item">
              <label>班次类型：</label>
              <el-tag :type="getShiftTagType(detailData.shift_type)">
                {{ detailData.shift_type_display }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div v-if="detailData.remark" class="detail-section">
          <h3 class="section-title">备注信息</h3>
          <div class="reason-content p-4 bg-gray-50 rounded-lg">
            {{ detailData.remark }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleDelete" type="danger" plain> 删除 </el-button>
        <el-button
          @click="handleEdit"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { getOrgScheduleStatusTagType } from '@/utils/constants.js'
import { get, del } from '@/utils/request.js'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'edit', 'close', 'delete'])

const loading = ref(false)
const detailData = ref(null)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  } else if (!newValue) {
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/schedule/detail/${props.itemId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取排班详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

const handleEdit = () => {
  emit('edit', detailData.value)
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除此排班记录吗？删除后无法恢复。', '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    })

    loading.value = true

    await del(`organizational-management/schedule/delete/${props.itemId}/`)

    ElMessage.success('删除成功')
    emit('delete', props.itemId)
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除排班失败:', error)
      ElMessage.error('删除失败')
    }
  } finally {
    loading.value = false
  }
}

const getShiftTagType = (shiftType) => {
  const shiftMap = {
    MORNING: 'success',
    AFTERNOON: 'warning',
    NIGHT: 'info',
  }
  return shiftMap[shiftType] || 'info'
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.reason-content {
  color: #374151;
  line-height: 1.625;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
