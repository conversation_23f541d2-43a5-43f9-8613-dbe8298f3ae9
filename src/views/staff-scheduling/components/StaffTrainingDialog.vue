<template>
  <el-dialog v-model="visible" title="培训记录管理" width="900px" class="training-dialog">
    <div class="mb-4 flex justify-between items-center">
      <h4 class="text-lg font-semibold text-gray-800">{{ staffData?.name }} - 培训记录</h4>
      <el-button
        type="primary"
        @click="showAddForm"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon><Plus /></el-icon>
        添加培训
      </el-button>
    </div>

    <el-table :data="trainings" stripe class="w-full">
      <el-table-column prop="title" label="培训名称" min-width="150" />
      <el-table-column prop="type" label="培训类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getTrainingTypeColor(row.type)">{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="startDate" label="开始日期" width="120" />
      <el-table-column prop="endDate" label="结束日期" width="120" />
      <el-table-column prop="duration" label="培训时长" width="100" />
      <el-table-column prop="score" label="考核成绩" width="100">
        <template #default="{ row }">
          <span
            :class="{
              'text-green-600 font-semibold': row.score >= 80,
              'text-red-500': row.score < 60,
            }"
          >
            {{ row.score }}分
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.statusText }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button size="small" @click="editTraining(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteTraining(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 培训表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'add' ? '添加培训' : '编辑培训'"
      width="600px"
      append-to-body
    >
      <el-form
        ref="trainingFormRef"
        :model="trainingForm"
        :rules="trainingRules"
        label-width="100px"
      >
        <el-form-item label="培训名称" prop="title">
          <el-input v-model="trainingForm.title" placeholder="请输入培训名称" />
        </el-form-item>
        <el-form-item label="培训类型" prop="type">
          <el-select v-model="trainingForm.type" placeholder="请选择培训类型" class="w-full">
            <el-option label="岗前培训" value="岗前培训" />
            <el-option label="在岗培训" value="在岗培训" />
            <el-option label="专业技能" value="专业技能" />
            <el-option label="安全培训" value="安全培训" />
            <el-option label="管理培训" value="管理培训" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="trainingForm.startDate"
            type="date"
            placeholder="选择开始日期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="trainingForm.endDate"
            type="date"
            placeholder="选择结束日期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="培训时长" prop="duration">
          <el-input v-model="trainingForm.duration" placeholder="如：8小时、2天" />
        </el-form-item>
        <el-form-item label="考核成绩" prop="score">
          <el-input-number
            v-model="trainingForm.score"
            :min="0"
            :max="100"
            placeholder="分数"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="培训状态" prop="status">
          <el-select v-model="trainingForm.status" placeholder="请选择状态" class="w-full">
            <el-option label="已完成" value="completed" />
            <el-option label="进行中" value="ongoing" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="trainingForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="formVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="saveTraining"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存更改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import {
  ElDialog,
  ElTable,
  ElTableColumn,
  ElButton,
  ElIcon,
  ElTag,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElInputNumber,
  ElMessage,
  ElMessageBox,
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  staffData: { type: Object, default: null },
})

const emit = defineEmits(['update:visible', 'save'])

const formVisible = ref(false)
const formMode = ref('add')
const trainingFormRef = ref()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const trainings = ref([
  {
    id: 1,
    title: '新员工岗前培训',
    type: '岗前培训',
    startDate: '2024-01-10',
    endDate: '2024-01-12',
    duration: '24小时',
    score: 85,
    status: 'completed',
    statusText: '已完成',
  },
  {
    id: 2,
    title: '急救技能培训',
    type: '专业技能',
    startDate: '2024-01-15',
    endDate: '2024-01-15',
    duration: '8小时',
    score: 92,
    status: 'completed',
    statusText: '已完成',
  },
])

const trainingForm = reactive({
  id: '',
  title: '',
  type: '',
  startDate: '',
  endDate: '',
  duration: '',
  score: 0,
  status: 'completed',
  remarks: '',
})

const trainingRules = {
  title: [{ required: true, message: '请输入培训名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择培训类型', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

const getTrainingTypeColor = (type) => {
  const colorMap = {
    岗前培训: 'primary',
    在岗培训: 'success',
    专业技能: 'warning',
    安全培训: 'danger',
    管理培训: 'info',
  }
  return colorMap[type] || ''
}

const getStatusType = (status) => {
  const statusMap = { completed: 'success', ongoing: 'warning', cancelled: 'info' }
  return statusMap[status] || 'info'
}

const showAddForm = () => {
  formMode.value = 'add'
  Object.assign(trainingForm, {
    id: '',
    title: '',
    type: '',
    startDate: '',
    endDate: '',
    duration: '',
    score: 0,
    status: 'completed',
    remarks: '',
  })
  formVisible.value = true
}

const editTraining = (training) => {
  formMode.value = 'edit'
  Object.assign(trainingForm, training)
  formVisible.value = true
}

const saveTraining = async () => {
  try {
    await trainingFormRef.value.validate()
    if (formMode.value === 'add') {
      trainings.value.push({
        ...trainingForm,
        id: Date.now(),
        statusText: getStatusText(trainingForm.status),
      })
      ElMessage.success('培训记录添加成功')
    } else {
      const index = trainings.value.findIndex((t) => t.id === trainingForm.id)
      if (index !== -1) {
        trainings.value[index] = { ...trainingForm, statusText: getStatusText(trainingForm.status) }
        ElMessage.success('培训记录更新成功')
      }
    }
    formVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const getStatusText = (status) => {
  const textMap = { completed: '已完成', ongoing: '进行中', cancelled: '已取消' }
  return textMap[status] || status
}

const deleteTraining = async (training) => {
  try {
    await ElMessageBox.confirm(`确定要删除培训记录"${training.title}"吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })
    trainings.value = trainings.value.filter((t) => t.id !== training.id)
    ElMessage.success('培训记录删除成功')
  } catch {
    // 用户取消
  }
}

const handleSave = () => {
  emit('save', trainings.value)
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.training-dialog {
  --el-dialog-border-radius: 12px;
}
</style>
