<template>
  <el-dialog
    v-model="visible"
    title="编辑排班信息"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="schedule-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="schedule-form">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="员工选择" prop="staff_sid">
              <el-select
                v-model="form.staff_sid"
                placeholder="请选择员工"
                class="w-full"
                :loading="baseDataStore.staffs.isLoading() || baseDataStore.staffs.searchLoading"
                filterable
                remote
                :remote-method="baseDataStore.staffs.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.staffs.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.staffs.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="职位" prop="position">
              <el-input v-model="form.position" placeholder="请输入职位信息" />
            </el-form-item>
          </div>
        </div>

        <!-- 排班信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">排班信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-form-item label="排班日期" prop="schedule_date">
              <el-date-picker
                v-model="form.schedule_date"
                type="date"
                placeholder="选择日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <el-form-item label="班次类型" prop="shift_type">
              <el-select v-model="form.shift_type" placeholder="请选择班次" class="w-full">
                <el-option
                  v-for="option in SHIFT_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">状态信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="排班状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" class="w-full">
                <el-option
                  v-for="option in ORG_SCHEDULE_STATUS_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息（可选）"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { put, get } from '@/utils/request.js'
import { SHIFT_TYPE_OPTIONS, ORG_SCHEDULE_STATUS_OPTIONS } from '@/utils/constants.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const baseDataStore = useBaseDataStore()
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const form = reactive({
  rid: '',
  staff_sid: '',
  position: '',
  schedule_date: '',
  shift_type: '',
  status: '',
  remark: '',
})

const rules = computed(() => ({
  staff_sid: [{ required: true, message: '请选择员工', trigger: 'change' }],
  position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  schedule_date: [{ required: true, message: '请选择排班日期', trigger: 'change' }],
  shift_type: [{ required: true, message: '请选择班次类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择排班状态', trigger: 'change' }],
}))

watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(resetForm, 5)
    if (visible) {
      if (props.itemId) {
        await fetchScheduleDetail(props.itemId)
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

const fetchScheduleDetail = async (scheduleId) => {
  if (!scheduleId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/schedule/detail/${scheduleId}/`)
    if (response) {
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到排班详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取排班详情失败:', error)
    ElMessage.error('获取排班详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    rid: apiData.rid,
    staff_sid: apiData.staff_sid,
    position: apiData.position,
    schedule_date: apiData.schedule_date,
    shift_type: apiData.shift_type,
    status: apiData.status,
    remark: apiData.remark || '',
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  Object.assign(form, {
    rid: '',
    staff_sid: '',
    position: '',
    schedule_date: '',
    shift_type: '',
    status: '',
    remark: '',
  })

  baseDataStore.staffs.clearSearch()

  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      staff_sid: form.staff_sid,
      position: form.position,
      schedule_date: form.schedule_date,
      shift_type: form.shift_type,
      status: form.status,
      remark: form.remark,
    }

    const res = await put(`organizational-management/schedule/update/${form.rid}/`, submitData)

    ElMessage.success('排班信息更新成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}

:deep(.el-date-editor .el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-editor .el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
