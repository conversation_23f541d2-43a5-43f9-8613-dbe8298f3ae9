<template>
  <el-dialog v-model="visible" title="考核记录管理" width="900px" class="assessment-dialog">
    <div class="mb-4 flex justify-between items-center">
      <h4 class="text-lg font-semibold text-gray-800">{{ staffData?.name }} - 考核记录</h4>
      <el-button
        type="primary"
        @click="showAddForm"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon><Plus /></el-icon>
        添加考核
      </el-button>
    </div>

    <el-table :data="assessments" stripe class="w-full">
      <el-table-column prop="title" label="考核名称" min-width="150" />
      <el-table-column prop="type" label="考核类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getAssessmentTypeColor(row.type)">{{ row.type }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="assessDate" label="考核日期" width="120" />
      <el-table-column prop="assessor" label="考核人" width="100" />
      <el-table-column prop="score" label="考核分数" width="100">
        <template #default="{ row }">
          <span
            :class="{
              'text-green-600 font-semibold': row.score >= 80,
              'text-yellow-600': row.score >= 60 && row.score < 80,
              'text-red-500': row.score < 60,
            }"
          >
            {{ row.score }}分
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="level" label="考核等级" width="100">
        <template #default="{ row }">
          <el-tag :type="getLevelType(row.level)">{{ row.level }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.statusText }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="{ row }">
          <el-button size="small" @click="editAssessment(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteAssessment(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 考核表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      :title="formMode === 'add' ? '添加考核' : '编辑考核'"
      width="600px"
      append-to-body
    >
      <el-form
        ref="assessmentFormRef"
        :model="assessmentForm"
        :rules="assessmentRules"
        label-width="100px"
      >
        <el-form-item label="考核名称" prop="title">
          <el-input v-model="assessmentForm.title" placeholder="请输入考核名称" />
        </el-form-item>
        <el-form-item label="考核类型" prop="type">
          <el-select v-model="assessmentForm.type" placeholder="请选择考核类型" class="w-full">
            <el-option label="年度考核" value="年度考核" />
            <el-option label="季度考核" value="季度考核" />
            <el-option label="月度考核" value="月度考核" />
            <el-option label="试用期考核" value="试用期考核" />
            <el-option label="专项考核" value="专项考核" />
          </el-select>
        </el-form-item>
        <el-form-item label="考核日期" prop="assessDate">
          <el-date-picker
            v-model="assessmentForm.assessDate"
            type="date"
            placeholder="选择考核日期"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="考核人" prop="assessor">
          <el-input v-model="assessmentForm.assessor" placeholder="请输入考核人" />
        </el-form-item>
        <el-form-item label="考核分数" prop="score">
          <el-input-number
            v-model="assessmentForm.score"
            :min="0"
            :max="100"
            placeholder="分数"
            class="w-full"
          />
        </el-form-item>
        <el-form-item label="考核等级" prop="level">
          <el-select v-model="assessmentForm.level" placeholder="请选择等级" class="w-full">
            <el-option label="优秀" value="优秀" />
            <el-option label="良好" value="良好" />
            <el-option label="合格" value="合格" />
            <el-option label="不合格" value="不合格" />
          </el-select>
        </el-form-item>
        <el-form-item label="考核状态" prop="status">
          <el-select v-model="assessmentForm.status" placeholder="请选择状态" class="w-full">
            <el-option label="已完成" value="completed" />
            <el-option label="进行中" value="ongoing" />
            <el-option label="待考核" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="考核说明" prop="remarks">
          <el-input
            v-model="assessmentForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入考核说明"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="formVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="saveAssessment"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存
        </el-button>
      </template>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存更改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import {
  ElDialog,
  ElTable,
  ElTableColumn,
  ElButton,
  ElIcon,
  ElTag,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElInputNumber,
  ElMessage,
  ElMessageBox,
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  staffData: { type: Object, default: null },
})

const emit = defineEmits(['update:visible', 'save'])

const formVisible = ref(false)
const formMode = ref('add')
const assessmentFormRef = ref()

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const assessments = ref([
  {
    id: 1,
    title: '2024年度工作考核',
    type: '年度考核',
    assessDate: '2024-01-05',
    assessor: '张主任',
    score: 85,
    level: '良好',
    status: 'completed',
    statusText: '已完成',
  },
  {
    id: 2,
    title: '第一季度绩效考核',
    type: '季度考核',
    assessDate: '2024-04-10',
    assessor: '李经理',
    score: 92,
    level: '优秀',
    status: 'completed',
    statusText: '已完成',
  },
])

const assessmentForm = reactive({
  id: '',
  title: '',
  type: '',
  assessDate: '',
  assessor: '',
  score: 0,
  level: '',
  status: 'completed',
  remarks: '',
})

const assessmentRules = {
  title: [{ required: true, message: '请输入考核名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择考核类型', trigger: 'change' }],
  assessDate: [{ required: true, message: '请选择考核日期', trigger: 'change' }],
  assessor: [{ required: true, message: '请输入考核人', trigger: 'blur' }],
  score: [{ required: true, message: '请输入考核分数', trigger: 'blur' }],
  level: [{ required: true, message: '请选择考核等级', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

const getAssessmentTypeColor = (type) => {
  const colorMap = {
    年度考核: 'primary',
    季度考核: 'success',
    月度考核: 'warning',
    试用期考核: 'danger',
    专项考核: 'info',
  }
  return colorMap[type] || ''
}

const getLevelType = (level) => {
  const levelMap = { 优秀: 'success', 良好: 'primary', 合格: 'warning', 不合格: 'danger' }
  return levelMap[level] || 'info'
}

const getStatusType = (status) => {
  const statusMap = { completed: 'success', ongoing: 'warning', pending: 'info' }
  return statusMap[status] || 'info'
}

const showAddForm = () => {
  formMode.value = 'add'
  Object.assign(assessmentForm, {
    id: '',
    title: '',
    type: '',
    assessDate: '',
    assessor: '',
    score: 0,
    level: '',
    status: 'completed',
    remarks: '',
  })
  formVisible.value = true
}

const editAssessment = (assessment) => {
  formMode.value = 'edit'
  Object.assign(assessmentForm, assessment)
  formVisible.value = true
}

const saveAssessment = async () => {
  try {
    await assessmentFormRef.value.validate()
    if (formMode.value === 'add') {
      assessments.value.push({
        ...assessmentForm,
        id: Date.now(),
        statusText: getStatusText(assessmentForm.status),
      })
      ElMessage.success('考核记录添加成功')
    } else {
      const index = assessments.value.findIndex((a) => a.id === assessmentForm.id)
      if (index !== -1) {
        assessments.value[index] = {
          ...assessmentForm,
          statusText: getStatusText(assessmentForm.status),
        }
        ElMessage.success('考核记录更新成功')
      }
    }
    formVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const getStatusText = (status) => {
  const textMap = { completed: '已完成', ongoing: '进行中', pending: '待考核' }
  return textMap[status] || status
}

const deleteAssessment = async (assessment) => {
  try {
    await ElMessageBox.confirm(`确定要删除考核记录"${assessment.title}"吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })
    assessments.value = assessments.value.filter((a) => a.id !== assessment.id)
    ElMessage.success('考核记录删除成功')
  } catch {
    // 用户取消
  }
}

const handleSave = () => {
  emit('save', assessments.value)
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.assessment-dialog {
  --el-dialog-border-radius: 12px;
}
</style>
