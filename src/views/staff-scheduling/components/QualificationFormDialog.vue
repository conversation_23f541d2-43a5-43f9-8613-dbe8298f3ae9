<template>
  <el-dialog
    v-model="visible"
    title="新增资质证书"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="qualification-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="qualification-form"
      >
        <!-- 证书信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">证书信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="证书名称" prop="certificate_name">
              <el-input
                v-model="form.certificate_name"
                placeholder="请输入证书名称"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="颁发机构" prop="issuing_authority">
              <el-input
                v-model="form.issuing_authority"
                placeholder="请输入颁发机构"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="有效期" prop="validity_period">
              <el-date-picker
                v-model="form.validity_period"
                type="date"
                placeholder="请选择有效期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </div>

          <el-form-item label="证书文件" prop="file">
            <FileUpload
              :file-types="['jpg', 'png', 'pdf', 'doc', 'docx']"
              :max-size="20"
              :multiple="false"
              :limit="1"
              v-model="form.file"
              action="file/sqc/upload/"
              field="staff_qualification_certificate_file"
              upload-text="上传证书文件"
              custom-tip-text="支持JPG、PNG、PDF、DOC、DOCX格式，文件不超过20MB"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          提交
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElMessage,
  ElDatePicker,
} from 'element-plus'
import { post } from '@/utils/request.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import FileUpload from '@/components/FileUpload.vue'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  staffData: {
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 表单数据
const form = reactive({
  certificate_name: '',
  issuing_authority: '',
  validity_period: '',
  file: [],
})

// 表单验证规则
const rules = {
  certificate_name: [{ required: true, message: '请输入证书名称', trigger: 'blur' }],
  issuing_authority: [{ required: true, message: '请输入颁发机构', trigger: 'blur' }],
  validity_period: [{ required: true, message: '请选择有效期', trigger: 'change' }],
  file: [{ required: true, message: '请上传证书文件', trigger: 'change' }],
}

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      setTimeout(() => {
        resetForm()
      }, 5)
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    certificate_name: '',
    issuing_authority: '',
    validity_period: '',
    file: [],
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 检查文件是否上传
    if (!form.file || form.file.length === 0) {
      ElMessage.warning('请上传证书文件')
      return
    }

    // 构造提交数据
    const submitData = {
      certificate_name: form.certificate_name,
      issuing_authority: form.issuing_authority,
      validity_period: form.validity_period,
      file: form.file[0], // 只取第一个文件的ID
    }

    const res = await post(
      `organizational-management/sqc/create/${props.staffData.sid}/`,
      submitData,
    )

    ElMessage.success('资质证书创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-picker) {
  width: 100%;
}

:deep(.el-date-picker .el-input__wrapper) {
  border-color: #dcdfe6;
}

:deep(.el-date-picker:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-picker.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
