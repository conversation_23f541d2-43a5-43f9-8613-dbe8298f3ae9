<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    align-center=""
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-position="left">
      <!-- 基本信息 -->
      <div class="form-section mb-6">
        <h4 class="section-title">排班基本信息</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <el-form-item label="选择部门" prop="department" class="col-span-2">
            <el-select
              v-model="form.department"
              placeholder="请选择部门"
              class="w-full"
              :loading="
                baseDataStore.departments.isLoading() || baseDataStore.departments.searchLoading
              "
              filterable
              remote
              :remote-method="baseDataStore.departments.performSearch"
              :clearable="true"
              reserve-keyword
              remote-show-suffix
              @change="handleDepartmentChange"
              @clear="baseDataStore.departments.clearSearch"
            >
              <el-option
                v-for="dept in baseDataStore.departments.getDisplayOptions()"
                :key="dept.value"
                :label="dept.label"
                :value="dept.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="开始日期" prop="start_date">
            <el-date-picker
              v-model="form.start_date"
              type="date"
              placeholder="选择开始日期"
              class="w-full"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-form-item>

          <el-form-item label="结束日期" prop="end_date">
            <el-date-picker
              v-model="form.end_date"
              type="date"
              placeholder="选择结束日期"
              class="w-full"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 员工分配 -->
      <div class="form-section mb-6">
        <h4 class="section-title">员工分配</h4>
        <div class="space-y-4">
          <div
            v-for="(schedule, index) in form.schedules"
            :key="schedule.id"
            class="assignment-item bg-gray-50 p-4 rounded-lg border border-gray-200"
          >
            <div class="flex items-end justify-between mb-4">
              <h5 class="text-sm font-medium text-gray-700">分配 {{ index + 1 }}</h5>
              <el-button
                v-if="form.schedules.length > 1"
                type="danger"
                size="small"
                circle
                @click="removeSchedule(index)"
              >
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <el-form-item :label="`员工`" :prop="`schedules.${index}.staff_sid`">
                <el-select
                  v-model="schedule.staff_sid"
                  placeholder="请选择员工"
                  class="w-full"
                  filterable
                  :loading="staffsLoading"
                  @change="(value) => handleStaffChange(value, index)"
                >
                  <el-option
                    v-for="staff in filteredStaffs"
                    :key="staff.sid"
                    :label="staff.name"
                    :value="staff.sid"
                    :disabled="isStaffSelected(staff.sid, index)"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="`班次`" :prop="`schedules.${index}.shift_type`">
                <el-select
                  v-model="schedule.shift_type"
                  placeholder="请选择班次"
                  class="w-full"
                  @change="() => handleShiftChange(index)"
                >
                  <el-option
                    v-for="shift in SHIFT_TYPE_OPTIONS"
                    :key="shift.value"
                    :label="shift.label"
                    :value="shift.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="`岗位`" :prop="`schedules.${index}.position`">
                <el-input
                  v-model="schedule.position"
                  placeholder="如：护士、产康师"
                  class="w-full"
                  @change="() => handlePositionChange(index)"
                />
              </el-form-item>
            </div>

            <!-- 后端验证错误显示 - 占整行 -->
            <div v-if="schedule.error" class="mt-4">
              <el-alert
                type="error"
                :title="schedule.error"
                show-icon
                :closable="false"
                size="small"
                class="mb-2"
              />
            </div>
          </div>

          <!-- 添加更多分配 -->
          <div class="text-center">
            <el-button
              type="primary"
              @click="addSchedule"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              <el-icon class="mr-2">
                <Plus />
              </el-icon>
              添加员工分配
            </el-button>
          </div>
        </div>
      </div>

      <!-- 备注 -->
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          class="w-full"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ mode === 'add' ? '提交' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
  ElAlert,
  ElMessage,
  ElIcon,
} from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import { get, post } from '@/utils/request.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { format } from 'date-fns'
import { SHIFT_TYPE_OPTIONS, getShiftTypeText } from '@/utils/constants.js'
import { formatDateRanges } from '@/utils/dateUtils.js'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  scheduleData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const validating = ref(false)

// 基础数据store
const baseDataStore = useBaseDataStore()

// 弹窗可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 弹窗标题
const dialogTitle = computed(() => {
  return props.mode === 'add' ? '制定新排班' : '编辑排班'
})

// 表单数据
const form = reactive({
  department: '',
  start_date: '',
  end_date: '',
  schedules: [
    {
      id: 1,
      staff_sid: '',
      shift_type: '',
      position: '',
      error: '',
    },
  ],
  remark: '',
})

// 员工选项
const staffs = ref([])
const staffsLoading = ref(false)

// 根据选择部门筛选员工
const filteredStaffs = computed(() => {
  return staffs.value
})

// 日期验证器
const validateStartDate = (rule, value, callback) => {
  if (value) {
    const today = format(new Date(), 'yyyy-MM-dd')
    if (value < today) {
      callback(new Error('开始日期不能早于今天'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请选择开始日期'))
  }
}

const validateEndDate = (rule, value, callback) => {
  if (value) {
    if (form.start_date && value < form.start_date) {
      callback(new Error('结束日期不能早于开始日期'))
    } else {
      callback()
    }
  } else {
    callback(new Error('请选择结束日期'))
  }
}

// 表单验证规则
const rules = {
  department: [{ required: true, message: '请选择部门', trigger: 'change' }],
  start_date: [{ validator: validateStartDate, trigger: 'change' }],
  end_date: [{ validator: validateEndDate, trigger: 'change' }],
}

// 新分配ID计数器
let assignmentIdCounter = 1

// 获取员工列表
const fetchStaffs = async (department) => {
  if (!department) {
    staffs.value = []
    return
  }

  staffsLoading.value = true
  try {
    const response = await get('organizational-management/staff/list/', {
      department: department,
      page: 1,
      page_size: 100, // 获取更多员工
    })

    if (response?.list) {
      staffs.value = response.list
    } else {
      staffs.value = []
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
    ElMessage.error('获取员工列表失败')
    staffs.value = []
  } finally {
    staffsLoading.value = false
  }
}

// 处理部门选择变化
const handleDepartmentChange = (department) => {
  // 清空所有员工分配
  form.schedules.forEach((schedule) => {
    schedule.staff_sid = ''
    schedule.position = ''
    clearValidationErrors(schedule)
  })

  // 获取该部门的员工列表
  if (department) {
    fetchStaffs(department)
  } else {
    staffs.value = []
  }
}

// 清理验证错误
const clearValidationErrors = (schedule) => {
  schedule.error = ''
}

// 处理员工选择变化
const handleStaffChange = (staffId, scheduleIndex) => {
  const staff = staffs.value.find((emp) => emp.sid === staffId)
  const schedule = form.schedules[scheduleIndex]

  if (staff && schedule) {
    schedule.position = staff.position

    // 清理验证错误
    clearValidationErrors(schedule)

    // 触发后端验证
    triggerBackendValidation()
  }
}

// 处理班次变化
const handleShiftChange = (scheduleIndex) => {
  const schedule = form.schedules[scheduleIndex]
  if (schedule) {
    // 清理验证错误
    clearValidationErrors(schedule)

    // 触发后端验证
    triggerBackendValidation()
  }
}

// 处理岗位变化
const handlePositionChange = (scheduleIndex) => {
  const schedule = form.schedules[scheduleIndex]
  if (schedule) {
    clearValidationErrors(schedule)
    triggerBackendValidation()
  }
}

// 处理日期变化
const handleDateChange = () => {
  // 清理所有验证错误
  form.schedules.forEach((schedule) => {
    clearValidationErrors(schedule)
  })

  // 触发后端验证
  triggerBackendValidation()
}

// 添加员工分配
const addSchedule = () => {
  assignmentIdCounter++
  form.schedules.push({
    id: assignmentIdCounter,
    staff_sid: '',
    shift_type: '',
    position: '',
    error: '',
  })
}

// 移除员工分配
const removeSchedule = (index) => {
  form.schedules.splice(index, 1)
  // 触发后端验证
  triggerBackendValidation()
}

// 防抖延迟
let validationTimer = null

// 触发后端验证
const triggerBackendValidation = () => {
  // 检查是否有必要的数据
  const hasValidData =
    form.start_date &&
    form.end_date &&
    form.schedules.some((s) => s.staff_sid && s.shift_type && s.position)

  if (!hasValidData) {
    return
  }

  // 清除之前的定时器
  if (validationTimer) {
    clearTimeout(validationTimer)
  }

  // 防抖处理，500ms后执行验证
  validationTimer = setTimeout(() => {
    performBackendValidation()
  }, 500)
}

// 执行后端验证
const performBackendValidation = async () => {
  if (validating.value) return

  // 过滤出有完整数据的分配
  const validSchedules = form.schedules.filter((s) => s.staff_sid && s.shift_type && s.position)

  if (validSchedules.length === 0) {
    return
  }

  validating.value = true

  try {
    const response = await post(
      'organizational-management/schedule/validate/',
      {
        start_date: form.start_date,
        end_date: form.end_date,
        schedules: validSchedules.map((schedule) => ({
          staff_sid: schedule.staff_sid,
          position: schedule.position,
          shift_type: schedule.shift_type,
        })),
      },
      {
        noCodeCheck: true,
      },
    )

    // 清理所有后端验证错误
    form.schedules.forEach((schedule) => {
      schedule.error = ''
    })

    // 处理验证结果
    if (response?.data?.details) {
      processValidationErrors(response.data.details)
    }
  } catch (error) {
    console.error('排班验证失败:', error)
    // 如果是验证失败的错误响应，处理错误信息
    if (error?.response?.data?.data?.details) {
      processValidationErrors(error.response.data.data.details)
    }
  } finally {
    validating.value = false
  }
}

// 处理验证错误
const processValidationErrors = (details) => {
  Object.keys(details).forEach((staffSid) => {
    const schedule = form.schedules.find((s) => s.staff_sid === staffSid)
    if (!schedule) return

    const errors = details[staffSid].errors || []
    const errorMessages = []

    errors.forEach((error) => {
      const staff = staffs.value.find((emp) => emp.sid === staffSid)
      const staffName = staff?.name || '该员工'
      const shiftText = getShiftTypeText(schedule.shift_type)

      switch (error.type) {
        case 'duplicate':
          errorMessages.push('本次提交的排班存在重复')
          break

        case 'conflict': {
          const conflictDates = formatDateRanges(error.msg)
          errorMessages.push(`员工${staffName}的${shiftText}发生冲突于：${conflictDates}`)
          break
        }

        case 'multiple': {
          const multipleDates = formatDateRanges(error.msg)
          errorMessages.push(`员工${staffName}已有排班于：${multipleDates}`)
          break
        }

        case 'staff':
        case 'shift':
          errorMessages.push(error.msg)
          break
      }
    })

    // 将所有错误消息合并为一个字符串
    schedule.error = errorMessages.join('；')
  })
}

// 判断员工是否在其他分配中被选中
const isStaffSelected = (staffSid, currentScheduleIndex) => {
  const selectedStaffIds = form.schedules
    .filter((schedule, index) => index !== currentScheduleIndex)
    .map((schedule) => schedule.staff_sid)
  return selectedStaffIds.includes(staffSid)
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    department: '',
    start_date: '',
    end_date: '',
    schedules: [
      {
        id: 1,
        staff_sid: '',
        shift_type: '',
        position: '',
        error: '',
      },
    ],
    remark: '',
  })
  assignmentIdCounter = 1
}

// 初始化表单数据
const initForm = () => {
  if (props.scheduleData && props.mode === 'edit') {
    Object.assign(form, props.scheduleData)
    // 触发相关检查
    if (form.schedules && form.schedules.length > 0) {
      form.schedules.forEach((schedule, index) => {
        if (schedule.staff_sid) {
          handleStaffChange(schedule.staff_sid, index)
        }
      })
    }
  } else {
    resetForm()
  }
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value.validate()

    // 验证员工分配
    if (!form.schedules || form.schedules.length === 0) {
      ElMessage.warning('请添加至少一个员工分配')
      return
    }

    // 验证每个分配是否完整
    for (let i = 0; i < form.schedules.length; i++) {
      const schedule = form.schedules[i]
      if (!schedule.staff_sid || !schedule.shift_type || !schedule.position) {
        ElMessage.warning(`请完善第 ${i + 1} 个员工分配信息`)
        return
      }
    }

    // 检查是否有错误
    const hasError = form.schedules.some((s) => s.error)
    if (hasError) {
      ElMessage.warning('请解决排班冲突后再提交')
      return
    }

    saving.value = true

    // 构建提交数据
    const submitData = {
      start_date: form.start_date,
      end_date: form.end_date,
      remark: form.remark,
      schedules: form.schedules.map((schedule) => ({
        staff_sid: schedule.staff_sid,
        position: schedule.position,
        shift_type: schedule.shift_type,
      })),
    }

    try {
      // 提交到后端
      const response = await post('organizational-management/schedule/create/', submitData)

      emit('save', response)
      ElMessage.success(`排班${props.mode === 'add' ? '添加' : '更新'}成功`)
      handleClose()
    } catch (error) {
      console.error('排班提交失败:', error)
      ElMessage.error('排班提交失败')
    } finally {
      saving.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
  // 清理定时器
  if (validationTimer) {
    clearTimeout(validationTimer)
    validationTimer = null
  }
}

// 监听弹窗可见性变化
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      initForm()
    }
  },
  { immediate: true },
)
</script>

<style scoped>
.form-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-left: 8px;
  border-left: 3px solid #e91e63;
}

.availability-check,
.work-time-stats,
.qualification-check {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.assignment-item {
  transition: all 0.3s ease;
}

.assignment-item:hover {
  background-color: #f8f9fa !important;
}

.availability-check-inline,
.work-time-stats-inline,
.qualification-check-inline {
  margin-top: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
