<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="staff-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="staff-form"
        autocomplete="off"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="员工号" prop="staff_number">
              <el-input v-model="form.staff_number" placeholder="请输入员工号" autocomplete="off" />
            </el-form-item>
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入员工姓名" autocomplete="off" />
            </el-form-item>
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="form.gender">
                <el-radio v-for="option in genderOptions" :key="option.value" :value="option.value">
                  {{ option.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="出生日期" prop="birth_date">
              <el-date-picker
                v-model="form.birth_date"
                type="date"
                placeholder="选择出生日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="民族" prop="ethnicity">
              <el-input v-model="form.ethnicity" placeholder="请输入民族" autocomplete="off" />
            </el-form-item>
            <el-form-item label="籍贯" prop="native_place">
              <el-input v-model="form.native_place" placeholder="请输入籍贯" autocomplete="off" />
            </el-form-item>
            <el-form-item label="血型" prop="blood_type">
              <el-select v-model="form.blood_type" placeholder="请选择血型" class="w-full">
                <el-option
                  v-for="option in bloodTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="身份证号" prop="identity_number">
              <el-input
                v-model="form.identity_number"
                placeholder="请输入身份证号"
                autocomplete="off"
              />
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" autocomplete="off" />
            </el-form-item>
            <el-form-item label="家庭住址" prop="home_address">
              <el-input
                v-model="form.home_address"
                placeholder="请输入家庭住址"
                autocomplete="off"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 紧急联系人信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">紧急联系人信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="紧急联系人" prop="emergency_contact">
              <el-input
                v-model="form.emergency_contact"
                placeholder="请输入紧急联系人"
                autocomplete="off"
              />
            </el-form-item>
            <el-form-item label="联系电话" prop="emergency_contact_phone">
              <el-input
                v-model="form.emergency_contact_phone"
                placeholder="请输入紧急联系电话"
                autocomplete="off"
              />
            </el-form-item>
            <el-form-item label="关系" prop="emergency_contact_relation">
              <el-select
                v-model="form.emergency_contact_relation"
                placeholder="请选择关系"
                class="w-full"
              >
                <el-option
                  v-for="option in relationOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 职位信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">职位信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="所属部门" prop="department">
              <el-select
                v-model="form.department"
                placeholder="请选择部门"
                class="w-full"
                :loading="
                  baseDataStore.departments.isLoading() || baseDataStore.departments.searchLoading
                "
                filterable
                remote
                :remote-method="baseDataStore.departments.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.departments.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.departments.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="职位" prop="position">
              <el-input v-model="form.position" placeholder="请输入职位" autocomplete="off" />
            </el-form-item>
            <el-form-item label="角色" prop="role">
              <el-select
                v-model="form.role"
                placeholder="请选择角色"
                class="w-full"
                :loading="baseDataStore.roles.isLoading() || baseDataStore.roles.searchLoading"
                filterable
                remote
                :remote-method="baseDataStore.roles.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.roles.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.roles.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="入职日期" prop="hire_date">
              <el-date-picker
                v-model="form.hire_date"
                type="date"
                placeholder="选择入职日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="合同号" prop="contract_number">
              <el-input
                v-model="form.contract_number"
                placeholder="请输入合同号"
                autocomplete="off"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 密码设置(仅新增时显示) -->
        <div v-if="mode === 'add'" class="form-section mb-6">
          <h4 class="section-title">密码设置</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="密码" prop="password">
              <el-input
                v-model="form.password"
                type="password"
                placeholder="请输入密码"
                show-password
                autocomplete="new-password"
              />
            </el-form-item>
            <el-form-item label="确认密码" prop="password_confirm">
              <el-input
                v-model="form.password_confirm"
                type="password"
                placeholder="请再次输入密码"
                show-password
                autocomplete="new-password"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '保存员工信息' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
  ElMessage,
  ElRadio,
  ElRadioGroup,
} from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'
import {
  GENDER_OPTIONS,
  BLOOD_TYPE_MAP,
  EMERGENCY_CONTACT_RELATION_OPTIONS,
} from '@/utils/constants.js'

const props = defineProps({
  visible: { type: Boolean, default: false },
  itemId: { type: [String, Number], default: null },
  mode: { type: String, default: 'add' },
})

const emit = defineEmits(['update:visible', 'save'])

// 基础数据store
const baseDataStore = useBaseDataStore()

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => (props.mode === 'add' ? '新增员工信息' : '编辑员工信息'))

// 枚举选项
const genderOptions = GENDER_OPTIONS

const bloodTypeOptions = Object.values(BLOOD_TYPE_MAP).map((item) => ({
  label: item.text,
  value: item.value,
}))

const relationOptions = EMERGENCY_CONTACT_RELATION_OPTIONS

// 表单数据
const form = reactive({
  name: '',
  phone: '',
  birth_date: '',
  ethnicity: '',
  native_place: '',
  gender: '',
  blood_type: '',
  identity_number: '',
  password: '',
  password_confirm: '',
  home_address: '',
  emergency_contact: '',
  emergency_contact_phone: '',
  emergency_contact_relation: '',
  staff_number: '',
  department: '',
  position: '',
  role: '',
  hire_date: '',
  contract_number: '',
})

// 密码确认验证器
const validatePasswordConfirm = (rule, value, callback) => {
  if (props.mode === 'add') {
    if (value === '') {
      callback(new Error('请再次输入密码'))
    } else if (value !== form.password) {
      callback(new Error('两次输入密码不一致'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// 表单验证规则
const rules = computed(() => {
  const baseRules = {
    name: [{ required: true, message: '请输入员工姓名', trigger: 'blur' }],
    phone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
    ],
    birth_date: [{ required: true, message: '请选择出生日期', trigger: 'change' }],
    ethnicity: [{ required: true, message: '请输入民族', trigger: 'blur' }],
    native_place: [{ required: true, message: '请输入籍贯', trigger: 'blur' }],
    gender: [{ required: true, message: '请选择性别', trigger: 'change' }],
    blood_type: [{ required: true, message: '请选择血型', trigger: 'change' }],
    identity_number: [
      { required: true, message: '请输入身份证号', trigger: 'blur' },
      {
        pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
        message: '身份证号格式不正确',
        trigger: 'blur',
      },
    ],
    home_address: [{ required: true, message: '请输入家庭住址', trigger: 'blur' }],
    emergency_contact: [{ required: true, message: '请输入紧急联系人', trigger: 'blur' }],
    emergency_contact_phone: [
      { required: true, message: '请输入紧急联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' },
    ],
    emergency_contact_relation: [{ required: true, message: '请选择关系', trigger: 'change' }],
    staff_number: [{ required: true, message: '请输入员工号', trigger: 'blur' }],
    department: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
    position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
    role: [{ required: true, message: '请输入角色', trigger: 'blur' }],
    hire_date: [{ required: true, message: '请选择入职日期', trigger: 'change' }],
    contract_number: [{ required: true, message: '请输入合同号', trigger: 'blur' }],
  }

  // 新增模式下添加密码验证
  if (props.mode === 'add') {
    baseRules.password = [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
    ]
    baseRules.password_confirm = [{ validator: validatePasswordConfirm, trigger: 'blur' }]
  }

  return baseRules
})

// 监听弹窗显示状态
watch(
  () => props.visible,
  async (visible) => {
    setTimeout(resetForm, 5)
    if (visible) {
      if (props.mode === 'edit' && props.itemId) {
        // 编辑模式：获取详情数据
        await fetchStaffDetail(props.itemId)
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 获取员工详情数据（用于编辑模式）
const fetchStaffDetail = async (staffId) => {
  if (!staffId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/staff/detail/${staffId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到员工详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取员工详情失败:', error)
    ElMessage.error('获取员工详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    name: apiData.name || '',
    phone: apiData.phone || '',
    birth_date: apiData.birth_date || '',
    ethnicity: apiData.ethnicity || '',
    native_place: apiData.native_place || '',
    gender: apiData.gender || '',
    blood_type: apiData.blood_type || '',
    identity_number: apiData.identity_number || '',
    home_address: apiData.home_address || '',
    emergency_contact: apiData.emergency_contact || '',
    emergency_contact_phone: apiData.emergency_contact_phone || '',
    emergency_contact_relation: apiData.emergency_contact_relation || '',
    staff_number: apiData.staff_number || '',
    department: apiData.department?.rid || '',
    position: apiData.position || '',
    role: apiData.role?.rid || '',
    hire_date: apiData.hire_date || '',
    contract_number: apiData.contract_number || '',
    // 编辑模式下不设置密码相关字段
    password: '',
    password_confirm: '',
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    name: '',
    phone: '',
    birth_date: '',
    ethnicity: '',
    native_place: '',
    gender: '',
    blood_type: '',
    identity_number: '',
    password: '',
    password_confirm: '',
    home_address: '',
    emergency_contact: '',
    emergency_contact_phone: '',
    emergency_contact_relation: '',
    staff_number: '',
    department: '',
    position: '',
    role: '',
    hire_date: '',
    contract_number: '',
  })

  // 重置搜索状态
  baseDataStore.departments.clearSearch()
  baseDataStore.roles.clearSearch()

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      name: form.name,
      phone: form.phone,
      birth_date: form.birth_date,
      ethnicity: form.ethnicity,
      native_place: form.native_place,
      gender: form.gender,
      blood_type: form.blood_type,
      identity_number: form.identity_number,
      home_address: form.home_address,
      emergency_contact: form.emergency_contact,
      emergency_contact_phone: form.emergency_contact_phone,
      emergency_contact_relation: form.emergency_contact_relation,
      staff_number: form.staff_number,
      department: form.department,
      position: form.position,
      role: form.role,
      hire_date: form.hire_date,
      contract_number: form.contract_number,
    }

    // 新增模式下添加密码字段
    if (props.mode === 'add') {
      submitData.password = form.password
      submitData.password_confirm = form.password_confirm
    }

    let res
    if (props.mode === 'add') {
      res = await post('organizational-management/staff/create/', submitData)
    } else {
      res = await put(`organizational-management/staff/update/${props.itemId}/`, submitData)
    }

    ElMessage.success(props.mode === 'add' ? '员工信息保存成功' : '员工信息更新成功')
    emit('save', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.staff-dialog {
  --el-dialog-border-radius: 12px;
}

.staff-form {
  padding: 0 8px;
}

.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
