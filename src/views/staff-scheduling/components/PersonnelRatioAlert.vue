<template>
  <div class="personnel-ratio-alert bg-yellow-50 border border-yellow-200 rounded-lg p-4">
    <div class="flex items-start">
      <el-icon class="text-yellow-600 mr-3 mt-0.5">
        <WarningFilled />
      </el-icon>
      <div class="flex-1">
        <h4 class="text-yellow-800 font-semibold mb-2">人员配比提示</h4>
        <p class="text-yellow-700 text-sm leading-relaxed">
          根据当前入住母婴数量 <strong>{{ currentMothers }} 对</strong>，建议护理人员至少需要
          <strong>{{ requiredNurses }} 名</strong> (按1:3配比)。 当前已排班
          <strong class="text-red-600">{{ scheduledNurses }} 名</strong>，
          <span class="text-red-600 font-medium">请注意调整人员安排。</span>
        </p>
        <div class="mt-3">
          <el-progress
            :percentage="progressPercentage"
            :color="progressColor"
            :show-text="false"
            class="mb-2"
          />
          <p class="text-xs text-yellow-600">
            配置进度：{{ scheduledNurses }} / {{ requiredNurses }} 名护理人员 ({{
              progressPercentage
            }}%)
          </p>
        </div>
      </div>
      <el-button
        type="primary"
        size="small"
        @click="handleOptimizeSchedule"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500 ml-3"
      >
        优化排班
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElIcon, ElButton, ElProgress, ElMessage } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'

const props = defineProps({
  currentMothers: {
    type: Number,
    required: true,
  },
  requiredNurses: {
    type: Number,
    required: true,
  },
  scheduledNurses: {
    type: Number,
    required: true,
  },
})

// 计算配置进度百分比
const progressPercentage = computed(() => {
  return Math.min(Math.round((props.scheduledNurses / props.requiredNurses) * 100), 100)
})

// 进度条颜色
const progressColor = computed(() => {
  const percentage = progressPercentage.value
  if (percentage < 60) return '#f56565' // 红色
  if (percentage < 80) return '#ed8936' // 橙色
  if (percentage < 100) return '#ecc94b' // 黄色
  return '#48bb78' // 绿色
})

// 优化排班建议
const handleOptimizeSchedule = () => {
  ElMessage.info('排班优化建议功能开发中...')
}
</script>

<style scoped>
.personnel-ratio-alert {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
