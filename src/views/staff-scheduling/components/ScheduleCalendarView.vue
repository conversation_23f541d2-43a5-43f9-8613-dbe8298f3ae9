<template>
  <div
    class="schedule-calendar-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Calendar />
          </el-icon>
          排班日历 - {{ viewTypeText }}
        </h3>
        <div class="flex items-center gap-4">
          <!-- 日期导航 -->
          <div class="flex items-center gap-2">
            <el-button size="small" @click="previousPeriod">
              <el-icon>
                <ArrowLeft />
              </el-icon>
            </el-button>
            <span class="text-sm font-medium min-w-32 text-center">{{ currentPeriodText }}</span>
            <el-button size="small" @click="nextPeriod">
              <el-icon>
                <ArrowRight />
              </el-icon>
            </el-button>
          </div>
          <el-button
            size="small"
            @click="goToToday"
            type="primary"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            {{ goToTodayText }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="p-8 text-center">
      <el-icon class="animate-spin text-2xl text-pink-500 mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-500">加载中...</p>
    </div>

    <!-- 日历内容 -->
    <div v-else class="calendar-content p-6">
      <!-- 日视图 -->
      <div v-if="viewType === 'daily'" class="daily-view">
        <div class="space-y-4">
          <!-- 时间段卡片 -->
          <div
            v-for="shift in dailyShifts"
            :key="shift.name"
            class="shift-card bg-gray-50 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-3">
              <h4 class="font-semibold text-gray-800 flex items-center">
                <el-icon class="mr-2" :class="shift.iconColor">
                  <Clock />
                </el-icon>
                {{ shift.name }} ({{ shift.time }})
              </h4>
              <div class="text-sm text-gray-500">共 {{ shift.staff.length }} 人</div>
            </div>

            <!-- 横向滚动容器 -->
            <div class="staff-scroll-container overflow-x-auto">
              <div class="flex gap-3 min-w-max pb-2">
                <div
                  v-for="staff in shift.staff"
                  :key="staff.rid"
                  class="staff-card bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all cursor-pointer flex-shrink-0"
                  style="width: 240px"
                  @click="handleEditSchedule(staff)"
                >
                  <div class="p-3">
                    <div class="flex items-center mb-2">
                      <el-avatar :size="28" class="mr-2 flex-shrink-0">
                        <el-icon class="text-sm">
                          <User />
                        </el-icon>
                      </el-avatar>
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-1">
                          <p class="font-medium text-gray-800 truncate" :title="staff.name">
                            {{ staff.name }}
                          </p>
                          <el-tag
                            :type="getOrgScheduleStatusTagType(staff.status)"
                            size="small"
                            class="ml-1 flex-shrink-0"
                          >
                            {{ staff.status_display }}
                          </el-tag>
                        </div>
                      </div>
                    </div>

                    <div class="space-y-1">
                      <p class="text-sm text-gray-600 truncate" :title="staff.position">
                        {{ staff.position }}
                      </p>
                      <p class="text-xs text-gray-500 truncate" :title="staff.staff_sid">
                        编号: {{ staff.staff_sid }}
                      </p>
                      <p
                        v-if="staff.remark"
                        class="text-xs text-gray-500 truncate"
                        :title="staff.remark"
                      >
                        备注: {{ staff.remark }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- 空状态提示 -->
                <div
                  v-if="shift.staff.length === 0"
                  class="flex items-center justify-center bg-gray-50 rounded-lg text-gray-400 flex-shrink-0"
                  style="width: 240px; height: 100px"
                >
                  <div class="text-center">
                    <p class="text-sm">暂无排班</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 周视图 -->
      <div v-else-if="viewType === 'weekly'" class="weekly-view">
        <div class="weekly-calendar bg-white rounded-lg border border-gray-200 overflow-hidden">
          <!-- 表头 -->
          <div class="grid grid-cols-8 bg-gray-50 border-b border-gray-200">
            <div
              class="p-4 font-semibold text-gray-700 text-center border-r border-gray-200 flex items-center justify-center"
            >
              班次时间
            </div>
            <div
              v-for="day in weekDays"
              :key="day.date"
              class="p-4 font-semibold text-center border-r border-gray-200 last:border-r-0"
              :class="day.isToday ? 'bg-pink-50 text-pink-700' : 'text-gray-700'"
            >
              <div class="text-sm font-medium">{{ day.dayName }}</div>
              <div class="text-xs text-gray-500 mt-1">{{ formatDate(day.date) }}</div>
            </div>
          </div>

          <!-- 内容行 -->
          <template v-for="shift in shifts" :key="shift.name">
            <div class="grid grid-cols-8 border-b border-gray-100 min-h-32">
              <!-- 班次信息 -->
              <div
                class="bg-gray-50 p-4 border-r border-gray-200 flex flex-col justify-center text-center"
              >
                <div class="font-medium text-gray-800 text-sm">{{ shift.name }}</div>
                <div class="text-xs text-gray-500 mt-1">{{ shift.time }}</div>
              </div>

              <!-- 每日排班 -->
              <div
                v-for="day in weekDays"
                :key="`${shift.type}-${day.date}`"
                class="p-3 border-r border-gray-200 last:border-r-0"
              >
                <div class="space-y-2 h-full">
                  <div
                    v-for="staff in getDayShiftStaff(day.date, shift.name)"
                    :key="staff.rid"
                    class="bg-white border border-gray-100 rounded-lg p-3 text-xs hover:border-gray-200 transition-all cursor-pointer relative"
                    @click="handleEditSchedule(staff)"
                    :title="`${staff.name} - ${staff.position} - ${staff.status_display}${staff.remark ? ' - ' + staff.remark : ''}`"
                  >
                    <!-- 状态标签放在右上角 -->
                    <el-tag
                      :type="getOrgScheduleStatusTagType(staff.status)"
                      size="small"
                      class="absolute top-2 right-2"
                    >
                      {{ staff.status_display }}
                    </el-tag>

                    <div class="flex items-center mb-2 pr-16">
                      <el-avatar :size="24" class="mr-2 flex-shrink-0">
                        <el-icon class="text-xs">
                          <User />
                        </el-icon>
                      </el-avatar>
                      <div class="flex-1 min-w-0">
                        <div class="font-medium text-gray-800 truncate mb-1">{{ staff.name }}</div>
                        <div class="text-gray-600 truncate">{{ staff.position }}</div>
                      </div>
                    </div>

                    <!-- 备注占整行 -->
                    <div v-if="staff.remark" class="text-gray-500 text-xs mt-2 leading-relaxed">
                      {{ staff.remark }}
                    </div>
                  </div>

                  <!-- 无排班状态 -->
                  <div
                    v-if="getDayShiftStaff(day.date, shift.name).length === 0"
                    class="text-center text-gray-400 text-xs flex items-center justify-center h-full"
                  >
                    <div>
                      <div>暂无排班</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 月视图 -->
      <div v-else-if="viewType === 'monthly'" class="monthly-view">
        <!-- 月历表格 -->
        <div class="monthly-calendar border border-gray-200 rounded-lg overflow-hidden bg-white">
          <!-- 星期表头 -->
          <div class="grid grid-cols-7 bg-gray-50 border-b border-gray-200">
            <div
              v-for="day in weekdays"
              :key="day"
              class="p-4 text-center font-semibold text-gray-700 border-r border-gray-200 last:border-r-0"
            >
              {{ day }}
            </div>
          </div>

          <!-- 日期网格 -->
          <div class="grid grid-cols-7">
            <div
              v-for="day in monthlyDays"
              :key="day.key"
              class="min-h-24 border-r border-b border-gray-200 last:border-r-0 relative hover:bg-gray-50 transition-colors cursor-pointer"
              :class="{
                'bg-gray-50 text-gray-400': !day.isCurrentMonth,
                'bg-pink-50 border-pink-200': day.isToday,
              }"
            >
              <!-- 日期数字 -->
              <div class="p-3">
                <div
                  class="text-sm font-semibold inline-flex items-center justify-center w-6 h-6 rounded-full mb-2"
                  :class="{
                    'text-gray-400': !day.isCurrentMonth,
                    'text-white bg-pink-500': day.isToday,
                    'text-gray-800': day.isCurrentMonth && !day.isToday,
                  }"
                >
                  {{ day.date }}
                </div>

                <!-- 排班信息 - 紧凑显示 -->
                <div class="space-y-1">
                  <!-- 班次点状指示器 -->
                  <template v-for="shift in day.shifts" :key="shift.name">
                    <div v-if="shift.count > 0" class="flex items-center justify-between text-xs">
                      <div class="flex items-center gap-1">
                        <span
                          class="inline-block w-2 h-2 rounded-full"
                          :class="getShiftStyle(shift.name, true)"
                        ></span>
                        <span class="text-gray-600 font-medium">{{ shift.name }}</span>
                      </div>
                      <span class="text-gray-800 font-semibold text-xs bg-gray-100 px-1 rounded">{{
                        shift.count
                      }}</span>
                    </div>
                  </template>

                  <!-- 无排班状态 -->
                  <div
                    v-if="day.schedules.length === 0 && day.isCurrentMonth"
                    class="text-xs text-gray-400 text-center py-1"
                  >
                    <span class="opacity-60">暂无排班</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElIcon, ElButton, ElAvatar, ElTag } from 'element-plus'
import { Calendar, Clock, User, ArrowLeft, ArrowRight, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'
import { SHIFT_TYPE_MAP, getOrgScheduleStatusTagType } from '@/utils/constants.js'

// 定义属性
const props = defineProps({
  viewType: {
    type: String,
    required: true,
    validator: (value) => ['daily', 'weekly', 'monthly'].includes(value),
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['edit-schedule', 'quick-assign'])

// 内部状态管理
const loading = ref(false)
const scheduleData = ref([])

// 当前日期
const currentDate = ref(new Date())

// 视图类型文本
const viewTypeText = computed(() => {
  const textMap = {
    daily: '日视图',
    weekly: '周视图',
    monthly: '月视图',
  }
  return textMap[props.viewType]
})

// 回到当前按钮文本
const goToTodayText = computed(() => {
  const textMap = {
    daily: '今天',
    weekly: '本周',
    monthly: '本月',
  }
  return textMap[props.viewType]
})

// 星期标题
const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

// 格式化日期为YYYY-MM-DD格式（避免时区问题）
const formatDateToString = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 根据视图类型获取日期范围
const getDateRange = () => {
  const date = currentDate.value
  let start, end

  if (props.viewType === 'daily') {
    start = formatDateToString(date)
    end = start
  } else if (props.viewType === 'weekly') {
    const startOfWeek = new Date(date)
    startOfWeek.setDate(date.getDate() - date.getDay())
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(startOfWeek.getDate() + 6)
    start = formatDateToString(startOfWeek)
    end = formatDateToString(endOfWeek)
  } else if (props.viewType === 'monthly') {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    start = formatDateToString(firstDay)
    end = formatDateToString(lastDay)
  }

  return { start, end }
}

// 加载排班数据
const loadScheduleData = async () => {
  loading.value = true
  try {
    const { start, end } = getDateRange()

    // 构建请求参数，忽略筛选条件中的日期参数
    const params = {
      start_date: start,
      end_date: end,
    }

    // 添加非日期相关的筛选条件
    if (props.filters.department_rid) {
      params.department_rid = props.filters.department_rid
    }
    if (props.filters.staff_sid) {
      params.staff_sid = props.filters.staff_sid
    }
    if (props.filters.shift_type) {
      params.shift_type = props.filters.shift_type
    }

    const response = await get('organizational-management/schedule/query/', params)
    scheduleData.value = response || []
  } catch (error) {
    console.error('获取排班数据失败:', error)
    ElMessage.error('获取排班数据失败')
    scheduleData.value = []
  } finally {
    loading.value = false
  }
}

// 根据后端数据格式适配显示数据
const processScheduleData = computed(() => {
  const processed = {}

  scheduleData.value.forEach((item) => {
    const date = item.schedule_date
    if (!processed[date]) {
      processed[date] = {}
    }

    const shiftType = item.shift_type_display || item.shift_type
    if (!processed[date][shiftType]) {
      processed[date][shiftType] = []
    }

    processed[date][shiftType].push({
      rid: item.rid,
      staff_sid: item.staff_sid,
      staff_name: item.staff_name,
      position: item.position,
      status: item.status,
      status_display: item.status_display,
      remark: item.remark,
    })
  })

  return processed
})

// 日视图班次数据
const dailyShifts = computed(() => {
  const currentDateStr = formatDateToString(currentDate.value)

  // 获取当日所有排班数据，按班次类型分组
  const currentDaySchedules = scheduleData.value.filter(
    (item) => item.schedule_date === currentDateStr,
  )

  // 按班次显示名称分组
  const shiftGroups = {}
  currentDaySchedules.forEach((item) => {
    const shiftDisplay = item.shift_type_display
    if (!shiftGroups[shiftDisplay]) {
      shiftGroups[shiftDisplay] = []
    }
    shiftGroups[shiftDisplay].push({
      rid: item.rid,
      staff_sid: item.staff_sid,
      name: item.staff_name,
      position: item.position,
      schedule_date: item.schedule_date,
      shift_type: item.shift_type,
      shift_type_display: item.shift_type_display,
      status: item.status,
      status_display: item.status_display,
      remark: item.remark,
    })
  })

  // 转换为显示格式，保持班次顺序
  const shiftOrder = ['早班', '中班', '晚班', '夜班']
  return shiftOrder
    .filter((shiftName) => shiftGroups[shiftName])
    .map((shiftName) => {
      // 找到对应的班次配置
      const shiftConfig = Object.values(SHIFT_TYPE_MAP).find((shift) => shift.text === shiftName)

      return {
        name: shiftName,
        type: shiftConfig?.value || 'UNKNOWN',
        time: shiftConfig?.time || '时间待定',
        iconColor: shiftConfig?.color || 'text-gray-500',
        bgColor: shiftConfig?.bgColor || 'bg-gray-100',
        staff: shiftGroups[shiftName],
      }
    })
})

// 班次配置
const shifts = computed(() => {
  // 使用 Map 按班次显示名称去重，保留第一个遇到的配置
  const shiftMap = new Map()

  Object.values(SHIFT_TYPE_MAP).forEach((shift) => {
    if (!shiftMap.has(shift.text)) {
      shiftMap.set(shift.text, {
        name: shift.text,
        type: shift.value,
        time: shift.time,
        color: shift.color,
        bgColor: shift.bgColor,
      })
    }
  })

  return Array.from(shiftMap.values())
})

// 周视图天数
const weekDays = computed(() => {
  const days = []
  const startOfWeek = new Date(currentDate.value)
  startOfWeek.setDate(currentDate.value.getDate() - currentDate.value.getDay())

  for (let i = 0; i < 7; i++) {
    const day = new Date(startOfWeek)
    day.setDate(startOfWeek.getDate() + i)
    const today = new Date()
    days.push({
      date: formatDateToString(day),
      dayName: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][i],
      isToday: day.toDateString() === today.toDateString(),
    })
  }
  return days
})

// 月视图天数
const monthlyDays = computed(() => {
  const days = []
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  // 获取当月第一天
  const firstDay = new Date(year, month, 1)

  // 获取第一周的开始日期（可能是上个月的日期）
  const startDate = new Date(firstDay)
  startDate.setDate(firstDay.getDate() - firstDay.getDay())

  // 生成6周的日期（42天）
  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)

    const dateString = formatDateToString(date)
    const today = new Date()
    const isToday = date.toDateString() === today.toDateString()
    const isCurrentMonth = date.getMonth() === month

    // 获取该日期的排班数据
    const schedules = getDateSchedules(dateString)
    const shifts = getDateShiftStats(schedules)

    days.push({
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
      date: date.getDate(),
      fullDate: dateString,
      isToday,
      isCurrentMonth,
      schedules,
      shifts,
    })
  }

  return days
})

// 当前时期文本
const currentPeriodText = computed(() => {
  const date = currentDate.value
  if (props.viewType === 'daily') {
    return date.toLocaleDateString('zh-CN', { month: 'long', day: 'numeric', weekday: 'long' })
  } else if (props.viewType === 'weekly') {
    const startOfWeek = new Date(date)
    startOfWeek.setDate(date.getDate() - date.getDay())
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(startOfWeek.getDate() + 6)
    return `${startOfWeek.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })}`
  } else {
    return date.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })
  }
})

// 获取指定日期和班次的员工
const getDayShiftStaff = (date, shift) => {
  const dayData = processScheduleData.value[date] || {}
  const staffList = dayData[shift] || []

  return staffList.map((item) => ({
    rid: item.rid,
    staff_sid: item.staff_sid,
    name: item.staff_name,
    position: item.position,
    status: item.status,
    status_display: item.status_display,
    remark: item.remark,
  }))
}

// 获取指定日期的排班数据
const getDateSchedules = (date) => {
  return scheduleData.value.filter((item) => item.schedule_date === date)
}

// 获取指定日期的班次统计
const getDateShiftStats = (schedules) => {
  const shiftCounts = {}

  // 初始化班次计数 - 使用唯一的班次显示名称
  const uniqueShifts = new Set()
  Object.values(SHIFT_TYPE_MAP).forEach((shift) => {
    uniqueShifts.add(shift.text)
  })

  uniqueShifts.forEach((shiftText) => {
    shiftCounts[shiftText] = 0
  })

  schedules.forEach((schedule) => {
    const shiftDisplay = schedule.shift_type_display
    if (Object.prototype.hasOwnProperty.call(shiftCounts, shiftDisplay)) {
      shiftCounts[shiftDisplay]++
    }
  })

  // 返回去重后的班次统计
  return Array.from(uniqueShifts).map((shiftText) => {
    // 找到对应的班次配置（优先使用第一个匹配的）
    const shiftConfig = Object.values(SHIFT_TYPE_MAP).find((shift) => shift.text === shiftText)

    return {
      name: shiftText,
      count: shiftCounts[shiftText],
      colorClass:
        shiftConfig?.bgColor.replace('bg-', 'bg-').replace('-100', '-500') || 'bg-gray-500',
    }
  })
}

// 格式化日期显示
const formatDate = (dateStr) => {
  const date = new Date(dateStr)
  return `${date.getMonth() + 1}/${date.getDate()}`
}

// 获取班次样式
const getShiftStyle = (shiftName, onlyColor = false) => {
  const shiftStyleMap = {
    早班: onlyColor ? 'bg-emerald-400' : 'bg-emerald-50 text-emerald-700',
    中班: onlyColor ? 'bg-amber-400' : 'bg-amber-50 text-amber-700',
    晚班: onlyColor ? 'bg-purple-400' : 'bg-purple-50 text-purple-700',
    夜班: onlyColor ? 'bg-indigo-400' : 'bg-indigo-50 text-indigo-700',
  }
  return shiftStyleMap[shiftName] || (onlyColor ? 'bg-gray-400' : 'bg-gray-50 text-gray-700')
}

// 前一个时期
const previousPeriod = () => {
  const date = new Date(currentDate.value)
  if (props.viewType === 'daily') {
    date.setDate(date.getDate() - 1)
  } else if (props.viewType === 'weekly') {
    date.setDate(date.getDate() - 7)
  } else if (props.viewType === 'monthly') {
    date.setMonth(date.getMonth() - 1)
  }
  currentDate.value = date
  loadScheduleData()
}

// 下一个时期
const nextPeriod = () => {
  const date = new Date(currentDate.value)
  if (props.viewType === 'daily') {
    date.setDate(date.getDate() + 1)
  } else if (props.viewType === 'weekly') {
    date.setDate(date.getDate() + 7)
  } else if (props.viewType === 'monthly') {
    date.setMonth(date.getMonth() + 1)
  }
  currentDate.value = date
  loadScheduleData()
}

// 回到今天
const goToToday = () => {
  currentDate.value = new Date()
  loadScheduleData()
}

// 编辑排班
const handleEditSchedule = (schedule) => {
  emit('edit-schedule', schedule)
}

// 暴露方法给父组件，用于搜索时调用
const refreshData = () => {
  loadScheduleData()
}

defineExpose({
  refreshData,
})

// 监听视图类型变化
watch(
  () => props.viewType,
  () => {
    loadScheduleData()
  },
)

onMounted(() => {
  // 初始化数据
  loadScheduleData()
})
</script>

<style scoped>
.schedule-calendar-container {
  transition: all 0.3s ease;
}

.shift-card {
  transition: all 0.3s ease;
}

.shift-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.staff-card {
  transition: all 0.3s ease;
}

.staff-card:hover {
  transform: translateY(-2px);
}

/* 日视图横向滚动样式 */
.staff-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #e5e7eb #f9fafb;
}

.staff-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.staff-scroll-container::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 3px;
}

.staff-scroll-container::-webkit-scrollbar-thumb {
  background: #e5e7eb;
  border-radius: 3px;
}

.staff-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #d1d5db;
}

.weekly-view {
  overflow-x: auto;
}

.calendar-placeholder {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
}

/* 月视图样式 */
.monthly-calendar .min-h-24 {
  min-height: 6rem;
}

.monthly-calendar .min-h-24:hover {
  background-color: #f9fafb !important;
}

.monthly-legend {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}

/* 周视图样式 */
.weekly-calendar {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.weekly-calendar .grid {
  display: grid;
}

/* 周视图员工卡片样式 */
.weekly-view .bg-white.border {
  transition: all 0.2s ease;
}

.weekly-view .bg-white.border:hover {
  transform: translateY(-1px);
}

/* 月视图动画 */
.monthly-calendar .min-h-24 {
  transition: all 0.2s ease;
}

.monthly-calendar .min-h-24:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 今天高亮 */
.monthly-calendar .bg-pink-50 {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}
</style>
