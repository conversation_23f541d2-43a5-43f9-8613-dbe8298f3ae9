<template>
  <div class="training-section mr-4">
    <div class="flex items-center justify-between mb-4">
      <h3 class="section-title mb-0">培训记录</h3>
      <div class="flex gap-2">
        <el-button
          size="small"
          type="primary"
          @click="handleAddTraining"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-1"><Plus /></el-icon>
          新增培训
        </el-button>
      </div>
    </div>
    <div v-loading="loading" class="training-table-container">
      <el-table
        :data="records"
        stripe
        class="w-full"
        size="small"
        @row-click="handleTrainingRowClick"
      >
        <el-table-column prop="training_topics" label="培训主题" min-width="120" align="center" />
        <el-table-column prop="training_date" label="培训日期" min-width="110" align="center" />
        <el-table-column prop="training_duration" label="培训时长" min-width="80" align="center">
          <template #default="{ row }">
            <span>{{ row.training_duration }}小时</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="training_result_label"
          label="培训结果"
          min-width="80"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="getTrainingResultTagType(row.training_result)" size="small">
              {{ row.training_result_label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" fixed="right" align="center">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click.stop="handleEditTraining(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click.stop="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 培训记录表单对话框 -->
    <TrainingFormDialog
      v-model="trainingFormVisible"
      :training-data="currentTraining"
      :staff-data="staffData"
      @success="handleTrainingSuccess"
    />

    <!-- 培训记录详情对话框 -->
    <TrainingDetailDialog v-model="trainingDetailVisible" :training-data="selectedTrainingDetail" />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import {
  ElButton,
  ElTable,
  ElTableColumn,
  ElTag,
  ElIcon,
  ElMessage,
  ElMessageBox,
} from 'element-plus'
import { get, del } from '@/utils/request.js'
import { getTrainingResultTagType } from '@/utils/constants.js'
import TrainingFormDialog from './TrainingFormDialog.vue'
import TrainingDetailDialog from './TrainingDetailDialog.vue'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  staffId: { type: [String, Number], required: true },
  staffData: { type: Object, default: null },
})

const loading = ref(false)
const records = ref([])
const trainingFormVisible = ref(false)
const currentTraining = ref(null)
const trainingDetailVisible = ref(false)
const selectedTrainingDetail = ref(null)

const fetchRecords = async () => {
  if (!props.staffId) return
  loading.value = true
  try {
    const response = await get(`organizational-management/stra/list/${props.staffId}/`)
    records.value = response || []
  } catch (error) {
    showErrorTip(error)
    records.value = []
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除培训记录"${row.training_topics}"吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await del(`organizational-management/stra/delete/${row.rid}/`)
    ElMessage.success('培训记录删除成功')
    fetchRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除培训记录失败')
    }
  }
}

const handleAddTraining = () => {
  currentTraining.value = null
  trainingFormVisible.value = true
}

const handleEditTraining = (training) => {
  currentTraining.value = training
  trainingFormVisible.value = true
}

const handleTrainingRowClick = (row) => {
  selectedTrainingDetail.value = row
  trainingDetailVisible.value = true
}

const handleTrainingSuccess = () => {
  fetchRecords() // 重新加载培训记录
}

watch(
  () => props.staffId,
  () => {
    fetchRecords()
  },
  { immediate: true },
)

onMounted(() => {
  fetchRecords()
})

// 提供给外部刷新
defineExpose({ fetchRecords })
</script>

<style scoped>
.training-table-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.section-title.mb-0 {
  margin-bottom: 0;
}

:deep(.el-table) {
  --el-table-border-color: #e5e7eb;
}

:deep(.el-table th) {
  background-color: #f9fafb !important;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table__fixed-header-wrapper th) {
  background-color: #f9fafb !important;
}

:deep(.el-table td) {
  padding: 0.5rem;
}
</style>
