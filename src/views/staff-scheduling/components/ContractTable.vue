<template>
  <div class="contract-section mr-4">
    <div class="flex items-center justify-between mb-4">
      <h3 class="section-title mb-0">合同文件</h3>
      <div class="flex gap-2">
        <el-button
          size="small"
          type="primary"
          @click="handleAddContract"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-1"><Plus /></el-icon>
          新增合同
        </el-button>
      </div>
    </div>
    <div v-loading="loading" class="contract-table-container">
      <el-table
        :data="records"
        stripe
        class="w-full"
        size="small"
        @row-click="handleContractRowClick"
      >
        <el-table-column prop="contract_number" label="合同号" min-width="120" align="center" />
        <el-table-column
          prop="contract_validity_period"
          label="合同有效期"
          min-width="110"
          align="center"
        />
        <el-table-column label="操作" min-width="80" fixed="right" align="center">
          <template #default="{ row }">
            <el-button size="small" type="danger" @click.stop="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 合同文件表单对话框 -->
    <ContractFormDialog
      v-model="contractFormVisible"
      :staff-data="staffData"
      @success="handleContractSuccess"
    />

    <!-- 合同文件详情对话框 -->
    <ContractDetailDialog v-model="contractDetailVisible" :contract-data="selectedContractDetail" />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElButton, ElTable, ElTableColumn, ElIcon, ElMessage, ElMessageBox } from 'element-plus'
import { get, del } from '@/utils/request.js'
import ContractFormDialog from './ContractFormDialog.vue'
import ContractDetailDialog from './ContractDetailDialog.vue'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  staffId: { type: [String, Number], required: true },
  staffData: { type: Object, default: null },
})

const loading = ref(false)
const records = ref([])
const contractFormVisible = ref(false)
const contractDetailVisible = ref(false)
const selectedContractDetail = ref(null)

const fetchRecords = async () => {
  if (!props.staffId) return
  loading.value = true
  try {
    const response = await get(`organizational-management/staff-contract/list/${props.staffId}/`)
    records.value = response || []
  } catch (error) {
    showErrorTip(error)
    records.value = []
  } finally {
    loading.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除合同"${row.contract_number}"吗？`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await del(`organizational-management/staff-contract/delete/${row.rid}/`)
    ElMessage.success('合同文件删除成功')
    fetchRecords()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除合同文件失败')
    }
  }
}

const handleAddContract = () => {
  contractFormVisible.value = true
}

const handleContractRowClick = (row) => {
  selectedContractDetail.value = row
  contractDetailVisible.value = true
}

const handleContractSuccess = () => {
  fetchRecords() // 重新加载合同记录
}

watch(
  () => props.staffId,
  () => {
    fetchRecords()
  },
  { immediate: true },
)

onMounted(() => {
  fetchRecords()
})

// 提供给外部刷新
defineExpose({ fetchRecords })
</script>

<style scoped>
.contract-table-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.section-title.mb-0 {
  margin-bottom: 0;
}

:deep(.el-table) {
  --el-table-border-color: #e5e7eb;
}

:deep(.el-table th) {
  background-color: #f9fafb !important;
  color: #374151;
  font-weight: 600;
}

:deep(.el-table__fixed-header-wrapper th) {
  background-color: #f9fafb !important;
}

:deep(.el-table td) {
  padding: 0.5rem;
}
</style>
