<template>
  <div class="visitor-management-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">微信小程序预约参观管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理微信小程序预约参观</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="success"
            @click="handleExportPDF"
            class="bg-green-500 hover:bg-green-600 border-green-500"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            导出PDF
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 访客预约列表组件 -->
    <VisitorAppointmentTable
      :api-url="apiUrl"
      :filters="filters"
      @mark-approved="openApproveDialog"
      @mark-complete="handleMarkComplete"
      @stats-update="handleStatsUpdate"
      ref="visitorTableRef"
    />

    <!-- 访客预约审批弹窗 -->
    <VisitorAppointmentApproveDialog
      v-model:visible="approveDialogVisible"
      :visitor-data="currentVisitor"
      @approve="handleApprove"
      @reject="handleReject"
    />
  </div>
</template>

<script setup>
import FilterPanel from '@/components/FilterPanel.vue'
import { VISITOR_APPOINTMENT_STATUS_MAP } from '@/utils/constants.js'
import { get, put } from '@/utils/request.js'
import { Check, Clock, Download, TrendCharts, User } from '@element-plus/icons-vue'
import { ElButton, ElIcon, ElMessage, ElMessageBox } from 'element-plus'
import { reactive, ref } from 'vue'
import VisitorAppointmentApproveDialog from './housekeeping/components/VisitorAppointmentApproveDialog.vue'
import VisitorAppointmentTable from './housekeeping/components/VisitorAppointmentTable.vue'

// API配置
const apiUrl = 'customer-service/wechatapp/visit/list/'

// 响应式数据
const detailVisible = ref(false)
const detailLoading = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentVisitor = ref(null)
const approveDialogVisible = ref(false)
const approveLoading = ref(false)

// 获取组件引用
const visitorTableRef = ref(null)

// 筛选条件
const filters = reactive({
  sk: '',
  aps: '',
  apt: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '访客姓名/电话',
    placeholder: '输入访客姓名或电话',
  },
  {
    key: 'aps',
    type: 'select',
    label: '状态',
    placeholder: '选择状态',
    options: [
      { label: VISITOR_APPOINTMENT_STATUS_MAP.PENDING.text, value: 'PENDING' },
      { label: VISITOR_APPOINTMENT_STATUS_MAP.CONFIRMED.text, value: 'CONFIRMED' },
      { label: VISITOR_APPOINTMENT_STATUS_MAP.REJECTED.text, value: 'REJECTED' },
      { label: VISITOR_APPOINTMENT_STATUS_MAP.CANCELLED.text, value: 'CANCELLED' },
      { label: VISITOR_APPOINTMENT_STATUS_MAP.COMPLETED.text, value: 'COMPLETED' },
    ],
  },
  {
    key: 'apt',
    type: 'date',
    label: '来访日期',
    placeholder: '选择来访日期',
  },
]

// 统计数据
const todayVisitors = ref(0)
const currentVisitors = ref(0)
const leftVisitors = ref(0)
const monthlyTotal = ref(0)

const handleSearch = () => {
  // 重置分页并重新加载数据
  visitorTableRef.value?.resetPagination()
}

function openApproveDialog(visitor) {
  console.log('openApproveDialog', visitor)
  currentVisitor.value = visitor
  approveDialogVisible.value = true
}

async function handleApprove() {
  approveLoading.value = true
  try {
    await put(`customer-service/wechatapp/visit/audit/${currentVisitor.value.vid}/`, {
      result: true,
    })
    ElMessage.success('已审核通过')
    visitorTableRef.value?.refresh()
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  }

  approveLoading.value = false
  approveDialogVisible.value = false
}

async function handleReject() {
  approveLoading.value = true
  try {
    await put(`customer-service/wechatapp/visit/audit/${currentVisitor.value.vid}/`, {
      result: false,
    })
    ElMessage.success('已拒绝')
    visitorTableRef.value?.refresh()
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  }
  approveLoading.value = false
  approveDialogVisible.value = false
}

async function handleMarkComplete(visitor) {
  try {
    await ElMessageBox.confirm(
      `确认标记访客 "${visitor.visitor_name}" 的预约参观为完成状态吗？`,
      '确认标记完成',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await put(`customer-service/wechatapp/visit/complete/${visitor.vid}/`)
    ElMessage.success('已标记为完成')
    visitorTableRef.value?.refresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('标记完成失败:', error)
      ElMessage.error('标记完成失败')
    }
  }
}

const handleExportPDF = () => {
  // 导出PDF功能
  ElMessage.info('导出功能开发中...')
}
</script>

<style scoped>
.visitor-management-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
