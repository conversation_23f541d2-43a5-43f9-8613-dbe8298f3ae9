<template>
  <div class="visitor-management-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">访客管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理访客登记和访问记录</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="success"
            @click="handleExportPDF"
            class="bg-green-500 hover:bg-green-600 border-green-500"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            导出PDF
          </el-button>
          <el-button
            type="primary"
            @click="handleAddVisitor"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增访客登记
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计概览 -->
    <div class="stats-overview mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="stat-card bg-white rounded-lg border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-green-600 text-xl">
              <User />
            </el-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">今日来访</p>
            <p class="text-2xl font-semibold text-gray-900">{{ todayVisitors }}</p>
          </div>
        </div>
      </div>

      <div class="stat-card bg-white rounded-lg border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-yellow-600 text-xl">
              <Clock />
            </el-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">在访人数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ currentVisitors }}</p>
          </div>
        </div>
      </div>

      <div class="stat-card bg-white rounded-lg border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-blue-600 text-xl">
              <Check />
            </el-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">已离开</p>
            <p class="text-2xl font-semibold text-gray-900">{{ leftVisitors }}</p>
          </div>
        </div>
      </div>

      <div class="stat-card bg-white rounded-lg border border-gray-200 p-4">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
            <el-icon class="text-pink-600 text-xl">
              <TrendCharts />
            </el-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm text-gray-600">本月总计</p>
            <p class="text-2xl font-semibold text-gray-900">{{ monthlyTotal }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 访客列表组件 -->
    <VisitorTable
      :api-url="apiUrl"
      :filters="filters"
      @view="handleViewVisitor"
      @edit="handleEditVisitor"
      @mark-visiting="handleMarkVisiting"
      @mark-exit="handleMarkExit"
      @stats-update="handleStatsUpdate"
      ref="visitorTableRef"
    />

    <!-- 访客详情弹窗 -->
    <VisitorDetailDialog
      v-model:visible="detailVisible"
      :visitor-data="currentVisitor"
      :loading="detailLoading"
      @edit="handleEditFromDetail"
    />

    <!-- 访客表单弹窗 -->
    <VisitorFormDialog
      v-model:visible="formVisible"
      :visitor-data="currentVisitor"
      :mode="formMode"
      @save="handleSaveVisitor"
    />

    <!-- 标记离开弹窗 -->
    <VisitorExitDialog
      v-model:visible="exitVisible"
      :visitor-data="currentVisitor"
      @save="handleSaveExit"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElButton, ElMessage, ElIcon } from 'element-plus'
import { Plus, Download, User, Clock, Check, TrendCharts } from '@element-plus/icons-vue'
import { get, put } from '@/utils/request.js'
import FilterPanel from '@/components/FilterPanel.vue'
import VisitorTable from './housekeeping/components/VisitorTable.vue'
import VisitorDetailDialog from './housekeeping/components/VisitorDetailDialog.vue'
import VisitorFormDialog from './housekeeping/components/VisitorFormDialog.vue'
import VisitorExitDialog from './housekeeping/components/VisitorExitDialog.vue'

// API配置
const apiUrl = 'customer-service/visitor/list/'

// 响应式数据
const detailVisible = ref(false)
const detailLoading = ref(false)
const formVisible = ref(false)
const exitVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentVisitor = ref(null)

// 获取组件引用
const visitorTableRef = ref(null)

// 筛选条件
const filters = reactive({
  sk: '',
  aps: '',
  apt: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '访客姓名/电话',
    placeholder: '输入访客姓名或电话',
  },
  {
    key: 'aps',
    type: 'select',
    label: '状态',
    placeholder: '选择状态',
    options: [
      { label: '待访问', value: 'PENDING' },
      { label: '正在访问', value: 'VISITING' },
      { label: '已离开', value: 'LEAVED' },
    ],
  },
  {
    key: 'apt',
    type: 'date',
    label: '来访日期',
    placeholder: '选择来访日期',
  },
]

// 统计数据
const todayVisitors = ref(0)
const currentVisitors = ref(0)
const leftVisitors = ref(0)
const monthlyTotal = ref(0)

// 方法
const handleAddVisitor = () => {
  currentVisitor.value = null
  formMode.value = 'add'
  formVisible.value = true
}

const handleViewVisitor = async (visitor) => {
  detailLoading.value = true
  detailVisible.value = true
  try {
    // 调用详情API获取完整数据
    const data = await get(`customer-service/visitor/detail/${visitor.vid}/`)
    currentVisitor.value = data
  } catch (error) {
    console.error('获取访客详情失败:', error)
    ElMessage.error('获取访客详情失败')
    detailVisible.value = false
  } finally {
    detailLoading.value = false
  }
}

const handleEditVisitor = async (visitor) => {
  try {
    // 调用详情API获取完整数据用于编辑
    const data = await get(`customer-service/visitor/detail/${visitor.vid}/`)
    currentVisitor.value = data
    formMode.value = 'edit'
    formVisible.value = true
  } catch (error) {
    console.error('获取访客详情失败:', error)
    ElMessage.error('获取访客详情失败')
  }
}

const handleEditFromDetail = (visitor) => {
  // 从详情页面跳转到编辑，数据已经加载完成
  currentVisitor.value = visitor
  formMode.value = 'edit'
  formVisible.value = true
}

const handleMarkVisiting = async (visitor) => {
  try {
    // 直接调用标记访问中API
    await put(`customer-service/visitor/visiting/${visitor.vid}/`)
    ElMessage.success('访客已标记为访问中')
    // 刷新列表
    visitorTableRef.value?.refresh()
  } catch (error) {
    console.error('标记访问中失败:', error)
    ElMessage.error('标记访问中失败')
  }
}

const handleMarkExit = (visitor) => {
  currentVisitor.value = { ...visitor }
  exitVisible.value = true
}

const handleSearch = () => {
  // 重置分页并重新加载数据
  visitorTableRef.value?.resetPagination()
}

const handleStatsUpdate = (statsData) => {
  // 更新统计数据
  todayVisitors.value = statsData.visitor_count_today || 0
  currentVisitors.value = statsData.visitor_count_current || 0
  leftVisitors.value = statsData.visitor_count_left_today || 0
  monthlyTotal.value = statsData.visitor_count_this_month || 0
}

const handleSaveVisitor = (response) => {
  // API调用已在组件内部完成，这里只需要刷新列表
  console.log('访客保存成功:', response)

  // 刷新列表数据
  visitorTableRef.value?.refresh()

  // 弹窗已在组件内部关闭，这里不需要再设置
  // formVisible.value = false
}

const handleSaveExit = (response) => {
  // API调用已在组件内部完成，这里只需要刷新列表
  console.log('访客离开成功:', response)

  // 刷新列表数据
  visitorTableRef.value?.refresh()

  // 弹窗已在组件内部关闭，这里不需要再设置
  // exitVisible.value = false
}

const handleExportPDF = () => {
  // 导出PDF功能
  ElMessage.info('导出功能开发中...')
}
</script>

<style scoped>
.visitor-management-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>
