<template>
  <div class="">
    <div class="mb-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">角色权限管理</h1>
          <p class="text-gray-600 mt-2">管理系统角色和权限分配</p>
        </div>
        <el-button
          type="primary"
          @click="handleCreate"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon><Plus /></el-icon>
          新建角色
        </el-button>
      </div>
    </div>

    <!-- 筛选区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="filters"
      @search="handleSearch"
    />

    <!-- 角色列表 -->
    <RoleTable
      ref="roleTableRef"
      :filters="filters"
      @edit="handleEdit"
      @delete="handleDelete"
      @view-detail="handleViewDetail"
    />

    <!-- 角色编辑对话框 -->
    <RoleFormDialog
      v-model="formDialogVisible"
      :role-data="currentRole"
      :permissions="allPermissions"
      @success="handleFormSuccess"
    />

    <!-- 角色详情对话框 -->
    <RoleDetailDialog
      v-model="detailDialogVisible"
      :role-data="currentRole"
      :all-permissions="allPermissions"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request'
import FilterPanel from '@/components/FilterPanel.vue'
import RoleTable from './components/RoleTable.vue'
import RoleFormDialog from './components/RoleFormDialog.vue'
import RoleDetailDialog from './components/RoleDetailDialog.vue'

// 筛选条件
const filters = reactive({
  sk: ''
})

// 筛选字段配置
const filterFields = [
  {
    key: 'sk',
    label: '角色名称',
    type: 'input',
    placeholder: '请输入角色名称'
  }
]

// 对话框状态
const formDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentRole = ref(null)
const allPermissions = ref([])

// 组件引用
const roleTableRef = ref()

// 获取权限列表
const loadPermissions = async () => {
  try {
    const data = await get('permission/permission-list/')
    allPermissions.value = data
  } catch (error) {
    console.error('获取权限列表失败:', error)
    ElMessage.error('获取权限列表失败')
  }
}

// 搜索处理
const handleSearch = () => {
  roleTableRef.value?.resetPagination()
}

// 新建角色
const handleCreate = () => {
  currentRole.value = null
  formDialogVisible.value = true
}

// 编辑角色
const handleEdit = (role) => {
  currentRole.value = role
  formDialogVisible.value = true
}

// 删除角色
const handleDelete = async (role) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${role.name}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    await del(`permission/role-delete/${role.rid}/`)
    ElMessage.success('角色删除成功')
    roleTableRef.value?.refresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败')
    }
  }
}

// 查看详情
const handleViewDetail = (role) => {
  currentRole.value = role
  detailDialogVisible.value = true
}

// 从详情页编辑
const handleEditFromDetail = (role) => {
  detailDialogVisible.value = false
  currentRole.value = role
  formDialogVisible.value = true
}

// 表单提交成功
const handleFormSuccess = () => {
  formDialogVisible.value = false
  roleTableRef.value?.refresh()
}

// 组件挂载
onMounted(() => {
  loadPermissions()
})
</script>
