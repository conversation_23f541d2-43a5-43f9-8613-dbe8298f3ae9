<template>
  <div class="">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">审计日志</h1>
      <p class="text-gray-600 mt-2">查看系统操作日志和审计记录</p>
    </div>

    <!-- 筛选面板 -->
    <FilterPanel
      :fields="filterFields"
      :filters="filters"
      @search="handleSearch"
    />

    <!-- 审计日志表格 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <el-icon class="mr-2 text-blue-500">
              <List />
            </el-icon>
            审计日志列表
          </h3>
          <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
        </div>
      </div>

      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        class="w-full"
        style="width: 100%"
        :header-cell-style="{
          backgroundColor: '#f9fafb',
          color: '#374151',
          fontWeight: '600',
          borderBottom: '1px solid #e5e7eb',
          textAlign: 'center',
        }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column prop="lid" label="日志ID" min-width="180" fixed="left">
          <template #default="{ row }">
            <span class="font-mono text-sm text-gray-600">{{ row.lid }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="operator" label="操作员" min-width="120">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <el-avatar :size="32" class="mr-2 bg-blue-100 text-blue-600">
                {{ row.operator.charAt(0) }}
              </el-avatar>
              <span class="font-medium">{{ row.operator }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="ip_address" label="IP地址" min-width="140">
          <template #default="{ row }">
            <el-tag type="info" size="small">
              {{ row.ip_address }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="operation_module" label="操作模块" min-width="140">
          <template #default="{ row }">
            <span class="font-medium">{{ row.operation_module }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="operation_type" label="操作类型" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getOperationTypeTagType(row.operation_type)" size="small">
              {{ row.operation_type_display }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="operation_detail" label="操作详情" min-width="380">
          <template #default="{ row }">
            <div class="text-center">
              <div class="text-gray-600 text-sm leading-relaxed flex items-center justify-center flex-wrap">
                <template v-for="(part, index) in parseOperationDetail(row.operation_detail)" :key="index">
                  <span v-if="part.type === 'normal'" class="inline-block">{{ part.text }}</span>
                  <el-tag v-else-if="part.type === 'bracket'" size="small" class="mx-0.5" style="background-color: #f3f4f6; color: #8B5CF6; border: 1px solid #8B5CF6; font-weight: 500;">{{ part.text }}</el-tag>
                  <el-tag v-else-if="part.type === 'angle'" type="info" size="small" class="mx-0.5">{{ part.text }}</el-tag>
                </template>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="80" fixed="right">
          <template #default="{ row }">
            <div class="flex justify-center">
              <el-button
                v-if="hasExtraData(row)"
                @click="handleViewChange(row)"
                type="primary"
                size="small"
                text
              >
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <span v-else class="text-xs text-gray-400">-</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="operation_time" label="操作时间" min-width="160">
          <template #default="{ row }">
            <div class="text-sm text-gray-600">
              <div>{{ formatDate(row.operation_time) }}</div>
              <div class="text-xs text-gray-400">{{ formatTime(row.operation_time) }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-end"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 变更详情对话框 -->
    <AuditChangeDialog
      v-model="changeDialogVisible"
      :audit-log="selectedAuditLog"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { List, View } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'
import FilterPanel from '@/components/FilterPanel.vue'
import AuditChangeDialog from '@/components/AuditChangeDialog.vue'

// 操作类型枚举
const operationTypes = [
  { value: 'CREATE', label: '创建' },
  { value: 'UPDATE', label: '更新' },
  { value: 'DELETE', label: '删除' },
  { value: 'QUERY', label: '查询' },
  { value: 'UPLOAD', label: '文件上传' },
  { value: 'DOWNLOAD', label: '文件下载' },
  { value: 'LOGIN', label: '登录' },
  { value: 'LOGOUT', label: '登出' },
  { value: 'EXPORT', label: '导出' },
  { value: 'REQUEST_AUDIT', label: '申请审核' },
  { value: 'STATUS_CHANGE', label: '状态更改' },
  { value: 'OTHER', label: '其他' }
]

// 筛选字段配置
const filterFields = [
  {
    key: 'sk',
    label: '搜索',
    type: 'input',
    placeholder: '用户名/IP地址/操作模块'
  },
  {
    key: 'operation_type',
    label: '操作类型',
    type: 'select',
    placeholder: '请选择操作类型',
    options: operationTypes
  }
]

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 筛选条件
const filters = ref({
  sk: '',
  operation_type: ''
})

// 变更详情对话框
const changeDialogVisible = ref(false)
const selectedAuditLog = ref({})

// 计算属性
const total = computed(() => totalCount.value)

// 获取操作类型标签类型
const getOperationTypeTagType = (type) => {
  const typeMap = {
    'CREATE': 'success',
    'UPDATE': 'warning',
    'DELETE': 'danger',
    'QUERY': 'info',
    'UPLOAD': 'primary',
    'DOWNLOAD': 'primary',
    'LOGIN': 'success',
    'LOGOUT': 'info',
    'EXPORT': 'warning',
    'REQUEST_AUDIT': 'success',
    'STATUS_CHANGE': 'primary',
    'OTHER': 'default'
  }
  return typeMap[type] || 'default'
}

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm:ss')
}

// 检查是否有额外数据
const hasExtraData = (row) => {
  return row.extra_data && Object.keys(row.extra_data).length > 0
}

// 解析操作详情，识别[]和<>包围的内容
const parseOperationDetail = (detail) => {
  if (!detail) return [{ type: 'normal', text: '' }]
  
  const parts = []
  let current = ''
  let i = 0
  
  while (i < detail.length) {
    const char = detail[i]
    
    if (char === '[') {
      // 保存当前普通文本
      if (current) {
        parts.push({ type: 'normal', text: current })
        current = ''
      }
      
      // 查找匹配的]
      let j = i + 1
      let bracketContent = ''
      while (j < detail.length && detail[j] !== ']') {
        bracketContent += detail[j]
        j++
      }
      
      if (j < detail.length) {
        parts.push({ type: 'bracket', text: bracketContent })
        i = j + 1
      } else {
        current += char
        i++
      }
    } else if (char === '<') {
      // 保存当前普通文本
      if (current) {
        parts.push({ type: 'normal', text: current })
        current = ''
      }
      
      // 查找匹配的>
      let j = i + 1
      let angleContent = ''
      while (j < detail.length && detail[j] !== '>') {
        angleContent += detail[j]
        j++
      }
      
      if (j < detail.length) {
        parts.push({ type: 'angle', text: angleContent })
        i = j + 1
      } else {
        current += char
        i++
      }
    } else {
      current += char
      i++
    }
  }
  
  // 添加剩余的普通文本
  if (current) {
    parts.push({ type: 'normal', text: current })
  }
  
  return parts
}

// 查看变更详情
const handleViewChange = (row) => {
  selectedAuditLog.value = row
  changeDialogVisible.value = true
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    
    // 添加搜索条件
    if (filters.value.sk) {
      params.sk = filters.value.sk
    }
    
    // 添加操作类型筛选
    if (filters.value.operation_type) {
      params.operation_type = filters.value.operation_type
    }
    
    const data = await get('audit/log/list/', params)
    tableData.value = data.list || []
    totalCount.value = data.total_count || 0
  } catch (error) {
    console.error('获取审计日志失败:', error)
    ElMessage.error('获取审计日志失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}


// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 组件挂载后加载数据
onMounted(() => {
  loadData()
})
</script>
