<template>
  <div class="health-knowledge-manager">
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">健康知识管理</h3>
        <p class="manager-desc">管理小程序健康知识内容，当前共 {{ knowledgeList.length }} 篇文章</p>
      </div>
      <el-button 
        type="primary" 
        @click="showCreateDialog = true"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        新增知识
      </el-button>
    </div>

    <div class="knowledge-content" v-loading="loading">
      <div v-if="knowledgeList.length === 0" class="empty-state">
        <el-empty description="暂无健康知识">
          <el-button 
            type="primary" 
            @click="showCreateDialog = true"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            创建第一篇知识
          </el-button>
        </el-empty>
      </div>

      <div v-else class="knowledge-grid">
        <div
          v-for="item in knowledgeList"
          :key="item.rid"
          class="knowledge-card"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="header-left">
              <div class="knowledge-icon">
                <el-icon>
                  <Reading />
                </el-icon>
              </div>
              <div class="header-info">
                <h4 class="knowledge-title">{{ item.title }}</h4>
                <div class="knowledge-meta">
                  <span class="meta-item">
                    <el-icon class="meta-icon">
                      <Clock />
                    </el-icon>
                    {{ formatTime(item.created_at) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="card-actions">
              <el-button
                type="primary"
                size="small"
                :icon="View"
                circle
                @click="handleView(item)"
                title="查看详情"
                class="action-btn view-btn"
              />
              <el-button
                type="warning"
                size="small"
                :icon="Edit"
                circle
                @click="handleEdit(item)"
                title="编辑"
                class="action-btn edit-btn"
              />
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                circle
                @click="handleDelete(item)"
                title="删除"
                class="action-btn delete-btn"
              />
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-body">
            <div class="knowledge-summary">
              {{ item.summary }}
            </div>
          </div>

          <!-- 卡片底部 -->
          <div class="card-footer">
            <div class="footer-tag">
              <span class="tag">健康知识</span>
            </div>
            <div class="footer-actions">
              <el-button
                type="text"
                size="small"
                @click="handleView(item)"
                class="read-more-btn"
              >
                阅读全文
                <el-icon class="ml-1">
                  <ArrowRight />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <HealthKnowledgeDetailDialog
      v-model="showDetailDialog"
      :knowledge-data="currentKnowledge"
    />

    <!-- 创建对话框 -->
    <HealthKnowledgeEditDialog
      v-model="showCreateDialog"
      :is-edit="false"
      @success="handleCreateSuccess"
    />

    <!-- 编辑对话框 -->
    <HealthKnowledgeEditDialog
      v-model="showEditDialog"
      :knowledge-data="currentKnowledge"
      :is-edit="true"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElIcon, ElButton, ElEmpty } from 'element-plus'
import { Plus, View, Edit, Delete, Clock, Reading, ArrowRight } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request'
import { format } from 'date-fns'
import HealthKnowledgeDetailDialog from './HealthKnowledgeDetailDialog.vue'
import HealthKnowledgeEditDialog from './HealthKnowledgeEditDialog.vue'

// 响应式数据
const loading = ref(false)
const knowledgeList = ref([])
const currentKnowledge = ref(null)
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)

// 获取健康知识列表
const fetchKnowledgeList = async () => {
  loading.value = true
  try {
    const data = await get('maternity-center/health/knowledge/list/')
    knowledgeList.value = data || []
  } catch (error) {
    console.error('获取健康知识列表失败:', error)
    ElMessage.error('获取健康知识列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm')
  } catch {
    return timeStr
  }
}

// 查看详情
const handleView = (item) => {
  currentKnowledge.value = item
  showDetailDialog.value = true
}

// 编辑
const handleEdit = (item) => {
  currentKnowledge.value = item
  showEditDialog.value = true
}

// 删除
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除健康知识"${item.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    )

    await del(`maternity-center/health/knowledge/delete/${item.rid}/`)
    ElMessage.success('删除成功')
    
    // 从列表中移除
    knowledgeList.value = knowledgeList.value.filter(k => k.rid !== item.rid)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除健康知识失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 创建成功
const handleCreateSuccess = (newKnowledge) => {
  knowledgeList.value.unshift(newKnowledge)
  ElMessage.success('创建成功')
}

// 编辑成功
const handleEditSuccess = (updatedKnowledge) => {
  const index = knowledgeList.value.findIndex(k => k.rid === updatedKnowledge.rid)
  if (index !== -1) {
    knowledgeList.value[index] = updatedKnowledge
  }
  ElMessage.success('更新成功')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchKnowledgeList()
})
</script>

<style scoped>
.health-knowledge-manager {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-info {
  flex: 1;
}

.manager-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.manager-desc {
  font-size: 14px;
  color: #6b7280;
}

.knowledge-content {
  min-height: 384px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 384px;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

.knowledge-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  overflow: hidden;
}

.knowledge-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  border-color: #e879a1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 20px 0 20px;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.knowledge-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #e879a1, #f093b0);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.knowledge-icon .el-icon {
  font-size: 24px;
  color: white;
}

.header-info {
  flex: 1;
  min-width: 0;
}

.knowledge-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.knowledge-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
}

.meta-icon {
  font-size: 14px;
  margin-right: 4px;
}

.card-actions {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.view-btn:hover {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.edit-btn:hover {
  background-color: #f59e0b;
  border-color: #f59e0b;
}

.delete-btn:hover {
  background-color: #ef4444;
  border-color: #ef4444;
}

.card-body {
  padding: 0 20px 16px 20px;
}

.knowledge-summary {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background-color: #fafbfc;
  border-top: 1px solid #f0f0f0;
}

.footer-tag {
  display: flex;
  align-items: center;
}

.tag {
  background: linear-gradient(135deg, #e879a1, #f093b0);
  color: white;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.footer-actions {
  display: flex;
  align-items: center;
}

.read-more-btn {
  color: #e879a1;
  font-size: 13px;
  font-weight: 500;
  padding: 4px 8px;
  transition: all 0.2s ease;
}

.read-more-btn:hover {
  color: #d1477a;
  background-color: rgba(232, 121, 161, 0.1);
}
</style>
