<template>
  <el-dialog
    v-model="visible"
    title="上传轮播图"
    width="600px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="carousel-upload-dialog"
  >
    <div class="upload-content">
      <div class="upload-section">
        <h4 class="section-title">选择图片</h4>
        <div class="upload-area" @click="triggerFileUpload" v-loading="uploading">
          <div v-if="previewUrl" class="preview-container">
            <el-image
              :src="previewUrl"
              fit="cover"
              class="preview-image"
              :preview-src-list="[previewUrl]"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <p>图片预览失败</p>
                </div>
              </template>
            </el-image>
            <div class="preview-overlay">
              <el-button
                type="primary"
                size="small"
                @click.stop="triggerFileUpload"
                :loading="uploading"
              >
                重新选择
              </el-button>
            </div>
          </div>
          
          <div v-else class="upload-placeholder">
            <el-icon class="upload-icon">
              <Plus />
            </el-icon>
            <p class="upload-text">{{ uploading ? '上传中...' : '点击选择轮播图' }}</p>
            <p class="upload-hint">支持 JPG、PNG、GIF 格式</p>
          </div>
        </div>
        
        <input
          ref="fileInputRef"
          type="file"
          accept="image/*"
          @change="handleFileSelect"
          style="display: none"
          :disabled="uploading"
        />
        
        <div class="upload-tips">
          <div class="tip-item">
            <el-icon class="tip-icon"><InfoFilled /></el-icon>
            <span>建议图片尺寸：750x400 像素，比例 16:9</span>
          </div>
          <div class="tip-item">
            <el-icon class="tip-icon"><InfoFilled /></el-icon>
            <span>文件大小不超过 5MB</span>
          </div>
          <div class="tip-item">
            <el-icon class="tip-icon"><InfoFilled /></el-icon>
            <span>支持 JPG、PNG、GIF 格式</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleUpload" 
          :loading="uploading"
          :disabled="!selectedFile"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ uploading ? '上传中...' : '确认上传' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Picture, InfoFilled } from '@element-plus/icons-vue'
import { post } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 组件引用
const fileInputRef = ref()

// 状态管理
const uploading = ref(false)
const selectedFile = ref(null)
const previewUrl = ref('')

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 触发文件选择
const triggerFileUpload = () => {
  if (uploading.value) return
  fileInputRef.value?.click()
}

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }

  selectedFile.value = file
  
  // 创建预览URL
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
  previewUrl.value = URL.createObjectURL(file)
}

// 处理上传
const handleUpload = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请先选择图片')
    return
  }

  uploading.value = true

  try {
    // 创建FormData
    const formData = new FormData()
    formData.append('carousel_file', selectedFile.value)

    console.log('开始上传轮播图...')

    // 调用上传接口
    const response = await post('maternity-center/carousel/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })

    console.log('轮播图上传成功:', response)

    ElMessage.success('轮播图上传成功')
    emit('success', response)
    handleClose()
  } catch (error) {
    console.error('上传轮播图失败:', error)
    if (error.msg) {
      ElMessage.error(error.msg)
    } else {
      ElMessage.error('上传轮播图失败')
    }
  } finally {
    uploading.value = false
  }
}

// 重置状态
const resetState = () => {
  selectedFile.value = null
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
  uploading.value = false
  
  // 重置文件输入
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

// 关闭对话框
const handleClose = () => {
  resetState()
  visible.value = false
}
</script>

<style scoped>
.carousel-upload-dialog {
  --el-dialog-padding-primary: 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 0;
  margin-right: 0;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

.upload-content {
  width: 100%;
}

.upload-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ec4899;
  display: inline-block;
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #ec4899;
  background: #fdf2f8;
}

.preview-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preview-container:hover .preview-overlay {
  opacity: 1;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 16px;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.upload-tips {
  margin-top: 16px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #0ea5e9;
}

.tip-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #374151;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  color: #0ea5e9;
  margin-right: 8px;
  font-size: 16px;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #9ca3af;
  background: #f3f4f6;
}

.image-error .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-loading-mask) {
  border-radius: 12px;
}
</style>
