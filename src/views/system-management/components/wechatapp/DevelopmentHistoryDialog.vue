<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑发展历程' : '添加发展历程'"
    width="600px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="history-dialog"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        label-position="top"
        class="history-form"
      >
        <!-- 年份 -->
        <el-form-item label="年份" prop="year">
          <el-input-number
            v-model="formData.year"
            :min="1900"
            :max="2100"
            :step="1"
            placeholder="请输入年份"
            class="w-full"
            controls-position="right"
          />
        </el-form-item>

        <!-- 描述 -->
        <el-form-item label="发展历程描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="6"
            placeholder="请输入该年份的重要发展历程和里程碑事件..."
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElDialog, ElForm, ElFormItem, ElInput, ElInputNumber, ElButton } from 'element-plus'
import { post, put } from '@/utils/request'

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  historyData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// 定义 emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 表单数据
const formData = ref({
  year: new Date().getFullYear(),
  description: ''
})

// 表单验证规则
const formRules = {
  year: [
    { required: true, message: '请输入年份', trigger: 'blur' },
    { type: 'number', min: 1900, max: 2100, message: '年份必须在1900-2100之间', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入发展历程描述', trigger: 'blur' },
    { min: 10, message: '描述至少需要10个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// 初始化表单
const initForm = () => {
  if (props.isEdit && props.historyData) {
    formData.value = {
      year: props.historyData.year,
      description: props.historyData.description
    }
  } else {
    formData.value = {
      year: new Date().getFullYear(),
      description: ''
    }
  }
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    submitting.value = true

    // 发送请求
    let response
    if (props.isEdit) {
      response = await put(`maternity-center/development/history/update/${props.historyData.rid}/`, formData.value)
    } else {
      response = await post('maternity-center/development/history/create/', formData.value)
    }

    emit('success', response.data)
    visible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 处理关闭
const handleClose = () => {
  if (submitting.value) return
  visible.value = false
}
</script>

<style scoped>


.history-form {
  padding: 0 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-dialog__header) {
  padding: 24px 24px 0;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 24px;
}
</style>
