<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="contact-edit-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="contact-form"
        autocomplete="off"
      >
        <!-- 联系图片 -->
        <div class="form-section mb-6">
          <h4 class="section-title">联系图片</h4>
          <div class="image-upload-container">
            <div class="image-preview" @click="triggerFileUpload" v-loading="uploading">
              <div v-if="form.contact_image" class="image-display">
                <img
                  :src="form.contact_image"
                  alt="联系图片"
                  class="preview-image"
                />
                <div class="image-overlay">
                  <el-icon class="upload-icon"><Plus /></el-icon>
                  <p>点击重新上传</p>
                </div>
              </div>
              <div v-else class="upload-placeholder">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <p>{{ uploading ? '上传中...' : '点击上传联系图片' }}</p>
              </div>
            </div>
            <input
              ref="fileInputRef"
              type="file"
              accept="image/*"
              @change="handleImageUpload"
              style="display: none"
              :disabled="uploading"
            />
          </div>
          <p class="upload-tip">支持 JPG、PNG 格式，文件大小不超过 5MB，建议尺寸 16:9</p>
        </div>

        <!-- 联系信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">联系信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="联系电话" prop="contact_phone">
              <el-input 
                v-model="form.contact_phone" 
                placeholder="请输入联系电话" 
                autocomplete="off"
                maxlength="20"
              >
                <template #prefix>
                  <el-icon class="text-pink-500">
                    <Phone />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="联系邮箱" prop="contact_email">
              <el-input 
                v-model="form.contact_email" 
                placeholder="请输入联系邮箱" 
                autocomplete="off"
                maxlength="100"
              >
                <template #prefix>
                  <el-icon class="text-pink-500">
                    <Message />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="联系地址" prop="address">
              <el-input 
                v-model="form.address" 
                type="textarea"
                :rows="3"
                placeholder="请输入联系地址"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="营业时间" prop="business_hours">
              <el-input 
                v-model="form.business_hours" 
                placeholder="例如：星期一至星期日 / 08:00 - 20:00" 
                autocomplete="off"
                maxlength="100"
              >
                <template #prefix>
                  <el-icon class="text-pink-500">
                    <Clock />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ submitting ? '保存中...' : (isCreate ? '创建' : '更新') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Picture, Phone, Message, Clock } from '@element-plus/icons-vue'
import { post, put } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  contactData: {
    type: Object,
    default: null
  },
  isCreate: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 组件引用
const formRef = ref()
const fileInputRef = ref()

// 状态管理
const loading = ref(false)
const submitting = ref(false)
const uploading = ref(false)

// 表单数据
const form = reactive({
  contact_image: '',
  contact_phone: '',
  contact_email: '',
  address: '',
  business_hours: ''
})

// 表单验证规则
const rules = {
  contact_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  ],
  contact_email: [
    { required: true, message: '请输入联系邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入联系地址', trigger: 'blur' },
    { min: 5, max: 200, message: '地址长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  business_hours: [
    { required: true, message: '请输入营业时间', trigger: 'blur' },
    { max: 100, message: '营业时间不能超过100个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const dialogTitle = computed(() => {
  return props.isCreate ? '创建联系信息' : '编辑联系信息'
})

// 触发文件上传
const triggerFileUpload = () => {
  if (uploading.value) return
  fileInputRef.value?.click()
}

// 处理图片上传
const handleImageUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }

  // 创建预览URL
  form.contact_image = URL.createObjectURL(file)
  
  // 保存文件对象供提交时使用
  form._imageFile = file
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.contact_image = ''
  form.contact_phone = ''
  form.contact_email = ''
  form.address = ''
  form.business_hours = ''
  form._imageFile = null
  
  // 重置文件输入
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

// 填充表单数据
const fillForm = () => {
  if (props.contactData) {
    form.contact_image = props.contactData.contact_image || ''
    form.contact_phone = props.contactData.contact_phone || ''
    form.contact_email = props.contactData.contact_email || ''
    form.address = props.contactData.address || ''
    form.business_hours = props.contactData.business_hours || ''
  }
}

// 检查字段是否有变更
const hasFieldChanged = (fieldName, newValue) => {
  if (props.isCreate) return true
  if (!props.contactData) return true
  return props.contactData[fieldName] !== newValue
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    submitting.value = true

    // 创建FormData
    const formData = new FormData()

    if (props.isCreate) {
      // 创建模式：提交所有字段
      if (form._imageFile) {
        formData.append('contact_image', form._imageFile)
      }
      formData.append('contact_phone', form.contact_phone)
      formData.append('contact_email', form.contact_email)
      formData.append('address', form.address)
      formData.append('business_hours', form.business_hours)
    } else {
      // 更新模式：只提交变更的字段
      if (form._imageFile) {
        // 有新上传的图片
        formData.append('contact_image', form._imageFile)
      }

      if (hasFieldChanged('contact_phone', form.contact_phone)) {
        formData.append('contact_phone', form.contact_phone)
      }

      if (hasFieldChanged('contact_email', form.contact_email)) {
        formData.append('contact_email', form.contact_email)
      }

      if (hasFieldChanged('address', form.address)) {
        formData.append('address', form.address)
      }

      if (hasFieldChanged('business_hours', form.business_hours)) {
        formData.append('business_hours', form.business_hours)
      }
    }

    let data
    if (props.isCreate) {
      // 创建联系信息
      data = await post('maternity-center/contact/us/create/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    } else {
      // 更新联系信息
      data = await put('maternity-center/contact/us/update/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    }

    emit('success', data)
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.msg) {
      ElMessage.error(error.msg)
    } else {
      ElMessage.error(props.isCreate ? '创建联系信息失败' : '更新联系信息失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  visible.value = false
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    if (!props.isCreate && props.contactData) {
      fillForm()
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
.contact-edit-dialog {
  --el-dialog-padding-primary: 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 0;
  margin-right: 0;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

.contact-form {
  width: 100%;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ec4899;
  display: inline-block;
}

.image-upload-container {
  margin-bottom: 12px;
}

.image-preview {
  width: 100%;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.image-preview:hover {
  border-color: #ec4899;
  background: #fdf2f8;
}

.image-display {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-display:hover .image-overlay {
  opacity: 1;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  width: 100%;
  height: 100%;
}

.upload-icon {
  font-size: 48px;
  color: #9ca3af;
  margin-bottom: 12px;
}

.image-overlay .upload-icon {
  font-size: 32px;
  color: white;
  margin-bottom: 8px;
}

.image-overlay p {
  color: white;
  font-size: 14px;
  margin: 0;
}



.upload-tip {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
}

:deep(.el-input__inner) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
}

/* 表单项间距调整 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}
</style>
