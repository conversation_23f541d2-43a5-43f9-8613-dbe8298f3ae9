<template>
  <div class="contact-us-manager">
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">联系我们</h3>
        <p class="manager-desc">管理小程序联系我们页面的信息和图片</p>
      </div>
      <el-button 
        v-if="contactData"
        type="primary" 
        @click="showEditDialog = true"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Edit />
        </el-icon>
        编辑信息
      </el-button>
      <el-button 
        v-else
        type="primary" 
        @click="showEditDialog = true"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        创建信息
      </el-button>
    </div>

    <div class="contact-content" v-loading="loading">
      <div v-if="!contactData" class="empty-state">
        <el-empty description="暂无联系信息">
          <el-button 
            type="primary" 
            @click="showEditDialog = true"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            创建联系信息
          </el-button>
        </el-empty>
      </div>

      <div v-else class="contact-display">
        <div class="contact-image-section">
          <h4 class="section-title">联系图片</h4>
          <div class="image-container">
            <el-image
              :src="contactData.contact_image"
              fit="contain"
              class="contact-image"
              :preview-src-list="[contactData.contact_image]"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <p>图片加载失败</p>
                </div>
              </template>
            </el-image>
          </div>
        </div>

        <div class="contact-info-section">
          <h4 class="section-title">联系信息</h4>
          <div class="info-list">
            <div class="info-row">
              <div class="info-label">
                <el-icon class="label-icon">
                  <Phone />
                </el-icon>
                <span class="label-text">联系电话</span>
              </div>
              <div class="info-value">{{ contactData.contact_phone || '-' }}</div>
            </div>

            <div class="info-row">
              <div class="info-label">
                <el-icon class="label-icon">
                  <Message />
                </el-icon>
                <span class="label-text">联系邮箱</span>
              </div>
              <div class="info-value">{{ contactData.contact_email || '-' }}</div>
            </div>

            <div class="info-row">
              <div class="info-label">
                <el-icon class="label-icon">
                  <Location />
                </el-icon>
                <span class="label-text">联系地址</span>
              </div>
              <div class="info-value">{{ contactData.address || '-' }}</div>
            </div>

            <div class="info-row">
              <div class="info-label">
                <el-icon class="label-icon">
                  <Clock />
                </el-icon>
                <span class="label-text">营业时间</span>
              </div>
              <div class="info-value">{{ contactData.business_hours || '-' }}</div>
            </div>

            <div class="info-row">
              <div class="info-label">
                <el-icon class="label-icon">
                  <Calendar />
                </el-icon>
                <span class="label-text">更新时间</span>
              </div>
              <div class="info-value">{{ formatTime(contactData.updated_at) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <ContactUsEditDialog
      v-model="showEditDialog"
      :contact-data="contactData"
      :is-create="!contactData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElIcon, ElButton, ElEmpty, ElImage } from 'element-plus'
import { Edit, Plus, Picture, Phone, Message, Location, Clock, Calendar } from '@element-plus/icons-vue'
import { get } from '@/utils/request'
import { format } from 'date-fns'
import ContactUsEditDialog from './ContactUsEditDialog.vue'

// 响应式数据
const loading = ref(false)
const contactData = ref(null)
const showEditDialog = ref(false)

// 获取联系信息
const fetchContactData = async () => {
  loading.value = true
  try {
    const data = await get('maternity-center/contact/us/detail/')
    contactData.value = data
  } catch (error) {
    console.error('获取联系信息失败:', error)
    // 如果是"请先创建数据"的情况，不显示错误信息
    if (error.msg !== '请先创建数据') {
      ElMessage.error('获取联系信息失败')
    }
    contactData.value = null
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm')
  } catch {
    return timeStr
  }
}

// 处理编辑成功
const handleEditSuccess = (updatedData) => {
  contactData.value = updatedData
  ElMessage.success(contactData.value ? '联系信息更新成功' : '联系信息创建成功')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchContactData()
})
</script>

<style scoped>
.contact-us-manager {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}

.header-info {
  flex: 1;
}

.manager-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 4px 0;
}

.manager-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.contact-content {
  padding: 24px;
  min-height: 300px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.contact-display {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 32px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ec4899;
  display: inline-block;
}

.contact-image-section {
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.image-container {
  width: 100%;
  height: 240px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #9ca3af;
  background: #f3f4f6;
}

.image-error .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.contact-info-section {
  background: #fafafa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  display: flex;
  align-items: center;
  min-width: 120px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.label-icon {
  color: #ec4899;
  margin-right: 8px;
  font-size: 16px;
}

.label-text {
  white-space: nowrap;
}

.info-value {
  flex: 1;
  font-size: 15px;
  font-weight: 500;
  color: #374151;
  word-break: break-all;
  margin-left: 20px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-loading-mask) {
  border-radius: 12px;
}

@media (max-width: 768px) {
  .contact-display {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
</style>
