<template>
  <div class="development-history-manager">
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">发展历程</h3>
        <p class="manager-desc">管理月子中心发展历程，当前共 {{ histories.length }} 条记录</p>
      </div>
      <el-button 
        type="primary" 
        @click="handleCreate"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        添加历程
      </el-button>
    </div>

    <div class="history-content" v-loading="loading">
      <div v-if="histories.length === 0" class="empty-state">
        <el-empty description="暂无发展历程">
          <el-button 
            type="primary" 
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            添加第一条历程
          </el-button>
        </el-empty>
      </div>

      <div v-else class="timeline-container">
        <div class="timeline">
          <div
            v-for="(item, index) in sortedHistories"
            :key="item.rid"
            class="timeline-item"
            :class="{ 'is-last': index === sortedHistories.length - 1 }"
          >
            <div class="timeline-marker">
              <div class="marker-dot"></div>
              <div v-if="index !== sortedHistories.length - 1" class="marker-line"></div>
            </div>
            
            <div class="timeline-content">
              <div class="content-header">
                <div class="year-badge">{{ item.year }}</div>
                <div class="action-buttons">
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Edit"
                    circle
                    @click="handleEdit(item)"
                    title="编辑"
                  />
                  <el-button
                    type="danger"
                    size="small"
                    :icon="Delete"
                    circle
                    @click="handleDelete(item)"
                    title="删除"
                  />
                </div>
              </div>
              
              <div class="content-description">
                {{ item.description }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 发展历程编辑对话框 -->
    <DevelopmentHistoryDialog
      v-model="dialogVisible"
      :history-data="currentHistory"
      :is-edit="isEditMode"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox, ElIcon, ElButton, ElEmpty } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { del } from '@/utils/request'
import DevelopmentHistoryDialog from './DevelopmentHistoryDialog.vue'

// 定义 props
const props = defineProps({
  histories: {
    type: Array,
    default: () => []
  }
})

// 定义 emits
const emit = defineEmits(['refresh'])

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const isEditMode = ref(false)
const currentHistory = ref(null)

// 按年份排序的历程列表（降序）
const sortedHistories = computed(() => {
  return [...props.histories].sort((a, b) => b.year - a.year)
})

// 处理创建
const handleCreate = () => {
  currentHistory.value = null
  isEditMode.value = false
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (item) => {
  currentHistory.value = { ...item }
  isEditMode.value = true
  dialogVisible.value = true
}

// 处理删除
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${item.year} 年的发展历程吗？删除后无法恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    )

    loading.value = true
    await del(`maternity-center/development/history/delete/${item.rid}/`)
    
    ElMessage.success('发展历程删除成功')
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除发展历程失败:', error)
      ElMessage.error('删除发展历程失败')
    }
  } finally {
    loading.value = false
  }
}

// 处理操作成功
const handleSuccess = () => {
  ElMessage.success(isEditMode.value ? '发展历程更新成功' : '发展历程创建成功')
  emit('refresh')
}
</script>

<style scoped>
.development-history-manager {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}

.header-info {
  flex: 1;
}

.manager-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 4px 0;
}

.manager-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.history-content {
  padding: 24px;
  min-height: 300px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.timeline-container {
  max-width: 800px;
  margin: 0 auto;
}

.timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 32px;
}

.timeline-item.is-last {
  margin-bottom: 0;
}

.timeline-marker {
  position: relative;
  margin-right: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.marker-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #ec4899;
  border: 3px solid #fce7f3;
  z-index: 2;
}

.marker-line {
  width: 2px;
  flex: 1;
  background: #e5e7eb;
  margin-top: 8px;
  min-height: 60px;
}

.timeline-content {
  flex: 1;
  background: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.year-badge {
  background: linear-gradient(135deg, #ec4899, #f97316);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.content-description {
  color: #374151;
  line-height: 1.6;
  font-size: 14px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-loading-mask) {
  border-radius: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-marker {
    margin-right: 16px;
  }
  
  .timeline-content {
    padding: 16px;
  }
  
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
