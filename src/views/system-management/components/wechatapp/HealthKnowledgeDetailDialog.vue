<template>
  <el-dialog
    v-model="visible"
    title="健康知识详情"
    width="800px"
    align-center
    :before-close="handleClose"
    class="health-knowledge-detail-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <div v-if="detailData" class="knowledge-detail">

        <!-- 标题 -->
        <div class="detail-section">
          <h3 class="section-title">标题</h3>
          <div class="summary-content">
            {{ detailData.title }}
          </div>
        </div>

        <!-- 摘要信息 -->
        <div class="detail-section">
          <h3 class="section-title">摘要信息</h3>
          <div class="summary-content">
            {{ detailData.summary }}
          </div>
        </div>

        <!-- 详细内容 -->
        <div class="detail-section">
          <h3 class="section-title">详细内容</h3>
          <div class="content-text">
            {{ detailData.content }}
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">系统信息：</h3>
          <div class="info-grid">
            <div class="info-row">
              <span class="info-label">创建时间：</span>
              <el-tag type="info">{{ formatTime(detailData.created_at) }}</el-tag>
            </div>
            <div class="info-row">
              <span class="info-label">更新时间：</span>
              <el-tag type="info">{{ formatTime(detailData.updated_at) }}</el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request'
import { format } from 'date-fns'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  knowledgeData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(visible, async (newVal) => {
  if (newVal && props.knowledgeData) {
    await fetchKnowledgeDetail()
  }
})

// 获取健康知识详情
const fetchKnowledgeDetail = async () => {
  if (!props.knowledgeData?.rid) return

  loading.value = true
  try {
    const data = await get(`maternity-center/health/knowledge/detail/${props.knowledgeData.rid}/`)
    detailData.value = data
  } catch (error) {
    console.error('获取健康知识详情失败:', error)
    ElMessage.error('获取详情失败')
    handleClose()
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm:ss')
  } catch {
    return timeStr
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  detailData.value = null
}
</script>

<style scoped>
.health-knowledge-detail-dialog {
  --el-dialog-padding-primary: 0;
}

.dialog-content {
  padding: 24px;
}

.knowledge-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-left: 12px;
  border-left: 4px solid #e879a1;
  background-color: #fafafa;
  padding: 8px 12px;
  margin: 0 0 16px 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px 32px;
  padding: 0 16px;
}

.info-row {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.info-label {
  color: #6b7280;
  font-size: 14px;
  width: 80px;
  flex-shrink: 0;
}

.info-value {
  color: #1f2937;
  font-size: 14px;
  flex: 1;
}

.summary-content {
  color: #1f2937;
  font-size: 14px;
  line-height: 1.6;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.content-text {
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.8;
  color: #1f2937;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  min-height: 120px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
