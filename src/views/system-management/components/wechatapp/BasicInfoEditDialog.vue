<template>
  <el-dialog
    v-model="visible"
    title="编辑基本信息"
    width="600px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="basic-info-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="basic-info-form"
        autocomplete="off"
      >
        <div class="form-section">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="中心名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入月子中心名称" 
                autocomplete="off"
                maxlength="100"
                show-word-limit
              >
                <template #prefix>
                  <el-icon class="text-pink-500">
                    <Shop />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="中心地址" prop="address">
              <el-input 
                v-model="form.address" 
                type="textarea"
                :rows="3"
                placeholder="请输入月子中心详细地址"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="联系电话" prop="contact">
              <el-input 
                v-model="form.contact" 
                placeholder="请输入联系电话" 
                autocomplete="off"
                maxlength="20"
              >
                <template #prefix>
                  <el-icon class="text-pink-500">
                    <Phone />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <el-form-item label="容纳人数" prop="capacity">
              <el-input-number
                v-model="form.capacity"
                :min="1"
                :max="9999"
                placeholder="请输入容纳人数"
                class="w-full"
              />
              <div class="text-sm text-gray-500 mt-1">
                请输入月子中心最大容纳人数
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          保存更新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Shop, Phone } from '@element-plus/icons-vue'
import { put } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  basicInfo: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 组件引用
const formRef = ref()

// 状态管理
const loading = ref(false)
const submitting = ref(false)

// 表单数据
const form = reactive({
  name: '',
  address: '',
  contact: '',
  capacity: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入月子中心名称', trigger: 'blur' },
    { min: 2, max: 100, message: '中心名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入月子中心地址', trigger: 'blur' },
    { min: 5, max: 200, message: '地址长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  ],
  capacity: [
    { required: true, message: '请输入容纳人数', trigger: 'blur' },
    { type: 'number', min: 1, max: 9999, message: '容纳人数必须在 1 到 9999 之间', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.name = ''
  form.address = ''
  form.contact = ''
  form.capacity = 1
}

// 填充表单数据
const fillForm = () => {
  if (props.basicInfo) {
    form.name = props.basicInfo.name || ''
    form.address = props.basicInfo.address || ''
    form.contact = props.basicInfo.contact || ''
    form.capacity = props.basicInfo.capacity || 1
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    submitting.value = true
    
    const submitData = {
      name: form.name,
      address: form.address,
      contact: form.contact,
      capacity: form.capacity
    }
    
    const data = await put('maternity-center/basic/info/update/', submitData)
    
    emit('success', data)
    handleClose()
  } catch (error) {
    console.error('更新失败:', error)
    if (error.msg) {
      ElMessage.error(error.msg)
    } else {
      ElMessage.error('更新基本信息失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  visible.value = false
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    fillForm()
  }
})
</script>

<style scoped>
.basic-info-dialog {
  --el-dialog-padding-primary: 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 0;
  margin-right: 0;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

.basic-info-form {
  width: 100%;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ec4899;
  display: inline-block;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
}

:deep(.el-input__inner) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

/* 表单项间距调整 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}
</style>
