<template>
  <div class="brand-intro-manager">
    <!-- 品牌介绍卡片 -->
    <BrandIntroCard 
      :brand-data="brandData"
      :loading="loading"
      @edit="handleEditBrand"
      @create="handleCreateBrand"
    />

    <!-- 发展历程管理 -->
    <div v-if="brandData" class="mt-6">
      <DevelopmentHistoryManager 
        :histories="brandData.development_histories || []"
        @refresh="fetchBrandData"
      />
    </div>

    <!-- 品牌介绍编辑对话框 -->
    <BrandIntroDialog
      v-model="brandDialogVisible"
      :brand-data="brandData"
      :is-edit="isEditMode"
      @success="handleBrandSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request'
import BrandIntroCard from './BrandIntroCard.vue'
import DevelopmentHistoryManager from './DevelopmentHistoryManager.vue'
import BrandIntroDialog from './BrandIntroDialog.vue'

// 响应式数据
const loading = ref(false)
const brandData = ref(null)
const brandDialogVisible = ref(false)
const isEditMode = ref(false)

// 获取品牌介绍数据
const fetchBrandData = async () => {
  loading.value = true
  try {
    const response = await get('maternity-center/brand/introduction/')

    // 检查是否有数据
      if (response === null) {
        brandData.value = null
    } else {
      brandData.value = response
    }
  } catch (error) {
    console.error('获取品牌介绍失败:', error)
    ElMessage.error('获取品牌介绍失败')
  } finally {
    loading.value = false
  }
}

// 处理创建品牌介绍
const handleCreateBrand = () => {
  isEditMode.value = false
  brandDialogVisible.value = true
}

// 处理编辑品牌介绍
const handleEditBrand = () => {
  isEditMode.value = true
  brandDialogVisible.value = true
}

// 处理品牌介绍操作成功
const handleBrandSuccess = (newBrandData) => {
  brandData.value = newBrandData
  ElMessage.success(isEditMode.value ? '品牌介绍更新成功' : '品牌介绍创建成功')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBrandData()
})
</script>

<style scoped>
/* 组件样式 */
</style>
