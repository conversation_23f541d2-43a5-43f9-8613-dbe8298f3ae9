<template>
  <div class="carousel-manager">
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">轮播图管理</h3>
        <p class="manager-desc">管理小程序首页轮播图，当前共 {{ carouselList.length }} 张图片</p>
      </div>
      <el-button 
        type="primary" 
        @click="showUploadDialog = true"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        上传轮播图
      </el-button>
    </div>

    <div class="carousel-content" v-loading="loading">
      <div v-if="carouselList.length === 0" class="empty-state">
        <el-empty description="暂无轮播图">
          <el-button 
            type="primary" 
            @click="showUploadDialog = true"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            上传第一张轮播图
          </el-button>
        </el-empty>
      </div>

      <div v-else class="carousel-grid">
        <div
          v-for="item in carouselList"
          :key="item.rid"
          class="carousel-card"
        >
          <div class="image-container">
            <el-image
              :src="item.url"
              fit="cover"
              class="carousel-image"
              :preview-src-list="[item.url]"
              :initial-index="0"
              preview-teleported
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <p>图片加载失败</p>
                </div>
              </template>
            </el-image>
          </div>

          <div class="card-footer">
            <div class="upload-time">
              <el-icon class="time-icon">
                <Clock />
              </el-icon>
              <span>{{ formatTime(item.created_at) }}</span>
            </div>
            <el-button
              type="danger"
              size="small"
              :icon="Delete"
              circle
              @click="handleDelete(item)"
              class="delete-btn"
              title="删除轮播图"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <CarouselUploadDialog
      v-model="showUploadDialog"
      @success="handleUploadSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElIcon, ElButton, ElEmpty, ElImage } from 'element-plus'
import { Plus, Delete, Picture, Clock } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request'
import { format } from 'date-fns'
import CarouselUploadDialog from './CarouselUploadDialog.vue'

// 响应式数据
const loading = ref(false)
const carouselList = ref([])
const showUploadDialog = ref(false)

// 获取轮播图列表
const fetchCarouselList = async () => {
  loading.value = true
  try {
    const data = await get('maternity-center/carousel/list/')
    carouselList.value = data.list || []
  } catch (error) {
    console.error('获取轮播图列表失败:', error)
    ElMessage.error('获取轮播图列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm')
  } catch {
    return timeStr
  }
}

// 处理删除
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张轮播图吗？删除后无法恢复。',
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    )

    await del(`maternity-center/carousel/delete/${item.rid}/`)

    // 从本地列表中移除该项
    const index = carouselList.value.findIndex(carousel => carousel.rid === item.rid)
    if (index !== -1) {
      carouselList.value.splice(index, 1)
    }

    ElMessage.success('轮播图删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除轮播图失败:', error)
      ElMessage.error('删除轮播图失败')
    }
  }
}

// 处理上传成功
const handleUploadSuccess = (newCarousel) => {
  // 将新上传的轮播图添加到列表开头
  carouselList.value.unshift(newCarousel)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchCarouselList()
})
</script>

<style scoped>
.carousel-manager {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}

.header-info {
  flex: 1;
}

.manager-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 4px 0;
}

.manager-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.carousel-content {
  padding: 24px;
  min-height: 300px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.carousel-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.carousel-card {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
}

.carousel-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.image-container {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.carousel-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #9ca3af;
  background: #f3f4f6;
}

.image-error .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 0 0 12px 12px;
}

.upload-time {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #6b7280;
}

.time-icon {
  margin-right: 4px;
  font-size: 14px;
}

.delete-btn {
  width: 28px;
  height: 28px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-loading-mask) {
  border-radius: 12px;
}
</style>
