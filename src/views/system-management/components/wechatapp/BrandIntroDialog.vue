<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑品牌介绍' : '创建品牌介绍'"
    width="1000px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="brand-intro-dialog"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="top"
        class="brand-form"
      >
        <!-- 品牌图片上传 -->
        <el-form-item prop="introduction_image">
          <template #label>
            <div class="collapsible-label" @click="toggleImageSection">
              <span>品牌图片</span>
              <el-icon class="collapse-icon" :class="{ 'collapsed': !imageExpanded }">
                <ArrowDown />
              </el-icon>
            </div>
          </template>

          <el-collapse-transition>
            <div v-show="imageExpanded" class="image-upload-section">
              <div class="upload-area" @click="triggerFileUpload" v-loading="imageUploading">
                <div v-if="imagePreview" class="preview-container">
                  <el-image
                    :src="imagePreview"
                    fit="cover"
                    class="preview-image"
                    :preview-src-list="[imagePreview]"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <p>图片预览失败</p>
                      </div>
                    </template>
                  </el-image>
                  <div class="preview-overlay">
                    <el-button
                      type="primary"
                      size="small"
                      @click.stop="triggerFileUpload"
                      :loading="imageUploading"
                    >
                      重新选择
                    </el-button>
                  </div>
                </div>

                <div v-else class="upload-placeholder">
                  <el-icon class="upload-icon">
                    <Plus />
                  </el-icon>
                  <p class="upload-text">{{ imageUploading ? '上传中...' : '点击选择品牌图片' }}</p>
                  <p class="upload-hint">支持 JPG、PNG 格式，建议尺寸 800x400</p>
                </div>
              </div>

              <input
                ref="fileInputRef"
                type="file"
                accept="image/*"
                @change="handleImageSelect"
                style="display: none"
                :disabled="imageUploading"
              />
            </div>
          </el-collapse-transition>
        </el-form-item>

        <!-- 品牌介绍 -->
        <el-form-item label="品牌介绍" prop="brand_introduction">
          <el-input
            v-model="formData.brand_introduction"
            type="textarea"
            :rows="4"
            placeholder="请输入品牌介绍内容..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <!-- 品牌优势 -->
        <el-form-item label="品牌优势" prop="brand_advantage">
          <el-input
            v-model="formData.brand_advantage"
            type="textarea"
            :rows="4"
            placeholder="请输入品牌优势内容..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <!-- 品牌愿景 -->
        <el-form-item label="品牌愿景" prop="brand_vision">
          <el-input
            v-model="formData.brand_vision"
            type="textarea"
            :rows="4"
            placeholder="请输入品牌愿景内容..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <!-- 荣誉资质 -->
        <el-form-item label="荣誉资质" prop="honor_qualification">
          <el-input
            v-model="formData.honor_qualification"
            type="textarea"
            :rows="4"
            placeholder="请输入荣誉资质内容..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElIcon, ElImage, ElCollapseTransition } from 'element-plus'
import { Plus, Picture, ArrowDown } from '@element-plus/icons-vue'
import { post, put, upload } from '@/utils/request'

// 定义 props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  brandData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// 定义 emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const fileInputRef = ref()
const submitting = ref(false)
const imageUploading = ref(false)
const imagePreview = ref('')
const selectedImageFile = ref(null)
const imageExpanded = ref(true) // 图片区域展开状态

// 表单数据
const formData = ref({
  brand_introduction: '',
  brand_advantage: '',
  brand_vision: '',
  honor_qualification: ''
})

// 表单验证规则
const formRules = {
  brand_introduction: [
    { required: true, message: '请输入品牌介绍', trigger: 'blur' }
  ],
  brand_advantage: [
    { required: true, message: '请输入品牌优势', trigger: 'blur' }
  ],
  brand_vision: [
    { required: true, message: '请输入品牌愿景', trigger: 'blur' }
  ],
  honor_qualification: [
    { required: true, message: '请输入荣誉资质', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    initForm()
  }
})

// 初始化表单
const initForm = () => {
  if (props.isEdit && props.brandData) {
    formData.value = {
      brand_introduction: props.brandData.brand_introduction || '',
      brand_advantage: props.brandData.brand_advantage || '',
      brand_vision: props.brandData.brand_vision || '',
      honor_qualification: props.brandData.honor_qualification || ''
    }
    imagePreview.value = props.brandData.introduction_image || ''
    // 如果有图片，默认展开图片区域
    imageExpanded.value = !!props.brandData.introduction_image
  } else {
    formData.value = {
      brand_introduction: '',
      brand_advantage: '',
      brand_vision: '',
      honor_qualification: ''
    }
    imagePreview.value = ''
    // 创建模式下默认展开图片区域
    imageExpanded.value = true
  }
  selectedImageFile.value = null

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 触发文件选择
const triggerFileUpload = () => {
  fileInputRef.value?.click()
}

// 切换图片区域展开/折叠状态
const toggleImageSection = () => {
  imageExpanded.value = !imageExpanded.value
}

// 处理图片选择
const handleImageSelect = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过 5MB')
    return
  }

  selectedImageFile.value = file

  // 创建预览
  const reader = new FileReader()
  reader.onload = (e) => {
    imagePreview.value = e.target.result
  }
  reader.readAsDataURL(file)

  // 如果图片区域是折叠状态，自动展开
  if (!imageExpanded.value) {
    imageExpanded.value = true
  }
}

// 处理提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    // 检查图片
    if (!imagePreview.value) {
      ElMessage.error('请选择品牌图片')
      return
    }

    submitting.value = true

    // 准备表单数据
    const formDataToSubmit = new FormData()
    
    // 添加文本字段
    Object.keys(formData.value).forEach(key => {
      formDataToSubmit.append(key, formData.value[key])
    })

    // 添加图片文件
    if (selectedImageFile.value) {
      formDataToSubmit.append('introduction_image', selectedImageFile.value)
    }

    // 发送请求
    let response
    if (props.isEdit) {
      response = await upload('maternity-center/brand/introduction/update/', formDataToSubmit)
    } else {
      response = await upload('maternity-center/brand/introduction/create/', formDataToSubmit)
    }

    emit('success', response)
    visible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    submitting.value = false
  }
}

// 处理关闭
const handleClose = () => {
  if (submitting.value) return
  visible.value = false
}
</script>

<style scoped>


.dialog-content {
  max-height: 75vh;
  overflow-y: auto;
}

.brand-form {
  padding: 0 8px;
}

.collapsible-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
  font-weight: 600;
  color: #374151;
  padding: 4px 0;
  transition: color 0.3s ease;
}

.collapsible-label:hover {
  color: #ec4899;
}

.collapse-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.collapse-icon.collapsed {
  transform: rotate(-90deg);
}

.image-upload-section {
  width: 100%;
  margin-top: 8px;
}

.upload-area {
  width: 100%;
  min-height: 400px;
  max-height: 500px;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #ec4899;
  background-color: #fdf2f8;
}

.preview-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.preview-container:hover .preview-overlay {
  opacity: 1;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #6b7280;
}

.upload-icon {
  font-size: 48px;
  color: #d1d5db;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.upload-hint {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #9ca3af;
  background: #f3f4f6;
}

.image-error .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-dialog__header) {
  padding: 24px 24px 0;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 24px;
}

/* 确保图片在预览时能完整显示 */
:deep(.el-image__inner) {
  object-fit: contain !important;
}

/* 优化对话框在不同屏幕尺寸下的显示 */
@media (max-width: 1200px) {
  .upload-area {
    min-height: 300px;
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .upload-area {
    min-height: 250px;
    max-height: 300px;
  }
}
</style>
