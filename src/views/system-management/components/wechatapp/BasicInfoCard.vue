<template>
  <div class="basic-info-card">
    <div class="card-header">
      <h3 class="card-title">月子中心基本信息</h3>
      <el-button 
        type="primary" 
        @click="handleEdit"
        :loading="loading"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Edit />
        </el-icon>
        编辑信息
      </el-button>
    </div>

    <div class="card-content" v-loading="loading">
      <div class="info-list">
        <div class="info-row">
          <div class="info-label">
            <el-icon class="label-icon">
              <Shop />
            </el-icon>
            <span class="label-text">中心名称</span>
          </div>
          <div class="info-value">{{ basicInfo.name || '-' }}</div>
        </div>

        <div class="info-row">
          <div class="info-label">
            <el-icon class="label-icon">
              <Location />
            </el-icon>
            <span class="label-text">中心地址</span>
          </div>
          <div class="info-value">{{ basicInfo.address || '-' }}</div>
        </div>

        <div class="info-row">
          <div class="info-label">
            <el-icon class="label-icon">
              <Phone />
            </el-icon>
            <span class="label-text">联系电话</span>
          </div>
          <div class="info-value">{{ basicInfo.contact || '-' }}</div>
        </div>

        <div class="info-row">
          <div class="info-label">
            <el-icon class="label-icon">
              <User />
            </el-icon>
            <span class="label-text">容纳人数</span>
          </div>
          <div class="info-value">{{ basicInfo.capacity || 0 }} 人</div>
        </div>

        <div class="info-row">
          <div class="info-label">
            <el-icon class="label-icon">
              <Key />
            </el-icon>
            <span class="label-text">中心编号</span>
          </div>
          <div class="info-value">{{ basicInfo.cid || '-' }}</div>
        </div>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <BasicInfoEditDialog
      v-model="editDialogVisible"
      :basic-info="basicInfo"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElIcon, ElButton } from 'element-plus'
import { Edit, Shop, Location, Phone, User, Key } from '@element-plus/icons-vue'
import { get } from '@/utils/request'
import BasicInfoEditDialog from './BasicInfoEditDialog.vue'

// 响应式数据
const loading = ref(false)
const basicInfo = ref({
  name: '',
  address: '',
  contact: '',
  capacity: 0,
  cid: ''
})
const editDialogVisible = ref(false)

// 获取基本信息
const fetchBasicInfo = async () => {
  loading.value = true
  try {
    const data = await get('maternity-center/basic/info/')
    basicInfo.value = data || {}
  } catch (error) {
    console.error('获取基本信息失败:', error)
    ElMessage.error('获取基本信息失败')
  } finally {
    loading.value = false
  }
}

// 处理编辑
const handleEdit = () => {
  editDialogVisible.value = true
}

// 处理编辑成功
const handleEditSuccess = (updatedInfo) => {
  basicInfo.value = { ...updatedInfo }
  ElMessage.success('基本信息更新成功')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchBasicInfo()
})
</script>

<style scoped>
.basic-info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.card-content {
  padding: 0;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.info-row {
  display: flex;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row:hover {
  background: #fdf2f8;
}

.info-label {
  display: flex;
  align-items: center;
  min-width: 120px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
}

.label-icon {
  color: #ec4899;
  margin-right: 8px;
  font-size: 16px;
}

.label-text {
  white-space: nowrap;
}

.info-value {
  flex: 1;
  font-size: 15px;
  font-weight: 500;
  color: #374151;
  word-break: break-all;
  margin-left: 40px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-loading-mask) {
  border-radius: 12px;
}
</style>
