<template>
  <div class="brand-intro-card">
    <div class="card-header">
      <div class="header-info">
        <h3 class="card-title">品牌介绍</h3>
        <p class="card-desc">管理月子中心品牌信息和发展历程</p>
      </div>
      <el-button 
        v-if="brandData"
        type="primary" 
        @click="$emit('edit')"
        :loading="loading"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Edit />
        </el-icon>
        编辑品牌信息
      </el-button>
    </div>

    <div class="card-content" v-loading="loading">
      <!-- 未创建状态 -->
      <div v-if="!brandData" class="empty-state">
        <el-empty description="还未创建品牌介绍">
          <el-button 
            type="primary" 
            @click="$emit('create')"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-1">
              <Plus />
            </el-icon>
            创建品牌介绍
          </el-button>
        </el-empty>
      </div>

      <!-- 已创建状态 -->
      <div v-else class="brand-content">
        <!-- 品牌图片 -->
        <div class="brand-image-section">
          <div class="image-container">
            <el-image
              :src="brandData.introduction_image"
              fit="cover"
              class="brand-image"
              :preview-src-list="[brandData.introduction_image]"
              preview-teleported
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <p>图片加载失败</p>
                </div>
              </template>
            </el-image>
          </div>
        </div>

        <!-- 品牌信息 -->
        <div class="brand-info-section">
          <div class="info-item">
            <div class="info-label">
              <el-icon class="label-icon">
                <Document />
              </el-icon>
              <span class="label-text">品牌介绍</span>
            </div>
            <div class="info-content">{{ brandData.brand_introduction || '-' }}</div>
          </div>

          <div class="info-item">
            <div class="info-label">
              <el-icon class="label-icon">
                <Star />
              </el-icon>
              <span class="label-text">品牌优势</span>
            </div>
            <div class="info-content">{{ brandData.brand_advantage || '-' }}</div>
          </div>

          <div class="info-item">
            <div class="info-label">
              <el-icon class="label-icon">
                <View />
              </el-icon>
              <span class="label-text">品牌愿景</span>
            </div>
            <div class="info-content">{{ brandData.brand_vision || '-' }}</div>
          </div>

          <div class="info-item">
            <div class="info-label">
              <el-icon class="label-icon">
                <Trophy />
              </el-icon>
              <span class="label-text">荣誉资质</span>
            </div>
            <div class="info-content">{{ brandData.honor_qualification || '-' }}</div>
          </div>

          <!-- 时间信息 -->
          <div class="time-info">
            <div class="time-item">
              <span class="time-label">创建时间：</span>
              <span class="time-value">{{ formatTime(brandData.created_at) }}</span>
            </div>
            <div class="time-item">
              <span class="time-label">更新时间：</span>
              <span class="time-value">{{ formatTime(brandData.updated_at) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElIcon, ElButton, ElEmpty, ElImage } from 'element-plus'
import { Edit, Plus, Picture, Document, Star, View, Trophy } from '@element-plus/icons-vue'
import { format } from 'date-fns'

// 定义 props
defineProps({
  brandData: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义 emits
defineEmits(['edit', 'create'])

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm')
  } catch {
    return timeStr || '-'
  }
}
</script>

<style scoped>
.brand-intro-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}

.header-info {
  flex: 1;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 4px 0;
}

.card-desc {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.card-content {
  padding: 24px;
  min-height: 300px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.brand-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
}



.image-container {
  width: 100%;
  height: 240px;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.brand-image {
  width: 100%;
  height: 100%;
  cursor: pointer;
  object-fit: cover;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #9ca3af;
  background: #f3f4f6;
}

.image-error .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}



.info-item {
  margin-bottom: 20px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.label-icon {
  color: #ec4899;
  margin-right: 8px;
  font-size: 16px;
}

.label-text {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.info-content {
  color: #6b7280;
  line-height: 1.6;
  white-space: pre-wrap;
  font-size: 14px;
  padding-left: 24px;
}

.time-info {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 24px;
}

.time-item {
  font-size: 12px;
}

.time-label {
  color: #9ca3af;
}

.time-value {
  color: #6b7280;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-empty) {
  padding: 40px 0;
}

:deep(.el-loading-mask) {
  border-radius: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .brand-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .image-container {
    height: 160px;
  }
}
</style>
