<template>
  <div class="role-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <UserFilled />
          </el-icon>
          角色列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 个角色</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column prop="name" label="角色名称" min-width="150" fixed="left">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="32" class="mr-3 bg-pink-100 text-pink-600">
              {{ row.name.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="描述" min-width="200">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.description || '暂无描述' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="权限数量" min-width="120">
        <template #default="{ row }">
          <el-tag type="info" size="small"> {{ getPermissionCount(row) }} 个权限 </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" min-width="150">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.created_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="updated_at" label="更新时间" min-width="150">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.updated_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="250" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="default"
              size="small"
              class="text-blue-600 border-blue-200 hover:bg-blue-50"
              @click.stop="handleViewDetail(row)"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleEdit(row)"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              编辑
            </el-button>
            <el-button
              @click.stop="handleDelete(row)"
              type="danger"
              size="small"
              class="text-red-600 border-red-200 hover:bg-red-50"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UserFilled } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request'

const emit = defineEmits(['edit', 'delete', 'view-detail'])

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get('permission/role-list/', requestParams)
    tableData.value = data || []

    // 如果返回的是数组，直接使用；如果是对象包含list，则取list
    if (Array.isArray(data)) {
      tableData.value = data
      totalCount.value = data.length
    } else if (data && data.list) {
      tableData.value = data.list
      totalCount.value = data.total_count || data.total || data.list.length
    } else {
      tableData.value = []
      totalCount.value = 0
    }
  } catch (error) {
    console.error('获取角色列表失败:', error)
    ElMessage.error('获取角色列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 获取权限数量
const getPermissionCount = (role) => {
  if (!role.permissions) return 0
  return Array.isArray(role.permissions) ? role.permissions.length : 0
}

// 时间格式化
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm')
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleDelete = (row) => {
  emit('delete', row)
}

const handleViewDetail = (row) => {
  emit('view-detail', row)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 监听筛选条件变化
watch(
  () => props.filters,
  () => {
    resetPagination()
  },
  { deep: true },
)

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.role-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.role-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
