<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="role-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="role-form"
        autocomplete="off"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="角色名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入角色名称" 
                autocomplete="off"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="角色描述" prop="description">
              <el-input 
                v-model="form.description" 
                type="textarea"
                :rows="3"
                placeholder="请输入角色描述"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </div>
        </div>

        <!-- 权限配置 -->
        <div class="form-section mb-6">
          <h4 class="section-title">权限配置</h4>
          <div class="permission-section">
            <div class="permission-header mb-4">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">
                  请选择该角色拥有的权限（已选择 {{ selectedPermissions.length }} 个权限）
                </span>
                <div class="flex gap-2">
                  <el-button size="small" @click="expandAll">
                    展开全部
                  </el-button>
                  <el-button size="small" @click="collapseAll">
                    收起全部
                  </el-button>
                  <el-button size="small" type="warning" @click="clearAll">
                    清空选择
                  </el-button>
                </div>
              </div>
            </div>
            <PermissionTree
              ref="permissionTreeRef"
              :permissions="permissions"
              :checked-permissions="selectedPermissions"
              @update:checked-permissions="handlePermissionChange"
            />
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ isEdit ? '更新角色' : '创建角色' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { get, post, put } from '@/utils/request'
import PermissionTree from './PermissionTree.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: null
  },
  permissions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 组件引用
const formRef = ref()
const permissionTreeRef = ref()

// 状态管理
const loading = ref(false)
const submitting = ref(false)
const selectedPermissions = ref([])

// 表单数据
const form = reactive({
  name: '',
  description: '',
  permissions: []
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '角色描述不能超过200个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const isEdit = computed(() => !!props.roleData)

const dialogTitle = computed(() => {
  return isEdit.value ? '编辑角色' : '新建角色'
})

// 权限变化处理
const handlePermissionChange = (permissions) => {
  selectedPermissions.value = permissions
  form.permissions = permissions
}

// 树操作方法
const expandAll = () => {
  if (permissionTreeRef.value) {
    permissionTreeRef.value.expandAll()
  }
}

const collapseAll = () => {
  if (permissionTreeRef.value) {
    permissionTreeRef.value.collapseAll()
  }
}

const clearAll = () => {
  selectedPermissions.value = []
  form.permissions = []
  if (permissionTreeRef.value) {
    permissionTreeRef.value.setCheckedPermissions([])
  }
}

// 加载角色详情
const loadRoleDetail = async (roleId) => {
  loading.value = true
  try {
    const data = await get(`permission/role-detail/${roleId}/`)
    
    // 填充表单数据
    form.name = data.name || ''
    form.description = data.description || ''
    form.permissions = data.permissions || []
    selectedPermissions.value = data.permissions || []
    
    // 设置权限树选中状态
    nextTick(() => {
      if (permissionTreeRef.value) {
        permissionTreeRef.value.setCheckedPermissions(data.permissions || [])
      }
    })
  } catch (error) {
    console.error('获取角色详情失败:', error)
    ElMessage.error('获取角色详情失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.name = ''
  form.description = ''
  form.permissions = []
  selectedPermissions.value = []
  
  nextTick(() => {
    if (permissionTreeRef.value) {
      permissionTreeRef.value.setCheckedPermissions([])
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()
    
    submitting.value = true
    
    const submitData = {
      name: form.name,
      description: form.description,
      permissions: selectedPermissions.value
    }
    
    if (isEdit.value) {
      // 编辑角色
      await put(`permission/role-update/${props.roleData.rid}/`, submitData)
      ElMessage.success('角色更新成功')
    } else {
      // 创建角色
      await post('permission/role-create/', submitData)
      ElMessage.success('角色创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.msg) {
      ElMessage.error(error.msg)
    } else {
      ElMessage.error(isEdit.value ? '角色更新失败' : '角色创建失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  visible.value = false
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    if (isEdit.value && props.roleData) {
      // 编辑模式，加载角色详情
      loadRoleDetail(props.roleData.rid)
    } else {
      // 新建模式，重置表单
      resetForm()
    }
  }
})
</script>

<style scoped>
.role-dialog {
  --el-dialog-padding-primary: 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 0;
  margin-right: 0;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

.role-form {
  width: 100%;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ec4899;
  display: inline-block;
}

.permission-section {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e5e7eb;
}

.permission-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
}

:deep(.el-input__inner) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
}

/* 表单项间距调整 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item:last-child) {
  margin-bottom: 0;
}
</style>