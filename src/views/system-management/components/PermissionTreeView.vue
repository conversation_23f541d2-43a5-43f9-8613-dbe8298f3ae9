<template>
  <div class="permission-tree-view">
    <div
      v-for="category in permissions"
      :key="category.category"
      class="permission-category"
    >
      <!-- 主分类标题 -->
      <div class="category-title">
        <el-icon><Folder /></el-icon>
        <span>{{ category.category }}</span>
        <span class="count">({{ getTotalPermissionCount(category) }}个权限)</span>
      </div>
      
      <!-- 直接权限 -->
      <div v-if="category.permissions && category.permissions.length > 0" class="permission-list">
        <div
          v-for="permission in category.permissions"
          :key="permission.value"
          class="permission-item"
        >
          • {{ permission.label }}
        </div>
      </div>
      
      <!-- 子分类 -->
      <div v-if="category.subcategories && category.subcategories.length > 0" class="subcategories">
        <div
          v-for="subcategory in category.subcategories"
          :key="subcategory.category"
          class="subcategory"
        >
          <div class="subcategory-title">
            <el-icon><FolderOpened /></el-icon>
            <span>{{ subcategory.category }}</span>
            <span class="count">({{ getTotalPermissionCount(subcategory) }}个权限)</span>
          </div>
          
          <!-- 子分类权限 -->
          <div v-if="subcategory.permissions && subcategory.permissions.length > 0" class="permission-list">
            <div
              v-for="permission in subcategory.permissions"
              :key="permission.value"
              class="permission-item"
            >
              • {{ permission.label }}
            </div>
          </div>
          
          <!-- 更深层子分类 -->
          <PermissionTreeView
            v-if="subcategory.subcategories && subcategory.subcategories.length > 0"
            :permissions="subcategory.subcategories"
            class="nested"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Folder, FolderOpened } from '@element-plus/icons-vue'

defineProps({
  permissions: {
    type: Array,
    default: () => []
  }
})

// 递归计算权限总数
const getTotalPermissionCount = (category) => {
  let count = 0
  
  if (category.permissions) {
    count += category.permissions.length
  }
  
  if (category.subcategories) {
    category.subcategories.forEach(sub => {
      count += getTotalPermissionCount(sub)
    })
  }
  
  return count
}
</script>

<style scoped>
.permission-tree-view {
  font-size: 14px;
  line-height: 1.5;
}

.permission-category {
  margin-bottom: 20px;
}

.permission-category:last-child {
  margin-bottom: 0;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #e5e7eb;
}

.category-title .el-icon {
  color: #ec4899;
}

.subcategory {
  margin-left: 20px;
  margin-bottom: 12px;
}

.subcategory-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 6px;
}

.subcategory-title .el-icon {
  color: #3b82f6;
}

.permission-list {
  margin-left: 24px;
  margin-bottom: 8px;
}

.permission-item {
  color: #6b7280;
  margin-bottom: 2px;
  line-height: 1.4;
}

.count {
  font-size: 12px;
  color: #9ca3af;
  font-weight: normal;
}

.nested {
  margin-left: 20px;
  margin-top: 8px;
}

.nested .category-title {
  font-size: 13px;
  color: #059669;
}

.nested .category-title .el-icon {
  color: #059669;
}
</style>