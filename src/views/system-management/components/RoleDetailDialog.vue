<template>
  <el-dialog
    v-model="visible"
    title="角色详情"
    width="800px"
    align-center
    :before-close="handleClose"
    class="role-detail-dialog"
  >
    <div class="role-detail-content" v-loading="loading">
      <!-- 基本信息 -->
      <div class="detail-section mb-6">
        <h4 class="section-title">基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">角色ID：</label>
            <span class="info-value">{{ roleDetail.rid || '-' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">角色名称：</label>
            <span class="info-value">{{ roleDetail.name || '-' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">角色描述：</label>
            <span class="info-value">{{ roleDetail.description || '暂无描述' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">权限数量：</label>
            <span class="info-value">
              <el-tag type="info" size="small">
                {{ (roleDetail.permissions || []).length }} 个权限
              </el-tag>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">创建时间：</label>
            <span class="info-value">{{ formatDate(roleDetail.created_at) }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">更新时间：</label>
            <span class="info-value">{{ formatDate(roleDetail.updated_at) }}</span>
          </div>
        </div>
      </div>

      <!-- 权限列表 -->
      <div class="detail-section">
        <h4 class="section-title">权限列表</h4>
        <div class="permissions-container">
          <div v-if="!roleDetail.permissions || roleDetail.permissions.length === 0" class="no-permissions">
            <el-empty description="该角色暂无权限" />
          </div>
          <div v-else class="permissions-tree">
            <PermissionTreeView :permissions="organizedPermissions" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          关闭
        </el-button>
        <el-button 
          type="primary" 
          @click="handleEdit"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑角色
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Folder } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request'
import PermissionTreeView from './PermissionTreeView.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: null
  },
  allPermissions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'edit'])

// 状态管理
const loading = ref(false)
const roleDetail = ref({})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

// 将角色权限按分类组织成树形结构
const organizedPermissions = computed(() => {
  if (!roleDetail.value.permissions || !props.allPermissions.length) {
    return []
  }

  // 创建权限值的Set，用于快速查找
  const rolePermissionSet = new Set(roleDetail.value.permissions)
  
  // 递归过滤和构建权限树
  const filterPermissions = (categories) => {
    return categories.map(category => {
      const filteredCategory = {
        category: category.category,
        permissions: [],
        subcategories: []
      }
      
      // 过滤直接权限
      if (category.permissions) {
        filteredCategory.permissions = category.permissions.filter(permission =>
          rolePermissionSet.has(permission.value)
        )
      }
      
      // 递归过滤子分类
      if (category.subcategories) {
        filteredCategory.subcategories = filterPermissions(category.subcategories)
        // 只保留有权限的子分类
        filteredCategory.subcategories = filteredCategory.subcategories.filter(sub =>
          (sub.permissions && sub.permissions.length > 0) ||
          (sub.subcategories && sub.subcategories.length > 0)
        )
      }
      
      return filteredCategory
    }).filter(category =>
      // 只保留有权限的分类
      (category.permissions && category.permissions.length > 0) ||
      (category.subcategories && category.subcategories.length > 0)
    )
  }
  
  return filterPermissions(props.allPermissions)
})

// 时间格式化
const formatDate = (dateTime) => {
  if (!dateTime) return '-'
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 加载角色详情
const loadRoleDetail = async (roleId) => {
  if (!roleId) return
  
  loading.value = true
  try {
    const data = await get(`permission/role-detail/${roleId}/`)
    roleDetail.value = data
  } catch (error) {
    console.error('获取角色详情失败:', error)
    ElMessage.error('获取角色详情失败')
    roleDetail.value = {}
  } finally {
    loading.value = false
  }
}

// 编辑角色
const handleEdit = () => {
  emit('edit', roleDetail.value)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  roleDetail.value = {}
}

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal && props.roleData) {
    loadRoleDetail(props.roleData.rid)
  }
})
</script>

<style scoped>
.role-detail-dialog {
  --el-dialog-padding-primary: 0;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__header) {
  padding: 20px 24px 0;
  margin-right: 0;
}

:deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

.role-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ec4899;
  display: inline-block;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.info-label {
  font-weight: 600;
  color: #374151;
  min-width: 80px;
  margin-right: 8px;
}

.info-value {
  color: #6b7280;
  word-break: break-all;
}

.permissions-container {
  background: white;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  max-height: 400px;
  overflow-y: auto;
}

.no-permissions {
  padding: 20px;
  text-align: center;
}

.permissions-tree {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条 */
.permissions-container::-webkit-scrollbar,
.role-detail-content::-webkit-scrollbar {
  width: 6px;
}

.permissions-container::-webkit-scrollbar-track,
.role-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.permissions-container::-webkit-scrollbar-thumb,
.role-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.permissions-container::-webkit-scrollbar-thumb:hover,
.role-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-label {
    margin-bottom: 4px;
    margin-right: 0;
  }
}
</style>