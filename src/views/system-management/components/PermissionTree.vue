<template>
  <div class="permission-tree-container">
    <el-tree
      ref="treeRef"
      :data="treeData"
      :props="treeProps"
      show-checkbox
      node-key="value"
      :check-strictly="false"
      :default-checked-keys="checkedKeys"
      :expand-on-click-node="false"
      class="permission-tree"
      @check="handleCheck"
    >
      <template #default="{ node, data }">
        <div class="tree-node">
          <el-icon v-if="data.category" class="mr-1 text-pink-500">
            <Folder />
          </el-icon>
          <el-icon v-else class="mr-1 text-gray-500">
            <Key />
          </el-icon>
          <span class="node-label">{{ data.label || data.category }}</span>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Folder, Key } from '@element-plus/icons-vue'

const props = defineProps({
  permissions: {
    type: Array,
    default: () => []
  },
  checkedPermissions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:checkedPermissions'])

const treeRef = ref()
const checkedKeys = ref([])

const treeProps = {
  children: 'children',
  label: 'label'
}

// 将扁平的权限数据转换为树形结构
const treeData = computed(() => {
  const transformData = (data) => {
    const result = []
    
    data.forEach(item => {
      const node = {
        label: item.category,
        category: item.category,
        children: []
      }
      
      // 如果有直接的权限
      if (item.permissions && item.permissions.length > 0) {
        item.permissions.forEach(permission => {
          node.children.push({
            label: permission.label,
            value: permission.value,
            isLeaf: true
          })
        })
      }
      
      // 如果有子分类
      if (item.subcategories && item.subcategories.length > 0) {
        const subNodes = transformData(item.subcategories)
        node.children.push(...subNodes)
      }
      
      result.push(node)
    })
    
    return result
  }
  
  return transformData(props.permissions)
})

// 处理选中状态变化
const handleCheck = () => {
  nextTick(() => {
    const checkedNodes = treeRef.value.getCheckedNodes()
    const permissions = checkedNodes
      .filter(node => node.value) // 只取有value的节点（实际权限）
      .map(node => node.value)
    
    emit('update:checkedPermissions', permissions)
  })
}

// 监听外部传入的选中权限
watch(() => props.checkedPermissions, (newPermissions) => {
  checkedKeys.value = newPermissions || []
  nextTick(() => {
    if (treeRef.value) {
      treeRef.value.setCheckedKeys(checkedKeys.value)
    }
  })
}, { immediate: true, deep: true })

// 设置选中的权限（供外部调用）
const setCheckedPermissions = (permissions) => {
  checkedKeys.value = permissions || []
  nextTick(() => {
    if (treeRef.value) {
      treeRef.value.setCheckedKeys(checkedKeys.value)
    }
  })
}

// 获取选中的权限
const getCheckedPermissions = () => {
  if (!treeRef.value) return []
  const checkedNodes = treeRef.value.getCheckedNodes()
  return checkedNodes
    .filter(node => node.value)
    .map(node => node.value)
}

// 展开所有节点
const expandAll = () => {
  if (!treeRef.value) return
  
  // 递归获取所有节点的key
  const getAllNodeKeys = (nodes) => {
    const keys = []
    const traverse = (nodeList, parentKey = '') => {
      nodeList.forEach((node, index) => {
        const nodeKey = parentKey ? `${parentKey}-${index}` : `${index}`
        if (node.children && node.children.length > 0) {
          keys.push(nodeKey)
          traverse(node.children, nodeKey)
        }
      })
    }
    traverse(nodes)
    return keys
  }
  
  // 通过直接操作DOM元素来展开节点
  nextTick(() => {
    const allKeys = getAllNodeKeys(treeData.value)
    // Element Plus树组件没有直接的expandAll方法，我们通过遍历节点来展开
    const expandNodeRecursively = (nodes) => {
      nodes.forEach((node) => {
        if (node.childNodes && node.childNodes.length > 0) {
          node.expanded = true
          expandNodeRecursively(node.childNodes)
        }
      })
    }
    
    if (treeRef.value.store && treeRef.value.store.root) {
      expandNodeRecursively(treeRef.value.store.root.childNodes)
    }
  })
}

// 收起所有节点
const collapseAll = () => {
  if (!treeRef.value) return
  
  nextTick(() => {
    const collapseNodeRecursively = (nodes) => {
      nodes.forEach((node) => {
        node.expanded = false
        if (node.childNodes && node.childNodes.length > 0) {
          collapseNodeRecursively(node.childNodes)
        }
      })
    }
    
    if (treeRef.value.store && treeRef.value.store.root) {
      collapseNodeRecursively(treeRef.value.store.root.childNodes)
    }
  })
}

// 暴露方法给父组件
defineExpose({
  setCheckedPermissions,
  getCheckedPermissions,
  expandAll,
  collapseAll
})
</script>

<style scoped>
.permission-tree-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 8px;
}

.tree-node {
  display: flex;
  align-items: center;
  width: 100%;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-tree-node__content:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-tree-node__expand-icon) {
  color: #ec4899;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #ec4899;
  border-color: #ec4899;
}

:deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
  background-color: #ec4899;
  border-color: #ec4899;
}

:deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner::before) {
  background-color: #fff;
}

:deep(.el-tree-node__label) {
  font-size: 14px;
  color: #374151;
}

/* 自定义滚动条 */
.permission-tree-container::-webkit-scrollbar {
  width: 6px;
}

.permission-tree-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.permission-tree-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.permission-tree-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>