<template>
  <div class="wechat-app-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">微信小程序管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理微信小程序的基本信息、轮播图、品牌介绍和联系方式</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="mb-6">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="" type="border-card">
        <el-tab-pane label="基本信息" name="basic">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <InfoFilled />
              </el-icon>
              基本信息
            </div>
          </template>
          <BasicInfoCard />
        </el-tab-pane>

        <el-tab-pane label="首页轮播图" name="carousel">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Picture />
              </el-icon>
              首页轮播图
            </div>
          </template>
          <CarouselManager />
        </el-tab-pane>

        <el-tab-pane label="品牌介绍" name="brand">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Star />
              </el-icon>
              品牌介绍
            </div>
          </template>
          <BrandIntroManager />
        </el-tab-pane>

        <el-tab-pane label="联系我们" name="contact">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Phone />
              </el-icon>
              联系我们
            </div>
          </template>
          <ContactUsManager />
        </el-tab-pane>

        <el-tab-pane label="健康知识" name="health">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Reading />
              </el-icon>
              健康知识
            </div>
          </template>
          <HealthKnowledgeManager />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElTabs, ElTabPane, ElIcon } from 'element-plus'
import { InfoFilled, Picture, Star, Phone, Reading } from '@element-plus/icons-vue'
import BasicInfoCard from './components/wechatapp/BasicInfoCard.vue'
import CarouselManager from './components/wechatapp/CarouselManager.vue'
import ContactUsManager from './components/wechatapp/ContactUsManager.vue'
import BrandIntroManager from './components/wechatapp/BrandIntroManager.vue'
import HealthKnowledgeManager from './components/wechatapp/HealthKnowledgeManager.vue'

// 响应式数据
const activeTab = ref('basic')

// 方法
const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)
}
</script>

<style scoped>
.wechat-app-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

:deep(.wechat-tabs .el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

:deep(.wechat-tabs .el-tabs__content) {
  padding: 0;
}

:deep(.wechat-tabs .el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-weight: 500;
}

:deep(.wechat-tabs .el-tabs__item:hover) {
  color: #ec4899;
}
</style>