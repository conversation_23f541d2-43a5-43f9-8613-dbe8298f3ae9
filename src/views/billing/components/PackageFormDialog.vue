<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑套餐' : '新增套餐'"
    width="800px"
    align-center=""
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="package-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="package-form"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <el-form-item label="套餐名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入套餐名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="天数" prop="stay_days">
            <el-input v-model.number="formData.stay_days" placeholder="请输入天数" />
          </el-form-item>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <el-form-item label="价格" prop="price">
            <el-input v-model.number="formData.price" placeholder="请输入价格" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="formData.status">
              <el-radio value="ACTIVE">启用</el-radio>
              <el-radio value="DISABLED">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <el-form-item label="套餐描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入套餐描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElRadioGroup,
  ElRadio,
  ElButton,
  ElMessage,
} from 'element-plus'
import { put, post } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  packageData: {
    type: Object,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success', 'close'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const isEdit = computed(() => {
  return props.packageData !== null && props.packageData !== undefined
})

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  description: '',
  basePrice: null,
  discountPrice: null,
  validUntil: '',
  status: 'ACTIVE',
})

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: '',
    description: '',
    basePrice: null,
    discountPrice: null,
    validUntil: '',
    status: 'ACTIVE',
  })
  formRef.value?.clearValidate()
}

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7 // 禁用昨天及之前的日期
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入套餐名称', trigger: 'blur' },
    { min: 2, max: 50, message: '套餐名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  type: [{ required: true, message: '请选择套餐类型', trigger: 'change' }],
  description: [
    { required: true, message: '请输入套餐描述', trigger: 'blur' },
    { max: 200, message: '描述不能超过 200 个字符', trigger: 'blur' },
  ],
  basePrice: [
    { required: true, message: '请输入基础价格', trigger: 'blur' },
    { type: 'number', min: 1, message: '基础价格必须大于 0', trigger: 'blur' },
  ],
  discountPrice: [
    { required: true, message: '请输入优惠价格', trigger: 'blur' },
    { type: 'number', min: 1, message: '优惠价格必须大于 0', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value && formData.basePrice && value > formData.basePrice) {
          callback(new Error('优惠价格不能大于基础价格'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  validUntil: [{ required: true, message: '请选择有效期', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

// 监听套餐数据变化
watch(
  () => props.packageData,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        name: newData.name || '',
        stay_days: newData.stay_days || '',
        description: newData.description || '',
        price: newData.price || null,
        status: newData.status,
      })
    } else {
      resetForm()
    }
  },
  { immediate: true },
)

// 监听对话框显示状态
watch(dialogVisible, (visible) => {
  if (visible) {
    nextTick(() => {
      formRef.value?.clearValidate()
    })
  }
})

// 方法
const handleOpen = () => {
  if (!isEdit.value) {
    resetForm()
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
  emit('close')
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    submitting.value = true

    if (isEdit.value) {
      await put(`/organizational-management/packages/update/${props.packageData.rid}/`, formData)
    } else {
      await post(`/organizational-management/packages/create/`, formData)
    }

    ElMessage.success(isEdit.value ? '套餐更新成功' : '套餐创建成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.price-info {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-input-number .el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input-number:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-input-number .el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor.el-input) {
  transition: all 0.2s;
}

:deep(.el-date-editor:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-editor .el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
