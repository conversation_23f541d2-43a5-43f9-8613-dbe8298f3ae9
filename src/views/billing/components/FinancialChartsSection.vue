<template>
  <div class="mb-8">
    <div class="grid grid-cols-3 gap-6">
      <!-- 收入趋势图 -->
      <div class="col-span-2">
        <el-card class="chart-card h-full" shadow="hover">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-semibold text-gray-800">收入趋势</span>
              <div class="flex items-center gap-2">
                <el-tag size="small" type="primary">近6个月</el-tag>
                <el-button size="small" text @click="refreshRevenueChart">
                  <Refresh class="w-4 h-4" />
                </el-button>
              </div>
            </div>
          </template>
          <div class="chart-container" style="height: 320px">
            <v-chart :option="revenueChartOption" autoresize />
          </div>
        </el-card>
      </div>

      <!-- 套餐分布图 -->
      <div class="col-span-1">
        <el-card class="chart-card h-full" shadow="hover">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-semibold text-gray-800">套餐分布</span>
              <el-tag size="small" type="success">本月</el-tag>
            </div>
          </template>
          <div class="chart-container" style="height: 320px">
            <v-chart :option="packageChartOption" autoresize />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Refresh } from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
])

const revenueData = computed(() => {
  if (!props.data.income_trends) return []
  return props.data.income_trends.map(item => ({
    month: item.month,
    value: item.amount
  }))
})

const packageData = computed(() => {
  if (!props.data.package_distribution) return []
  return props.data.package_distribution.map((item, index) => ({
    name: item.package_name,
    value: item.count,
    color: getPackageColor(index)
  }))
})

const getPackageColor = (index) => {
  const colors = ['#E77FA1', '#43B884', '#FFB357', '#6A7EE7', '#FF9F40']
  return colors[index % colors.length]
}

// 收入趋势图配置
const revenueChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
    },
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#E77FA1',
    borderWidth: 1,
    textStyle: {
      color: '#333',
    },
    formatter: function (params) {
      const data = params[0]
      return `${data.name}<br/>收入: ¥${data.value.toLocaleString()}`
    },
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '8%',
    top: '5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: revenueData.value.map((item) => item.month),
    axisLine: {
      lineStyle: {
        color: '#e0e0e0',
      },
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#666',
      fontSize: 12,
    },
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#666',
      fontSize: 12,
      formatter: function (value) {
        return '¥' + (value / 10000).toFixed(0) + '万'
      },
    },
    splitLine: {
      lineStyle: {
        color: '#f0f0f0',
        type: 'dashed',
      },
    },
  },
  series: [
    {
      name: '月收入',
      data: revenueData.value.map((item) => item.value),
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#E77FA1',
        width: 3,
      },
      itemStyle: {
        color: '#E77FA1',
        borderWidth: 3,
        borderColor: '#fff',
        shadowColor: 'rgba(231, 127, 161, 0.3)',
        shadowBlur: 10,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(231, 127, 161, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(231, 127, 161, 0.05)',
            },
          ],
        },
      },
    },
  ],
}))

// 套餐分布饼图配置
const packageChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#E77FA1',
    borderWidth: 1,
    textStyle: {
      color: '#333',
    },
    formatter: function (params) {
      return `${params.name}<br/>占比: ${params.percent}%<br/>数量: ${params.value}份`
    },
  },
  legend: {
    bottom: '5%',
    left: 'center',
    textStyle: {
      color: '#666',
      fontSize: 12,
    },
    itemWidth: 12,
    itemHeight: 12,
  },
  series: [
    {
      name: '套餐分布',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 6,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}: {c}份',
        fontSize: 11,
        color: '#666',
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold',
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.2)',
        },
      },
      data: packageData.value.map((item) => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: item.color,
        },
      })),
    },
  ],
}))

// 刷新收入图表
const refreshRevenueChart = () => {
  // 由父组件重新获取数据
  console.log('刷新收入图表数据')
}
</script>

<style scoped>
.chart-card {
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.chart-card:hover {
  border-color: rgba(231, 127, 161, 0.3);
}

.chart-container {
  width: 100%;
}
</style>
