<template>
  <div class="package-table">
    <el-card class="table-card" shadow="never">
      <div class="table-header mb-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <h3 class="text-lg font-semibold text-gray-800">套餐列表</h3>
            <el-tag v-if="data.length > 0" type="info" size="small">
              共 {{ pagination.total }} 条记录
            </el-tag>
          </div>
        </div>
      </div>

      <div class="table-content">
        <el-table
          :data="data"
          :loading="loading"
          stripe
          class="package-data-table"
          @sort-change="handleSortChange"
          empty-text="暂无套餐数据"
          :header-cell-style="{
            backgroundColor: '#f9fafb',
            color: '#374151',
            fontWeight: '600',
            borderBottom: '1px solid #e5e7eb',
            textAlign: 'center',
          }"
          :cell-style="{ textAlign: 'center' }"
        >
          <!-- 套餐名称 -->
          <el-table-column prop="name" label="套餐名称" min-width="180" sortable>
            <template #default="{ row }">
              <div class="flex flex-col">
                <span class="font-medium text-gray-900">{{ row.name }}</span>
                <span class="text-sm text-gray-500 mt-1">{{ row.description }}</span>
              </div>
            </template>
          </el-table-column>

          <!-- 价格 -->
          <el-table-column prop="price" label="价格" min-width="120" sortable>
            <template #default="{ row }">
              <div class="flex flex-col items-center">
                <span class="font-medium text-pink-600"> ¥{{ row.price }} </span>
                <!-- <span class="text-xs text-green-600" v-if="row.basePrice > row.discountPrice">
                  省 ¥{{ (row.basePrice - row.discountPrice).toLocaleString() }}
                </span> -->
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="stay_days" label="天数" width="180" />

          <!-- 状态 -->
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'" size="small">
                {{ row.status === 'ACTIVE' ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 创建时间 -->
          <el-table-column prop="created_at" label="创建时间" min-width="120" sortable>
            <template #default="{ row }">
              <span class="text-sm text-gray-600">{{ row.created_at }}</span>
            </template>
          </el-table-column>

          <!-- 操作 -->
          <el-table-column
            v-permission="'ogm.charge.edit'"
            label="操作"
            min-width="180"
            fixed="right"
          >
            <template #default="{ row }">
              <div class="flex items-center gap-2 justify-center">
                <el-button
                  type="primary"
                  size="small"
                  @click="$emit('edit', row)"
                  class="bg-blue-500 hover:bg-blue-600 border-blue-500"
                >
                  编辑
                </el-button>

                <!-- <el-button
                  :type="row.status === 'active' ? 'warning' : 'success'"
                  size="small"
                  @click="$emit('toggle-status', row)"
                >
                  {{ row.status === 'active' ? '停用' : '启用' }}
                </el-button> -->

                <el-button type="danger" size="small" @click="$emit('delete', row)">
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="table-footer mt-4" v-if="data.length > 0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="$emit('size-change', $event)"
          @current-change="$emit('current-change', $event)"
          class="justify-end"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ElCard, ElTable, ElTableColumn, ElTag, ElButton, ElIcon, ElPagination } from 'element-plus'
import { Edit, Delete } from '@element-plus/icons-vue'

defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    required: true,
  },
})

defineEmits(['edit', 'delete', 'toggle-status', 'size-change', 'current-change'])

// 获取套餐类型标签类型
const getTypeTagType = (type) => {
  const typeMap = {
    '21天': 'info',
    '28天': 'success',
    '42天': 'warning',
    '56天': 'danger',
  }
  return typeMap[type] || 'info'
}

// 检查是否即将过期
const isExpiringSoon = (validUntil) => {
  const today = new Date()
  const expiry = new Date(validUntil)
  const diffTime = expiry - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 30 && diffDays > 0
}

// 获取过期状态文本
const getExpiryStatus = (validUntil) => {
  const today = new Date()
  const expiry = new Date(validUntil)
  const diffTime = expiry - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) {
    return '已过期'
  } else if (diffDays <= 30) {
    return `${diffDays}天后过期`
  } else {
    return '有效期内'
  }
}

// 排序处理
const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', { column, prop, order })
  // 这里可以添加排序逻辑
}
</script>

<style scoped>
.package-table {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.table-header {
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
}

:deep(.package-data-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.package-data-table .el-table__header-wrapper) {
  background-color: #f9fafb;
}

:deep(.package-data-table .el-table__header th) {
  background-color: #f9fafb;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

:deep(.package-data-table .el-table__row:hover) {
  background-color: #f8fafc;
}

:deep(.package-data-table .el-button) {
  border-radius: 4px;
  font-size: 12px;
  padding: 4px 8px;
}

:deep(.el-pagination) {
  padding: 16px 0 0 0;
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}
</style>
