<template>
  <div class="statement-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Document />
          </el-icon>
          结算单列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ totalCount }} 个结算单</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="statements"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column prop="statementNumber" label="结算单号" width="280" fixed="left">
        <template #default="{ row }">
          <span class="font-mono font-medium">{{ row.statementNumber }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="customerName" label="客户姓名" width="150">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-icon class="mr-2 text-pink-500">
              <User />
            </el-icon>
            <span class="font-medium">{{ row.customerName }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="roomNumber" label="房间号" width="130">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-icon class="mr-1 text-gray-500">
              <House />
            </el-icon>
            <span class="font-mono">{{ row.roomNumber }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="packageType" label="套餐类型" width="170">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.packageType }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="入住日期" width="180">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.checkInDate }}</span>
        </template>
      </el-table-column>

      <el-table-column label="退房日期" width="180">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.checkOutDate }}</span>
        </template>
      </el-table-column>

      <el-table-column label="总金额" width="130" align="right">
        <template #default="{ row }">
          <span 
            class="font-medium text-red-600"
            :class="{ 'line-through': row.status === 'REFUNDED' }"
          >
            ¥{{ row.totalAmount.toLocaleString() }}
          </span>
        </template>
      </el-table-column>

      <el-table-column label="结算状态" width="120">
        <template #default="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            :effect="row.status === 'FULL_PAID' || row.status === 'REFUNDED' ? 'dark' : 'plain'"
            size="small"
          >
            {{ row.statusText }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="是否续住" width="80">
        <template #default="{ row }">
          <el-icon 
            :class="row.has_renew ? 'text-green-500' : 'text-gray-400'"
            :size="18"
          >
            <component :is="row.has_renew ? 'CircleCheck' : 'CircleClose'" />
          </el-icon>
        </template>
      </el-table-column>

      <el-table-column prop="createDate" label="创建日期" width="140">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.createDate }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="remarks" label="备注" min-width="80" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.remarks || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="240" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              :icon="View"
              @click="handleView(row)"
              class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
            >
            </el-button>
            <el-button
              type="warning"
              size="small"
              :icon="Edit"
              @click="handleEdit(row)"
              class="bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
            >
            </el-button>
            <el-button
              type="success"
              size="small"
              :icon="Printer"
              @click="handlePrint(row)"
              class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
            >
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElTable, ElTableColumn, ElTag, ElButton, ElPagination, ElIcon } from 'element-plus'
import { Document, User, House, View, Edit, Printer, Delete, CircleCheck, CircleClose } from '@element-plus/icons-vue'

// 定义属性
const props = defineProps({
  statements: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  total: {
    type: Number,
    default: 0,
  },
})

// 定义事件
const emit = defineEmits(['view', 'edit', 'print', 'cancel', 'pagination-change'])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)

// 使用传入的total
const totalCount = computed(() => props.total)

// 获取状态标签类型
const getStatusType = (status) => {
  const statusMap = {
    FULL_PAID: 'success',      // 已全款支付 - 绿色
    PARTIAL_PAID: 'warning',   // 已部分支付 - 橙色
    UNPAID: 'danger',          // 未支付 - 红色
    REFUNDED: 'danger',        // 已退款 - 红色（更显眼）
  }
  return statusMap[status] || 'info'
}

// 查看结算单
const handleView = (statement) => {
  emit('view', statement)
}

// 编辑结算单
const handleEdit = (statement) => {
  emit('edit', statement)
}

// 打印结算单
const handlePrint = (statement) => {
  emit('print', statement)
}

// 取消结算单
const handleCancel = (statement) => {
  emit('cancel', statement)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('pagination-change', { page, size: pageSize.value })
}
</script>

<style scoped>
.statement-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.statement-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

/* 操作按钮样式优化 */
.action-buttons .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.action-buttons .el-button .el-icon {
  font-size: 14px;
}

/* 自定义支付状态标签颜色 */
:deep(.el-tag.el-tag--success.el-tag--dark) {
  color: #fff !important;
  background-color: #2BEA8E !important;
  border-color: #2BEA8E !important;
}

/* 续住状态图标颜色 */
.text-green-500 {
  color: #2BEA8E !important;
}
</style>
