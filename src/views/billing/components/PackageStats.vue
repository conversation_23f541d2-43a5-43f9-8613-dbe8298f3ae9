<template>
  <div class="package-stats mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 总套餐数 -->
      <div
        class="stat-card bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">总套餐数</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.totalPackages }}</p>
          </div>
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <el-icon class="text-blue-600 text-xl">
                <Box />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 启用套餐数 -->
      <div
        class="stat-card bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">启用套餐</p>
            <p class="text-2xl font-bold text-green-600 mt-1">{{ stats.activePackages }}</p>
          </div>
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <el-icon class="text-green-600 text-xl">
                <CircleCheck />
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 即将过期 -->
      <div
        class="stat-card bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm font-medium text-gray-600">即将过期</p>
            <p class="text-2xl font-bold text-orange-600 mt-1">{{ stats.expiringSoon }}</p>
          </div>
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <el-icon class="text-orange-600 text-xl">
                <Warning />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElIcon } from 'element-plus'
import { Box, CircleCheck, Warning } from '@element-plus/icons-vue'

defineProps({
  stats: {
    type: Object,
    required: true,
    default: () => ({
      totalPackages: 0,
      activePackages: 0,
      expiringSoon: 0,
    }),
  },
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}
</style>
