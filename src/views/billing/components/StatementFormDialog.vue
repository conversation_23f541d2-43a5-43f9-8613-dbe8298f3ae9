<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="statement-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="statement-form"
      >
        <!-- 客户信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">客户信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="客户姓名" prop="customerName">
              <el-input v-model="formData.customerName" placeholder="请输入客户姓名" />
            </el-form-item>
            <el-form-item label="联系电话" prop="customerPhone">
              <el-input v-model="formData.customerPhone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="身份证号" prop="customerIdCard">
              <el-input v-model="formData.customerIdCard" placeholder="请输入身份证号" />
            </el-form-item>
          </div>
        </div>

        <!-- 入住信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">入住信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="房间号" prop="roomNumber">
              <el-select v-model="formData.roomNumber" placeholder="请选择房间" class="w-full">
                <el-option label="301 - 豪华套房" value="301" />
                <el-option label="302 - 豪华套房" value="302" />
                <el-option label="205 - 标准套房" value="205" />
                <el-option label="206 - 标准套房" value="206" />
                <el-option label="108 - 标准单间" value="108" />
              </el-select>
            </el-form-item>
            <el-form-item label="套餐类型" prop="packageType">
              <el-select v-model="formData.packageType" placeholder="请选择套餐类型" class="w-full">
                <el-option label="标准月子套餐" value="标准月子套餐" />
                <el-option label="豪华月子套餐" value="豪华月子套餐" />
                <el-option label="VIP月子套餐" value="VIP月子套餐" />
              </el-select>
            </el-form-item>
            <el-form-item label="入住日期" prop="checkInDate">
              <el-date-picker
                v-model="formData.checkInDate"
                type="date"
                placeholder="选择入住日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full!"
              />
            </el-form-item>
            <el-form-item label="退房日期" prop="checkOutDate">
              <el-date-picker
                v-model="formData.checkOutDate"
                type="date"
                placeholder="选择退房日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full!"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 费用信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">费用信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="房费" prop="roomFee">
              <el-input-number
                v-model="formData.roomFee"
                :min="0"
                :precision="2"
                placeholder="请输入房费"
                class="w-full!"
              />
            </el-form-item>
            <el-form-item label="餐费" prop="mealFee">
              <el-input-number
                v-model="formData.mealFee"
                :min="0"
                :precision="2"
                placeholder="请输入餐费"
                class="w-full!"
              />
            </el-form-item>
            <el-form-item label="护理费" prop="careFee">
              <el-input-number
                v-model="formData.careFee"
                :min="0"
                :precision="2"
                placeholder="请输入护理费"
                class="w-full!"
              />
            </el-form-item>
            <el-form-item label="其他费用" prop="otherFee">
              <el-input-number
                v-model="formData.otherFee"
                :min="0"
                :precision="2"
                placeholder="请输入其他费用"
                class="w-full!"
              />
            </el-form-item>
            <el-form-item label="优惠金额" prop="discount">
              <el-input-number
                v-model="formData.discount"
                :min="0"
                :precision="2"
                placeholder="请输入优惠金额"
                class="w-full!"
              />
            </el-form-item>
            <el-form-item label="总金额" prop="totalAmount">
              <div class="total-amount-display">
                <span class="amount-label">¥</span>
                <span class="amount-value">{{ calculatedTotal.toLocaleString() }}</span>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">其他信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="备注说明" prop="remarks">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息..."
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '创建结算单' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElButton,
  ElDatePicker,
} from 'element-plus'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  statementData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  customerName: '',
  customerPhone: '',
  customerIdCard: '',
  roomNumber: '',
  packageType: '',
  checkInDate: '',
  checkOutDate: '',
  roomFee: 0,
  mealFee: 0,
  careFee: 0,
  otherFee: 0,
  discount: 0,
  remarks: '',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新建结算单' : '编辑结算单'
})

const calculatedTotal = computed(() => {
  const total =
    formData.roomFee + formData.mealFee + formData.careFee + formData.otherFee - formData.discount
  return Math.max(0, total)
})

// 表单验证规则
const rules = {
  customerName: [{ required: true, message: '请输入客户姓名', trigger: 'blur' }],
  customerPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  roomNumber: [{ required: true, message: '请选择房间', trigger: 'change' }],
  packageType: [{ required: true, message: '请选择套餐类型', trigger: 'change' }],
  checkInDate: [{ required: true, message: '请选择入住日期', trigger: 'change' }],
  checkOutDate: [{ required: true, message: '请选择退房日期', trigger: 'change' }],
  roomFee: [{ required: true, message: '请输入房费', trigger: 'blur' }],
}

// 监听结算单数据变化
watch(
  () => props.statementData,
  (newData) => {
    if (newData) {
      // 编辑模式，填充数据
      Object.assign(formData, {
        ...newData,
        roomFee: newData.roomFee || 0,
        mealFee: newData.mealFee || 0,
        careFee: newData.careFee || 0,
        otherFee: newData.otherFee || 0,
        discount: newData.discount || 0,
      })
    }
  },
  { immediate: true },
)

// 方法
const handleOpen = () => {
  if (props.mode === 'add') {
    resetForm()
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    customerName: '',
    customerPhone: '',
    customerIdCard: '',
    roomNumber: '',
    packageType: '',
    checkInDate: '',
    checkOutDate: '',
    roomFee: 0,
    mealFee: 0,
    careFee: 0,
    otherFee: 0,
    discount: 0,
    remarks: '',
  })
  formRef.value?.clearValidate()
}

const handleSave = async () => {
  try {
    await formRef.value.validate()

    saving.value = true

    const saveData = {
      ...formData,
      totalAmount: calculatedTotal.value,
    }

    // 模拟保存延迟
    setTimeout(() => {
      emit('save', saveData)
      saving.value = false
    }, 500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.total-amount-display {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  height: 32px;
  width: 100%;
  text-align: center;
}

.amount-label {
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
  margin-right: 4px;
}

.amount-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #dc2626;
  font-family: 'Courier New', monospace;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-input-number:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-input-number.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
