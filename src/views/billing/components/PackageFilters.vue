<template>
  <div class="package-filter-container bg-white border border-gray-200 rounded-lg p-4 sm:p-6 mb-6">
    <!-- 过滤器主体 -->
    <div class="flex gap-3">
      <!-- 套餐名称搜索 -->
      <div class="filter-item flex-1">
        <label class="filter-label block text-sm font-medium text-gray-700 mb-2">套餐名称</label>
        <el-input
          v-model="localFilters.name"
          placeholder="请输入套餐名称"
          clearable
          @clear="handleFilterChange"
          @keyup.enter="handleSearch"
          @input="handleFilterChange"
          class="w-full"
        >
          <template #prefix>
            <el-icon class="text-gray-400">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <!-- 套餐类型 -->
      <div class="filter-item flex-1">
        <label class="filter-label block text-sm font-medium text-gray-700 mb-2">套餐类型</label>
        <el-select
          v-model="localFilters.packageType"
          placeholder="请选择套餐类型"
          clearable
          @change="handleFilterChange"
          class="w-full"
        >
          <el-option label="21天" value="21天" />
          <el-option label="28天" value="28天" />
          <el-option label="42天" value="42天" />
          <el-option label="56天" value="56天" />
        </el-select>
      </div>

      <!-- 状态 -->
      <div class="filter-item flex-1">
        <label class="filter-label block text-sm font-medium text-gray-700 mb-2">状态</label>
        <el-select
          v-model="localFilters.status"
          placeholder="请选择状态"
          clearable
          @change="handleFilterChange"
          class="w-full"
        >
          <el-option label="启用" value="active" />
          <el-option label="停用" value="inactive" />
        </el-select>
      </div>

      <!-- 价格区间 -->
      <div class="filter-item flex-1">
        <label class="filter-label block text-sm font-medium text-gray-700 mb-2">价格区间</label>
        <el-select
          v-model="localFilters.priceRange"
          placeholder="请选择价格区间"
          clearable
          @change="handleFilterChange"
          class="w-full"
        >
          <el-option label="2万以下" value="0-20000" />
          <el-option label="2-4万" value="20000-40000" />
          <el-option label="4-6万" value="40000-60000" />
          <el-option label="6-8万" value="60000-80000" />
          <el-option label="8万以上" value="80000-999999" />
        </el-select>
      </div>

      <!-- 搜索按钮 -->
      <div class="filter-item flex items-end w-20 flex-shrink-0">
        <el-button
          @click="handleSearch"
          type="primary"
          class="w-full bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          <el-icon class="mr-2">
            <Search />
          </el-icon>
          搜索
        </el-button>
      </div>
    </div>

    <!-- 活跃过滤器标签和清空按钮 -->
    <div v-if="hasActiveFilters" class="mt-4 flex">
      <div class="flex-1 flex items-center mb-2">
        <span class="text-sm text-gray-600">当前筛选：</span>
        <div class="flex flex-wrap gap-2">
          <el-tag
            v-for="tag in activeFilterTags"
            :key="tag.key"
            closable
            @close="clearFilter(tag.key)"
            class="filter-tag"
            type="info"
          >
            {{ tag.label }}：{{ tag.value }}
          </el-tag>
        </div>
      </div>
      <el-button
        @click="clearAllFilters"
        class="text-gray-600 border-gray-300 hover:text-pink-500 hover:border-pink-300"
        type="default"
        size="small"
      >
        清空筛选
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { reactive, computed } from 'vue'
import { ElInput, ElSelect, ElOption, ElButton, ElIcon, ElTag } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

const props = defineProps({
  filters: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['update:filters', 'search'])

// 本地筛选条件
const localFilters = reactive({ ...props.filters })

// 标签映射
const filterLabels = {
  name: '套餐名称',
  packageType: '套餐类型',
  status: '状态',
  priceRange: '价格区间',
}

// 状态标签映射
const statusMap = {
  active: '启用',
  inactive: '停用',
}

// 价格区间标签映射
const priceRangeMap = {
  '0-20000': '2万以下',
  '20000-40000': '2-4万',
  '40000-60000': '4-6万',
  '60000-80000': '6-8万',
  '80000-999999': '8万以上',
}

// 检查是否有活跃的过滤器
const hasActiveFilters = computed(() => {
  return Object.values(localFilters).some(
    (value) => value !== null && value !== undefined && value !== '' && String(value).trim() !== '',
  )
})

// 活跃过滤器标签
const activeFilterTags = computed(() => {
  return Object.entries(localFilters)
    .filter(
      ([key, value]) =>
        filterLabels[key] &&
        value !== null &&
        value !== undefined &&
        value !== '' &&
        String(value).trim() !== '',
    )
    .map(([key, value]) => {
      let displayValue = value
      if (key === 'status') {
        displayValue = statusMap[value] || value
      } else if (key === 'priceRange') {
        displayValue = priceRangeMap[value] || value
      }
      return {
        key,
        label: filterLabels[key],
        value: displayValue,
      }
    })
})

// 筛选条件变化
const handleFilterChange = () => {
  emit('update:filters', { ...localFilters })
}

// 搜索
const handleSearch = () => {
  emit('update:filters', { ...localFilters })
  emit('search')
}

// 清除单个筛选条件
const clearFilter = (key) => {
  localFilters[key] = ''
  handleFilterChange()
  emit('search')
}

// 清空所有筛选条件
const clearAllFilters = () => {
  Object.keys(localFilters).forEach((key) => {
    localFilters[key] = ''
  })
  handleFilterChange()
  emit('search')
}
</script>

<style scoped>
.package-filter-container {
  animation: slideIn 0.3s ease-out;
}

.filter-item {
  min-width: 0;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.filter-tag {
  margin-left: 8px;
  margin-right: 4px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #ec4899;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}
</style>
