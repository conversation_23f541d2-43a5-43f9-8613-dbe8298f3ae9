<template>
  <div class="mb-8">
    <div class="grid grid-cols-4 gap-6">
      <el-card
      v-for="(stat, index) in financialStats"
      :key="index"
      class="financial-stat-card relative"
      :body-style="{ padding: '24px' }"
      shadow="hover"
      >
      <!-- 即将开放蒙层 (仅对同比增长卡片) -->
      <div v-if="stat.title === '同比增长'" class="absolute inset-0 bg-black/50 z-20 flex items-center justify-center rounded-lg">
          <span class="text-white text-lg font-bold">即将开放</span>
        </div>

        <div class="relative overflow-hidden">

          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-20 h-20 opacity-10">
            <div
              :class="`w-full h-full rounded-full transform translate-x-6 -translate-y-6`"
              :style="{ backgroundColor: stat.bgColor }"
            ></div>
          </div>

          <div class="relative z-10">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <!-- 图标 -->
                <div
                  class="w-12 h-12 rounded-lg flex items-center justify-center mb-4"
                  :style="{
                    background: `linear-gradient(135deg, ${stat.gradientFrom}, ${stat.gradientTo})`,
                  }"
                >
                  <component :is="stat.icon" class="w-6 h-6 text-white" />
                </div>

                <!-- 标题 -->
                <div class="text-sm font-medium text-gray-600 mb-2">
                  {{ stat.title }}
                </div>

                <!-- 主要数值 -->
                <div class="flex items-baseline gap-2 mb-1">
                  <span class="text-2xl font-bold text-gray-800">
                    {{ stat.mainValue }}
                  </span>
                  <span v-if="stat.unit" class="text-sm font-medium text-gray-500">
                    {{ stat.unit }}
                  </span>
                </div>

                <!-- 变化趋势 -->
                <div v-if="stat.change" class="flex items-center gap-1">
                  <component
                    :is="stat.changeType === 'up' ? TrendCharts : ArrowDown"
                    :class="[
                      'w-4 h-4',
                      stat.changeType === 'up' ? 'text-green-500' : 'text-red-500',
                    ]"
                  />
                  <span
                    :class="[
                      'text-xs font-medium',
                      stat.changeType === 'up' ? 'text-green-600' : 'text-red-600',
                    ]"
                  >
                    {{ stat.change }}
                  </span>
                  <span class="text-xs text-gray-500">较上月</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, markRaw, computed, watch } from 'vue'
import { Money, User, House, TrendCharts, ArrowDown } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
})

const financialStats = computed(() => {
  if (!props.data || Object.keys(props.data).length === 0) return []
  
  return [
    {
      title: '本月收入',
      mainValue: formatCurrency(props.data.monthly_income?.current_month || 0),
      unit: '',
      change: formatPercentage(props.data.monthly_income?.change_rate || 0),
      changeType: (props.data.monthly_income?.change_rate || 0) >= 0 ? 'up' : 'down',
      icon: markRaw(Money),
      gradientFrom: '#E77FA1',
      gradientTo: '#D56C8E',
      bgColor: '#E77FA1',
    },
    {
      title: '本月入住人数',
      mainValue: (props.data.monthly_admissions?.current_month || 0).toString(),
      unit: '人',
      change: formatPercentage(props.data.monthly_admissions?.change_rate || 0),
      changeType: (props.data.monthly_admissions?.change_rate || 0) >= 0 ? 'up' : 'down',
      icon: markRaw(User),
      gradientFrom: '#43B884',
      gradientTo: '#369970',
      bgColor: '#43B884',
    },
    {
      title: '房间使用率',
      mainValue: (props.data.room_usage_rate?.current_month || 0).toFixed(1),
      unit: '%',
      change: formatPercentage(props.data.room_usage_rate?.change_rate || 0),
      changeType: (props.data.room_usage_rate?.change_rate || 0) >= 0 ? 'up' : 'down',
      icon: markRaw(House),
      gradientFrom: '#6A7EE7',
      gradientTo: '#5A6FD7',
      bgColor: '#6A7EE7',
    },
    {
      title: '同比增长',
      mainValue: '0',
      unit: '%',
      change: '+0%',
      changeType: 'up',
      icon: markRaw(TrendCharts),
      gradientFrom: '#FFB357',
      gradientTo: '#FF9F40',
      bgColor: '#FFB357',
    },
  ]
})

const formatCurrency = (amount) => {
  return '¥' + amount.toLocaleString()
}

const formatPercentage = (rate) => {
  if (rate === null || rate === undefined || isNaN(rate)) return '+0.0%'
  const sign = rate >= 0 ? '+' : ''
  return `${sign}${rate.toFixed(1)}%`
}
</script>

<style scoped>
.financial-stat-card {
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.financial-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(231, 127, 161, 0.3);
}
</style>
