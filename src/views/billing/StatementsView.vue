<template>
  <div class="statements-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">财务管理 - 结算单管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理客户结算单，处理费用结算</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <!-- 新建结算单按钮暂时隐藏，后续开发 -->
          <!-- <el-button
            type="primary"
            @click="handleCreateStatement"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新建结算单
          </el-button> -->
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 结算单列表组件 -->
    <StatementTable
      :statements="statements"
      :loading="loading"
      :total="total"
      @view="handleViewStatement"
      @edit="handleEditStatement"
      @print="handlePrintStatement"
      @cancel="handleCancelStatement"
      @pagination-change="handlePaginationChange"
    />

    <!-- 结算单表单弹窗 -->
    <StatementFormDialog
      v-model:visible="formVisible"
      :statement-data="currentStatement"
      :mode="formMode"
      @save="handleSaveStatement"
    />

    <!-- 结算单详情弹窗 -->
    <BillDetailDialog
      v-model:visible="detailVisible"
      :bill-id="currentBillId"
      @edit="handleEditFromDetail"
    />

    <!-- 结算单编辑弹窗 -->
    <BillEditDialog
      v-model:visible="editVisible"
      :bill-id="currentBillId"
      @save="handleSaveBillEdit"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElMessage, ElIcon, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import StatementTable from './components/StatementTable.vue'
import StatementFormDialog from './components/StatementFormDialog.vue'
import BillDetailDialog from './components/BillDetailDialog.vue'
import BillEditDialog from './components/BillEditDialog.vue'
import { get } from '@/utils/request'

// 响应式数据
const loading = ref(false)
const formVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentStatement = ref(null)

// 详情和编辑弹窗相关
const detailVisible = ref(false)
const editVisible = ref(false)
const currentBillId = ref('')

// 筛选条件
const filters = reactive({
  sk: '',
  status: '',
  page: 1,
  page_size: 20,
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '搜索',
    placeholder: '输入客户姓名或房间号',
  },
  {
    key: 'status',
    type: 'select',
    label: '支付状态',
    placeholder: '选择支付状态',
    options: [
      { label: '已全款支付', value: 'FULL_PAID' },
      { label: '已部分支付', value: 'PARTIAL_PAID' },
      { label: '未支付', value: 'UNPAID' },
      { label: '已退款', value: 'REFUNDED' },
    ],
  },
]

// 结算单列表数据
const statements = ref([])
const total = ref(0)

// 方法
const handleCreateStatement = () => {
  currentStatement.value = null
  formMode.value = 'add'
  formVisible.value = true
}

const handleViewStatement = (statement) => {
  currentBillId.value = statement.statementNumber
  detailVisible.value = true
}

const handleEditStatement = (statement) => {
  currentBillId.value = statement.statementNumber
  editVisible.value = true
}

const handleEditFromDetail = () => {
  detailVisible.value = false
  editVisible.value = true
}

const handleSaveBillEdit = () => {
  editVisible.value = false
  loadData() // 重新加载数据
  ElMessage.success('结算单编辑成功')
}

const handlePrintStatement = (statement) => {
  ElMessage.success(`打印结算单：${statement.statementNumber}`)
  // 这里可以实现打印功能
}

const handleCancelStatement = async (statement) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消结算单 ${statement.statementNumber} 吗？此操作不可恢复。`,
      '确认取消',
      {
        confirmButtonText: '确认取消',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    )

    // 模拟API调用
    loading.value = true
    setTimeout(() => {
      const index = statements.value.findIndex((s) => s.id === statement.id)
      if (index !== -1) {
        statements.value[index].status = 'cancelled'
        statements.value[index].statusText = '已取消'
      }
      loading.value = false
      ElMessage.success('结算单已取消')
    }, 500)
  } catch {
    // 用户取消操作
  }
}

// 获取结算单列表
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: filters.page,
      page_size: filters.page_size,
    }
    
    if (filters.sk && filters.sk.trim()) {
      params.sk = filters.sk.trim()
    }
    
    if (filters.status) {
      params.status = filters.status
    }
    
    const data = await get('/organizational-management/bills/list/', params)
    
    statements.value = data.list.map(item => ({
      id: item.bid,
      statementNumber: item.bid,
      customerName: item.maternity_name,
      roomNumber: item.room_number,
      packageType: item.package_type,
      checkInDate: item.check_in_date,
      checkOutDate: item.check_out_date,
      totalAmount: parseFloat(item.payable_amount),
      status: item.payment_status,
      statusText: item.payment_status_display,
      createDate: item.created_at.split(' ')[0],
      remarks: item.remark || '',
      has_renew: item.has_renew || false,
    }))
    
    total.value = data.total_count
    filters.page = data.page
    filters.page_size = data.page_size
  } catch (error) {
    ElMessage.error('获取结算单列表失败')
    console.error('Failed to fetch bills list:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  filters.page = 1
  loadData()
}

const handlePaginationChange = ({ page, size }) => {
  filters.page = page
  filters.page_size = size
  loadData()
}

const handleSaveStatement = (statementData) => {
  if (formMode.value === 'add') {
    // 添加新结算单
    statements.value.push({
      ...statementData,
      id: Date.now().toString(),
      statementNumber: `BILL${Date.now()}`,
      status: 'pending',
      statusText: '待结算',
      createDate: new Date().toISOString().split('T')[0],
    })
    ElMessage.success('结算单创建成功')
  } else {
    // 更新结算单
    const index = statements.value.findIndex((statement) => statement.id === statementData.id)
    if (index !== -1) {
      statements.value[index] = statementData
    }
    ElMessage.success('结算单更新成功')
  }
  formVisible.value = false
}

onMounted(() => {
  // 初始化数据加载
  loadData()
})
</script>

<style scoped>
.statements-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
