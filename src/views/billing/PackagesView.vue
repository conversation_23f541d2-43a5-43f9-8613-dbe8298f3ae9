<template>
  <div class="packages-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">计费管理 - 套餐价格管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理月子中心套餐价格，设置优惠政策</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            v-permission="'ogm.charge.edit'"
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增套餐
          </el-button>
        </div>
      </div>
    </div>

    <!-- 过滤器 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" />

    <!-- 数据表格 -->
    <PackageTable
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      @edit="handleEdit"
      @delete="handleDelete"
      @toggle-status="handleToggleStatus"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!-- 创建/编辑套餐模态框 -->
    <PackageFormDialog
      v-model="showCreateModal"
      :package-data="selectedPackage"
      @success="handleFormSuccess"
      @close="selectedPackage = null"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElButton, ElIcon, ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import PackageStats from './components/PackageStats.vue'
import PackageFilters from './components/PackageFilters.vue'
import PackageTable from './components/PackageTable.vue'
import PackageFormDialog from './components/PackageFormDialog.vue'
import { get, del } from '@/utils/request'

// 响应式数据
const loading = ref(false)
const showCreateModal = ref(false)
const selectedPackage = ref(null)

// 统计数据
const stats = ref({
  totalPackages: 4,
  activePackages: 3,
  expiringSoon: 1,
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    label: '关键字搜索',
    type: 'input',
    placeholder: '输入名称、描述等关键字',
    width: 'min-w-48',
  },
  {
    key: 'status',
    label: '状态',
    type: 'select',
    placeholder: '全部状态',
    options: [
      { label: '启用', value: 'ACTIVE' },
      { label: '禁用', value: 'DISABLED' },
    ],
  },
]

// 筛选条件
const filters = reactive({
  sk: '',
  status: '',
  packageType: '',
  name: '',
  priceRange: [],
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
})

// 表格数据
const tableData = ref([])

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: pagination.current,
      page_size: pagination.size,
    }

    // 添加过滤参数
    if (filters.sk && filters.sk.trim()) {
      params.sk = filters.sk.trim()
    }
    if (filters.status) {
      params.status = filters.status
    }
    if (filters.packageType) {
      params.packageType = filters.packageType
    }
    if (filters.name) {
      params.name = filters.name
    }
    if (filters.priceRange && filters.priceRange.length === 2) {
      params.minPrice = filters.priceRange[0]
      params.maxPrice = filters.priceRange[1]
    }

    console.log('loadData called with params:', params)
    const data = await get('/organizational-management/packages/list/', params)
    tableData.value = data.list
    pagination.total = data.total_count
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  console.log('handleSearch called, current filters:', filters)
  pagination.current = 1
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

const handleCreate = () => {
  selectedPackage.value = null
  showCreateModal.value = true
}

const handleEdit = (packageData) => {
  selectedPackage.value = packageData
  showCreateModal.value = true
}

const handleDelete = async (packageData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除套餐"${packageData.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        type: 'warning',
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
      },
    )

    // 模拟API调用
    await del(`/organizational-management/packages/delete/${packageData.id}/`)

    ElMessage.success('套餐删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleToggleStatus = async (packageData) => {
  const action = packageData.status === 'active' ? '停用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}套餐"${packageData.name}"吗？`, `确认${action}`, {
      type: 'warning',
    })

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))

    ElMessage.success(`套餐${action}成功`)
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${action}失败`)
    }
  }
}

const handleFormSuccess = () => {
  loadData()
}

// 监听过滤器变化
watch(
  () => filters,
  (newFilters) => {
    console.log('Filters changed:', newFilters)
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.packages-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
