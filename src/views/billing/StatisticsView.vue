<template>
  <div class="">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold text-gray-800 mb-2">财务统计</h1>
      <p class="text-gray-600">实时监控月子中心财务收支状况</p>
    </div>

    <!-- 财务统计卡片 -->
    <FinancialStatsCards :data="statsData" />

    <!-- 图表区域 -->
    <FinancialChartsSection :data="statsData" />

    <!-- 最近交易记录 -->
    <RecentTransactions :data="statsData" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import request from '@/utils/request'
import FinancialStatsCards from './components/FinancialStatsCards.vue'
import FinancialChartsSection from './components/FinancialChartsSection.vue'
import RecentTransactions from './components/RecentTransactions.vue'

const statsData = ref({})

const fetchStatsData = async () => {
  try {
    const response = await request.get('/organizational-management/charge/overview/')
    statsData.value = response
  } catch (error) {
    ElMessage.error('获取财务数据失败')
  }
}

onMounted(() => {
  fetchStatsData()
})
</script>

<style scoped></style>
