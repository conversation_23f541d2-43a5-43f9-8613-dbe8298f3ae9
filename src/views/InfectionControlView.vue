<template>
  <div class="infection-control-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">院感与环境管理</h1>
            <p class="text-sm text-gray-600 mt-1">院感组织制度、感染监测、清洁消毒记录管理</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签页导航 -->
    <div class="mb-6">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="" type="border-card">
        <el-tab-pane label="院感组织与制度" name="organization">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Document />
              </el-icon>
              院感组织与制度
            </div>
          </template>
          <InfectionDocumentTab />
        </el-tab-pane>

        <el-tab-pane label="感染监测与报告" name="monitor">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Monitor />
              </el-icon>
              感染监测与报告
            </div>
          </template>
          <InfectionDetectionTab />
        </el-tab-pane>

        <!-- <el-tab-pane label="健康档案与防控培训" name="health">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <User />
              </el-icon>
              健康档案与防控培训
            </div>
          </template>
          <HealthTab />
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElTabs, ElTabPane, ElIcon } from 'element-plus'
import { Document, Monitor, User } from '@element-plus/icons-vue'
import InfectionDocumentTab from '@/components/infection-control/InfectionDocumentTab.vue'
import InfectionDetectionTab from '@/components/infection-control/InfectionDetectionTab.vue'
import HealthTab from '@/components/infection-control/HealthTab.vue'

// 响应式数据
const activeTab = ref('organization')

// 方法
const handleTabChange = (tabName) => {
  console.log('切换到标签页:', tabName)
}
</script>

<style scoped>
:deep(.itabs) {
  .el-tabs__header {
    background-color: transparent;
    .el-tabs__item {
      border: none;
    }
  }
}
.infection-control-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

:deep(.infection-tabs .el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

:deep(.infection-tabs .el-tabs__content) {
  padding: 0;
}

:deep(.infection-tabs .el-tabs__item) {
  padding: 0 20px;
  height: 50px;
  line-height: 50px;
  font-weight: 500;
}

:deep(.infection-tabs .el-tabs__item:hover) {
  color: #ec4899;
}
</style>
