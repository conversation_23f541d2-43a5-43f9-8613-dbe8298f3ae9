<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="equipment-form-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
        class="equipment-form"
      >
        <!-- 设备照片上传区 -->
        <div class="form-section mb-6">
          <h4 class="section-title">设备照片</h4>
          <div class="photo-upload-container">
            <div class="photo-preview" @click="triggerFileUpload" v-loading="uploading">
              <el-image
                v-if="form.image_url"
                :src="form.image_url"
                :alt="form.name"
                fit="cover"
                class="preview-image"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <p>点击重新上传</p>
                  </div>
                </template>
              </el-image>
              <div v-else class="upload-placeholder">
                <el-icon class="upload-icon"><Plus /></el-icon>
                <p>{{ uploading ? '上传中...' : '点击上传设备照片' }}</p>
              </div>
            </div>
            <input
              ref="fileInputRef"
              type="file"
              accept="image/*"
              @change="handleFileUpload"
              style="display: none"
              :disabled="uploading"
            />
          </div>
          <p class="upload-tip">支持 JPG、PNG 格式，文件大小不超过 5MB</p>
        </div>

        <!-- 基本信息区 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <el-form-item label="设备名称" prop="name" required>
            <el-input v-model="form.name" placeholder="请输入设备名称" class="w-full" />
          </el-form-item>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="设备类型" prop="type" required>
              <el-select v-model="form.type" placeholder="请选择设备类型" class="w-full">
                <el-option
                  v-for="option in EQUIPMENT_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="设备状态" prop="status" required>
              <el-select
                v-model="form.status"
                placeholder="请选择设备状态"
                class="w-full"
                @change="handleStatusChange"
              >
                <el-option
                  v-for="option in EQUIPMENT_STATUS_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <el-form-item label="设备功能" prop="function" required>
            <el-input
              v-model="form.function"
              type="textarea"
              :rows="3"
              placeholder="请描述设备的主要功能和作用"
              class="w-full"
            />
          </el-form-item>
        </div>

        <!-- 使用信息区 -->
        <div class="form-section mb-6">
          <h4 class="section-title">使用信息</h4>
          <el-form-item label="使用方式" prop="usage_method" required>
            <el-input
              v-model="form.usage_method"
              type="textarea"
              :rows="3"
              placeholder="请详细描述设备的使用方法和操作步骤"
              class="w-full"
            />
          </el-form-item>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="使用时长" prop="usage_duration" required>
              <el-input
                v-model="form.usage_duration"
                placeholder="例如: 30分钟/次, 持续使用等"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="消毒方式" prop="disinfection_method" required>
              <el-input
                v-model="form.disinfection_method"
                type="textarea"
                :rows="2"
                placeholder="请详细描述消毒方式和步骤"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>


      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'create' ? '添加设备' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Picture } from '@element-plus/icons-vue'
import { post, put } from '@/utils/request.js'
import { EQUIPMENT_TYPE_OPTIONS, EQUIPMENT_STATUS_OPTIONS } from '@/utils/constants.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  mode: {
    type: String,
    default: 'create', // create, edit
  },
})

const emit = defineEmits(['update:modelValue', 'submit'])

// 响应式数据
const formRef = ref(null)
const fileInputRef = ref(null)
const submitting = ref(false)
const uploading = ref(false)

// 对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 对话框标题
const dialogTitle = computed(() => {
  return props.mode === 'create' ? '添加新设备' : '编辑设备信息'
})

// 表单数据
const form = reactive({
  name: '',
  image: '', // 存储图片的rid，用于提交
  image_url: '', // 存储图片的url，用于预览
  type: '',
  function: '',
  status: 'USING',
  status_display: '使用中',
  usage_method: '',
  usage_duration: '',
  disinfection_method: '',
  rid: '', // 编辑时使用
})

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  function: [{ required: true, message: '请输入设备功能', trigger: 'blur' }],
  usage_method: [{ required: true, message: '请输入使用方式', trigger: 'blur' }],
  usage_duration: [{ required: true, message: '请输入使用时长', trigger: 'blur' }],
  disinfection_method: [{ required: true, message: '请输入消毒方式', trigger: 'blur' }],
  status: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    image: '',
    image_url: '',
    type: '',
    function: '',
    status: 'USING',
    status_display: '使用中',
    usage_method: '',
    usage_duration: '',
    disinfection_method: '',
    rid: '',
  })
  formRef.value?.clearValidate()
}

// 状态选项映射
const statusOptions = EQUIPMENT_STATUS_OPTIONS.map(option => ({
  ...option,
  display: option.label
}))

// 监听状态变化，同步更新display字段
const handleStatusChange = (value) => {
  const option = statusOptions.find(opt => opt.value === value)
  form.status_display = option ? option.display : ''
}

// 监听外部数据变化
watch(
  () => props.formData,
  (newData) => {
    if (newData && Object.keys(newData).length > 0) {
      Object.assign(form, newData)
      // 处理图片数据：如果是编辑模式且有图片，将图片URL同时设置为预览URL
      if (newData.image && !form.image_url) {
        form.image_url = newData.image
      }
    } else {
      resetForm()
    }
  },
  { immediate: true, deep: true },
)

// 监听对话框打开
watch(dialogVisible, (visible) => {
  if (visible) {
    if (props.mode === 'create') {
      resetForm()
    }
  }
})

// 触发文件上传
const triggerFileUpload = () => {
  fileInputRef.value?.click()
}

// 处理文件上传
const handleFileUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }

  uploading.value = true

  try {
    // 创建FormData
    const formData = new FormData()
    formData.append('equipment_image', file)

    console.log('开始上传设备图片...')

    // 调用图片上传接口
    const response = await post('file/equipment/image/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })

    console.log('图片上传成功:', response)

    // 设置图片rid和url
    form.image = response.rid // 用于提交
    form.image_url = response.url // 用于预览
    ElMessage.success('图片上传成功')

  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error('图片上传失败，请重试')
  } finally {
    uploading.value = false
    // 清空文件输入框
    event.target.value = ''
  }
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate()

    submitting.value = true

    // 准备提交数据
    const submitData = {
      name: form.name,
      image: form.image,
      type: form.type,
      function: form.function,
      status: form.status,
      status_display: form.status_display,
      usage_method: form.usage_method,
      usage_duration: form.usage_duration,
      disinfection_method: form.disinfection_method,
    }

    console.log('提交数据:', submitData)

    let response
    if (props.mode === 'create') {
      // 创建设备
      response = await post('organizational-management/equipment/create/', submitData)
      console.log('创建设备成功:', response)
      ElMessage.success('设备添加成功')
    } else {
      // 更新设备
      response = await put(`organizational-management/equipment/update/${form.rid}/`, submitData)
      console.log('更新设备成功:', response)
      ElMessage.success('设备更新成功')
    }

    emit('submit', response)
    dialogVisible.value = false

  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error(props.mode === 'create' ? '添加设备失败' : '更新设备失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.equipment-form-dialog {
  --el-dialog-border-radius: 12px;
}

.equipment-form {
  padding: 0 8px;
}

.form-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease;
}

.form-section:hover {
  border-color: #f3e8ff;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  position: relative;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 20px;
  background-color: #ec4899;
  margin-right: 8px;
  border-radius: 2px;
}

.photo-upload-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.photo-preview {
  width: 200px;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  background-color: #f9fafb;
}

.photo-preview:hover {
  border-color: #ec4899;
  background-color: #fef7ff;
  transform: scale(1.02);
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #6b7280;
  text-align: center;
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 8px;
  color: #9ca3af;
}

.upload-placeholder p {
  font-size: 0.875rem;
  margin: 0;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #9ca3af;
  text-align: center;
}

.image-error p {
  font-size: 0.875rem;
  margin-top: 8px;
}

.upload-tip {
  text-align: center;
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 8px;
  margin-bottom: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input__wrapper),
:deep(.el-textarea__inner),
:deep(.el-select .el-input__wrapper),
:deep(.el-date-picker .el-input__wrapper),
:deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover),
:deep(.el-textarea__inner:hover),
:deep(.el-select:hover .el-input__wrapper),
:deep(.el-date-picker:hover .el-input__wrapper),
:deep(.el-input-number:hover .el-input__wrapper) {
  border-color: #f3e8ff;
}

:deep(.el-input__wrapper.is-focus),
:deep(.el-textarea__inner:focus),
:deep(.el-select .el-input__wrapper.is-focus),
:deep(.el-date-picker .el-input__wrapper.is-focus),
:deep(.el-input-number .el-input__wrapper.is-focus) {
  border-color: #ec4899;
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 20px 24px 16px;
}

:deep(.el-dialog__title) {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .photo-preview {
    width: 150px;
    height: 150px;
  }

  .form-section {
    padding: 16px;
  }

  .grid {
    grid-template-columns: 1fr;
  }
}
</style>
