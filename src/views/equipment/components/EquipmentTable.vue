<template>
  <div class="equipment-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Cpu />
          </el-icon>
          设备列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ totalCount }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="image_url" label="照片" width="100" fixed="left">
        <template #default="{ row }">
          <div class="flex justify-center">
            <el-image
              :src="getImageUrl(row.image_url)"
              :alt="row.name"
              class="device-image"
              fit="cover"
              @error="handleImageError"
            >
              <template #error>
                <div class="image-placeholder">
                  <div class="equipment-icon">{{ getEquipmentIcon(row.type) }}</div>
                  <div class="equipment-type-text">{{ getEquipmentTypeText(row.type) }}</div>
                </div>
              </template>
            </el-image>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="rid" label="设备编号" min-width="120" fixed="left">
        <template #default="{ row }">
          <span class="font-mono text-pink-600 font-medium">{{ row.rid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="name" label="设备名称" min-width="150">
        <template #default="{ row }">
          <span class="font-medium text-gray-900">{{ row.name }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="type" label="设备类型" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getEquipmentTypeTagType(row.type)" size="small">
            {{ getEquipmentTypeText(row.type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="function" label="设备功能" min-width="150">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.function || '暂无' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="usage_method" label="使用方式" min-width="150">
        <template #default="{ row }">
          <span class="text-gray-600 text-sm">{{ row.usage_method || '暂无' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="usage_duration" label="使用时长" min-width="100">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.usage_duration || '暂无' }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="disinfection_method" label="消毒方式" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-600 text-sm">{{ row.disinfection_method || '暂无' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getEquipmentStatusTagType(row.status)" size="small">
            {{ getEquipmentStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" size="small" @click.stop="emit('view', row)">
              查看
            </el-button>
            <el-button type="warning" size="small" @click.stop="emit('edit', row)">
              编辑
            </el-button>
            <el-button
              v-if="showDelete"
              type="danger"
              size="small"
              @click.stop="emit('delete', row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Cpu, Picture } from '@element-plus/icons-vue'
import { get } from '@/utils/request.js'
import {
  getEquipmentTypeText,
  getEquipmentTypeTagType,
  getEquipmentStatusText,
  getEquipmentStatusTagType,
} from '@/utils/constants.js'

const emit = defineEmits(['edit', 'delete', 'view', 'row-click'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'organizational-management/equipment/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
  showDelete: {
    type: Boolean,
    default: true,
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = data.list
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 图片URL处理
const getImageUrl = (imageUrl) => {
  if (!imageUrl) return ''
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }
  return `${import.meta.env.VITE_API_BASE_URL}/${imageUrl}`
}

// 图片加载错误处理
const handleImageError = (event) => {
  console.log('图片加载失败:', event.target.src)
}

// 获取设备类型图标
const getEquipmentIcon = (type) => {
  const iconMap = {
    MEDICAL: '🏥',
    NURING: '🩺',
    CLEAN: '🧽',
    KITCHEN: '🍽️',
    OTHER: '⚙️',
  }
  return iconMap[type] || '⚙️'
}

// 行点击处理
const handleRowClick = (row) => {
  emit('row-click', row)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 监听filters变化，重新加载数据
watch(
  () => props.filters,
  () => {
    currentPage.value = 1
    loadData()
  },
  { deep: true },
)

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.equipment-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.equipment-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.device-image {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.device-image:hover {
  border-color: #ec4899;
  transform: scale(1.05);
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.3s ease;
}

.image-placeholder:hover {
  background: linear-gradient(135deg, #ec4899 0%, #f472b6 100%);
  color: white;
  transform: scale(1.05);
}

.equipment-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.equipment-type-text {
  font-size: 8px;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  transition: all 0.2s ease;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-buttons .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }
}
</style>
