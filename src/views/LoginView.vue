<template>
  <div
    class="min-h-screen bg-gradient-to-br from-pink-50 to-pink-100 flex items-center justify-center px-4"
  >
    <div class="max-w-md w-full">
      <!-- Logo and Title -->
      <div class="text-center mb-8">
        <div
          class="mx-auto h-16 w-16 rounded-full flex items-center justify-center mb-4"
          style="background-color: rgb(231, 127, 161)"
        >
          <House class="w-8 h-8 text-white" />
        </div>
        <h2 class="text-3xl font-bold text-gray-900">医院月子中心管理台</h2>
        <p class="text-gray-600 mt-2">月子中心管理系统</p>
      </div>

      <!-- Login Form -->
      <div class="bg-white rounded-lg shadow-xl p-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6 text-center">登录</h3>

        <!-- Login Type Tabs -->
        <div class="login-tabs mb-6">
          <div 
            class="tab-item" 
            :class="{ active: loginType === 'password' }"
            @click="loginType = 'password'"
          >
            密码登录
          </div>
          <div 
            class="tab-item" 
            :class="{ active: loginType === 'sms' }"
            @click="loginType = 'sms'"
          >
            验证码登录
          </div>
        </div>

        <!-- Password Login Form -->
        <el-form
          v-if="loginType === 'password'"
          ref="loginFormRef"
          :model="loginForm"
          :rules="passwordRules"
          @submit.prevent="handleLogin"
          size="large"
        >
          <el-form-item prop="cid">
            <el-select
              v-model="loginForm.cid"
              placeholder="选择月子中心"
              class="w-full"
              :loading="centerLoading"
              @focus="loadMaternityCenters"
            >
              <template #prefix>
                <House class="w-4 h-4 text-gray-400" />
              </template>
              <el-option
                v-for="center in maternityCenters"
                :key="center.cid"
                :label="center.name"
                :value="center.cid"
              />
            </el-select>
          </el-form-item>

          <el-form-item prop="phone">
            <el-input v-model="loginForm.phone" placeholder="手机号" class="w-full">
              <template #prefix>
                <User class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              show-password
              class="w-full"
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <Lock class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" class="w-full" :loading="loading" @click="handleLogin">
              登录
            </el-button>
          </el-form-item>
          <el-form-item>
            <div class="flex justify-center w-full">
              <el-link type="primary" underline="never">忘记密码？</el-link>
            </div>
          </el-form-item>
        </el-form>

        <!-- SMS Login Form -->
        <el-form
          v-if="loginType === 'sms'"
          ref="smsFormRef"
          :model="smsForm"
          :rules="smsRules"
          @submit.prevent="handleSmsLogin"
          size="large"
        >
          <el-form-item prop="cid">
            <el-select
              v-model="smsForm.cid"
              placeholder="选择月子中心"
              class="w-full"
              :loading="centerLoading"
              @focus="loadMaternityCenters"
            >
              <template #prefix>
                <House class="w-4 h-4 text-gray-400" />
              </template>
              <el-option
                v-for="center in maternityCenters"
                :key="center.cid"
                :label="center.name"
                :value="center.cid"
              />
            </el-select>
          </el-form-item>

          <el-form-item prop="phone">
            <el-input v-model="smsForm.phone" placeholder="手机号" class="w-full">
              <template #prefix>
                <User class="w-4 h-4 text-gray-400" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="code">
            <div class="verification-code-container">
              <el-input 
                v-model="smsForm.code" 
                placeholder="请输入6位验证码"
                maxlength="6"
                class="code-input-field"
                @input="formatCodeInput"
                @keyup.enter="handleSmsLogin"
              >
                <template #prefix>
                  <Lock class="w-4 h-4 text-gray-400" />
                </template>
              </el-input>
              <el-button 
                :disabled="!canSendCode || countdown > 0" 
                :loading="sendingCode"
                @click="sendVerificationCode"
                class="send-code-btn"
                type="primary"
                plain
              >
                {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button 
              type="primary" 
              class="w-full" 
              :loading="loading" 
              :disabled="!canSmsLogin"
              @click="handleSmsLogin"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="text-center mt-6 text-sm text-gray-600">
        © 2025 医院月子中心管理台. 版权所有.
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { User, Lock, House } from '@element-plus/icons-vue'
import { get, post } from '@/utils/request'
import '@/assets/ct4.js'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const centerLoading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
const loginType = ref('password')
const codeSent = ref(false)
const loginFormRef = ref(null)
const smsFormRef = ref(null)
const maternityCenters = ref([])
const captchaObj = ref(null)
let countdownTimer = null

const loginForm = reactive({
  cid: '',
  phone: '',
  password: '',
})

const smsForm = reactive({
  cid: '',
  phone: '',
  code: '',
})

const passwordRules = {
  cid: [
    { required: true, message: '请选择月子中心', trigger: 'change' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' },
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少需要6个字符', trigger: 'blur' },
  ],
}

const smsRules = {
  cid: [
    { required: true, message: '请选择月子中心', trigger: 'change' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' },
  ],
  code: [
    { required: true, message: '请输入6位验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' },
  ],
}

const formatCodeInput = (value) => {
  // 只允许数字输入，限制6位
  const numericValue = value.replace(/\D/g, '')
  smsForm.code = numericValue.slice(0, 6)
}

const canSendCode = computed(() => {
  return smsForm.cid && /^1[3-9]\d{9}$/.test(smsForm.phone)
})

const canSmsLogin = computed(() => {
  return smsForm.cid && 
         /^1[3-9]\d{9}$/.test(smsForm.phone) && 
         /^\d{6}$/.test(smsForm.code) &&
         codeSent.value
})

const loadMaternityCenters = async () => {
  if (maternityCenters.value.length > 0) return
  
  centerLoading.value = true
  try {
    const response = await get('/maternity-center/list/')
    if (response) {
      maternityCenters.value = response
    } else {
      ElMessage.error('获取月子中心列表失败')
    }
  } catch (error) {
    console.error('加载月子中心列表失败:', error)
    ElMessage.error('获取月子中心列表失败')
  } finally {
    centerLoading.value = false
  }
}

const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 初始化阿里云验证码
const initCaptcha = () => {
  // 请替换为您的实际 appId
  const appId = '5b96fef3516676f28fb14b1088c37bd4' // 请替换为您的实际 appId
  
  if (typeof window.initAlicom4 === 'function') {
    window.initAlicom4({
      captchaId: appId,
      product: 'bind'
    }, function(captcha) {
      captchaObj.value = captcha
      
      // 验证成功回调
      captcha.onSuccess(function() {
        console.log('验证成功')
        // 获取验证结果数据
        const validateData = captcha.getValidate()
        console.log('验证数据:', validateData)
        
        if (validateData) {
          // 验证成功后发送短信，传递验证数据
          sendSmsCode(validateData)
        } else {
          ElMessage.error('获取验证数据失败，请重试')
          sendingCode.value = false
        }
      })
      
      // 验证失败回调
      captcha.onError(function(error) {
        console.error('验证失败:', error)
        ElMessage.error('验证失败，请重试')
        sendingCode.value = false
      })
    })
  } else {
    ElMessage.error('验证码SDK加载失败')
  }
}

// 发送短信验证码
const sendSmsCode = async (validateData = null) => {
  try {
    const requestData = {
      phone: smsForm.phone,
      cid: smsForm.cid
    }
    
    if (validateData) {
      requestData.lot_number = validateData.lot_number
      requestData.captcha_output = validateData.captcha_output
      requestData.pass_token = validateData.pass_token
      requestData.gen_time = validateData.gen_time
    }
    
    console.log('发送短信请求数据:', requestData)
    
    const response = await post('/message/staff/send-code/', requestData)
    
    ElMessage.success('验证码发送成功，有效期为五分钟。')
    codeSent.value = true
    startCountdown()

  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error(`发送验证码失败：${error.msg}`)
  } finally {
    sendingCode.value = false
  }
}

// 发送验证码按钮点击事件
const sendVerificationCode = async () => {
  if (!canSendCode.value) {
    ElMessage.warning('请先选择月子中心并输入正确的手机号')
    return
  }

  sendingCode.value = true
  
  // 如果验证码对象存在，先显示验证码
  if (captchaObj.value) {
    captchaObj.value.showCaptcha()
  } else {
    // 如果验证码未初始化，直接发送短信（备用方案）
    await sendSmsCode()
  }
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const loginData = {
          phone: loginForm.phone,
          password: loginForm.password,
          user_type: 'staff',
          cid: loginForm.cid
        }
        const result = await authStore.login(loginData)

        if (result.success) {
          ElMessage.success('登录成功！')
          router.push('/dashboard')
        }
      } catch (error) {
        console.log('loginview error', error)
        ElMessage.error(`登录失败: ${error.message}`)
      } finally {
        loading.value = false
      }
    }
  })
}

const handleSmsLogin = async () => {
  if (!smsFormRef.value) return

  await smsFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        const loginData = {
          phone: smsForm.phone,
          code: smsForm.code,
          user_type: 'staff',
          cid: smsForm.cid
        }
        const result = await authStore.loginWithSms(loginData)

        if (result.success) {
          ElMessage.success('登录成功！')
          router.push('/dashboard')
        }
      } catch (error) {
        console.log('sms login error', error)
        ElMessage.error(`登录失败: ${error.message}`)
      } finally {
        loading.value = false
      }
    }
  })
}

onMounted(() => {
  loadMaternityCenters()
  // 初始化验证码
  setTimeout(() => {
    initCaptcha()
  }, 1000) // 等待SDK加载完成
})

// 清理定时器
const cleanup = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 组件销毁时清理定时器
onBeforeUnmount(() => {
  cleanup()
})

// 如果已经登录，直接跳转到dashboard
if (authStore.isLoggedIn) {
  router.push('/dashboard')
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}

code {
  font-size: 12px;
}

/* 登录标签样式 */
.login-tabs {
  display: flex;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 24px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 8px 0;
  cursor: pointer;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
  color: #606266;
  font-size: 14px;
}

.tab-item:hover {
  color: rgb(231, 127, 161);
}

.tab-item.active {
  background: white;
  color: rgb(231, 127, 161);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 验证码输入框样式 */
.verification-code-container {
  display: flex;
  gap: 12px;
  align-items: center;
  width: 100%;
}

.code-input-field {
  flex: 1;
  min-width: 0;
}

:deep(.code-input-field .el-input__inner) {
  text-align: left;
  letter-spacing: 4px;
  font-size: 14px;
  font-weight: normal;
}

.send-code-btn {
  white-space: nowrap;
  width: 120px;
  flex-shrink: 0;
}

/* 去除input自动填充的样式 */
:deep(.el-input__inner:-webkit-autofill) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
  background-image: none !important;
}

:deep(.el-input__inner:-webkit-autofill:hover) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
}

:deep(.el-input__inner:-webkit-autofill:focus) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
}

:deep(.el-input__inner:-webkit-autofill:active) {
  -webkit-box-shadow: 0 0 0 1000px white inset !important;
  -webkit-text-fill-color: #606266 !important;
  background-color: white !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .verification-code-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .send-code-btn {
    width: 100%;
  }
}
</style>
