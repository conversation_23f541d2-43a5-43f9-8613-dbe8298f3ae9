<template>
  <div class="report-export-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">报表与导出中心</h1>
            <p class="text-sm text-gray-600 mt-1">生成各类报表和数据导出</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="success"
            @click="handleExportCSV"
            class="bg-green-500 hover:bg-green-600 border-green-500"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            导出CSV
          </el-button>
          <el-button
            type="info"
            @click="handleExportPDF"
            class="bg-blue-500 hover:bg-blue-600 border-blue-500"
          >
            <el-icon class="mr-2">
              <Document />
            </el-icon>
            导出PDF
          </el-button>
          <el-button
            type="warning"
            @click="handlePrint"
            class="bg-orange-500 hover:bg-orange-600 border-orange-500"
          >
            <el-icon class="mr-2">
              <Printer />
            </el-icon>
            打印报表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex gap-6">
      <!-- 左侧报表分类 -->
      <ReportCategories
        :categories="reportCategories"
        :active-report="activeReport"
        @select-report="handleSelectReport"
        class="w-64 flex-shrink-0"
      />

      <!-- 右侧报表内容区域 -->
      <div class="flex-1">
        <!-- 筛选搜索组件 -->
        <FilterPanel
          :fields="currentFilterFields"
          :filters="filters"
          @search="handleGenerateReport"
          class="mb-6"
        />

        <!-- 报表内容显示区域 -->
        <ReportContentArea
          :report-data="reportData"
          :report-config="currentReportConfig"
          :loading="loading"
          @export="handleExport"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElButton, ElMessage, ElIcon } from 'element-plus'
import { Download, Document, Printer } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import ReportCategories from './report-export/ReportCategories.vue'
import ReportContentArea from './report-export/ReportContentArea.vue'

// 响应式数据
const loading = ref(false)
const activeReport = ref('room_occupancy_rate')
const reportData = ref(null)

// 筛选条件
const filters = reactive({
  dateStart: '',
  dateEnd: '',
  roomType: '',
  clientName: '',
})

// 报表分类配置
const reportCategories = ref([
  {
    id: 'room_management',
    name: '房务管理',
    icon: 'Home',
    reports: [
      { id: 'room_occupancy_rate', name: '入住率统计报表', icon: 'BarChart3' },
      { id: 'room_turnover_report', name: '房间周转率报表', icon: 'RefreshCw' },
      { id: 'room_revenue_report', name: '房型收入分析', icon: 'TrendingUp' },
    ],
  },
  {
    id: 'maternal_baby',
    name: '母婴记录',
    icon: 'Baby',
    reports: [
      { id: 'newborn_feeding_summary', name: '新生儿喂养汇总', icon: 'Milk' },
      { id: 'maternal_recovery_progress', name: '产妇康复进展统计', icon: 'Heart' },
    ],
  },
  {
    id: 'staff_scheduling',
    name: '人员与排班',
    icon: 'Users',
    reports: [{ id: 'staff_workload_report', name: '员工工时与负荷报表', icon: 'Clock' }],
  },
  {
    id: 'customer_feedback',
    name: '客户反馈',
    icon: 'MessageSquare',
    reports: [{ id: 'satisfaction_summary', name: '客户满意度汇总报告', icon: 'Star' }],
  },
  {
    id: 'financial',
    name: '财务相关',
    icon: 'DollarSign',
    reports: [{ id: 'monthly_revenue_summary', name: '月度营收汇总', icon: 'PieChart' }],
  },
])

// 报表配置映射
const reportConfigs = {
  room_occupancy_rate: {
    title: '入住率统计报表',
    type: 'table_chart',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'roomType',
        type: 'select',
        label: '房间类型',
        placeholder: '选择房间类型',
        options: [
          { label: 'VIP套房', value: 'vip' },
          { label: '豪华单间', value: 'deluxe' },
          { label: '标准单间', value: 'standard' },
        ],
      },
    ],
    columns: [
      { prop: 'date', label: '日期', width: 120 },
      { prop: 'totalRooms', label: '总房间数', width: 100 },
      { prop: 'occupiedRooms', label: '已入住数', width: 100 },
      { prop: 'vacantRooms', label: '空置数', width: 100 },
      { prop: 'occupancyRate', label: '入住率', width: 100 },
    ],
  },
  room_turnover_report: {
    title: '房间周转率报表',
    type: 'table_chart',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'roomType',
        type: 'select',
        label: '房间类型',
        placeholder: '选择房间类型',
        options: [
          { label: 'VIP套房', value: 'vip' },
          { label: '豪华单间', value: 'deluxe' },
          { label: '标准单间', value: 'standard' },
        ],
      },
    ],
    columns: [
      { prop: 'date', label: '日期', width: 120 },
      { prop: 'roomNumber', label: '房间号', width: 100 },
      { prop: 'checkIns', label: '入住次数', width: 100 },
      { prop: 'avgStayDays', label: '平均住宿天数', width: 120 },
      { prop: 'turnoverRate', label: '周转率', width: 100 },
    ],
  },
  room_revenue_report: {
    title: '房型收入分析',
    type: 'table_chart',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'roomType',
        type: 'select',
        label: '房间类型',
        placeholder: '选择房间类型',
        options: [
          { label: 'VIP套房', value: 'vip' },
          { label: '豪华单间', value: 'deluxe' },
          { label: '标准单间', value: 'standard' },
        ],
      },
    ],
    columns: [
      { prop: 'roomType', label: '房型', width: 120 },
      { prop: 'totalRevenue', label: '总收入', width: 120 },
      { prop: 'avgPrice', label: '平均单价', width: 100 },
      { prop: 'occupancyDays', label: '入住天数', width: 100 },
      { prop: 'revenueRatio', label: '收入占比', width: 100 },
    ],
  },
  newborn_feeding_summary: {
    title: '新生儿喂养汇总',
    type: 'table',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'clientName',
        type: 'input',
        label: '客户姓名/房号',
        placeholder: '输入客户姓名或房号',
      },
    ],
    columns: [
      { prop: 'date', label: '日期', width: 120 },
      { prop: 'babyName', label: '宝宝姓名', width: 120 },
      { prop: 'totalMilk', label: '总奶量(ml)', width: 120 },
      { prop: 'feedingTimes', label: '喂养次数', width: 100 },
    ],
  },
  maternal_recovery_progress: {
    title: '产妇康复进展统计',
    type: 'table',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'clientName',
        type: 'input',
        label: '客户姓名',
        placeholder: '输入客户姓名',
      },
      {
        key: 'recoveryStage',
        type: 'select',
        label: '康复阶段',
        placeholder: '选择康复阶段',
        options: [
          { label: '产后1-7天', value: 'stage1' },
          { label: '产后8-14天', value: 'stage2' },
          { label: '产后15-28天', value: 'stage3' },
        ],
      },
    ],
    columns: [
      { prop: 'date', label: '日期', width: 120 },
      { prop: 'clientName', label: '客户姓名', width: 120 },
      { prop: 'recoveryScore', label: '康复评分', width: 100 },
      { prop: 'bodyWeight', label: '体重(kg)', width: 100 },
      { prop: 'mood', label: '情绪状态', width: 100 },
    ],
  },
  staff_workload_report: {
    title: '员工工时与负荷报表',
    type: 'table',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'department',
        type: 'select',
        label: '部门',
        placeholder: '选择部门',
        options: [
          { label: '护理部', value: 'nursing' },
          { label: '后勤部', value: 'logistics' },
          { label: '营养部', value: 'nutrition' },
        ],
      },
      {
        key: 'staffName',
        type: 'input',
        label: '员工姓名',
        placeholder: '输入员工姓名',
      },
    ],
    columns: [
      { prop: 'staffName', label: '员工姓名', width: 120 },
      { prop: 'department', label: '部门', width: 100 },
      { prop: 'workHours', label: '工作时长(h)', width: 120 },
      { prop: 'overtime', label: '加班时长(h)', width: 120 },
      { prop: 'workload', label: '工作负荷', width: 100 },
    ],
  },
  satisfaction_summary: {
    title: '客户满意度汇总报告',
    type: 'table_chart',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'serviceType',
        type: 'select',
        label: '服务类型',
        placeholder: '选择服务类型',
        options: [
          { label: '护理服务', value: 'nursing' },
          { label: '膳食服务', value: 'catering' },
          { label: '房间设施', value: 'facility' },
        ],
      },
    ],
    columns: [
      { prop: 'date', label: '日期', width: 120 },
      { prop: 'serviceType', label: '服务类型', width: 120 },
      { prop: 'avgScore', label: '平均评分', width: 100 },
      { prop: 'reviewCount', label: '评价数量', width: 100 },
      { prop: 'satisfactionRate', label: '满意度', width: 100 },
    ],
  },
  monthly_revenue_summary: {
    title: '月度营收汇总',
    type: 'table_chart',
    filterFields: [
      {
        key: 'dateStart',
        type: 'date',
        label: '开始日期',
        placeholder: '选择开始日期',
      },
      {
        key: 'dateEnd',
        type: 'date',
        label: '结束日期',
        placeholder: '选择结束日期',
      },
      {
        key: 'revenueType',
        type: 'select',
        label: '收入类型',
        placeholder: '选择收入类型',
        options: [
          { label: '房费收入', value: 'room' },
          { label: '服务收入', value: 'service' },
          { label: '其他收入', value: 'other' },
        ],
      },
    ],
    columns: [
      { prop: 'month', label: '月份', width: 120 },
      { prop: 'roomRevenue', label: '房费收入', width: 120 },
      { prop: 'serviceRevenue', label: '服务收入', width: 120 },
      { prop: 'totalRevenue', label: '总收入', width: 120 },
      { prop: 'growthRate', label: '增长率', width: 100 },
    ],
  },
}

// 当前报表配置
const currentReportConfig = computed(() => {
  return reportConfigs[activeReport.value] || {}
})

// 当前过滤器字段
const currentFilterFields = computed(() => {
  return currentReportConfig.value.filterFields || []
})

// 处理报表选择
const handleSelectReport = (reportId) => {
  activeReport.value = reportId
  reportData.value = null
  // 清空筛选条件
  Object.keys(filters).forEach((key) => {
    filters[key] = ''
  })
}

// 生成报表
const handleGenerateReport = async () => {
  if (!currentReportConfig.value.title) {
    ElMessage.warning('请先选择报表类型')
    return
  }

  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 生成模拟数据
    reportData.value = generateMockData(activeReport.value)
    ElMessage.success('报表生成成功')
  } catch (error) {
    ElMessage.error('报表生成失败')
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = (reportType) => {
  if (reportType === 'room_occupancy_rate') {
    return {
      tableData: [
        {
          date: '2025-05-01',
          totalRooms: 50,
          occupiedRooms: 45,
          vacantRooms: 5,
          occupancyRate: '90%',
        },
        {
          date: '2025-05-02',
          totalRooms: 50,
          occupiedRooms: 48,
          vacantRooms: 2,
          occupancyRate: '96%',
        },
        {
          date: '2025-05-03',
          totalRooms: 50,
          occupiedRooms: 47,
          vacantRooms: 3,
          occupancyRate: '94%',
        },
        {
          date: '2025-05-04',
          totalRooms: 50,
          occupiedRooms: 46,
          vacantRooms: 4,
          occupancyRate: '92%',
        },
        {
          date: '2025-05-05',
          totalRooms: 50,
          occupiedRooms: 49,
          vacantRooms: 1,
          occupancyRate: '98%',
        },
      ],
      chartData: {
        dates: ['2025-05-01', '2025-05-02', '2025-05-03', '2025-05-04', '2025-05-05'],
        occupancyRates: [90, 96, 94, 92, 98],
      },
    }
  } else if (reportType === 'room_turnover_report') {
    return {
      tableData: [
        {
          date: '2025-05-01',
          roomNumber: 'VIP01',
          checkIns: 2,
          avgStayDays: 28,
          turnoverRate: '85%',
        },
        {
          date: '2025-05-01',
          roomNumber: 'VIP02',
          checkIns: 3,
          avgStayDays: 25,
          turnoverRate: '92%',
        },
        {
          date: '2025-05-01',
          roomNumber: 'DLX01',
          checkIns: 4,
          avgStayDays: 20,
          turnoverRate: '88%',
        },
      ],
      chartData: {
        dates: ['VIP01', 'VIP02', 'DLX01'],
        occupancyRates: [85, 92, 88],
      },
    }
  } else if (reportType === 'room_revenue_report') {
    return {
      tableData: [
        {
          roomType: 'VIP套房',
          totalRevenue: 285000,
          avgPrice: 1500,
          occupancyDays: 190,
          revenueRatio: '45%',
        },
        {
          roomType: '豪华单间',
          totalRevenue: 218000,
          avgPrice: 1200,
          occupancyDays: 182,
          revenueRatio: '35%',
        },
        {
          roomType: '标准单间',
          totalRevenue: 126000,
          avgPrice: 800,
          occupancyDays: 157,
          revenueRatio: '20%',
        },
      ],
      chartData: {
        values: [45, 35, 20],
      },
    }
  } else if (reportType === 'newborn_feeding_summary') {
    return {
      tableData: [
        { date: '2025-05-06', babyName: '小宝', totalMilk: 650, feedingTimes: 8 },
        { date: '2025-05-06', babyName: '小贝', totalMilk: 580, feedingTimes: 7 },
        { date: '2025-05-06', babyName: '小乐', totalMilk: 720, feedingTimes: 9 },
        { date: '2025-05-07', babyName: '小宝', totalMilk: 680, feedingTimes: 8 },
        { date: '2025-05-07', babyName: '小贝', totalMilk: 620, feedingTimes: 8 },
      ],
    }
  } else if (reportType === 'maternal_recovery_progress') {
    return {
      tableData: [
        {
          date: '2025-05-06',
          clientName: '张女士',
          recoveryScore: 85,
          bodyWeight: 58.5,
          mood: '良好',
        },
        {
          date: '2025-05-06',
          clientName: '李女士',
          recoveryScore: 78,
          bodyWeight: 62.3,
          mood: '一般',
        },
        {
          date: '2025-05-07',
          clientName: '王女士',
          recoveryScore: 92,
          bodyWeight: 55.8,
          mood: '优秀',
        },
      ],
    }
  } else if (reportType === 'staff_workload_report') {
    return {
      tableData: [
        {
          staffName: '张护士',
          department: '护理部',
          workHours: 168,
          overtime: 12,
          workload: '适中',
        },
        {
          staffName: '李护士',
          department: '护理部',
          workHours: 172,
          overtime: 16,
          workload: '较重',
        },
        {
          staffName: '王师傅',
          department: '后勤部',
          workHours: 160,
          overtime: 8,
          workload: '轻松',
        },
      ],
    }
  } else if (reportType === 'satisfaction_summary') {
    return {
      tableData: [
        {
          date: '2025-05-01',
          serviceType: '护理服务',
          avgScore: 4.8,
          reviewCount: 45,
          satisfactionRate: '96%',
        },
        {
          date: '2025-05-01',
          serviceType: '膳食服务',
          avgScore: 4.5,
          reviewCount: 42,
          satisfactionRate: '90%',
        },
        {
          date: '2025-05-01',
          serviceType: '房间设施',
          avgScore: 4.6,
          reviewCount: 38,
          satisfactionRate: '92%',
        },
      ],
      chartData: {
        dates: ['护理服务', '膳食服务', '房间设施'],
        occupancyRates: [96, 90, 92],
      },
    }
  } else if (reportType === 'monthly_revenue_summary') {
    return {
      tableData: [
        {
          month: '2025-01',
          roomRevenue: 285000,
          serviceRevenue: 65000,
          totalRevenue: 350000,
          growthRate: '+8.5%',
        },
        {
          month: '2025-02',
          roomRevenue: 312000,
          serviceRevenue: 72000,
          totalRevenue: 384000,
          growthRate: '+9.7%',
        },
        {
          month: '2025-03',
          roomRevenue: 298000,
          serviceRevenue: 68000,
          totalRevenue: 366000,
          growthRate: '-4.7%',
        },
        {
          month: '2025-04',
          roomRevenue: 325000,
          serviceRevenue: 75000,
          totalRevenue: 400000,
          growthRate: '+9.3%',
        },
      ],
      chartData: {
        dates: ['2025-01', '2025-02', '2025-03', '2025-04'],
        occupancyRates: [350, 384, 366, 400],
      },
    }
  }
  return { tableData: [] }
}

// 导出处理
const handleExport = (type) => {
  if (type === 'csv') {
    handleExportCSV()
  } else if (type === 'pdf') {
    handleExportPDF()
  }
}

const handleExportCSV = () => {
  ElMessage.success('CSV导出功能开发中...')
}

const handleExportPDF = () => {
  ElMessage.success('PDF导出功能开发中...')
}

const handlePrint = () => {
  ElMessage.success('打印功能开发中...')
}

onMounted(() => {
  // 页面加载时可以设置默认筛选条件
})
</script>

<style scoped>
.report-export-container {
  padding: 24px;
}

.page-header {
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 24px;
}
</style>
