<template>
  <div class="transfer-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 换房管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理客户换房申请，处理换房流程</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增换房申请
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 数据表格 -->
    <TransferTable
      ref="transferTableRef"
      :filters="currentFilters"
      @process-request="handleProcessRequest"
      @edit="handleEdit"
      @row-click="handleRowClick"
    />

    <!-- 表单对话框 -->
    <TransferFormDialog
      v-model="showFormDialog"
      :mode="formMode"
      :item-id="currentRow?.rid"
      @success="handleSubmitSuccess"
    />

    <!-- 换房处理对话框 -->
    <TransferProcessDialog
      v-model="showProcessDialog"
      :application-id="currentRow?.rid"
      @success="handleAuditSuccess"
      @close="handleCloseProcessDialog"
    />

    <!-- 详情查看对话框 -->
    <TransferDetailDialog
      v-model="showDetailDialog"
      :item-id="currentRow?.rid"
      @process="handleProcessFromDetail"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import TransferTable from '@/views/housekeeping/components/TransferTable.vue'
import TransferFormDialog from '@/views/housekeeping/components/TransferFormDialog.vue'
import TransferProcessDialog from '@/views/housekeeping/components/TransferProcessDialog.vue'
import TransferDetailDialog from '@/views/housekeeping/components/TransferDetailDialog.vue'
import { TRANSFER_STATUS_OPTIONS } from '@/utils/constants.js'

// 响应式数据
const showFormDialog = ref(false)
const showProcessDialog = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref(null)
const formMode = ref('add') // 'add' | 'edit'

// 获取 table 组件引用
const transferTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  aps: '',
  sk: '',
  apt: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'aps',
    type: 'select',
    label: '状态',
    placeholder: '请选择审批状态',
    options: TRANSFER_STATUS_OPTIONS,
  },
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入客户姓名或房间号',
  },
  {
    key: 'apt',
    type: 'date',
    label: '申请时间',
    placeholder: '选择日期',
  },
]

// 新增换房申请
const handleCreate = () => {
  formMode.value = 'add'
  currentRow.value = null
  showFormDialog.value = true
}

// 编辑换房申请
const handleEdit = (row) => {
  formMode.value = 'edit'
  currentRow.value = row
  showFormDialog.value = true
}

// 表单提交成功
const handleSubmitSuccess = () => {
  showFormDialog.value = false
  currentRow.value = null
  transferTableRef.value?.refresh() // 刷新表格数据
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  transferTableRef.value?.resetPagination()
}

// 处理换房申请
const handleProcessRequest = (row) => {
  currentRow.value = row
  showProcessDialog.value = true
  showDetailDialog.value = false
}

// 行点击 - 查看详情
const handleRowClick = (row) => {
  currentRow.value = row
  showDetailDialog.value = true
}

// 从详情对话框处理申请
const handleProcessFromDetail = (row) => {
  currentRow.value = row
  showDetailDialog.value = false
  showProcessDialog.value = true
}

// 审核成功
const handleAuditSuccess = () => {
  showProcessDialog.value = false
  currentRow.value = null
  transferTableRef.value?.refresh() // 刷新表格数据
}

// 关闭处理对话框
const handleCloseProcessDialog = () => {
  showProcessDialog.value = false
  currentRow.value = null
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentRow.value = null
}
</script>

<style scoped>
.transfer-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
