<template>
  <el-dialog
    v-model="visible"
    title="修改房间状态"
    width="600px"
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
  >
    <div class="status-dialog-content">
      <!-- 房间信息卡片 -->
      <div class="room-info-card bg-gray-50 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-medium text-gray-700 mb-3">房间信息</h4>
        <div class="grid grid-cols-3 gap-4">
          <div class="detail-item">
            <label>房号：</label>
            <span class="font-mono">{{ roomData?.room_number }}</span>
          </div>
          <div class="detail-item">
            <label>类型：</label>
            <span>{{ roomData?.room_type }}</span>
          </div>
          <div class="detail-item">
            <label>楼层：</label>
            <span>{{ roomData?.floor }}</span>
          </div>
        </div>
        <div class="mt-3">
          <label>当前状态：</label>
          <el-tag
            :type="getStatusType(roomData?.room_status)"
            :effect="roomData?.room_status === 'AVAILABLE' ? 'plain' : 'dark'"
            class="ml-2"
          >
            {{ roomData?.statusText }}
          </el-tag>
        </div>
      </div>

      <!-- 状态修改表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="status-form"
      >
        <el-form-item label="新状态" prop="room_status">
          <el-select
            v-model="formData.room_status"
            placeholder="请选择新状态"
            class="w-full"
            @change="handleStatusChange"
          >
            <el-option
              v-for="option in ROOM_STATUS_OPTIONS"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
            <el-option label="其他原因 (不可用)" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item
          label="状态原因"
          prop="reason_remark"
          v-if="formData.room_status !== 'AVAILABLE'"
        >
          <el-input
            v-model="formData.reason_remark"
            placeholder="请输入状态变更的原因或备注..."
            type="textarea"
            :rows="3"
          />
        </el-form-item>

        <el-form-item
          label="预计恢复时间"
          prop="expected_recovery_time"
          v-if="formData.room_status != 'AVAILABLE'"
        >
          <el-date-picker
            v-model="formData.expected_recovery_time"
            type="datetime"
            placeholder="选择预计恢复时间"
            class="w-full"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
          />
        </el-form-item>

        <el-form-item label="入住客户" prop="customer" v-if="formData.room_status === 'occupied'">
          <el-input v-model="formData.customer" placeholder="请输入入住客户姓名..." />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          保存修改
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElTag,
  ElDatePicker,
} from 'element-plus'
import { ROOM_STATUS_OPTIONS, getStatusTagType } from '@/utils/constants.js'
import { put } from '@/utils/request'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  roomData: {
    type: Object,
    default: null,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const formData = reactive({
  room_status: '',
  reason_remark: '',
  expected_recovery_time: null,
  customer: '',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 表单验证规则
const rules = computed(() => {
  const baseRules = {
    room_status: [{ required: true, message: '请选择新状态', trigger: 'change' }],
  }

  if (formData.room_status !== 'available') {
    baseRules.reason_remark = [{ required: true, message: '请输入状态变更原因', trigger: 'blur' }]
    baseRules.expected_recovery_time = [
      { required: true, message: '请输入预计恢复时间', trigger: 'blur' },
    ]
  }

  if (formData.room_status === 'occupied') {
    baseRules.customer = [{ required: true, message: '请输入入住客户姓名', trigger: 'blur' }]
  }

  return baseRules
})

// 监听房间数据变化
watch(
  () => props.roomData,
  (newData) => {
    if (newData) {
      formData.room_status = newData.room_status
      formData.reason_remark = ''
      formData.expected_recovery_time = null
      formData.customer = ''
    }
  },
  { immediate: true },
)

// 获取状态标签类型
const getStatusType = (room_status) => {
  if (room_status === 'other') return 'info'
  return getStatusTagType(room_status)
}

// 方法
const handleOpen = () => {
  if (props.roomData) {
    formData.room_status = props.roomData.room_status
    formData.reason_remark = ''
    formData.expected_recovery_time = null
    formData.customer = ''
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formData.room_status = ''
  formData.reason_remark = ''
  formData.expected_recovery_time = null
  formData.customer = ''
  formRef.value?.clearValidate()
}

const handleStatusChange = () => {
  // 状态改变时清空相关字段
  formData.reason_remark = ''
  formData.expected_recovery_time = null
  formData.customer = ''
}

const handleSave = async () => {
  try {
    await formRef.value.validate()

    saving.value = true

    const saveData = {
      room_status: formData.room_status,
      reason_remark: formData.reason_remark,
      expected_recovery_time: formData.expected_recovery_time,
    }
    let res = await put(`customer-service/room/change-status/${props.roomData.rid}/`, saveData)

    emit('save', res)
    saving.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.status-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.room-info-card {
  border: 1px solid #e5e7eb;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 3rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-picker:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}
</style>
