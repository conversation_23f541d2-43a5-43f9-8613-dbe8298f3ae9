<template>
  <div
    class="room-card bg-white border border-gray-200 rounded-lg p-4 min-h-40 flex flex-col cursor-pointer relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-pink-200"
    :class="[statusClass, { 'is-clickable': true }]"
    @click="handleClick"
  >
    <!-- 房间状态指示器 -->
    <div
      class="status-indicator absolute top-3 right-3 w-3 h-3 rounded-full shadow-sm"
      :class="statusClass"
    ></div>

    <!-- 房间基本信息 -->
    <div class="room-header mb-3 pr-6">
      <div class="room-number text-lg font-semibold text-gray-800 mb-1">{{ room.room_number }}</div>
      <div class="room-type text-xs text-gray-500 font-medium">{{ room.room_type }}</div>
    </div>

    <!-- 房间状态 -->
    <div class="room-status-section mb-3">
      <div
        class="status-badge inline-flex items-center px-2 py-1 rounded text-xs font-medium"
        :class="statusClass"
      >
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>

    <!-- 客户信息 -->
    <div class="room-content flex-1 flex flex-col justify-center">
      <div v-if="room.customer_info" class="customer-section">
        <div class="customer-info">
          <div class="customer-name text-sm font-medium text-gray-800 mb-1">
            {{ room.customer_info.name }}
          </div>
          <div
            v-if="room.customer_info.expected_check_in_date"
            class="duration text-xs text-gray-500"
          >
            {{ formatDuration(room.customer_info) }}
          </div>
        </div>
      </div>

      <div v-else class="empty-customer flex items-center justify-center py-4 text-gray-400">
        <div class="empty-text text-xs">暂无客户</div>
      </div>
    </div>

    <!-- 房间操作指示 -->
    <div class="room-actions mt-auto flex justify-end items-center h-4">
      <div
        class="action-dots flex gap-1 opacity-30 transition-opacity duration-300 hover:opacity-60"
      >
        <span class="dot w-1 h-1 rounded-full bg-gray-400"></span>
        <span class="dot w-1 h-1 rounded-full bg-gray-400"></span>
        <span class="dot w-1 h-1 rounded-full bg-gray-400"></span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { getRoomStatusInfo, ROOM_STATUS_MAP } from '@/utils/constants.js'

const props = defineProps({
  room: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['click'])

const statusClass = computed(() => {
  // 使用constants.js中的状态映射来生成CSS类名
  const status = props.room.current_status
  const statusInfo = getRoomStatusInfo(status)

  // 根据状态类型生成对应的CSS类名
  const statusTypeMap = {
    AVAILABLE: 'status-available',
    CHECKED_IN: 'status-occupied',
    UNAVAILABLE_CLEANING: 'status-cleaning',
    UNAVAILABLE_MAINTENANCE: 'status-maintenance',
    UNAVAILABLE_SWITCH_ROOM: 'status-maintenance',
    UNAVAILABLE_OTHER: 'status-maintenance',
    RESERVED: 'status-reserved',
  }

  return statusTypeMap[status] || 'status-available'
})

const statusText = computed(() => {
  const statusInfo = getRoomStatusInfo(props.room.current_status)
  // 对于 RoomCard，显示简化的状态文本
  const simpleTextMap = {
    可用: '可入住',
    已入住: '已入住',
    已预定: '已预订',
    '不可用（维修中）': '维修中',
    '不可用（待清洁）': '清洁中',
    '不可用（换房审批中）': '审批中',
    '不可用（其他原因）': '不可用',
  }
  return simpleTextMap[statusInfo.text] || statusInfo.text
})

// 格式化入住时长显示
const formatDuration = (customerInfo) => {
  if (customerInfo.check_in_status === '已入住') {
    return `入住时间: ${customerInfo.actual_check_in_date} - ${customerInfo.expected_check_out_date}`
  } else if (customerInfo.check_in_status === '已预定') {
    return `预订时间: ${customerInfo.expected_check_in_date} - ${customerInfo.expected_check_out_date}`
  }
  return ''
}

const handleClick = () => {
  emit('click', props.room)
}
</script>

<style scoped>
/* 状态指示器颜色 */
.status-indicator.status-available {
  background-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.status-indicator.status-occupied {
  background-color: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
}

.status-indicator.status-cleaning {
  background-color: #f59e0b;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.status-indicator.status-maintenance {
  background-color: #6b7280;
  box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
}

.status-indicator.status-reserved {
  background-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 状态徽章样式 */
.status-badge.status-available {
  background-color: #ecfdf5;
  color: #059669;
  border: 1px solid #d1fae5;
}

.status-badge.status-occupied {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-badge.status-cleaning {
  background-color: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}

.status-badge.status-maintenance {
  background-color: #f9fafb;
  color: #4b5563;
  border: 1px solid #d1d5db;
}

.status-badge.status-reserved {
  background-color: #eff6ff;
  color: #2563eb;
  border: 1px solid #bfdbfe;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .room-card {
    min-height: 140px;
    padding: 12px;
  }

  .room-number {
    font-size: 16px;
  }

  .status-indicator {
    width: 8px;
    height: 8px;
  }

  .room-header {
    padding-right: 20px;
  }

  .customer-name {
    font-size: 13px;
  }

  .duration {
    font-size: 11px;
  }
}

/* 打印样式 */
@media print {
  .room-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #e5e7eb;
  }

  .room-card:hover {
    transform: none;
    box-shadow: none;
  }
}
</style>
