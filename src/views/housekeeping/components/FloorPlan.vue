<template>
  <div class="floor-plan-container bg-white border border-gray-200 rounded-lg overflow-hidden mb-5">
    <!-- 楼层标题栏 -->
    <div class="floor-header bg-gray-50 border-b border-gray-200 p-6">
      <div class="floor-title-section flex justify-between items-center">
        <div class="floor-info">
          <div class="floor-title text-xl font-bold text-gray-800 mb-2">{{ floor.floor_name }}</div>
        </div>

        <!-- 状态图例 -->
        <StatusLegend :statuses="legendStatuses" />
      </div>
    </div>

    <!-- 楼层布局 -->
    <div class="floor-layout p-6">
      <!-- 房间布局 - 按行排列，每行中间插入走廊 -->
      <div v-if="floor.rooms && floor.rooms.length > 0" class="rooms-layout">
        <template v-for="(rowData, rowIndex) in roomRows" :key="`row-${rowIndex}`">
          <!-- 房间行 -->
          <div class="room-row flex flex-wrap gap-3 mb-4">
            <RoomCard
              v-for="room in rowData"
              :key="room.id"
              :room="room"
              @click="handleRoomClick"
              class="room-item flex-shrink-0"
              :class="roomCardClass"
            />
          </div>

          <!-- 走廊 (除了最后一行) -->
          <div
            v-if="rowIndex < roomRows.length - 1"
            class="corridor bg-gray-50 border border-gray-200 rounded-lg p-3 mb-4"
          >
            <div class="corridor-name text-sm text-gray-500 text-center font-medium">
              {{ `${rowIndex + 1}-${rowIndex + 2}层间走廊` }}
            </div>
          </div>
        </template>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state text-center py-12">
        <div class="empty-text text-lg font-medium text-gray-500 mb-2">该楼层暂无房间数据</div>
        <div class="empty-description text-sm text-gray-400">请联系管理员添加房间信息</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import RoomCard from './RoomCard.vue'
import StatusLegend from '../../../components/housekeeping/StatusLegend.vue'
import {
  ROOM_STATUS_MAP,
  getRoomStatusInfo,
  getRoomStatusColor,
  ROOM_STATUS_LEGEND_ORDER,
} from '@/utils/constants.js'

const props = defineProps({
  floor: {
    type: Object,
    required: true,
    validator: (value) => {
      return value && (value.floor_name || value.name) && Array.isArray(value.rooms)
    },
  },
})

const emit = defineEmits(['room-click'])

// 响应式屏幕宽度
const screenWidth = ref(window.innerWidth)

// 监听屏幕尺寸变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth)
})

// 根据屏幕宽度计算每行房间数量
const roomsPerRow = computed(() => {
  if (screenWidth.value >= 1536) return 6 // 2xl
  if (screenWidth.value >= 1280) return 5 // xl
  if (screenWidth.value >= 1024) return 4 // lg
  if (screenWidth.value >= 768) return 3 // md
  return 2 // sm and below
})

// 将房间按行分组
const roomRows = computed(() => {
  if (!props.floor.rooms || props.floor.rooms.length === 0) {
    return []
  }

  const rooms = props.floor.rooms
  const perRow = roomsPerRow.value
  const rows = []

  for (let i = 0; i < rooms.length; i += perRow) {
    rows.push(rooms.slice(i, i + perRow))
  }

  return rows
})

// 房间卡片的响应式类名
const roomCardClass = computed(() => {
  const baseWidth = 'w-full'
  if (screenWidth.value >= 1536) return `${baseWidth} max-w-[calc(16.666%-0.75rem)]` // 2xl: 1/6
  if (screenWidth.value >= 1280) return `${baseWidth} max-w-[calc(20%-0.75rem)]` // xl: 1/5
  if (screenWidth.value >= 1024) return `${baseWidth} max-w-[calc(25%-0.75rem)]` // lg: 1/4
  if (screenWidth.value >= 768) return `${baseWidth} max-w-[calc(33.333%-0.75rem)]` // md: 1/3
  return `${baseWidth} max-w-[calc(50%-0.75rem)]` // sm: 1/2
})

// 使用统一的图例状态顺序
const legendStatuses = ROOM_STATUS_LEGEND_ORDER

const handleRoomClick = (room) => {
  emit('room-click', room)
}

// 获取指定状态的房间数量
const getStatusCount = (status) => {
  return props.floor.rooms.filter((room) => room.current_status === status).length
}

// 保留原有的统计函数用于向后兼容
const getAvailableCount = () => {
  return getStatusCount('AVAILABLE')
}

const getOccupiedCount = () => {
  return getStatusCount('CHECKED_IN')
}
</script>

<style scoped>
.floor-plan-container {
  transition: all 0.3s ease;
}

.floor-plan-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.room-item {
  transition: transform 0.2s ease;
}

.room-item:hover {
  transform: translateY(-2px);
}

.corridor {
  position: relative;
  background: linear-gradient(90deg, transparent 0%, #f9fafb 20%, #f9fafb 80%, transparent 100%);
  border-style: dashed;
  transition: all 0.3s ease;
}

.corridor:hover {
  background: linear-gradient(90deg, transparent 0%, #f3f4f6 20%, #f3f4f6 80%, transparent 100%);
  border-color: #d1d5db;
}

.room-row {
  min-height: 160px;
  align-items: stretch;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .floor-header {
    padding: 1rem;
  }

  .floor-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .status-legend {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .floor-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .status-legend {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .corridor {
    padding: 0.5rem;
  }

  .corridor-name {
    font-size: 0.75rem;
  }
}
</style>
