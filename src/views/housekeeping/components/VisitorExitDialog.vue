<template>
  <el-dialog
    v-model="visible"
    title="标记访客离开"
    width="600px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="exit-dialog"
  >
    <div class="max-h-[60vh] overflow-y-auto">
      <!-- 访客信息展示 -->
      <div class="visitor-info mb-6 p-4 bg-gray-50 rounded-lg">
        <h4 class="font-semibold text-gray-800 mb-3 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <User />
          </el-icon>
          访客信息
        </h4>
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-600">访客姓名：</span>
            <span class="font-medium">{{ visitorData?.visitor_name }}</span>
          </div>
          <div>
            <span class="text-gray-600">联系电话：</span>
            <span class="font-medium">{{ visitorData?.visitor_phone }}</span>
          </div>
          <div>
            <span class="text-gray-600">被访者：</span>
            <span class="font-medium">{{ visitorData?.visited_maternity_info?.name || '无' }}</span>
          </div>
          <div>
            <span class="text-gray-600">来访时间：</span>
            <span class="font-medium">{{ formatDateTime(visitorData?.visit_time) }}</span>
          </div>
          <div>
            <span class="text-gray-600">访客人数：</span>
            <span class="font-medium">{{ visitorData?.visitor_count }}人</span>
          </div>
          <div>
            <span class="text-gray-600">来访事由：</span>
            <span class="font-medium">{{ visitorData?.visit_purpose }}</span>
          </div>
        </div>
      </div>

      <!-- 离开信息表单 -->
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="exit-form">
        <div class="form-section">
          <h4 class="section-title">离开记录</h4>

          <el-form-item label="实际离开时间" prop="leave_time">
            <el-date-picker
              v-model="formData.leave_time"
              type="datetime"
              placeholder="选择离开时间"
              class="w-full"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="离开备注" prop="leave_remark">
            <el-input
              v-model="formData.leave_remark"
              type="textarea"
              :rows="4"
              placeholder="记录访问情况、物品携带等信息..."
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          确认离开
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElDatePicker,
  ElIcon,
  ElMessage,
} from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { put } from '@/utils/request.js'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visitorData: {
    type: Object,
    default: null,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const formData = reactive({
  leave_time: '',
  leave_remark: '',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 表单验证规则
const rules = {
  leave_time: [{ required: true, message: '请选择离开时间', trigger: 'change' }],
  leave_remark: [{ required: true, message: '请输入离开备注', trigger: 'blur' }],
}

// 监听弹窗打开
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      resetForm()
      // 设置默认离开时间为当前时间
      const now = new Date()
      const timeStr = now.getFullYear() + '-' +
                     String(now.getMonth() + 1).padStart(2, '0') + '-' +
                     String(now.getDate()).padStart(2, '0') + ' ' +
                     String(now.getHours()).padStart(2, '0') + ':' +
                     String(now.getMinutes()).padStart(2, '0') + ':' +
                     String(now.getSeconds()).padStart(2, '0')
      formData.leave_time = timeStr
    }
  },
)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    leave_time: '',
    leave_remark: '',
  })
  formRef.value?.clearValidate()
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return date.toLocaleString('zh-CN')
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 保存数据
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    // 准备API提交数据
    const submitData = {
      leave_remark: formData.leave_remark,
      leave_time: formData.leave_time,
    }

    console.log('提交离开数据:', submitData)
    console.log('访客ID:', props.visitorData?.vid)

    // 调用标记离开API
    const response = await put(`customer-service/visitor/leave/${props.visitorData?.vid}/`, submitData)

    console.log('API响应:', response)
    ElMessage.success('访客离开记录成功')

    // 通知父组件保存成功
    emit('save', response)

    // 关闭弹窗
    visible.value = false
  } catch (error) {
    console.error('标记离开失败:', error)
    ElMessage.error('标记访客离开失败')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.exit-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.visitor-info {
  border-left: 4px solid #ec4899;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f3f4f6;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #ec4899;
  margin-right: 8px;
}

.form-section {
  padding: 20px 0;
  transition: all 0.3s ease;
}

.exit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.exit-form :deep(.el-input__wrapper) {
  border-radius: 6px;
}

.exit-form :deep(.el-rate) {
  height: auto;
}

.exit-form :deep(.el-rate__text) {
  color: #6b7280;
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}
</style>
