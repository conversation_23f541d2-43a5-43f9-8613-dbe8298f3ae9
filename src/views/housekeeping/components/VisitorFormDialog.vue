<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="visitor-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="140px"
        class="visitor-form "
      >
        <!-- 访客基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">访客基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="访客姓名" prop="visitor_name">
              <el-input v-model="formData.visitor_name" placeholder="请输入访客姓名" :readonly="isReadonly" />
            </el-form-item>
            <el-form-item label="联系电话" prop="visitor_phone">
              <el-input v-model="formData.visitor_phone" placeholder="请输入联系电话" :readonly="isReadonly" />
            </el-form-item>
            <el-form-item label="身份证号" prop="visitor_identity_number">
              <el-input v-model="formData.visitor_identity_number" placeholder="请输入身份证号（可选）" :readonly="isReadonly" />
            </el-form-item>
            <el-form-item label="访客人数" prop="visitor_count">
              <el-input-number v-model="formData.visitor_count" :min="1" :max="10" :disabled="isReadonly" class="w-full" />
            </el-form-item>
          </div>
        </div>

        <!-- 被访信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">被访信息</h4>

          <!-- 在院产妇选择 -->
          <div v-if="!isReadonly" class="mb-4">
            <el-form-item label="选择被访产妇" prop="visited_admission">
              <el-select
                v-model="formData.visited_admission"
                placeholder="请搜索并选择被访产妇"
                class="w-full"
                filterable
                remote
                reserve-keyword
                :remote-method="searchMaternityList"
                :loading="maternityLoading"
                clearable
                @change="handleMaternityChange"
              >
                <el-option
                  v-for="maternity in maternityList"
                  :key="maternity.aid"
                  :label="`${maternity.maternity_name}`"
                  :value="maternity.aid"
                >
                  <div class="flex justify-between items-center">
                    <span class="font-medium">{{ maternity.maternity_name }}</span>
                    <span class="text-sm text-gray-500">电话：{{ maternity.maternity_phone }} | 房间号：{{ maternity.room_number }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </div>

          <!-- 被访者详细信息显示 -->
          <div v-if="formData.visited_maternity_info && Object.keys(formData.visited_maternity_info).length > 0" class="mb-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h5 class="text-sm font-medium text-blue-800 mb-2">被访者详细信息</h5>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div><span class="text-gray-600">姓名：</span>{{ formData.visited_maternity_info.name || '-' }}</div>
                <div><span class="text-gray-600">房间：</span>{{ formData.visited_maternity_info.room || '-' }}</div>
                <div><span class="text-gray-600">电话：</span>{{ formData.visited_maternity_info.phone || '-' }}</div>
                <div><span class="text-gray-600">性别：</span>{{ formData.visited_maternity_info.gender || '-' }}</div>
                <div><span class="text-gray-600">年龄：</span>{{ formData.visited_maternity_info.age || '-' }}</div>
                <div><span class="text-gray-600">生日：</span>{{ formData.visited_maternity_info.birth_date || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="来访事由" prop="visit_purpose">
              <el-select v-model="formData.visit_purpose" placeholder="请选择来访事由" class="w-full" :disabled="isReadonly">
                <el-option label="探视" value="探视" />
                <el-option label="送物品" value="送物品" />
                <el-option label="陪护" value="陪护" />
                <el-option label="商务拜访" value="商务拜访" />
                <el-option label="参观" value="参观" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
            <el-form-item label="访问状态" prop="visit_status">
              <el-select v-model="formData.visit_status" placeholder="请选择访问状态" class="w-full" :disabled="isReadonly">
                <el-option label="待访问" value="PENDING" />
                <el-option label="正在访问" value="VISITING" />
              </el-select>
            </el-form-item>
          </div>
          <el-form-item label="预约备注" prop="appointment_remark">
            <el-input
              v-model="formData.appointment_remark"
              type="textarea"
              :rows="2"
              placeholder="请详细说明来访目的..."
              :readonly="isReadonly"
            />
          </el-form-item>
          <el-form-item v-if="formData.visit_result_remark" label="访问结果备注" prop="visit_result_remark">
            <el-input
              v-model="formData.visit_result_remark"
              type="textarea"
              :rows="2"
              readonly
            />
          </el-form-item>
        </div>

        <!-- 健康与安全 -->
        <div class="form-section mb-6">
          <h4 class="section-title">健康与安全信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="体温 (°C)" prop="temperature">
              <el-input-number
                v-model="formData.temperature"
                :min="35"
                :max="42"
                :precision="1"
                :step="0.1"
                placeholder="请测量体温"
                class="w-full"
                :disabled="isReadonly"
              />
            </el-form-item>
          </div>

          <!-- 健康状况检查单独一行 -->
          <div class="mt-4">
            <el-form-item label="健康状况检查">
              <div class="space-y-3">
                <div class="flex items-center">
                  <span class="text-sm text-gray-700 w-64">过去14天内是否有发热、咳嗽等症状?</span>
                  <el-radio-group v-model="formData.fever_symptoms" size="small" :disabled="isReadonly">
                    <el-radio :value="false">否</el-radio>
                    <el-radio :value="true">是</el-radio>
                  </el-radio-group>
                </div>
                <div class="flex items-center">
                  <span class="text-sm text-gray-700 w-64"
                    >过去14天内是否接触过确诊或疑似病例?</span
                  >
                  <el-radio-group v-model="formData.contact_with_case" size="small" :disabled="isReadonly">
                    <el-radio :value="false">否</el-radio>
                    <el-radio :value="true">是</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 访问时间 -->
        <div class="form-section mb-6">
          <h4 class="section-title">访问时间</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="来访时间" prop="visit_time">
              <el-date-picker
                v-model="formData.visit_time"
                type="datetime"
                placeholder="选择来访时间"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="isReadonly"
              />
            </el-form-item>
            <el-form-item label="预计离开时间" prop="expected_departure_time">
              <el-date-picker
                v-model="formData.expected_departure_time"
                type="datetime"
                placeholder="选择预计离开时间"
                class="w-full"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled="isReadonly"
              />
            </el-form-item>
            <el-form-item v-if="formData.actual_departure_time" label="实际离开时间" prop="actual_departure_time">
              <el-input :value="formData.actual_departure_time" readonly />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '保存登记信息' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElButton,
  ElDatePicker,
  ElRadioGroup,
  ElRadio,
  ElMessage,
} from 'element-plus'
import { get, post, put } from '@/utils/request.js'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visitorData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 在院产妇相关数据
const maternityList = ref([])
const maternityLoading = ref(false)

// 表单数据
const formData = reactive({
  vid: '',
  visitor_name: '',
  visitor_phone: '',
  visitor_identity_number: '',
  visitor_count: 1,
  visit_purpose: '',
  temperature: 36.5,
  fever_symptoms: false,
  contact_with_case: false,
  appointment_remark: '',
  visit_result_remark: '',
  visit_time: '',
  expected_departure_time: '',
  actual_departure_time: '',
  visit_status: 'PENDING',
  visited_maternity_info: {},
  visited_aid: '', // API提交用的被访者ID
  visited_admission: null, // 选择器显示用的ID
  // 兼容旧字段名
  visitorName: '',
  phone: '',
  idCard: '',
  relationship: '',
  visitedType: 'client',
  visitedPerson: '',
  roomNumber: '',
  purpose: '',
  purposeDetail: '',
  healthQuestions: {
    symptoms: false,
    contact: false,
  },
  arrivalTime: '',
  expectedDepartureTime: '',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新增访客登记' : '编辑访客信息'
})

const isReadonly = computed(() => false) // 表单模式下都不是只读

// 获取访问状态显示文本
const getVisitStatusDisplay = () => {
  const statusMap = {
    PENDING: '待访问',
    VISITING: '正在访问',
    LEAVED: '已离开'
  }
  return statusMap[formData.visit_status] || formData.visit_status
}

// 搜索在院产妇列表
const searchMaternityList = async (query = '') => {
  maternityLoading.value = true
  try {
    const params = query ? { sk: query } : {}
    const data = await get('customer-service/maternity-admission/in-house/select/list/', params)
    maternityList.value = data.list || []
  } catch (error) {
    console.error('获取在院产妇列表失败:', error)
    maternityList.value = []
  } finally {
    maternityLoading.value = false
  }
}

// 处理产妇选择变化
const handleMaternityChange = (maternityId) => {
  if (maternityId) {
    const selectedMaternity = maternityList.value.find(m => m.aid === maternityId)
    if (selectedMaternity) {
      // 更新被访者信息，包含新增的字段
      formData.visited_maternity_info = {
        name: selectedMaternity.maternity_name,
        phone: selectedMaternity.maternity_phone,
        room: selectedMaternity.room_number === '-' ? '暂无记录' : selectedMaternity.room_number,
        gender: selectedMaternity.gender_display || '女',
        age: selectedMaternity.age === '-' || !selectedMaternity.age ? '-' : selectedMaternity.age,
        birth_date: selectedMaternity.birth_date || '暂无记录'
      }
      // 更新visited_aid字段（用于API提交）
      formData.visited_aid = maternityId
      // 保持visited_admission字段（用于选择器显示）
      formData.visited_admission = maternityId
    }
  } else {
    // 清空被访者信息
    formData.visited_maternity_info = {}
    formData.visited_aid = ''
    formData.visited_admission = null
  }
}

// 表单验证规则
const rules = {
  visitor_name: [{ required: true, message: '请输入访客姓名', trigger: 'blur' }],
  visitor_phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  visit_purpose: [{ required: true, message: '请选择来访事由', trigger: 'change' }],
  visit_status: [{ required: true, message: '请选择访问状态', trigger: 'change' }],
  temperature: [{ required: true, message: '请测量体温', trigger: 'blur' }],
  visit_time: [{ required: true, message: '请选择来访时间', trigger: 'change' }],
  expected_departure_time: [{ required: true, message: '请选择预计离开时间', trigger: 'change' }],
}

// 监听访客数据变化
watch(
  () => props.visitorData,
  (newData) => {
    if (newData) {
      // 填充数据，支持新旧字段格式
      Object.assign(formData, {
        ...newData,
        // 确保关键字段正确映射
        visitor_name: newData.visitor_name || newData.visitorName || '',
        visitor_phone: newData.visitor_phone || newData.phone || '',
        visitor_identity_number: newData.visitor_identity_number || newData.idCard || '',
        visit_purpose: newData.visit_purpose || newData.purpose || '',
        appointment_remark: newData.appointment_remark || newData.remarks || newData.purposeDetail || '',
        visit_time: newData.visit_time || newData.arrivalTime || '',
        expected_departure_time: newData.expected_departure_time || newData.expectedDepartureTime || newData.departureTime || '',
        fever_symptoms: newData.fever_symptoms ?? newData.healthQuestions?.symptoms ?? false,
        contact_with_case: newData.contact_with_case ?? newData.healthQuestions?.contact ?? false,
        visited_maternity_info: newData.visited_maternity_info || {},
        visited_aid: newData.visited_aid || '',
        // 设置选择器的值，如果有visited_aid则使用它
        visited_admission: newData.visited_aid || null,
      })
    }
  },
  { immediate: true },
)

// 监听弹窗打开
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.mode === 'add') {
      resetForm()
    }
  },
)

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    vid: '',
    visitor_name: '',
    visitor_phone: '',
    visitor_identity_number: '',
    visitor_count: 1,
    visit_purpose: '',
    temperature: 36.5,
    fever_symptoms: false,
    contact_with_case: false,
    appointment_remark: '',
    visit_result_remark: '',
    visit_time: '',
    expected_departure_time: '',
    actual_departure_time: '',
    visit_status: 'PENDING',
    visited_maternity_info: {},
    visited_aid: '',
    visited_admission: null,
    // 兼容旧字段名
    visitorName: '',
    phone: '',
    idCard: '',
    relationship: '',
    visitedType: 'client',
    visitedPerson: '',
    roomNumber: '',
    purpose: '',
    purposeDetail: '',
    healthQuestions: {
      symptoms: false,
      contact: false,
    },
    arrivalTime: '',
    expectedDepartureTime: '',
  })
  formRef.value?.clearValidate()
  // 清空产妇列表
  maternityList.value = []
}

// 打开弹窗
const handleOpen = () => {
  // 如果是新增或编辑模式，加载产妇列表
  if (props.mode === 'add' || props.mode === 'edit') {
    searchMaternityList()
  }

  // 如果是新增模式，设置默认来访时间为当前时间
  if (props.mode === 'add') {
    const now = new Date()
    const timeStr = now.getFullYear() + '-' +
                   String(now.getMonth() + 1).padStart(2, '0') + '-' +
                   String(now.getDate()).padStart(2, '0') + ' ' +
                   String(now.getHours()).padStart(2, '0') + ':' +
                   String(now.getMinutes()).padStart(2, '0') + ':' +
                   String(now.getSeconds()).padStart(2, '0')
    formData.visit_time = timeStr
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 保存数据
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saving.value = true

    // 准备API提交数据
    const submitData = {
      visitor_name: formData.visitor_name,
      visitor_phone: formData.visitor_phone,
      visitor_identity_number: formData.visitor_identity_number || '',
      visited_aid: formData.visited_aid || '',
      visitor_count: formData.visitor_count,
      visit_purpose: formData.visit_purpose,
      temperature: formData.temperature,
      fever_symptoms: formData.fever_symptoms,
      contact_with_case: formData.contact_with_case,
      visit_time: formData.visit_time,
      expected_departure_time: formData.expected_departure_time,
      appointment_remark: formData.appointment_remark || '',
      visit_status: formData.visit_status,
    }

    console.log('提交数据:', submitData)

    let response
    if (props.mode === 'edit' && formData.vid) {
      // 编辑模式：调用更新API
      response = await put(`customer-service/visitor/update/${formData.vid}/`, submitData)
      ElMessage.success('访客信息更新成功')
    } else {
      // 新增模式：调用创建API
      response = await post('customer-service/visitor/create/', submitData)
      ElMessage.success('访客登记成功')
    }

    console.log('API响应:', response)

    // 通知父组件保存成功
    emit('save', response)

    // 关闭弹窗
    visible.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(props.mode === 'edit' ? '更新访客信息失败' : '访客登记失败')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.visitor-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f3f4f6;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #ec4899;
  margin-right: 8px;
}

.form-section {
  padding: 20px 0;
  transition: all 0.3s ease;
}

.visitor-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

.visitor-form :deep(.el-input__wrapper) {
  border-radius: 6px;
}

.visitor-form :deep(.el-select) {
  width: 100%;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}
</style>
