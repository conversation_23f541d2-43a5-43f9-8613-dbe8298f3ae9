<template>
  <div class="visitor-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <User />
          </el-icon>
          预约参观列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="visitor_name" label="访客姓名" min-width="100" fixed="left">
        <template #default="{ row }">
          <span class="font-medium">{{ row.visitor_name }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="visitor_phone" label="联系电话" min-width="130">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.visitor_phone }}</span>
        </template>
      </el-table-column>

      <el-table-column label="来访时间" min-width="160">
        <template #default="{ row }">
          <span class="text-sm whitespace-nowrap">{{ formatDateTime(row.visit_time) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="参观人数" min-width="160">
        <template #default="{ row }">
          <span class="text-sm whitespace-nowrap">{{ row.visitor_count }}人</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" min-width="100">
        <!-- 有 PENDING, CONFIRMED, REJECTED, CANCELLED, COMPLETED -->
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.appointment_status)" size="small">
            {{ row.appointment_status_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <!-- <el-button
              type="info"
              size="small"
              @click="handleView(row)"
              class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
            >
              查看
            </el-button> -->
            <el-button
              v-if="row.appointment_status === 'PENDING'"
              type="success"
              size="small"
              @click="openApproveDialog(row)"
              class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
            >
              审批
            </el-button>
            <el-button
              v-if="row.appointment_status === 'CONFIRMED'"
              type="primary"
              size="small"
              @click="handleMarkComplete(row)"
              class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
            >
              标记完成
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElButton,
  ElPagination,
  ElIcon,
  ElMessage,
} from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { get } from '@/utils/request.js'
import { getVisitorStatusTagType, getVisitorAppointmentStatusTagType } from '@/utils/constants.js'

// 定义属性
const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/visitor/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['mark-approved', 'mark-complete', 'row-click'])

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = data.list
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取访客列表失败:', error)
    ElMessage.error('获取访客列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 获取状态标签类型
const getStatusType = (status) => {
  return getVisitorAppointmentStatusTagType(status)
}

// 格式化完整日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 格式化日期
const formatDate = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return date.toLocaleDateString('zh-CN')
}

// 格式化时间
const formatTime = (dateTimeStr) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 行点击处理
const handleRowClick = (row) => {
  emit('row-click', row)
}

// 标记审核通过
const openApproveDialog = (visitor) => {
  emit('mark-approved', visitor)
}

// 标记完成
const handleMarkComplete = (visitor) => {
  emit('mark-complete', visitor)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 监听filters变化，重新加载数据
watch(
  () => props.filters,
  () => {
    currentPage.value = 1
    loadData()
  },
  { deep: true },
)

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.visitor-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.visitor-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
  align-items: center;
}

.table-header {
  transition: all 0.3s ease;
}

/* 确保表格内容不会被压缩 */
:deep(.el-table) {
  table-layout: auto !important;
}

:deep(.el-table .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 时间列强制不换行 */
:deep(.el-table .cell .whitespace-nowrap) {
  white-space: nowrap !important;
}

/* 被访者列允许换行显示 */
:deep(.el-table .cell:has(.font-medium)) {
  white-space: normal;
  word-break: break-all;
}
</style>
