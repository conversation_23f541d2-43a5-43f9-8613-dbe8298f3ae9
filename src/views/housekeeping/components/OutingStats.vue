<template>
  <div class="outing-stats-container bg-white border border-gray-200 rounded-lg p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="stat-item">
        <div class="flex items-center">
          <div class="stat-icon bg-blue-100 text-blue-600">
            <Calendar class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <div class="text-2xl font-bold text-gray-900">{{ stats.todayOut }}</div>
            <div class="text-sm text-gray-500">今日外出</div>
          </div>
        </div>
      </div>

      <div class="stat-item">
        <div class="flex items-center">
          <div class="stat-icon bg-yellow-100 text-yellow-600">
            <Clock class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <div class="text-2xl font-bold text-gray-900">{{ stats.notReturned }}</div>
            <div class="text-sm text-gray-500">未返回</div>
          </div>
        </div>
      </div>

      <div class="stat-item">
        <div class="flex items-center">
          <div class="stat-icon bg-red-100 text-red-600">
            <Warning class="w-6 h-6" />
          </div>
          <div class="ml-4">
            <div class="text-2xl font-bold text-red-600">{{ stats.overdue }}</div>
            <div class="text-sm text-gray-500">逾期未返</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Calendar, Clock, Warning } from '@element-plus/icons-vue'

defineProps({
  stats: {
    type: Object,
    required: true,
    default: () => ({
      todayOut: 0,
      notReturned: 0,
      overdue: 0,
    }),
  },
})
</script>

<style scoped>
.outing-stats-container {
  transition: all 0.3s ease;
}

.outing-stats-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
