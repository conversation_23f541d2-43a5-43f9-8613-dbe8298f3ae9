<template>
  <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
    <!-- 区块标题 -->
    <div class="flex items-center mb-6 pb-3 border-b border-gray-200">
      <div class="flex items-center text-lg font-semibold text-gray-800">
        <el-icon v-if="iconComponent" class="mr-3 text-xl text-pink-500">
          <component :is="iconComponent" />
        </el-icon>
        <span>{{ title }}</span>
      </div>
    </div>

    <!-- 区块内容 -->
    <div class="form-section-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { User, StarFilled, House, Money, Document, List } from '@element-plus/icons-vue'

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    default: '',
  },
})

// 图标组件映射
const iconMap = {
  User,
  Heart: StarFilled,
  House,
  Money,
  Document,
  List,
}

const iconComponent = computed(() => {
  return props.icon ? iconMap[props.icon] : null
})
</script>

<style scoped>
.form-section {
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.form-section-content :deep(.el-form-item) {
  margin-bottom: 1.5rem;
}

.form-section-content :deep(.el-form-item__label) {
  color: rgb(55 65 81);
  font-weight: 500;
}

.form-section-content :deep(.el-input__wrapper) {
  transition: all 0.2s;
}

.form-section-content :deep(.el-input__wrapper:hover) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.form-section-content :deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.form-section-content :deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

.form-section-content :deep(.el-date-picker:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}
</style>
