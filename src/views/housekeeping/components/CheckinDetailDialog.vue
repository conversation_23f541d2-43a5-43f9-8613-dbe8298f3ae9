<template>
  <el-dialog
    v-model="visible"
    align-center
    title="入住详情"
    width="800px"
    :before-close="handleClose"
  >
    <div
      v-if="checkinDetail"
      class="max-h-[70vh] overflow-y-auto detail-content"
      v-loading="loading"
    >
      <!-- 客户信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">客户信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>入住编号：</label>
            <span class="font-mono">{{ checkinDetail.aid }}</span>
          </div>
          <div class="detail-item">
            <label>产妇姓名：</label>
            <span>{{ checkinDetail.maternity?.name }}</span>
          </div>
          <div class="detail-item">
            <label>联系电话：</label>
            <span>{{ checkinDetail.maternity?.phone }}</span>
          </div>
          <div class="detail-item">
            <label>年龄：</label>
            <span>{{ checkinDetail.maternity?.age }}岁</span>
          </div>
          <div class="detail-item">
            <label>民族：</label>
            <span>{{ checkinDetail.maternity?.ethnicity }}</span>
          </div>
          <div class="detail-item">
            <label>籍贯：</label>
            <span>{{ checkinDetail.maternity?.native_place }}</span>
          </div>
          <div class="detail-item">
            <label>血型：</label>
            <span>{{ getBloodTypeText(checkinDetail.maternity?.blood_type) }}</span>
          </div>
          <div class="detail-item">
            <label>入住状态：</label>
            <el-tag :type="getCheckInStatusTagType(checkinDetail.check_in_status)">
              {{ getCheckInStatusText(checkinDetail.check_in_status) }}
            </el-tag>
          </div>
          <div class="detail-item col-span-2">
            <label>身份证号：</label>
            <span class="font-mono">{{ checkinDetail.maternity?.identity_number }}</span>
          </div>
          <div class="detail-item col-span-2">
            <label>家庭住址：</label>
            <span>{{ checkinDetail.maternity?.home_address }}</span>
          </div>
          <div class="detail-item">
            <label>紧急联系人：</label>
            <span>{{ checkinDetail.maternity?.emergency_contact }}</span>
          </div>
          <div class="detail-item">
            <label>联系人电话：</label>
            <span>{{ checkinDetail.maternity?.emergency_contact_phone }}</span>
          </div>
          <div class="detail-item">
            <label>联系人关系：</label>
            <span>{{
              getEmergencyContactRelationText(checkinDetail.maternity?.emergency_contact_relation)
            }}</span>
          </div>
        </div>
      </div>

      <!-- 孕产信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">孕产信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>预产期：</label>
            <span>{{ checkinDetail.expected_delivery_date }}</span>
          </div>
          <div class="detail-item">
            <label>实际分娩日期：</label>
            <span>{{ checkinDetail.actual_delivery_date || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>分娩医院：</label>
            <span>{{ checkinDetail.delivery_hospital }}</span>
          </div>
          <div class="detail-item">
            <label>分娩方式：</label>
            <span>{{ getDeliveryMethodText(checkinDetail.delivery_method) }}</span>
          </div>
          <div class="detail-item">
            <label>孕周：</label>
            <span>{{ checkinDetail.pregnancy_week }}周</span>
          </div>
          <div class="detail-item">
            <label>过敏史：</label>
            <span>{{ checkinDetail.allergy_history || '无' }}</span>
          </div>
        </div>
      </div>

      <!-- 入住信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">入住信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>预计入住日期：</label>
            <span>{{ checkinDetail.expected_check_in_date }}</span>
          </div>
          <div class="detail-item">
            <label>实际入住日期：</label>
            <span>{{ checkinDetail.actual_check_in_date || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>房间信息：</label>
            <span v-if="checkinDetail.room">
              <el-tag type="success">{{ checkinDetail.room.room_number }}</el-tag>
              <span class="ml-2 text-sm text-gray-500">{{ checkinDetail.room.room_type }}</span>
            </span>
            <span v-else class="text-gray-400">未分配</span>
          </div>
          <div class="detail-item">
            <label>入住来源：</label>
            <span>{{ getCheckInSourceText(checkinDetail.check_in_source) }}</span>
          </div>
          <div class="detail-item">
            <label>主班护士：</label>
            <span>{{ checkinDetail.chief_nurse_name || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>需要关注：</label>
            <el-tag :type="checkinDetail.need_attention ? 'warning' : 'info'">
              {{ checkinDetail.need_attention ? '是' : '否' }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 新生儿信息 -->
      <div
        v-if="checkinDetail.baby_list && checkinDetail.baby_list.length > 0"
        class="detail-section mb-6"
      >
        <h3 class="section-title">新生儿信息</h3>
        <div
          v-for="(baby, index) in checkinDetail.baby_list"
          :key="index"
          class="p-4 bg-gray-50 rounded-lg mb-3 last:mb-0"
        >
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>姓名：</label>
              <span>{{ baby.name }}</span>
            </div>
            <div class="detail-item">
              <label>性别：</label>
              <span>{{ getGenderText(baby.gender) }}</span>
            </div>
            <div class="detail-item">
              <label>出生时间：</label>
              <span>{{ baby.birth_time }}</span>
            </div>
            <div class="detail-item">
              <label>出生体重：</label>
              <span>{{ baby.birth_weight }}g</span>
            </div>
            <div class="detail-item">
              <label>出生孕周：</label>
              <span>{{ baby.birth_week }}周</span>
            </div>
            <div class="detail-item">
              <label>身长：</label>
              <span>{{ baby.birth_length }}cm</span>
            </div>
            <div class="detail-item">
              <label>过敏史：</label>
              <span>{{ baby.allergy_history || '无' }}</span>
            </div>
            <div class="detail-item">
              <label>手牌号：</label>
              <span>{{ baby.hand_card_number || '无' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 费用信息 -->
      <div v-if="checkinDetail.cost_info" class="detail-section mb-6">
        <h3 class="section-title">费用信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>套餐：</label>
            <span>{{ checkinDetail.cost_info.package_details?.name }}</span>
          </div>
          <div class="detail-item">
            <label>套餐价格：</label>
            <span class="text-green-600 font-semibold"
              >¥{{ checkinDetail.cost_info.package_details?.price }}</span
            >
          </div>
          <div class="detail-item">
            <label>押金：</label>
            <span>¥{{ checkinDetail.cost_info.deposit_amount }}</span>
          </div>
          <div class="detail-item">
            <label>定金：</label>
            <span>¥{{ checkinDetail.cost_info.earnest_amount }}</span>
          </div>
          <div class="detail-item">
            <label>应付金额：</label>
            <span class="text-red-600 font-semibold"
              >¥{{ checkinDetail.cost_info.payable_amount }}</span
            >
          </div>
          <div class="detail-item">
            <label>已付金额：</label>
            <span class="text-green-600">¥{{ checkinDetail.cost_info.paid_amount }}</span>
          </div>
          <div class="detail-item">
            <label>剩余金额：</label>
            <span class="text-orange-600 font-semibold"
              >¥{{ checkinDetail.cost_info.remaining_amount }}</span
            >
          </div>
          <div class="detail-item">
            <label>支付方式：</label>
            <span
              v-if="
                Array.isArray(checkinDetail.cost_info.payment_method) &&
                checkinDetail.cost_info.payment_method.length > 0
              "
            >
              <el-tag
                v-for="method in checkinDetail.cost_info.payment_method"
                :key="method"
                type="info"
                class="mr-1"
              >
                {{ getPaymentMethodText(method) }}
              </el-tag>
            </span>
            <span v-else-if="checkinDetail.cost_info.payment_method">
              {{ getPaymentMethodText(checkinDetail.cost_info.payment_method) }}
            </span>
            <span v-else class="text-gray-400">未设置</span>
          </div>
          <div class="detail-item">
            <label>支付状态：</label>
            <el-tag :type="getPaymentStatusType(checkinDetail.cost_info.payment_status)">
              {{ checkinDetail.cost_info.payment_status }}
            </el-tag>
          </div>
          <div v-if="checkinDetail.cost_info.remark" class="detail-item col-span-2">
            <label>费用备注：</label>
            <span>{{ checkinDetail.cost_info.remark }}</span>
          </div>
        </div>
      </div>

      <!-- 合同信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">合同信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>合同编号：</label>
            <span class="font-mono">{{ checkinDetail.contract_number }}</span>
          </div>
          <div class="detail-item">
            <label>合同文件：</label>
            <div
              v-if="checkinDetail.contract_file_urls && checkinDetail.contract_file_urls.length > 0"
            >
              <FileDisplayList :file-list="checkinDetail.contract_file_urls" max-height="200px" />
            </div>
            <span v-else class="text-gray-400">无</span>
          </div>
          <div v-if="checkinDetail.contract_remark" class="detail-item col-span-2">
            <label>合同备注：</label>
            <span>{{ checkinDetail.contract_remark }}</span>
          </div>
        </div>
      </div>

      <!-- 出院记录 -->
      <div
        v-if="
          checkinDetail.discharge_records_file_urls &&
          checkinDetail.discharge_records_file_urls.length > 0
        "
        class="detail-section mb-6"
      >
        <h3 class="section-title">出院记录</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>出院记录文件：</label>
            <div>
              <FileDisplayList
                :file-list="checkinDetail.discharge_records_file_urls"
                max-height="200px"
              />
            </div>
          </div>
          <div v-if="checkinDetail.discharge_records_remark" class="detail-item">
            <label>记录备注：</label>
            <span>{{ checkinDetail.discharge_records_remark }}</span>
          </div>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="detail-section">
        <h3 class="section-title">其他信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>最后更新：</label>
            <span>{{ checkinDetail.relative_last_update }}</span>
          </div>
          <div class="detail-item">
            <label>创建时间：</label>
            <span>{{ checkinDetail.created_at }}</span>
          </div>
          <div class="detail-item">
            <label>更新时间：</label>
            <span>{{ checkinDetail.updated_at }}</span>
          </div>
          <div v-if="checkinDetail.maternity_center" class="detail-item">
            <label>月子中心：</label>
            <span>{{ checkinDetail.maternity_center.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <!-- <el-button
          v-if="checkinDetail?.check_in_status === 'RESERVED'"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          @click="handleCheckin"
        >
          办理入住
        </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request'
import {
  getGenderText,
  getBloodTypeText,
  getEmergencyContactRelationText,
  getDeliveryMethodText,
  getCheckInSourceText,
  getCheckInStatusText,
  getCheckInStatusTagType,
  getPaymentMethodText,
} from '@/utils/constants'
import FileDisplayList from '@/components/FileDisplayList.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  checkinId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'checkin', 'close'])

const loading = ref(false)
const checkinDetail = ref(null)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

watch(
  () => props.modelValue,
  async (isOpen) => {
    if (isOpen && props.checkinId) {
      await fetchCheckinDetail(props.checkinId)
    }
  },
)

const fetchCheckinDetail = async (checkinId) => {
  if (!checkinId) return

  loading.value = true
  try {
    const response = await get(`customer-service/maternity-admission/detail/${checkinId}/`)
    checkinDetail.value = response
  } catch (error) {
    console.error('获取入住详情失败:', error)
    ElMessage.error('获取入住详情失败')
    checkinDetail.value = null
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
  checkinDetail.value = null
}

const getPaymentStatusType = (status) => {
  const typeMap = {
    已全部支付: 'success',
    已部分支付: 'warning',
    未支付: 'danger',
  }
  return typeMap[status] || 'info'
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 6rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
