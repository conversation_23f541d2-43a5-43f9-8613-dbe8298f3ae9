<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  loading: { type: Boolean, default: false },
})
const emit = defineEmits(['update:visible', 'approve', 'reject'])

const localVisible = ref(props.visible)
watch(
  () => props.visible,
  (v) => (localVisible.value = v),
)
watch(localVisible, (v) => emit('update:visible', v))

const handleClose = () => {
  localVisible.value = false
}
const handleApprove = () => {
  emit('approve')
}
const handleReject = () => {
  emit('reject')
}
</script>

<template>
  <el-dialog
    :model-value="localVisible"
    title="访客预约审批"
    width="400px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="mb-4">
      <slot>
        <div>请确认是否通过该访客预约申请？</div>
      </slot>
    </div>
    <template #footer>
      <el-button @click="handleReject" :loading="loading" type="danger">拒绝</el-button>
      <el-button @click="handleApprove" :loading="loading" type="primary">通过</el-button>
    </template>
  </el-dialog>
</template>
