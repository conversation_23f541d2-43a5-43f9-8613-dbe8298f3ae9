<template>
  <div
    class="calendar-container bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm"
  >
    <!-- 日历头部 - 优化设计 -->
    <div
      class="calendar-header bg-gradient-to-r from-pink-50 to-rose-50 border-b border-gray-200 p-6"
    >
      <div class="flex justify-between items-center">
        <div class="header-info">
          <div class="header-title text-2xl font-bold text-gray-800 mb-2 flex items-center gap-2">
            房间入住日历
          </div>
          <div class="header-subtitle text-sm text-gray-600">实时查看房间时间段占用情况</div>
        </div>

        <!-- 状态图例 -->
        <StatusLegend :statuses="ROOM_STATUS_LEGEND_ORDER" />
      </div>
    </div>

    <div v-if="data.length > 0" class="flex bg-gray-50">
      <!-- 房间信息列 - 优化设计 -->
      <div class="w-80 flex flex-col bg-white border-r border-gray-200 shadow-sm">
        <div
          class="text-sm font-semibold text-gray-700 h-16 flex items-center justify-center border-b border-gray-200 bg-gray-50"
        >
          <span class="text-base">房间信息</span>
        </div>
        <div
          v-for="room in rooms"
          :key="room.id"
          class="h-40 border-b border-gray-200 p-4 hover:bg-gray-50 transition-colors duration-200 room-info-card"
        >
          <div class="h-full flex flex-col justify-center space-y-2">
            <!-- 房间号 -->
            <div class="flex items-center justify-between">
              <span class="text-xl font-bold text-gray-800">{{ room.room_number }}</span>
              <!-- 房间状态标识 -->
              <div class="flex items-center gap-1">
                <div
                  class="w-3 h-3 rounded-full"
                  :style="{ backgroundColor: getRoomStatusColor(room.room_status) }"
                ></div>
                <span class="text-xs text-gray-600">{{
                  getRoomStatusInfo(room.room_status).text
                }}</span>
              </div>
            </div>

            <!-- 房间类型 -->
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md">类型</span>
              <span class="text-sm font-medium text-gray-700">{{ room.room_type }}</span>
            </div>

            <!-- 面积信息 -->
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md">面积</span>
              <span class="text-sm text-gray-600">{{ room.area }}㎡</span>
            </div>

            <!-- 朝向信息 -->
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md">朝向</span>
              <span class="text-sm text-gray-600">{{ room.orientation }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 日历主体 - 优化设计 -->
      <div class="flex-1 overflow-x-auto min-w-0 bg-white">
        <div
          class="border-b border-gray-200"
          :style="{ minWidth: `${(rooms[0]?.daily_status?.length || 7) * 140}px` }"
        >
          <!-- 日期表头 - 美化设计 -->
          <div
            class="grid h-16 bg-gradient-to-r from-gray-50 to-gray-100"
            :style="{
              gridTemplateColumns: `repeat(${rooms[0]?.daily_status?.length || 7}, minmax(140px, 1fr))`,
            }"
          >
            <div
              v-for="(item, index) in rooms[0].daily_status"
              :key="item.date"
              class="border-r border-gray-200 flex flex-col gap-1 justify-center text-center relative group hover:bg-white transition-colors duration-200"
              :class="{ 'bg-pink-50 border-pink-200': isToday(item.date) }"
            >
              <span
                class="text-sm font-bold text-gray-800"
                :class="{ 'text-pink-600': isToday(item.date) }"
              >
                {{ formatDate(item.date) }}
              </span>
              <span class="text-xs text-gray-500" :class="{ 'text-pink-500': isToday(item.date) }">
                周{{ formatWeekday(item.date) }}
              </span>
              <!-- 今日标识 -->
              <!-- <div
                v-if="isToday(item.date)"
                class="absolute bottom-1 left-1/2 transform -translate-x-1/2"
              >
                <div class="w-2 h-2 bg-pink-500 rounded-full today-indicator"></div>
              </div> -->
            </div>
          </div>

          <!-- 状态行 - 优化视觉效果 -->
          <div class="">
            <div
              class="border-b border-gray-200 h-40 grid items-center relative group hover:bg-gray-50/50 transition-colors duration-200"
              :style="{
                gridTemplateColumns: `repeat(${room.daily_status.length}, minmax(140px, 1fr))`,
              }"
              v-for="room in rooms"
              :key="room.id"
            >
              <div
                v-for="(block, blockIndex) in getStatusBlocks(room.daily_status)"
                :key="block.startIndex"
                :class="[
                  'flex items-center justify-center text-sm font-medium text-white h-32 mx-1 my-2 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer status-block',
                  {
                    'rounded-l-lg': blockIndex === 0,
                    'rounded-r-lg': blockIndex === getStatusBlocks(room.daily_status).length - 1,
                    'rounded-lg': getStatusBlocks(room.daily_status).length === 1,
                  },
                ]"
                :style="{
                  gridColumnStart: block.startIndex + 1,
                  gridColumnEnd: block.endIndex + 2,
                  backgroundColor: getStatusConfig(block.status).color,
                  backgroundImage: `linear-gradient(135deg, ${getStatusConfig(block.status).color}, ${adjustBrightness(getStatusConfig(block.status).color, -10)})`,
                }"
                @click="handleBlockClick(room, block)"
              >
                <div class="text-center">
                  <div class="text-sm font-semibold mb-1">{{ block.statusDisplay }}</div>
                  <div class="text-xs opacity-90">{{ block.width }}天</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="empty-state text-center py-16 bg-gray-50">
      <div class="max-w-sm mx-auto">
        <div
          class="w-24 h-24 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center"
        >
          <svg
            class="w-12 h-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            ></path>
          </svg>
        </div>
        <div class="empty-text text-xl font-semibold text-gray-600 mb-2">暂无房间数据</div>
        <div class="empty-description text-sm text-gray-500">请添加房间或调整筛选条件</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import StatusLegend from '../../../components/housekeeping/StatusLegend.vue'
import { getRoomStatusInfo, getRoomStatusColor, ROOM_STATUS_LEGEND_ORDER } from '@/utils/constants'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
})

const rooms = computed(() => {
  const rooms = []
  props.data.forEach((floor) => {
    floor.rooms.forEach((room) => {
      rooms.push(room)
    })
  })
  return rooms
})

// 统一的状态配置 - 基于constants.js中的ROOM_STATUS_MAP
const getStatusConfig = (status) => {
  const statusInfo = getRoomStatusInfo(status)
  const color = getRoomStatusColor(status)

  // 将hex颜色转换为RGB用于tailwind样式
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null
  }

  const rgb = hexToRgb(color)
  const rgbString = rgb ? `${rgb.r}, ${rgb.g}, ${rgb.b}` : '107, 114, 128'

  return {
    text: statusInfo.text,
    color: color,
    blockClass: `text-white font-medium`,
    dotClass: `bg-[${color}]`,
    legendClass: `bg-[${color}]`,
    style: {
      backgroundColor: color,
      color: 'white',
    },
  }
}

const formatDate = (date) => {
  const d = new Date(date)
  return `${d.getMonth() + 1}/${d.getDate()}`
}

const formatWeekday = (date) => {
  const d = new Date(date)
  const weekdays = ['日', '一', '二', '三', '四', '五', '六']
  return weekdays[d.getDay()]
}

const isToday = (date) => {
  const today = new Date()
  const checkDate = new Date(date)
  return today.toDateString() === checkDate.toDateString()
}

const getStatusBlocks = (daily_status) => {
  const blocks = []
  let currentBlock = null
  let currentStartIndex = 0

  daily_status.forEach((item, index) => {
    if (item.status !== currentBlock) {
      if (currentBlock) {
        blocks.push({
          startIndex: currentStartIndex,
          endIndex: index - 1,
          status: currentBlock,
          statusDisplay: getStatusConfig(currentBlock).text,
          width: index - currentStartIndex,
        })
      }
      currentBlock = item.status
      currentStartIndex = index
    }
  })

  if (currentBlock) {
    blocks.push({
      startIndex: currentStartIndex,
      endIndex: daily_status.length - 1,
      status: currentBlock,
      statusDisplay: getStatusConfig(currentBlock).text,
      width: daily_status.length - currentStartIndex,
    })
  }

  return blocks
}

// 调整颜色亮度的辅助函数
const adjustBrightness = (hex, percent) => {
  const num = parseInt(hex.replace('#', ''), 16)
  const amt = Math.round(2.55 * percent)
  const R = (num >> 16) + amt
  const G = ((num >> 8) & 0x00ff) + amt
  const B = (num & 0x0000ff) + amt
  return (
    '#' +
    (
      0x1000000 +
      (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000 +
      (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100 +
      (B < 255 ? (B < 1 ? 0 : B) : 255)
    )
      .toString(16)
      .slice(1)
  )
}

// 处理时间块点击事件
const handleBlockClick = (room, block) => {
  console.log('点击了房间', room.room_number, '的', block.statusDisplay, '时间段')
  // 这里可以添加详情查看或编辑功能
}
</script>

<style scoped>
/* 自定义滚动条样式 */
.calendar-container ::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.calendar-container ::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

.calendar-container ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1, #94a3b8);
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.calendar-container ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8, #64748b);
}

/* 状态块悬停效果增强 */
.status-block {
  position: relative;
  overflow: hidden;
}

/* 房间信息卡片增强效果 */
.room-info-card {
  position: relative;
  transition: all 0.3s ease;
}

/* 今日标识动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.today-indicator {
  animation: pulse 2s infinite;
}
</style>
