<template>
  <el-dialog
    v-model="visible"
    align-center
    title="房间详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="roomDetail" class="detail-content max-h-[70vh] overflow-y-auto" v-loading="loading">
      <!-- 基本信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">基本信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>房间号：</label>
            <span class="font-mono">{{ roomDetail.room_number }}</span>
          </div>
          <div class="detail-item">
            <label>房间类型：</label>
            <span>{{ roomDetail.room_type }}</span>
          </div>
          <div class="detail-item">
            <label>房间状态：</label>
            <el-tag :type="getStatusType(roomDetail.room_status)">
              {{ getStatusLabel(roomDetail.room_status) }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>面积：</label>
            <span>{{ roomDetail.area }}㎡</span>
          </div>
          <div class="detail-item">
            <label>楼层：</label>
            <span>{{ roomDetail.floor }}</span>
          </div>
          <div class="detail-item">
            <label>朝向：</label>
            <span>{{ roomDetail.orientation }}</span>
          </div>
          <!-- 预计恢复时间 -->
          <div v-if="roomDetail.expected_recovery_time" class="detail-item">
            <label>预计恢复时间：</label>
            <span>{{ roomDetail.expected_recovery_time }}</span>
          </div>
          <div v-if="roomDetail.reason_remark" class="detail-item">
            <label>原因备注：</label>
            <span>{{ roomDetail.reason_remark }}</span>
          </div>
        </div>
      </div>

      <!-- 入住信息 -->
      <div v-if="roomDetail.customer_info" class="detail-section mb-6">
        <h3 class="section-title">入住信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>客户姓名：</label>
            <span>{{ roomDetail.customer_info.name }}</span>
          </div>
          <div class="detail-item">
            <label>联系电话：</label>
            <span>{{ roomDetail.customer_info.phone }}</span>
          </div>
          <div class="detail-item">
            <label>入住日期：</label>
            <span>{{ roomDetail.customer_info.check_in_date }}</span>
          </div>
          <div class="detail-item">
            <label>预计退房：</label>
            <span>{{ roomDetail.customer_info.expected_checkout_date }}</span>
          </div>
        </div>
      </div>

      <!-- 房间设施 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">房间设施</h3>
        <div class="facility-content p-4 bg-gray-50 rounded-lg">
          <div
            v-if="roomDetail.facility_list && roomDetail.facility_list.length > 0"
            class="flex flex-wrap gap-2"
          >
            <el-tag v-for="facility in roomDetail.facility_list" :key="facility" size="small">
              {{ facility }}
            </el-tag>
          </div>
          <div v-else class="text-gray-500">无设施信息</div>
        </div>
      </div>

      <!-- 状态历史记录 -->
      <div
        v-if="roomDetail.room_status_history && roomDetail.room_status_history.length > 0"
        class="detail-section mb-6"
      >
        <h3 class="section-title">状态历史记录</h3>
        <div class="history-content">
          <div
            v-for="(history) in getRecentHistory()"
            :key="history.id"
            class="p-4 bg-gray-50 rounded-lg mb-3 last:mb-0"
          >
            <div class="flex justify-between items-start mb-2">
              <div class="flex items-center gap-2">
                <el-tag :type="getStatusType(history.old_status)" size="small">
                  {{ getStatusLabel(history.old_status) }}
                </el-tag>
                <span class="text-gray-400">→</span>
                <el-tag :type="getStatusType(history.new_status)" size="small">
                  {{ getStatusLabel(history.new_status) }}
                </el-tag>
              </div>
              <span class="text-sm text-gray-500">{{ history.created_at }}</span>
            </div>
            <div v-if="history.reason_remark" class="text-sm text-gray-600">
              <span class="font-medium">原因：</span>{{ history.reason_remark }}
            </div>
            <div v-if="history.expected_recovery_time" class="text-sm text-gray-600 mt-1">
              <span class="font-medium">预计恢复时间：</span>{{ history.expected_recovery_time }}
            </div>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div v-if="roomDetail.description" class="detail-section">
        <h3 class="section-title">备注信息</h3>
        <div class="reason-content p-4 bg-gray-50 rounded-lg">
          {{ roomDetail.description }}
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          @click="handleEdit"
        >
          编辑房间
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { get } from '@/utils/request'
import { getRoomStatusInfo } from '@/utils/constants'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  roomId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit', 'close'])

// 响应式数据
const loading = ref(false)
const roomDetail = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听roomId变化，获取房间详情
// watch(
//   () => props.roomId,
//   async (newRoomId) => {
//     if (newRoomId && props.modelValue) {
//       await fetchRoomDetail(newRoomId)
//     }
//   },
//   { immediate: true },
// )

// 监听dialog打开状态
watch(
  () => props.modelValue,
  async (isOpen) => {
    if (isOpen && props.roomId) {
      await fetchRoomDetail(props.roomId)
    }
  },
)

// 获取房间详情
const fetchRoomDetail = async (roomId) => {
  if (!roomId) return

  loading.value = true
  try {
    const response = await get(`/customer-service/room/detail/${roomId}/`)
    roomDetail.value = response
  } catch (error) {
    console.error('获取房间详情失败:', error)
    roomDetail.value = null
  } finally {
    loading.value = false
  }
}

// 获取最近3条状态历史记录
const getRecentHistory = () => {
  if (!roomDetail.value?.room_status_history) return []
  return roomDetail.value.room_status_history.slice(0, 3)
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
  roomDetail.value = null
}

// 编辑房间
const handleEdit = () => {
  emit('edit', roomDetail.value)
  handleClose()
}

// 状态相关方法
const getStatusType = (status) => {
  const typeMap = {
    AVAILABLE: 'success',
    OCCUPIED: 'danger',
    CLEANING: 'warning',
    MAINTENANCE: 'info',
    RESERVED: 'primary',
    UNAVAILABLE_OTHER: 'danger',
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusInfo = getRoomStatusInfo(status)
  return statusInfo.text
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
}

.facility-content,
.reason-content {
  color: #374151;
  line-height: 1.625;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
