<template>
  <div class="outing-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Calendar />
          </el-icon>
          外出记录
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="data"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column type="index" label="序号" min-width="80" fixed="left" />

      <el-table-column prop="roomNumber" label="房间号" min-width="100">
        <template #default="{ row }">
          <span class="font-mono font-medium">{{ row.roomNumber }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="customerName" label="客户姓名" min-width="120" />

      <el-table-column prop="outTime" label="外出时间" min-width="160" />

      <el-table-column prop="expectedReturnTime" label="预计返回时间" min-width="160" />

      <el-table-column prop="actualReturnTime" label="实际返回时间" min-width="160">
        <template #default="{ row }">
          <span v-if="row.actualReturnTime">{{ row.actualReturnTime }}</span>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="reason" label="外出原因" min-width="120" />

      <el-table-column prop="status" label="状态" min-width="120">
        <template #default="{ row }">
          <el-tag
            :type="getStatusType(row.status)"
            :effect="row.status === 'RETURNED' ? 'plain' : 'dark'"
            size="small"
          >
            {{ row.statusText }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="320" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <!-- 编辑按钮 - 在PENDING和APPROVED状态下可编辑 -->
            <template v-if="row.status === 'PENDING' || row.status === 'APPROVED'">
              <el-button
                type="primary"
                size="small"
                @click.stop="$emit('edit', row)"
                class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
              >
                编辑
              </el-button>
            </template>

            <template v-if="row.status === 'PENDING'">
              <el-button
                type="success"
                size="small"
                @click.stop="$emit('approve', row)"
                class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600"
              >
                批准
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click.stop="$emit('reject', row)"
                class="bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600"
              >
                拒绝
              </el-button>
            </template>

            <template v-if="row.status === 'APPROVED' || row.status === 'OVERDUE'">
              <el-button
                type="warning"
                size="small"
                @click.stop="$emit('return', row)"
                class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
              >
                返回
              </el-button>
            </template>

            <el-button
              type="danger"
              size="small"
              @click.stop="handleDelete(row)"
              class="bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElButton,
  ElPagination,
  ElIcon,
  ElMessageBox,
  ElMessage,
} from 'element-plus'
import { Calendar } from '@element-plus/icons-vue'
import { del } from '@/utils/request.js'
import { getOutingStatusTagType } from '@/utils/constants.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      page_size: 10,
      total_count: 0,
      total_page: 1,
    }),
  },
})

const emit = defineEmits([
  'view',
  'edit',
  'approve',
  'reject',
  'return',
  'delete',
  'size-change',
  'current-change',
  'pagination-change',
  'row-click',
])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => props.pagination.total_count || 0)

// 同步分页状态
watch(
  () => props.pagination,
  (newPagination) => {
    currentPage.value = newPagination.page || 1
    pageSize.value = newPagination.page_size || 10
  },
  { immediate: true, deep: true },
)

const getStatusType = (status) => {
  return getOutingStatusTagType(status)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('pagination-change', { page, size: pageSize.value })
}

// 点击行
const handleRowClick = (row, column, event) => {
  // 避免在操作按钮列点击时触发行点击事件
  if (column && (column.property === 'actions' || !column.property)) {
    return
  }
  // 检查点击的元素是否是按钮或者按钮的子元素
  if (event.target.closest('.el-button') || event.target.closest('.action-buttons')) {
    return
  }
  emit('row-click', row)
}

// 删除外出记录
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除"${row.customerName}"的外出申请吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    )

    // 调用删除接口
    await del(`customer-service/outing-application/delete/${row.oid}/`)

    ElMessage.success('外出申请删除成功')
    emit('delete', row)
  } catch (error) {
    showErrorTip(error)
  }
}
</script>

<style scoped>
.outing-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.outing-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table th) {
  padding: 12px 0;
}

:deep(.el-table .el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  padding: 4px 12px;
  font-size: 12px;
  min-width: auto;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
