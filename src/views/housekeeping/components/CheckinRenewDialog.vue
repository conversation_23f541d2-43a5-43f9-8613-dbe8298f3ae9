<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="500px"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="checkin-renew-dialog"
  >
    <div v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="60px"
        class="checkin-renew-form"
      >
        <!-- 客户信息展示 -->
        <div class="customer-info mb-6">
          <h4 class="info-title">客户信息</h4>
          <div class="info-content">
            <p><span class="label">客户姓名：</span>{{ customerInfo.name || '-' }}</p>
            <p><span class="label">联系电话：</span>{{ customerInfo.phone || '-' }}</p>
            <p><span class="label">当前房间：</span>{{ customerInfo.room || '-' }}</p>
            <p><span class="label">入住状态：</span>{{ customerInfo.status || '-' }}</p>
          </div>
        </div>

        <!-- 操作表单 -->
        <el-form-item label="天数" prop="renew_days" required>
          <el-input-number
            v-model="form.renew_days"
            :min="1"
            :max="365"
            class="w-full"
            :precision="0"
            controls-position="right"
          />
          <div class="form-item-tip">{{ mode === 'checkin' ? '入住天数' : '续住天数' }}</div>
        </el-form-item>

        <el-form-item label="费用" prop="renew_price" required>
          <el-input-number
            v-model="form.renew_price"
            :min="0"
            :precision="2"
            class="w-full"
            controls-position="right"
          />
          <div class="form-item-tip">{{ mode === 'checkin' ? '入住费用' : '续住费用' }}</div>
        </el-form-item>

        <el-form-item label="备注" prop="renew_reason" required>
          <el-input
            v-model="form.renew_reason"
            type="textarea"
            :rows="3"
            :placeholder="mode === 'checkin' ? '请输入入住原因或备注' : '请输入续住原因或备注'"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ confirmButtonText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: 'checkin', // checkin 办理入住, renew 续住
  },
  checkinId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 表单引用
const formRef = ref()

// 加载状态
const loading = ref(false)
const confirmLoading = ref(false)

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 弹窗标题
const dialogTitle = computed(() => {
  const titleMap = {
    checkin: '办理入住',
    renew: '办理续住',
  }
  return titleMap[props.mode] || '办理入住'
})

// 确认按钮文字
const confirmButtonText = computed(() => {
  const textMap = {
    checkin: '确认入住',
    renew: '确认续住',
  }
  return textMap[props.mode] || '确认'
})

// 客户信息
const customerInfo = reactive({
  name: '',
  phone: '',
  room: '',
  status: '',
})

// 表单数据
const form = reactive({
  renew_days: null,
  renew_reason: '',
  renew_price: null,
})

// 表单验证规则
const formRules = {
  renew_days: [
    { required: true, message: '请输入天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '天数必须在1-365之间', trigger: 'blur' },
  ],
  renew_price: [
    { required: true, message: '请输入费用', trigger: 'blur' },
    { type: 'number', min: 0, message: '费用不能为负数', trigger: 'blur' },
  ],
  renew_reason: [
    { required: true, message: '请输入原因或备注', trigger: 'blur' },
    { min: 2, max: 200, message: '原因或备注长度应在2-200字符之间', trigger: 'blur' },
  ],
}

// 获取客户信息
const fetchCustomerInfo = async (checkinId) => {
  if (!checkinId) return

  loading.value = true
  try {
    const response = await get(`customer-service/maternity-admission/detail/${checkinId}/`)
    if (response) {
      customerInfo.name = response.maternity?.name || ''
      customerInfo.phone = response.maternity?.phone || ''
      customerInfo.room = response.room?.room_number || response.room || ''

      // 根据状态码转换为中文显示
      const statusMap = {
        RESERVED: '已预定',
        CHECKED_IN: '已入住',
        CHECKED_OUT: '已退房',
      }
      customerInfo.status = statusMap[response.check_in_status] || response.check_in_status
    }
  } catch (error) {
    console.error('获取客户信息失败:', error)
    ElMessage.error('获取客户信息失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = async () => {
  Object.assign(form, {
    renew_days: null,
    renew_reason: '',
    renew_price: null,
  })

  Object.assign(customerInfo, {
    name: '',
    phone: '',
    room: '',
    status: '',
  })

  await nextTick()
  formRef.value?.clearValidate()
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 确认操作
const handleConfirm = async () => {
  try {
    await formRef.value.validate()

    confirmLoading.value = true

    const requestData = {
      renew_days: form.renew_days,
      renew_reason: form.renew_reason,
      renew_price: form.renew_price,
    }

    let response
    if (props.mode === 'checkin') {
      // 办理入住
      response = await put(
        `customer-service/maternity-admission/check-in/${props.checkinId}/`,
        requestData,
      )
    } else {
      // 办理续住
      response = await put(
        `customer-service/maternity-admission/renew/${props.checkinId}/`,
        requestData,
      )
    }

    const successMessage = props.mode === 'checkin' ? '入住办理成功' : '续住办理成功'
    ElMessage.success(successMessage)

    emit('success', { mode: props.mode, data: requestData, response })
    dialogVisible.value = false
  } catch (error) {
    console.error('操作失败:', error)
    const errorMessage = props.mode === 'checkin' ? '入住办理失败' : '续住办理失败'
    ElMessage.error(errorMessage)
  } finally {
    confirmLoading.value = false
  }
}

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible && props.checkinId) {
      // 获取客户信息
      await fetchCustomerInfo(props.checkinId)

      // 根据模式设置默认值
      if (props.mode === 'checkin') {
        // form.renew_days = 28 // 默认入住28天
        // form.renew_reason = '正常入住'
      } else {
        // form.renew_days = 7 // 默认续住7天
        // form.renew_reason = '需要延长住院时间'
      }
    } else if (!visible) {
      // 关闭时重置表单
      await resetForm()
    }
  },
)
</script>

<style scoped>
.customer-info {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-title::before {
  content: '';
  width: 3px;
  height: 14px;
  background-color: #ec4899;
  margin-right: 8px;
  border-radius: 2px;
}

.info-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-content p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
}

.info-content .label {
  font-weight: 500;
  color: #374151;
}

.form-item-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

:deep(.el-input_wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-input-number .el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input-number .el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input-number .el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
