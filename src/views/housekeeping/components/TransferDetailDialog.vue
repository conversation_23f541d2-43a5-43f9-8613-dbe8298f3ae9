<template>
  <el-dialog
    v-model="visible"
    title="换房申请详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 申请信息 -->
        <div class="detail-section">
          <h3 class="section-title">申请信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>申请人：</label>
              <span>{{ detailData.maternity_name }}</span>
            </div>
            <div class="detail-item">
              <label>申请时间：</label>
              <span>{{ detailData.application_time }}</span>
            </div>
            <div class="detail-item">
              <label>审批状态：</label>
              <el-tag :type="getStatusType(detailData.approval_status)">
                {{ detailData.approval_status_label }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 房间信息 -->
        <div class="detail-section">
          <h3 class="section-title">房间信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>原房间：</label>
              <el-tag type="info">{{ detailData.source_room }}</el-tag>
            </div>
            <div class="detail-item">
              <label>目标房间：</label>
              <el-tag type="success">{{ detailData.intented_room }}</el-tag>
            </div>
          </div>

          <!-- 目标房间详情 -->
          <div v-if="detailData.intented_room_detail" class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 class="text-sm font-medium text-gray-700 mb-3">目标房间详情</h4>
            <div class="grid grid-cols-3 gap-4 text-sm">
              <div class="detail-item">
                <label>房间面积：</label>
                <span>{{ detailData.intented_room_detail.area }}㎡</span>
              </div>
              <div class="detail-item">
                <label>朝向：</label>
                <span>{{ detailData.intented_room_detail.orientation }}</span>
              </div>
              <div class="detail-item">
                <label>楼层：</label>
                <span>{{ detailData.intented_room_detail.floor }}楼</span>
              </div>
              <div class="detail-item">
                <label>房间状态：</label>
                <el-tag
                  size="small"
                  :type="getRoomStatusType(detailData.intented_room_detail.room_status)"
                >
                  {{ getRoomStatusLabel(detailData.intented_room_detail.room_status) }}
                </el-tag>
              </div>
            </div>

            <!-- 设施列表 -->
            <div v-if="detailData.intented_room_detail.facility_list?.length" class="mt-3">
              <label class="text-sm font-medium text-gray-700">房间设施：</label>
              <div class="mt-1 flex flex-wrap gap-1">
                <el-tag
                  v-for="facility in detailData.intented_room_detail.facility_list"
                  :key="facility"
                  size="small"
                  effect="plain"
                >
                  {{ facility }}
                </el-tag>
              </div>
            </div>

            <!-- 房间描述 -->
            <div v-if="detailData.intented_room_detail.description" class="mt-3">
              <label class="text-sm font-medium text-gray-700">房间描述：</label>
              <p class="mt-1 text-sm text-gray-600">
                {{ detailData.intented_room_detail.description }}
              </p>
            </div>
          </div>
        </div>

        <!-- 费用信息 -->
        <div class="detail-section">
          <h3 class="section-title">费用信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>差价说明：</label>
              <span>{{ detailData.price_difference_description }}</span>
            </div>
            <div class="detail-item">
              <label>差价金额：</label>
              <span class="text-lg font-semibold text-orange-600">
                ¥{{ detailData.price_difference }}
              </span>
            </div>
          </div>
        </div>

        <!-- 申请原因 -->
        <div class="detail-section">
          <h3 class="section-title">申请原因</h3>
          <div class="reason-content p-4 bg-gray-50 rounded-lg">
            {{ detailData.reason }}
          </div>
        </div>

        <!-- 房间状态历史 -->
        <div
          v-if="detailData.intented_room_detail?.room_status_history?.length"
          class="detail-section"
        >
          <h3 class="section-title">目标房间状态历史</h3>
          <div class="space-y-3">
            <div
              v-for="history in detailData.intented_room_detail.room_status_history"
              :key="history.id"
              class="p-3 bg-gray-50 rounded-lg"
            >
              <div class="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">时间：</span>
                  <span>{{ history.created_at }}</span>
                </div>
                <div>
                  <span class="text-gray-600">状态变更：</span>
                  <el-tag size="small" type="info">{{
                    getRoomStatusLabel(history.old_status)
                  }}</el-tag>
                  <span class="mx-2">→</span>
                  <el-tag size="small" type="warning">{{
                    getRoomStatusLabel(history.new_status)
                  }}</el-tag>
                </div>
                <div v-if="history.expected_recovery_time">
                  <span class="text-gray-600">预计恢复：</span>
                  <span>{{ history.expected_recovery_time }}</span>
                </div>
              </div>
              <div v-if="history.reason_remark" class="mt-2 text-sm text-gray-600">
                <span class="text-gray-600">备注：</span>{{ history.reason_remark }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="detailData?.approval_status === 'PENDING'"
          @click="handleProcess"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          处理申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { getTransferStatusTagType } from '@/utils/constants.js'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'process', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  }else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`customer-service/room-change-application/detail/${props.itemId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取换房申请详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 处理申请
const handleProcess = () => {
  emit('process', detailData.value)
}

// 状态相关方法 - 使用全局常量工具函数
const getStatusType = (status) => {
  return getTransferStatusTagType(status)
}

// 房间状态相关方法
const getRoomStatusType = (status) => {
  const statusMap = {
    AVAILABLE: 'success',
    OCCUPIED: 'warning',
    UNAVAILABLE_MAINTENANCE: 'danger',
    UNAVAILABLE_CLEANING: 'info',
    UNAVAILABLE_OTHER: 'info',
    UNAVAILABLE_SWITCH_ROOM: 'warning',
  }
  return statusMap[status] || 'info'
}

const getRoomStatusLabel = (status) => {
  const statusMap = {
    AVAILABLE: '可用',
    OCCUPIED: '已入住',
    UNAVAILABLE_MAINTENANCE: '维修中',
    UNAVAILABLE_CLEANING: '清洁中',
    UNAVAILABLE_OTHER: '其他不可用',
    UNAVAILABLE_SWITCH_ROOM: '换房中',
  }
  return statusMap[status] || status
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.reason-content {
  color: #374151;
  line-height: 1.625;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
