<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="transfer-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="transfer-form">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="客户选择" prop="maternity_admission">
              <el-select
                v-model="form.maternity_admission"
                placeholder="请选择客户"
                class="w-full"
                :disabled="!!props.itemId"
                :loading="
                  baseDataStore.newMothers.isLoading() || baseDataStore.newMothers.searchLoading
                "
                filterable
                remote
                :remote-method="baseDataStore.newMothers.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.newMothers.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.newMothers.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
              <div v-if="props.itemId" class="text-sm text-gray-500 mt-1">
                编辑模式下客户信息不可修改
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 换房信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">换房信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="目标房间" prop="intented_room">
              <el-select
                v-model="form.intented_room"
                placeholder="请选择目标房间"
                class="w-full"
                :loading="baseDataStore.rooms.isLoading() || baseDataStore.rooms.searchLoading"
                filterable
                remote
                :remote-method="baseDataStore.rooms.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.rooms.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.rooms.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="换房原因" prop="reason">
              <el-input
                v-model="form.reason"
                type="textarea"
                :rows="3"
                placeholder="请输入换房原因，如：房型升级、医生建议等"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 费用信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">费用信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="价格差额" prop="price_difference">
              <el-input-number
                v-model="form.price_difference"
                :precision="2"
                :step="100"
                placeholder="0.00"
                class="w-full"
              />
              <div class="text-sm text-gray-500 mt-1">正数表示需补交费用，负数表示退还费用</div>
            </el-form-item>

            <el-form-item label="价格说明" prop="price_difference_description">
              <el-input
                v-model="form.price_difference_description"
                type="textarea"
                :rows="3"
                placeholder="请详细说明费用差额的计算依据和说明"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '提交申请' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElInputNumber,
  ElButton,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 基础数据store
const baseDataStore = useBaseDataStore()

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑换房申请' : '新增换房申请'
})

// 表单数据
const form = reactive({
  rid: '',
  maternity_admission: '',
  intented_room: '',
  reason: '',
  price_difference: 0,
  price_difference_description: '',
})

// 表单验证规则
const rules = computed(() => ({
  maternity_admission: !props.itemId
    ? [{ required: true, message: '请选择客户', trigger: 'change' }]
    : [], // 编辑模式下不验证客户选择
  intented_room: [{ required: true, message: '请选择目标房间', trigger: 'change' }],
  reason: [{ required: true, message: '请输入换房原因', trigger: 'blur' }],
  price_difference: [{ required: true, message: '请输入价格差额', trigger: 'blur' }],
  price_difference_description: [{ required: true, message: '请输入价格说明', trigger: 'blur' }],
}))

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(resetForm, 5)
    if (visible) {
      if (props.itemId) {
        // 编辑模式：获取详情数据
        await fetchTransferDetail(props.itemId)
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 获取换房申请详情数据（用于编辑模式）
const fetchTransferDetail = async (transferId) => {
  if (!transferId) return

  loading.value = true
  try {
    const response = await get(`customer-service/room-change-application/detail/${transferId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到换房申请详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取换房申请详情失败:', error)
    ElMessage.error('获取换房申请详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    rid: apiData.rid,
    maternity_admission: apiData.maternity_admission || '', // 编辑模式下可能不存在此字段
    intented_room: apiData.intented_room_detail?.id || '',
    reason: apiData.reason,
    price_difference: parseFloat(apiData.price_difference) || 0,
    price_difference_description: apiData.price_difference_description,
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    rid: '',
    maternity_admission: '',
    intented_room: '',
    reason: '',
    price_difference: 0,
    price_difference_description: '',
  })

  // 重置搜索状态
  baseDataStore.newMothers.clearSearch()
  baseDataStore.rooms.clearSearch()

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      intented_room: form.intented_room,
      reason: form.reason,
      price_difference: form.price_difference,
      price_difference_description: form.price_difference_description,
    }

    let res
    if (!props.itemId) {
      res = await post(
        `customer-service/room-change-application/create/${form.maternity_admission}/`,
        submitData,
      )
    } else {
      res = await put(`customer-service/room-change-application/update/${form.rid}/`, submitData)
    }

    ElMessage.success(props.itemId ? '换房申请更新成功' : '换房申请提交成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
