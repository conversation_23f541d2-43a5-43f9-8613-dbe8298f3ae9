<template>
  <el-dialog
    v-model="visible"
    align-center
    title="外出记录详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="outingDetail" class="detail-content" v-loading="loading">
      <!-- 基本信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">基本信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>房间号：</label>
            <span class="font-mono">{{ outingDetail.room_number }}</span>
          </div>
          <div class="detail-item">
            <label>客户姓名：</label>
            <span>{{ outingDetail.maternity_name }}</span>
          </div>
          <div class="detail-item">
            <label>年龄：</label>
            <span>{{ outingDetail.maternity_age }}岁</span>
          </div>
          <div class="detail-item">
            <label>外出原因：</label>
            <span>{{ outingDetail.outing_reason }}</span>
          </div>
          <div class="detail-item">
            <label>审批状态：</label>
            <el-tag :type="getApprovalStatusType(outingDetail.approval_status)">
              {{ outingDetail.approval_status_label }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>外出状态：</label>
            <el-tag :type="getOutingStatusType(outingDetail.outing_status)">
              {{ outingDetail.outing_status_label }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">时间信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>申请时间：</label>
            <span>{{ outingDetail.application_time }}</span>
          </div>
          <div class="detail-item">
            <label>外出时间：</label>
            <span>{{ outingDetail.outing_time }}</span>
          </div>
          <div class="detail-item">
            <label>预计返回时间：</label>
            <span>{{ outingDetail.expected_return_time }}</span>
          </div>
          <div class="detail-item">
            <label>实际返回时间：</label>
            <span v-if="outingDetail.actual_return_time">{{
              outingDetail.actual_return_time
            }}</span>
            <span v-else class="text-gray-400">未返回</span>
          </div>
          <div class="detail-item">
            <label>审批时间：</label>
            <span v-if="outingDetail.approval_time">{{ outingDetail.approval_time }}</span>
            <span v-else class="text-gray-400">-</span>
          </div>
          <div class="detail-item">
            <label>外出时长：</label>
            <span>{{ getOutingDuration() }}</span>
          </div>
        </div>
      </div>

      <!-- 审批信息 -->
      <div class="detail-section mb-6">
        <h3 class="section-title">审批信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div class="detail-item">
            <label>审批人：</label>
            <span>{{ outingDetail.approval_by || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>是否需要陪同：</label>
            <el-tag :type="outingDetail.need_accompany ? 'warning' : 'info'" size="small">
              {{ outingDetail.need_accompany ? '是' : '否' }}
            </el-tag>
          </div>
          <div class="detail-item">
            <label>陪同人员：</label>
            <span>{{ outingDetail.accompany_staff_name || '-' }}</span>
          </div>
          <div class="detail-item">
            <label>紧急联系电话：</label>
            <span>{{ outingDetail.emergency_contact_phone || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div v-if="outingDetail.remark" class="detail-section">
        <h3 class="section-title">备注信息</h3>
        <div class="reason-content p-4 bg-gray-50 rounded-lg">
          {{ outingDetail.remark }}
          <template v-if="outingDetail.return_remark">
            <div class="mt-2 border-t border-gray-200 pt-2">
              <span class="text-gray-500">返回备注：</span>{{ outingDetail.return_remark }}
            </div>
          </template>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <!-- <el-button
          v-if="outingDetail?.approval_status === 'PENDING'"
          type="success"
          @click="handleApprove"
          class="bg-green-500 border-green-500 hover:bg-green-600 hover:border-green-600"
        >
          批准申请
        </el-button>
        <el-button
          v-if="outingDetail?.approval_status === 'PENDING'"
          type="danger"
          @click="handleReject"
          class="bg-red-500 border-red-500 hover:bg-red-600 hover:border-red-600"
        >
          拒绝申请
        </el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { get } from '@/utils/request'
import { getAuditStatusTagType, getOutingCurrentStatusTagType } from '@/utils/constants.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  outingId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'approve', 'reject', 'close'])

// 响应式数据
const loading = ref(false)
const outingDetail = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听dialog打开状态
watch(
  () => props.modelValue,
  async (isOpen) => {
    if (isOpen && props.outingId) {
      await fetchOutingDetail(props.outingId)
    }
  },
)

// 获取外出申请详情
const fetchOutingDetail = async (outingId) => {
  if (!outingId) return

  loading.value = true
  try {
    const response = await get(`customer-service/outing-application/detail/${outingId}/`)
    outingDetail.value = response
  } catch (error) {
    console.error('获取外出申请详情失败:', error)
    outingDetail.value = null
  } finally {
    loading.value = false
  }
}

// 获取外出时长
const getOutingDuration = () => {
  if (!outingDetail.value) return '-'

  const outTime = new Date(outingDetail.value.outing_time)
  const returnTime = outingDetail.value.actual_return_time
    ? new Date(outingDetail.value.actual_return_time)
    : new Date()

  const duration = Math.abs(returnTime - outTime)
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))

  return `${hours}小时${minutes}分钟`
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
  outingDetail.value = null
}

// 状态相关方法 - 使用全局常量工具函数
const getApprovalStatusType = (status) => {
  return getAuditStatusTagType(status)
}

const getOutingStatusType = (status) => {
  return getOutingCurrentStatusTagType(status)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
}

.reason-content {
  color: #374151;
  line-height: 1.625;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
