<template>
  <el-dialog
    v-model="visible"
    title="换房申请审核"
    width="500px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="transfer-process-dialog"
  >
    <template #header>
      <div class="dialog-header flex items-center">
        <el-icon class="mr-3 text-pink-500 text-xl">
          <Edit />
        </el-icon>
        <div>
          <h3 class="text-lg font-semibold text-gray-800">换房申请审核</h3>
        </div>
      </div>
    </template>

    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="audit-form"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="formData.result" class="w-full">
            <el-radio :value="true" class="">
              <span class="text-green-600">同意</span>
            </el-radio>
            <el-radio :value="false">
              <span class="text-red-600">拒绝</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审核意见" prop="opinion">
          <el-input
            v-model="formData.opinion"
            type="textarea"
            :rows="4"
            placeholder="请填写审核意见或处理原因..."
            class="w-full"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose"> 取消 </el-button>
        <el-button
          @click="handleSubmit"
          type="primary"
          :loading="loading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          提交审核
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import request from '@/utils/request'

const emit = defineEmits(['update:modelValue', 'success', 'close'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  applicationId: {
    type: [String, Number],
    default: null,
  },
})

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  result: null,
  opinion: '',
})

// 表单验证规则
const formRules = {
  result: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  opinion: [{ required: true, message: '请填写审核意见', trigger: 'blur' }],
}

// 监听对话框打开，重置表单
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      formData.result = null
      formData.opinion = ''
    }
  },
)

// 提交审核
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (!props.applicationId) {
      ElMessage.error('申请ID不能为空')
      return
    }

    loading.value = true

    // 调用API
    await request.put(`customer-service/room-change-application/audit/${props.applicationId}/`, {
      result: formData.result,
      opinion: formData.opinion,
    })

    ElMessage.success('审核提交成功')
    emit('success', {
      result: formData.result,
      opinion: formData.opinion,
    })

    visible.value = false
  } catch (error) {
    console.error('审核提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (loading.value) {
    ElMessage.warning('操作进行中，请稍候...')
    return
  }

  visible.value = false
  emit('close')
}
</script>

<style scoped>
.transfer-process-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-header {
  padding: 0;
}

.dialog-content {
  padding: 1rem 0;
}

.audit-form {
  padding: 0 1rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 0;
}

:deep(.el-form-item__label) {
  color: rgb(55 65 81);
  font-weight: 500;
}

:deep(.el-radio__label) {
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 0;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
  padding-bottom: 0;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}
</style>
