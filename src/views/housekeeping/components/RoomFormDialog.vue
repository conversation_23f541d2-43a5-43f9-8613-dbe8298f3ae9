<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="room-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="room-form">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="房号" prop="room_number">
              <el-input v-model="formData.room_number" placeholder="请输入房号" />
            </el-form-item>
            <el-form-item label="房间类型" prop="room_type">
              <el-input v-model="formData.room_type" placeholder="请输入房间类型" />
            </el-form-item>
            <el-form-item label="楼层/区域" prop="floor">
              <el-input v-model="formData.floor" placeholder="请输入楼层/区域" />
            </el-form-item>
            <el-form-item label="面积 (m²)" prop="area">
              <el-input-number
                v-model="formData.area"
                :min="0"
                placeholder="请输入面积"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="朝向" prop="orientation">
              <el-input v-model="formData.orientation" placeholder="如：南向、北向等" />
            </el-form-item>
            <!-- <el-form-item label="价格 (日/套餐)" prop="price">
              <el-input v-model="formData.price" placeholder="请输入价格">
                <template #append>元</template>
              </el-input>
            </el-form-item> -->
          </div>
        </div>

        <!-- 设施信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基础设施清单</h4>
          <el-form-item prop="facility_list">
            <el-checkbox-group v-model="formData.facility_list" class="facilities-group">
              <el-checkbox v-for="facility in FACILITY_OPTIONS" :key="facility" :value="facility">
                {{ facility }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <!-- 描述信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">详细描述</h4>
          <el-form-item label="房间描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="3"
              placeholder="请输入房间的详细描述..."
            />
          </el-form-item>
        </div>

        <!-- 状态设置 -->
        <div class="form-section mb-6" v-if="mode === 'add'">
          <h4 class="section-title">初始状态</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="房间状态" prop="room_status">
              <el-select v-model="formData.room_status" placeholder="请选择初始状态" class="w-full">
                <el-option
                  v-for="option in ROOM_STATUS_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '添加房间' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElSelect,
  ElOption,
  ElButton,
  ElCheckboxGroup,
  ElCheckbox,
} from 'element-plus'
import { ROOM_STATUS_OPTIONS, FACILITY_OPTIONS } from '@/utils/constants.js'
import { post, put } from '@/utils/request.js'
import { useBaseDataStore } from '@/stores/baseData'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  roomData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const formData = reactive({
  rid: '',
  room_number: '',
  room_type: '',
  floor: '',
  area: null,
  orientation: '',
  price: '',
  facility_list: [],
  description: '',
  room_status: 'AVAILABLE',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新增房间' : '编辑房间信息'
})

// 表单验证规则
const rules = {
  room_number: [{ required: true, message: '请输入房号', trigger: 'blur' }],
  room_type: [{ required: true, message: '请输入房间类型', trigger: 'blur' }],
  floor: [{ required: true, message: '请输入楼层/区域', trigger: 'blur' }],
  area: [{ required: true, message: '请输入房间面积', trigger: 'blur' }],
}

// 监听房间数据变化
watch(
  () => props.roomData,
  (newData) => {
    if (newData) {
      // 编辑模式，填充数据
      Object.assign(formData, {
        ...newData,
        facility_list: newData.facility_list,
      })
    }
  },
  { immediate: true },
)

// 方法
const handleOpen = () => {
  if (props.mode === 'add') {
    resetForm()
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    rid: '',
    room_number: '',
    room_type: '',
    floor: '',
    area: null,
    orientation: '',
    price: '',
    facility_list: [],
    description: '',
    room_status: 'AVAILABLE',
  })
  formRef.value?.clearValidate()
}

const handleSave = async () => {
  try {
    await formRef.value.validate()

    saving.value = true

    const saveData = {
      room_status: formData.room_status,
      room_number: formData.room_number,
      room_type: formData.room_type,
      area: formData.area,
      orientation: formData.orientation,
      facility_list: formData.facility_list,
      description: formData.description,
      floor: formData.floor,
    }
    let res
    if (props.mode === 'add') {
      res = await post('customer-service/room/create/', saveData)
    } else {
      res = await put(`customer-service/room/update/${formData.rid}/`, saveData)
    }
    // 更新房间选项缓存
    const baseDataStore = useBaseDataStore()
    await baseDataStore.rooms.fetch(true)
    console.log('res', res)
    emit('save', res)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.facilities-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
