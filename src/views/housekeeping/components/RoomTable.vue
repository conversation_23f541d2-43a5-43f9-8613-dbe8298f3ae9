<template>
  <div class="room-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <House />
          </el-icon>
          房间列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 个房间</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="rooms"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="room_number" label="房号" width="80" fixed="left">
        <template #default="{ row }">
          <span class="font-mono font-medium">{{ row.room_number }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="room_type" label="房间类型" width="120">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-icon class="mr-2 text-pink-500">
              <House />
            </el-icon>
            <span class="font-medium">{{ row.room_type }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="floorText" label="所属楼层/区域" width="140">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.floorText }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="area" label="面积 (m²)" width="100" align="center">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.area }}m²</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="orientation" label="朝向" width="100" align="center">
      </el-table-column>

      <el-table-column prop="facilities" label="基础设施描述" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.facilities }}</span>
        </template>
      </el-table-column>

      <el-table-column label="当前状态" width="140">
        <template #default="{ row }">
          <el-tag
            :type="getStatusType(row.room_status)"
            :effect="row.room_status === 'AVAILABLE' ? 'plain' : 'dark'"
            size="small"
          >
            {{ row.statusText }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="备注" min-width="150" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.description || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column
        v-permission="'cus.housekeeping.edit'"
        label="操作"
        width="280"
        fixed="right"
      >
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click.stop="handleEdit(row)"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click.stop="handleChangeStatus(row)"
              class="bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
            >
              修改状态
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click.stop="handleDelete(row)"
              class="bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElButton,
  ElPagination,
  ElIcon,
  ElMessageBox,
  ElMessage,
} from 'element-plus'
import { House } from '@element-plus/icons-vue'
import { getStatusTagType } from '@/utils/constants.js'
import { del } from '@/utils/request.js'

// 定义属性
const props = defineProps({
  rooms: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      page_size: 10,
      total_count: 0,
      total_page: 1,
    }),
  },
})

// 定义事件
const emit = defineEmits(['edit', 'change-status', 'delete', 'pagination-change', 'row-click'])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => props.pagination.total_count || 0)

// 同步分页状态
watch(
  () => props.pagination,
  (newPagination) => {
    currentPage.value = newPagination.page || 1
    pageSize.value = newPagination.page_size || 10
  },
  { immediate: true, deep: true },
)

// 获取状态标签类型
const getStatusType = getStatusTagType

// 编辑房间
const handleEdit = (room) => {
  emit('edit', room)
}

// 修改状态
const handleChangeStatus = (room) => {
  emit('change-status', room)
}

// 删除房间
const handleDelete = async (room) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除房间 "${room.room_number}" 吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      },
    )

    // 调用删除接口
    await del(`customer-service/room/delete/${room.rid}/`)

    ElMessage.success('房间删除成功')
    emit('delete', room)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除房间失败:', error)
      ElMessage.error('删除房间失败，请稍后重试')
    }
  }
}

// 点击行
const handleRowClick = (row, column, event) => {
  // 避免在操作按钮列点击时触发行点击事件
  if (column && (column.property === 'actions' || !column.property)) {
    return
  }
  // 检查点击的元素是否是按钮或者按钮的子元素
  if (event.target.closest('.el-button') || event.target.closest('.action-buttons')) {
    return
  }
  emit('row-click', row)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('pagination-change', { page, size: pageSize.value })
}
</script>

<style scoped>
.room-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.room-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}
</style>
