<template>
  <el-dialog
    v-model="visible"
    title="访客详情"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="visitor-detail-dialog"
  >
    <div v-loading="loading" class="max-h-[70vh] overflow-y-auto">
      <div v-if="visitorData" class="visitor-detail-content">
        <!-- 访客基本信息 -->
        <div class="detail-section">
          <h4 class="section-title">访客基本信息</h4>
          <div class="section-content">
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">访客姓名</div>
                <div class="detail-value">{{ visitorData.visitor_name || '-' }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">联系电话</div>
                <div class="detail-value">{{ visitorData.visitor_phone || '-' }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">身份证号</div>
                <div class="detail-value">{{ visitorData.visitor_identity_number || '-' }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">访客人数</div>
                <div class="detail-value">{{ visitorData.visitor_count || 1 }}人</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 来访信息 -->
        <div class="detail-section">
          <h4 class="section-title">来访信息</h4>
          <div class="section-content">
            <div
              v-if="
                visitorData.visited_maternity_info &&
                Object.keys(visitorData.visited_maternity_info).length > 0
              "
              class="visited-info-card"
            >
              <div class="visited-info-header">
                <h5 class="visited-info-title">被访者详细信息</h5>
              </div>
              <div class="visited-info-content">
                <div class="visited-detail-item">
                  <span class="visited-label">姓名</span>
                  <span class="visited-value">{{
                    visitorData.visited_maternity_info.name || '-'
                  }}</span>
                </div>
                <div class="visited-detail-item">
                  <span class="visited-label">房间</span>
                  <span class="visited-value">{{
                    visitorData.visited_maternity_info.room || '-'
                  }}</span>
                </div>
                <div class="visited-detail-item">
                  <span class="visited-label">电话</span>
                  <span class="visited-value">{{
                    visitorData.visited_maternity_info.phone || '-'
                  }}</span>
                </div>
                <div class="visited-detail-item">
                  <span class="visited-label">性别</span>
                  <span class="visited-value">{{
                    visitorData.visited_maternity_info.gender || '-'
                  }}</span>
                </div>
                <div class="visited-detail-item">
                  <span class="visited-label">年龄</span>
                  <span class="visited-value">{{
                    visitorData.visited_maternity_info.age || '-'
                  }}</span>
                </div>
                <div class="visited-detail-item">
                  <span class="visited-label">生日</span>
                  <span class="visited-value">{{
                    visitorData.visited_maternity_info.birth_date || '-'
                  }}</span>
                </div>
              </div>
            </div>

            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">来访事由</div>
                <div class="detail-value">
                  <el-tag type="info" size="small">{{ visitorData.visit_purpose || '-' }}</el-tag>
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">访问状态</div>
                <div class="detail-value">
                  <el-tag :type="getStatusType(visitorData.visit_status)" size="small">
                    {{ visitorData.visit_status_display || '-' }}
                  </el-tag>
                </div>
              </div>
              <div v-if="visitorData.appointment_remark" class="detail-item wide">
                <div class="detail-label">预约备注</div>
                <div class="detail-value">{{ visitorData.appointment_remark }}</div>
              </div>
              <div v-if="visitorData.visit_result_remark" class="detail-item wide">
                <div class="detail-label">访问结果备注</div>
                <div class="detail-value">{{ visitorData.visit_result_remark }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 健康与安全信息 -->
        <div class="detail-section">
          <h4 class="section-title">健康与安全信息</h4>
          <div class="section-content">
            <div class="health-grid">
              <div class="detail-item">
                <div class="detail-label">体温 (°C)</div>
                <div class="detail-value">
                  <span
                    class="temperature-value"
                    :class="getTemperatureClass(visitorData.temperature)"
                  >
                    {{ visitorData.temperature }}°C
                  </span>
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">发热症状</div>
                <div class="detail-value">
                  <el-tag :type="visitorData.fever_symptoms ? 'danger' : 'success'" size="small">
                    {{ visitorData.fever_symptoms ? '是' : '否' }}
                  </el-tag>
                </div>
              </div>
              <div class="detail-item">
                <div class="detail-label">接触病例</div>
                <div class="detail-value">
                  <el-tag :type="visitorData.contact_with_case ? 'danger' : 'success'" size="small">
                    {{ visitorData.contact_with_case ? '是' : '否' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 访问时间 -->
        <div class="detail-section">
          <h4 class="section-title">访问时间</h4>
          <div class="section-content">
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">来访时间</div>
                <div class="detail-value">{{ formatDateTime(visitorData.visit_time) }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">预计离开时间</div>
                <div class="detail-value">
                  {{ formatDateTime(visitorData.expected_departure_time) }}
                </div>
              </div>
              <div v-if="visitorData.actual_departure_time" class="detail-item wide">
                <div class="detail-label">实际离开时间</div>
                <div class="detail-value">
                  {{ formatDateTime(visitorData.actual_departure_time) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="detail-section">
          <h4 class="section-title">系统信息</h4>
          <div class="section-content">
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-label">访客编号</div>
                <div class="detail-value visitor-id">{{ visitorData.vid || '-' }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">创建时间</div>
                <div class="detail-value">{{ formatDateTime(visitorData.created_at) }}</div>
              </div>
              <div class="detail-item wide">
                <div class="detail-label">更新时间</div>
                <div class="detail-value">{{ formatDateTime(visitorData.updated_at) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="visitorData && visitorData.visit_status !== 'LEAVED'"
          type="primary"
          @click="handleEdit"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          编辑信息
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElDialog, ElTag, ElButton } from 'element-plus'
import { getVisitorStatusTagType } from '@/utils/constants.js'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  visitorData: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'edit'])

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

// 获取状态标签类型
const getStatusType = (status) => {
  return getVisitorStatusTagType(status)
}

// 获取体温颜色类
const getTemperatureClass = (temperature) => {
  const temp = parseFloat(temperature)
  if (temp >= 37.3) {
    return 'text-red-600'
  } else if (temp >= 37.0) {
    return 'text-orange-600'
  }
  return 'text-green-600'
}

// 格式化完整日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'
  const date = new Date(dateTimeStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
}

// 编辑信息
const handleEdit = () => {
  emit('edit', props.visitorData)
  visible.value = false
}
</script>

<style scoped>
.visitor-detail-dialog :deep(.el-dialog__body) {
  padding: 32px;
  background: #fafafa;
}

.visitor-detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.detail-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f3f4f6;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #ec4899;
  margin-right: 10px;
  border-radius: 2px;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 24px;
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 20px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-item.wide {
  grid-column: span 2;
}

.detail-label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.detail-value {
  font-size: 14px;
  color: #374151;
  word-break: break-all;
  line-height: 1.5;
}

.temperature-value {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  font-size: 15px;
}

.visitor-id {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  color: #6b7280;
  background: #f9fafb;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
  width: fit-content;
}

.visited-info-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.visited-info-header {
  background: #ec4899;
  padding: 12px 16px;
}

.visited-info-title {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.visited-info-content {
  padding: 16px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px 16px;
}

.visited-detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.visited-label {
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  white-space: nowrap;
}

.visited-value {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}
</style>
