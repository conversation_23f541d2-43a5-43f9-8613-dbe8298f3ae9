<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="outing-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="outing-form">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="客户选择" prop="maternity_admission">
              <el-select
                v-model="form.maternity_admission"
                placeholder="请选择客户"
                class="w-full"
                :loading="
                  baseDataStore.newMothers.isLoading() || baseDataStore.newMothers.searchLoading
                "
                filterable
                remote
                :remote-method="baseDataStore.newMothers.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.newMothers.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.newMothers.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 时间安排 -->
        <div class="form-section mb-6">
          <h4 class="section-title">时间安排</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="外出时间" prop="outing_time">
              <el-date-picker
                v-model="form.outing_time"
                type="datetime"
                placeholder="选择外出时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="w-full"
              />
            </el-form-item>

            <el-form-item label="预计返回时间" prop="expected_return_time">
              <el-date-picker
                v-model="form.expected_return_time"
                type="datetime"
                placeholder="选择预计返回时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 外出详情 -->
        <div class="form-section mb-6">
          <h4 class="section-title">外出详情</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="外出原因" prop="outing_reason">
              <el-input
                v-model="form.outing_reason"
                placeholder="请输入外出原因，如：产后检查复诊"
              />
            </el-form-item>

            <el-form-item label="是否需要陪同">
              <el-switch
                v-model="form.need_accompany"
                active-text="需要陪同"
                inactive-text="无需陪同"
              />
            </el-form-item>

            <el-form-item v-if="form.need_accompany" label="陪同人员" prop="accompany_staff">
              <el-select
                v-model="form.accompany_staff"
                placeholder="请选择陪同人员"
                class="w-full"
                :loading="baseDataStore.nurses.isLoading() || baseDataStore.nurses.searchLoading"
                filterable
                remote
                :remote-method="baseDataStore.nurses.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                @clear="baseDataStore.nurses.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.nurses.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="紧急联系电话" prop="emergency_contact_phone">
              <el-input v-model="form.emergency_contact_phone" placeholder="请输入紧急联系电话" />
            </el-form-item>
          </div>
        </div>

        <!-- 备注说明 -->
        <div class="form-section mb-6">
          <h4 class="section-title">备注说明</h4>
          <el-form-item label="备注说明" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注说明，如：需要带护照和医保卡，有轻度恶露情况"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '提交申请' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElSwitch,
  ElButton,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 基础数据store
const baseDataStore = useBaseDataStore()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'add' ? '填写外出单' : '编辑外出单'
})

// 表单数据
const form = reactive({
  oid: '',
  maternity_admission: '',
  outing_time: '',
  expected_return_time: '',
  outing_reason: '',
  need_accompany: false,
  accompany_staff: null,
  emergency_contact_phone: '',
  remark: '',
})

// 表单验证规则
const rules = {
  maternity_admission: [{ required: true, message: '请选择客户', trigger: 'change' }],
  outing_time: [{ required: true, message: '请选择外出时间', trigger: 'change' }],
  expected_return_time: [
    { required: true, message: '请选择预计返回时间', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && form.outing_time && new Date(value) <= new Date(form.outing_time)) {
          callback(new Error('预计返回时间必须晚于外出时间'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  outing_reason: [{ required: true, message: '请输入外出原因', trigger: 'blur' }],
  accompany_staff: [
    {
      validator: (rule, value, callback) => {
        if (form.need_accompany && !value) {
          callback(new Error('请选择陪同人员'))
        } else {
          callback()
        }
      },
      trigger: 'change',
    },
  ],
  emergency_contact_phone: [
    { required: true, message: '请输入紧急联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
}

// 滚动到顶部
const scrollToTop = async () => {
  await nextTick()
  const scrollContainer = document.querySelector('.outing-dialog .max-h-\\[70vh\\]')
  if (scrollContainer) {
    scrollContainer.scrollTop = 0
  }
}

// 监听外出时间变化，重新验证预计返回时间
watch(
  () => form.outing_time,
  () => {
    if (form.expected_return_time) {
      nextTick(() => {
        formRef.value?.validateField('expected_return_time')
      })
    }
  },
)

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      await scrollToTop()

      if (props.mode === 'edit' && props.itemId) {
        // 编辑模式：获取详情数据
        await fetchOutingDetail(props.itemId)
        // 数据加载完成后清除验证状态
        await nextTick()
        formRef.value?.clearValidate()
      } else if (props.mode === 'add') {
        // 新增模式：重置表单
        await resetForm()
        // 设置默认时间
        // const now = new Date()
        // const later = new Date(now.getTime() + 3 * 60 * 60 * 1000)
        // form.outing_time = formatDateTime(now)
        // form.expected_return_time = formatDateTime(later)
      }
    } else {
      // 对话框关闭时清空所有数据
      await resetForm()
    }
  },
)

// 获取外出申请详情数据（用于编辑模式）
const fetchOutingDetail = async (outingId) => {
  if (!outingId) return

  loading.value = true
  try {
    const response = await get(`customer-service/outing-application/detail/${outingId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到外出申请详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取外出申请详情失败:', error)
    ElMessage.error('获取外出申请详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    oid: apiData.oid,
    maternity_admission: apiData.aid,
    outing_time: apiData.outing_time,
    expected_return_time: apiData.expected_return_time,
    outing_reason: apiData.outing_reason,
    need_accompany: apiData.need_accompany || false,
    accompany_staff: apiData.accompany_staff,
    emergency_contact_phone: apiData.emergency_contact_phone,
    remark: apiData.remark || '',
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    oid: '',
    maternity_admission: '',
    outing_time: '',
    expected_return_time: '',
    outing_reason: '',
    need_accompany: false,
    accompany_staff: null,
    emergency_contact_phone: '',
    remark: '',
  })

  // 重置搜索状态
  baseDataStore.newMothers.clearSearch()
  baseDataStore.nurses.clearSearch()

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      maternity_admission: form.maternity_admission,
      outing_time: form.outing_time,
      expected_return_time: form.expected_return_time,
      outing_reason: form.outing_reason,
      need_accompany: form.need_accompany,
      accompany_staff: form.accompany_staff || null,
      emergency_contact_phone: form.emergency_contact_phone,
      remark: form.remark || '',
    }

    let res
    if (props.mode === 'add') {
      res = await post(
        `customer-service/outing-application/create/${form.maternity_admission}/`,
        submitData,
      )
    } else {
      res = await put(`customer-service/outing-application/update/${form.oid}/`, submitData)
    }

    ElMessage.success(props.mode === 'add' ? '外出申请提交成功' : '外出申请更新成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
