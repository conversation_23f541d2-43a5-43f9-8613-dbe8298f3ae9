<template>
  <el-dialog
    v-model="visible"
    title="返回登记"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="return-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="return-form">
        <!-- 时间信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">返回时间</h4>
          <el-form-item label="返回时间" prop="actual_return_time">
            <el-date-picker
              v-model="form.actual_return_time"
              type="datetime"
              placeholder="请选择实际返回时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </div>

        <!-- 备注信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">返回备注</h4>
          <el-form-item label="返回备注" prop="return_remark">
            <el-input
              v-model="form.return_remark"
              type="textarea"
              :rows="3"
              placeholder="请输入返回备注"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          确认返回
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { put } from '@/utils/request'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  actual_return_time: '',
  return_remark: '',
})

// 表单验证规则
const rules = {
  actual_return_time: [{ required: true, message: '请选择实际返回时间', trigger: 'change' }],
  return_remark: [{ required: true, message: '请输入返回备注', trigger: 'blur' }],
}

// 监听外部visible变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal) {
      resetForm()
    }
  },
)

// 监听内部visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.actual_return_time = ''
  form.return_remark = ''
}

const handleSubmit = async () => {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    submitting.value = true
    await put(`customer-service/outing-application/return/${props.itemId}/`, {
      actual_return_time: form.actual_return_time,
      return_remark: form.return_remark,
    })
    emit('success')
    handleClose()
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor) {
  transition: all 0.2s;
}

:deep(.el-date-editor:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-editor.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
