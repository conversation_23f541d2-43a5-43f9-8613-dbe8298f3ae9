<template>
  <div class="checkin-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          入住登记列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ pagination.total_count }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="data"
      :loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="maternity" label="产妇姓名" min-width="150" fixed="left">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600 flex-shrink-0">
              {{ row.maternity?.charAt(0) || '' }}
            </el-avatar>
            <span class="font-medium truncate" :title="row.maternity">{{ row.maternity }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="phone" label="联系电话" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.phone }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="newborns" label="新生儿" min-width="150">
        <template #default="{ row }">
          <div class="text-sm">
            <div v-if="row.newborns && row.newborns.length > 0">
              <div v-for="(newborn, index) in row.newborns" :key="index" class="mb-1">
                <el-tag size="small" type="info">{{ newborn }}</el-tag>
              </div>
            </div>
            <span v-else class="text-gray-400">-</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="expected_check_in_date" label="预计入住日期" min-width="130">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.expected_check_in_date }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="stay_days" label="已住天数" min-width="100">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.stay_days }}天</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="actual_check_in_date" label="实际入住日期" min-width="130">
        <template #default="{ row }">
          <div class="text-sm">
            <span v-if="row.actual_check_in_date" class="text-green-600 font-medium">
              {{ row.actual_check_in_date }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="expected_check_out_date" label="预计出院日期" min-width="130">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.expected_check_out_date }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="actual_check_out_date" label="实际出院日期" min-width="130">
        <template #default="{ row }">
          <div class="text-sm">
            <span v-if="row.actual_check_out_date" class="text-green-600 font-medium">
              {{ row.actual_check_out_date }}
            </span>
            <span v-else class="text-gray-400">-</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="room_number" label="房间号" min-width="100">
        <template #default="{ row }">
          <span v-if="row.room_number">
            <el-tag type="success" size="small">{{ row.room_number }}</el-tag>
          </span>
          <span v-else class="text-gray-400 text-sm">未分配</span>
        </template>
      </el-table-column>

      <el-table-column prop="main_nurse" label="主班护士" min-width="100">
        <template #default="{ row }">
          <span v-if="row.main_nurse" class="text-sm">{{ row.main_nurse }}</span>
          <span v-else class="text-gray-400 text-sm">-</span>
        </template>
      </el-table-column>

      <el-table-column prop="check_in_status" label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.check_in_status)" size="small">
            {{ row.check_in_status }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="290" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons" @click.stop>
            <!-- 待入住状态的操作 -->
            <template v-if="row.check_in_status === '已预定'">
              <el-tooltip
                :content="row.room_number == '暂无' ? '请先更新房间号' : ''"
                :disabled="row.room_number != '暂无'"
                placement="top"
              >
                <el-button
                  v-permission="'cus.housekeeping.edit'"
                  type="primary"
                  size="small"
                  :disabled="row.room_number == '暂无'"
                  @click="handleCheckin(row)"
                  class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600 disabled:bg-gray-300 disabled:border-gray-300 disabled:cursor-not-allowed"
                  :loading="checkinLoadingMap[row.aid]"
                >
                  办理入住
                </el-button>
              </el-tooltip>
              <el-button
                v-permission="'cus.housekeeping.edit'"
                type="warning"
                size="small"
                @click="emit('edit', row)"
              >
                修改预约
              </el-button>
              <el-button
                v-permission="'cus.housekeeping.edit'"
                type="danger"
                size="small"
                @click="emit('cancel', row)"
              >
                取消预约
              </el-button>
            </template>

            <!-- 已入住状态的操作 -->
            <template v-if="row.check_in_status === '已入住'">
              <el-button
                v-permission="'cus.housekeeping.edit'"
                type="primary"
                size="small"
                @click="emit('extend', row)"
                class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
              >
                办理续住
              </el-button>
            </template>

            <!-- 删除操作（仅管理员可见） -->
            <el-button v-if="showDelete" type="danger" size="small" @click="emit('delete', row)">
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { List } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { put } from '@/utils/request'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showDelete: {
    type: Boolean,
    default: false,
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      page_size: 10,
      total_count: 0,
      total_page: 1,
    }),
  },
})

const emit = defineEmits([
  'edit',
  'delete',
  'view',
  'extend',
  'cancel',
  'row-click',
  'pagination-change',
  'refresh', // 添加刷新事件
])

// 添加loading状态管理
const checkinLoadingMap = ref({})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => props.pagination.total_count || 0)

// 监听分页参数变化，同步本地状态
watch(
  () => props.pagination,
  (newPagination) => {
    if (newPagination) {
      currentPage.value = newPagination.page || 1
      pageSize.value = newPagination.page_size || 10
    }
  },
  { immediate: true },
)

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    待入住: 'warning',
    已入住: 'success',
    已出院: 'info',
    已取消: 'danger',
  }
  return statusMap[status] || 'info'
}

// 行点击处理
const handleRowClick = (row) => {
  emit('view', row)
  emit('row-click', row)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('pagination-change', { page, size: pageSize.value })
}

// 办理入住处理
const handleCheckin = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要为 ${row.maternity} 办理入住吗？`, '确认办理入住', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 设置loading状态
    checkinLoadingMap.value[row.aid] = true

    // 调用办理入住接口
    await put(`customer-service/maternity-admission/check-in/${row.aid}/`, {})

    ElMessage.success('办理入住成功')

    // 通知父组件刷新列表
    emit('refresh')
  } catch (error) {
    showErrorTip(error)
  } finally {
    // 清除loading状态
    checkinLoadingMap.value[row.aid] = false
  }
}
</script>

<style scoped>
.checkin-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.checkin-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px 4px;
  justify-content: center;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}
</style>
