<template>
  <div class="bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 总览</h1>
            <p class="text-sm text-gray-600 mt-1">查看房间状态，管理入住情况</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制栏 -->
    <div class="controls-section bg-white border border-gray-200 rounded-lg mb-6">
      <div class="control-group p-4 flex justify-between items-center gap-4 flex-wrap">
        <div class="">
          <el-radio-group v-model="currentView" size="default">
            <el-radio-button value="grid"> 鸟瞰图 </el-radio-button>
            <el-radio-button value="calendar"> 日历视图 </el-radio-button>
          </el-radio-group>
        </div>

        <div class="action-buttons flex gap-3">
          <el-button
            type="primary"
            @click="quickCheckIn"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500 action-btn"
          >
            <Plus class="icon" size="16" />
            快速入住
          </el-button>
          <el-button
            type="success"
            @click="makeReservation"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500 text-white action-btn"
          >
            <Reading class="icon" size="16" />
            预约登记
          </el-button>
          <el-button
            type="warning"
            @click="markRoomStatus"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500 text-white action-btn"
          >
            <Setting class="icon" size="16" />
            房间管理
          </el-button>
        </div>
      </div>
    </div>

    <!-- 过滤搜索区域 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 网格视图 -->
      <div v-if="currentView === 'grid'" class="grid-view">
        <el-empty v-if="!gridData.length" description="暂无房间数据" :image-size="80" />
        <FloorPlan
          v-for="floor in gridData"
          :key="floor.floor"
          :floor="floor"
          @room-click="showRoomDetail"
          class="floor-section"
        />
      </div>

      <!-- 日历视图 -->
      <div v-if="currentView === 'calendar'" class="calendar-view">
        <CalendarView :data="calendarData" />
      </div>
    </div>

    <!-- 房间详情模态框 -->
    <RoomDetailDialog :visible="showModal" :room="selectedRoom" @close="closeModal" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from 'vue'
import { Plus, Reading, Setting } from '@element-plus/icons-vue'
import { useBaseDataStore } from '@/stores/baseData'
import FilterPanel from '@/components/FilterPanel.vue'
import FloorPlan from '@/views/housekeeping/components/FloorPlan.vue'
import CalendarView from '@/views/housekeeping/components/CalendarView.vue'
import RoomDetailDialog from '@/views/housekeeping/components/RoomDetailDialog.vue'
import { get } from '@/utils/request'
import { ROOM_STATUS_OPTIONS } from '@/utils/constants.js'
import { ElButton } from 'element-plus'
import { useRouter } from 'vue-router'
import { showErrorTip } from '@/utils/utils'

const router = useRouter()

// 使用 baseData store
const baseDataStore = useBaseDataStore()

const loading = ref(false)

// 当前视图模式
const currentView = ref('grid')
const gridData = ref([])

// 过滤器
const filters = reactive({})

// 日历视图数据
const calendarData = ref([])

// 过滤器字段配置 - 使用computed确保响应式更新
const filterFields = computed(() => [
  {
    key: 'aps',
    type: 'select',
    label: '房间状态',
    placeholder: '选择状态',
    options: ROOM_STATUS_OPTIONS,
  },
  {
    key: 'room_type',
    type: 'select',
    label: '房间类型',
    placeholder: '选择类型',
    options: baseDataStore.roomTypes.getOptions(),
  },
  {
    key: 'floor',
    type: 'select',
    label: '楼层区域',
    placeholder: '选择楼层',
    options: baseDataStore.floors.getOptions(),
  },
  {
    key: ['check_in_date_start', 'check_in_date_end'], // 直接使用数组作为key
    label: '日期范围',
    type: 'dateRange',
    placeholder: '选择日期范围',
  },
  {
    key: 'sk',
    type: 'input',
    label: '快速搜索',
    placeholder: '客户姓名 / 房间号',
  },
])

// 本地响应式数据
const showModal = ref(false)
const selectedRoom = ref(null)

// 方法
const showRoomDetail = (room) => {
  selectedRoom.value = room
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedRoom.value = null
}

const quickCheckIn = async () => {
  router.push('/housekeeping/maternal-management')
}

const makeReservation = () => {
  router.push('/housekeeping/checkin')
}

const markRoomStatus = () => {
  router.push('/housekeeping/rooms')
}

const handleSearch = () => {
  console.log('搜索:', filters)
  // 重新加载数据
  loadData()
}

// 数据加载方法
const loadData = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      ...filters,
    }

    console.log('params', params)

    if (currentView.value === 'calendar') {
      // 日历视图使用时间接口
      const data = await get('customer-service/room/overview/time', params)
      calendarData.value = data
    } else {
      // 网格视图使用普通接口
      const data = await get('customer-service/room/overview/normal', params)
      gridData.value = data
    }
  } catch (error) {
    showErrorTip(error)
  } finally {
    loading.value = false
  }
}

// 监听视图模式变化，重新加载数据
watch(currentView, () => {
  loadData()
})

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.page-header {
  transition: all 0.3s ease;
}

.stats-cards {
  min-width: 400px;
}

/* 统计卡片样式 - shadcn 风格 */
.stat-card {
  background: white;
  border: 1px solid hsl(214.3 31.8% 91.4%);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.stat-card:hover {
  border-color: hsl(214.3 31.8% 85%);
}

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.stat-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(215.4 16.3% 46.9%);
  margin: 0;
}

.stat-icon {
  color: hsl(215.4 16.3% 46.9%);
  flex-shrink: 0;
}

.stat-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.stat-indicator-success {
  background-color: hsl(142.1 76.2% 36.3%);
}

.stat-indicator-destructive {
  background-color: hsl(0 84.2% 60.2%);
}

.stat-indicator-warning {
  background-color: hsl(45.4 93.4% 47.5%);
}

.stat-indicator-info {
  background-color: hsl(221.2 83.2% 53.3%);
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: hsl(222.2 84% 4.9%);
  line-height: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 16px;
  border-radius: 6px;
}

.main-content {
  position: relative;
}

.grid-view {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.floor-section {
  animation: fadeIn 0.3s ease-out;
}

.calendar-view {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-radio-button__inner) {
  display: flex;
  align-items: center;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .page-header .flex {
    flex-direction: column;
    align-items: stretch;
  }

  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
    min-width: auto;
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .action-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-btn {
    justify-content: center;
  }
}
</style>
