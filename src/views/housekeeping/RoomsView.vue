<template>
  <div class="rooms-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 房间管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理房间信息，维护房间状态</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div v-permission="'cus.housekeeping.edit'" class="flex gap-3">
          <el-button
            type="primary"
            @click="handleAddRoom"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增房间
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 房间列表组件 -->
    <RoomTable
      :rooms="rooms"
      :loading="loading"
      :pagination="pagination"
      @edit="handleEditRoom"
      @change-status="handleChangeStatus"
      @delete="handleDeleteRoom"
      @pagination-change="handlePaginationChange"
      @row-click="handleRoomDetail"
    />

    <!-- 房间表单弹窗 -->
    <RoomFormDialog
      v-model:visible="formVisible"
      :room-data="currentRoom"
      :mode="formMode"
      @save="handleSaveRoom"
    />

    <!-- 状态修改弹窗 -->
    <RoomStatusDialog
      v-model:visible="statusVisible"
      :room-data="currentRoom"
      @save="handleSaveStatus"
    />

    <!-- 房间详情弹窗 -->
    <RoomDetailDialog
      v-model="detailVisible"
      :room-id="selectedRoomId"
      @edit="handleEditFromDetail"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElButton, ElMessage, ElIcon } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { get } from '@/utils/request.js'
import { ROOM_STATUS_OPTIONS, getRoomStatusInfo } from '@/utils/constants.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import FilterPanel from '@/components/FilterPanel.vue'
import RoomTable from '@/views/housekeeping/components/RoomTable.vue'
import RoomFormDialog from '@/views/housekeeping/components/RoomFormDialog.vue'
import RoomStatusDialog from '@/views/housekeeping/components/RoomStatusDialog.vue'
import RoomDetailDialog from '@/views/housekeeping/components/RoomDetailDialog.vue'

// 使用基础数据 store
const baseDataStore = useBaseDataStore()

// 响应式数据
const loading = ref(false)
const formVisible = ref(false)
const statusVisible = ref(false)
const detailVisible = ref(false)
const formMode = ref('add') // 'add' | 'edit'
const currentRoom = ref(null)
const selectedRoomId = ref(null)

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 10,
  total_count: 0,
  total_page: 1,
})

// 筛选条件
const filters = reactive({})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入关键字',
  },
  {
    key: 'room_type',
    type: 'select',
    label: '房间类型',
    placeholder: '选择房间类型',
    options: baseDataStore.roomTypes.getOptions(),
  },
  {
    key: 'aps',
    type: 'select',
    label: '房间状态',
    placeholder: '选择房间状态',
    options: ROOM_STATUS_OPTIONS,
  },
  {
    key: 'floor',
    type: 'select',
    label: '楼层',
    placeholder: '选择楼层',
    options: baseDataStore.floors.getOptions(),
  },
])

// 房间列表数据
const rooms = ref([])

// 方法
const handleAddRoom = () => {
  currentRoom.value = null
  formMode.value = 'add'
  formVisible.value = true
}

const handleEditRoom = (room) => {
  currentRoom.value = { ...room }
  formMode.value = 'edit'
  formVisible.value = true
}

const handleChangeStatus = (room) => {
  currentRoom.value = { ...room }
  statusVisible.value = true
}

// 删除房间
const handleDeleteRoom = (deletedRoom) => {
  // 从房间列表中移除被删除的房间
  const index = rooms.value.findIndex((room) => room.rid === deletedRoom.rid)
  if (index !== -1) {
    rooms.value.splice(index, 1)
    // 更新总数
    pagination.total_count = Math.max(0, pagination.total_count - 1)
  }
}

// 转换API数据格式
const transformRoomData = (apiData) => {
  return apiData.map((room) => washRoomData(room))
}
const washRoomData = (room) => {
  const statusInfo = getRoomStatusInfo(room.room_status)
  return {
    ...room,
    floorText: `${room.floor}楼`,
    facilities: room.facility_list.join(', ') || '暂无设施信息',
    statusText: statusInfo.text,
    // 保留原始数据以备后用
    originalData: room,
  }
}

const handleSearch = async () => {
  loading.value = true
  try {
    const params = {
      ...filters,
      page: pagination.page,
      page_size: pagination.page_size,
    }
    console.log('params', params)
    const data = await get('customer-service/room/list/', params)
    rooms.value = transformRoomData(data.list)

    // 更新分页信息
    pagination.page = data.page
    pagination.page_size = data.page_size
    pagination.total_count = data.total_count
    pagination.total_page = data.total_page
  } catch (error) {
    console.error('获取房间列表失败:', error)
    ElMessage.error('获取房间列表失败')
    rooms.value = []
  } finally {
    loading.value = false
  }
}

const handleSaveRoom = (roomData) => {
  let data = washRoomData(roomData)
  if (formMode.value === 'add') {
    // 添加新房间 - 这里应该调用API，暂时添加到本地列表
    rooms.value.push(data)
    ElMessage.success('房间添加成功')
    // TODO: 调用创建房间API
  } else {
    // 更新房间 - 这里应该调用API，暂时更新本地列表
    const index = rooms.value.findIndex((room) => room.rid == roomData.rid)
    if (index !== -1) {
      rooms.value[index] = data
    }
    ElMessage.success('房间更新成功')
    // TODO: 调用更新房间API
  }
  formVisible.value = false
}

const handleSaveStatus = (roomData) => {
  const index = rooms.value.findIndex((room) => room.rid == roomData.rid)
  if (index !== -1) {
    rooms.value[index] = washRoomData(roomData)
  }
  statusVisible.value = false
  ElMessage.success('状态更新成功')
}

// 处理分页变化
const handlePaginationChange = ({ page, size }) => {
  pagination.page = page
  pagination.page_size = size
  handleSearch()
}

// 查看房间详情
const handleRoomDetail = (room) => {
  selectedRoomId.value = room.rid
  detailVisible.value = true
}

// 从详情页面编辑
const handleEditFromDetail = (roomData) => {
  currentRoom.value = { ...roomData }
  formMode.value = 'edit'
  formVisible.value = true
}

// 关闭详情弹窗
const handleCloseDetail = () => {
  detailVisible.value = false
  selectedRoomId.value = null
}

onMounted(() => {
  // 初始化数据加载
  handleSearch()
})
</script>

<style scoped>
.rooms-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
