<template>
  <div class="checkout-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 退房管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理客户退房流程，处理退房结算</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <FilterPanel :fields="filterFields" :filters="searchFilters" @search="handleSearch" />

    <!-- 退房列表表格 -->
    <CheckoutTable
      :data="filteredData"
      :loading="tableLoading"
      @checkout="handleCheckout"
      @extend="handleExtend"
      @view="handleView"
      @print="handlePrint"
    />

    <!-- 退房结算表单弹窗 -->
    <el-dialog
      v-model="formVisible"
      title=""
      width="80%"
      align-center
      :close-on-click-modal="false"
      class="checkout-dialog"
    >
      <div
        v-if="formData"
        class="max-h-[70vh] overflow-y-auto checkout-form bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <!-- 表单标题 -->
        <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
          <h3 class="text-xl font-semibold text-gray-800">退房结算表单 ({{ formData.id }})</h3>
          <el-button @click="formVisible = false" type="info" size="small">
            <el-icon class="mr-1"><Close /></el-icon>
            关闭
          </el-button>
        </div>

        <el-form :model="formData" label-width="140px" class="checkout-form-content">
          <!-- 客户与房间信息 -->
          <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">客户与房间信息</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <el-form-item label="客户姓名">
                <span class="text-gray-800 font-medium">{{ formData.customerName }}</span>
              </el-form-item>
              <el-form-item label="房间号">
                <el-tag type="success" size="large">{{ formData.roomNumber }}</el-tag>
              </el-form-item>
              <el-form-item label="入住日期">
                <span class="text-gray-700">{{ formData.checkinDate || '2025-05-10' }}</span>
              </el-form-item>
              <el-form-item label="原定退房日期">
                <span class="text-gray-700">{{ formData.originalCheckoutDate }}</span>
              </el-form-item>
              <el-form-item label="实际退房日期" required>
                <el-date-picker
                  v-model="formData.actualCheckoutDate"
                  type="datetime"
                  placeholder="选择实际退房时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm"
                  class="w-full"
                />
              </el-form-item>
            </div>
          </div>

          <!-- 费用结算区 -->
          <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">费用结算</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <el-form-item label="应收总额">
                <div class="flex items-center">
                  <span class="text-lg font-semibold text-green-600"
                    >¥{{ formData.totalReceivable }}</span
                  >
                  <span class="text-sm text-gray-500 ml-2">(套餐费 + 其他消费)</span>
                </div>
              </el-form-item>
              <el-form-item label="已付金额">
                <div class="flex items-center">
                  <span class="text-lg font-semibold">¥{{ formData.amountPaid }}</span>
                  <span class="text-sm text-gray-500 ml-2">(预付款 + 押金 + 定金)</span>
                </div>
              </el-form-item>
              <el-form-item label="其他消费明细" class="col-span-full">
                <el-input
                  v-model="formData.otherCharges"
                  type="textarea"
                  :rows="3"
                  placeholder="如临时服务、商品购买等，可从其他模块关联"
                />
              </el-form-item>
              <el-form-item label="结算金额">
                <div class="flex items-center gap-4">
                  <div v-if="settlementAmount > 0" class="flex items-center">
                    <span class="text-sm text-gray-600 mr-2">退款:</span>
                    <span class="text-lg font-semibold text-green-600"
                      >¥{{ settlementAmount }}</span
                    >
                  </div>
                  <div v-else-if="settlementAmount < 0" class="flex items-center">
                    <span class="text-sm text-gray-600 mr-2">补交:</span>
                    <span class="text-lg font-semibold text-red-600"
                      >¥{{ Math.abs(settlementAmount) }}</span
                    >
                  </div>
                  <div v-else class="flex items-center">
                    <span class="text-lg font-semibold text-gray-600">已结清</span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="押金退还情况">
                <div class="flex items-center gap-4">
                  <el-select
                    v-model="formData.depositReturn"
                    placeholder="选择退还方式"
                    class="w-40"
                  >
                    <el-option label="全退" value="full" />
                    <el-option label="部分扣除" value="partial" />
                  </el-select>
                  <el-input
                    v-if="formData.depositReturn === 'partial'"
                    v-model="formData.depositDeductionReason"
                    placeholder="部分扣除原因"
                    class="flex-1"
                  />
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 物品检查确认 -->
          <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">物品检查确认</h3>
            </div>
            <el-form-item label="设施设备检查情况">
              <el-input
                v-model="formData.itemCheck"
                type="textarea"
                :rows="4"
                placeholder="记录房间设施设备检查情况，是否有损坏及赔偿等"
              />
            </el-form-item>
          </div>

          <!-- 客户签字确认 -->
          <div class="form-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
            <div class="section-header flex items-center mb-6 pb-3 border-b border-gray-200">
              <h3 class="section-title">客户签字确认</h3>
            </div>
            <el-form-item label="客户签字确认">
              <el-input
                v-model="formData.clientSignature"
                placeholder="[电子签名区域或手动输入确认]"
                class="w-full"
              >
                <template #append>
                  <el-button type="primary" size="small">
                    <el-icon class="mr-1"><Edit /></el-icon>
                    电子签名
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-center gap-4 mt-8 pt-6 border-t border-gray-200">
            <el-button
              type="primary"
              size="large"
              @click="handleFormSubmit"
              class="bg-green-500 hover:bg-green-600 border-green-500 hover:border-green-600 px-8"
            >
              <el-icon class="mr-2"><Check /></el-icon>
              确认结算并退房
            </el-button>
            <el-button
              type="info"
              size="large"
              @click="handlePrintSettlement"
              class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600 px-8"
            >
              <el-icon class="mr-2"><Printer /></el-icon>
              打印结算单
            </el-button>
            <el-button size="large" @click="formVisible = false" class="px-8">
              <el-icon class="mr-2"><Close /></el-icon>
              取消
            </el-button>
          </div>
        </el-form>

        <!-- 提示信息 -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 class="text-sm font-medium text-blue-800 mb-2">操作说明：</h4>
          <ul class="text-sm text-blue-700 space-y-1">
            <li>
              •
              <strong>延期续住：</strong
              >客户申请延期，确认延期时长。检查房间可用性，可能需要重新排房或协调。计算续住费用并收款。更新系统中的预计退房日期和房态。
            </li>
            <li>
              •
              <strong>提前退房：</strong>确认提前离所时间，按规定计算退款/补交费用，结算，更新房态。
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>

    <!-- 延期申请弹窗 -->
    <el-dialog
      v-model="showExtendDialog"
      title="申请延期续住"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="extend-form">
        <el-form :model="extendForm" label-width="120px">
          <el-form-item label="客户姓名">
            <span class="text-gray-800 font-medium">{{ extendForm.customerName }}</span>
          </el-form-item>
          <el-form-item label="当前房间">
            <el-tag type="success">{{ extendForm.roomNumber }}</el-tag>
          </el-form-item>
          <el-form-item label="原定退房日期">
            <span class="text-gray-700">{{ extendForm.originalCheckoutDate }}</span>
          </el-form-item>
          <el-form-item label="延期至" required>
            <el-date-picker
              v-model="extendForm.newCheckoutDate"
              type="date"
              placeholder="选择新的退房日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="延期天数">
            <span class="font-medium">{{ extendDays }}天</span>
          </el-form-item>
          <el-form-item label="续住费用">
            <el-input-number
              v-model="extendForm.additionalFee"
              :min="0"
              :precision="2"
              placeholder="请输入续住费用"
              class="w-full"
            />
          </el-form-item>
          <el-form-item label="延期原因">
            <el-input
              v-model="extendForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请输入延期原因"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showExtendDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleConfirmExtend"
            class="bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
          >
            确认延期
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Close, Check, Printer, Edit, Search, Download } from '@element-plus/icons-vue'
import CheckoutTable from '@/views/housekeeping/components/CheckoutTable.vue'
import FilterPanel from '@/components/FilterPanel.vue'

// 搜索过滤条件
const searchFilters = reactive({
  status: '',
  checkoutDate: '',
  keyword: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'status',
    type: 'select',
    label: '状态',
    placeholder: '选择状态',
    options: [
      { label: '待退房', value: 'pending_checkout' },
      { label: '已退房', value: 'checked_out' },
      { label: '提前退房', value: 'early_checkout' },
      { label: '延期续住', value: 'extended' },
    ],
  },
  {
    key: 'checkoutDate',
    type: 'date',
    label: '原定退房日期',
    placeholder: '选择日期',
  },
  {
    key: 'keyword',
    type: 'input',
    label: '客户姓名/房号',
    placeholder: '输入客户姓名或房间号',
  },
]

// 表格数据
const tableData = ref([])
const tableLoading = ref(false)

// 表单相关
const formVisible = ref(false)
const formData = ref({})
const showExtendDialog = ref(false)

// 延期表单数据
const extendForm = reactive({
  id: '',
  customerName: '',
  roomNumber: '',
  originalCheckoutDate: '',
  newCheckoutDate: '',
  additionalFee: 0,
  reason: '',
})

// 模拟数据
const mockData = [
  {
    id: 'IN2025005',
    customerName: '李妈妈',
    roomNumber: '201',
    originalCheckoutDate: '2025-06-07',
    actualCheckoutDate: '-',
    status: 'pending_checkout',
    checkinDate: '2025-05-10',
    totalReceivable: 25000,
    amountPaid: 26000,
    otherCharges: '',
    depositReturn: 'full',
    depositDeductionReason: '',
    itemCheck: '',
    clientSignature: '',
  },
  {
    id: 'IN2025002',
    customerName: '陈太太',
    roomNumber: '305',
    originalCheckoutDate: '2025-05-20',
    actualCheckoutDate: '2025-05-20',
    status: 'checked_out',
    checkinDate: '2025-04-20',
    totalReceivable: 28000,
    amountPaid: 28000,
  },
  {
    id: 'IN2025003',
    customerName: '王女士',
    roomNumber: '102',
    originalCheckoutDate: '2025-06-15',
    actualCheckoutDate: '-',
    status: 'extended',
    checkinDate: '2025-05-15',
    totalReceivable: 30000,
    amountPaid: 32000,
  },
  {
    id: 'IN2025004',
    customerName: '张太太',
    roomNumber: '208',
    originalCheckoutDate: '2025-06-01',
    actualCheckoutDate: '2025-05-30',
    status: 'early_checkout',
    checkinDate: '2025-05-01',
    totalReceivable: 26000,
    amountPaid: 27000,
  },
]

// 过滤后的数据
const filteredData = computed(() => {
  let result = tableData.value

  if (searchFilters.status) {
    result = result.filter((item) => item.status === searchFilters.status)
  }

  if (searchFilters.checkoutDate) {
    result = result.filter((item) => item.originalCheckoutDate === searchFilters.checkoutDate)
  }

  if (searchFilters.keyword) {
    const keyword = searchFilters.keyword.toLowerCase()
    result = result.filter(
      (item) =>
        item.customerName.toLowerCase().includes(keyword) ||
        item.roomNumber.toLowerCase().includes(keyword),
    )
  }

  return result
})

// 计算结算金额
const settlementAmount = computed(() => {
  if (!formData.value) return 0
  const receivable = formData.value.totalReceivable || 0
  const paid = formData.value.amountPaid || 0
  return paid - receivable
})

// 计算延期天数
const extendDays = computed(() => {
  if (!extendForm.originalCheckoutDate || !extendForm.newCheckoutDate) return 0
  const original = new Date(extendForm.originalCheckoutDate)
  const newDate = new Date(extendForm.newCheckoutDate)
  const diffTime = newDate - original
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
})

// 获取数据
const fetchData = async () => {
  tableLoading.value = true
  try {
    await new Promise((resolve) => setTimeout(resolve, 500))
    tableData.value = mockData
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    tableLoading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  tableLoading.value = true
  setTimeout(() => {
    tableLoading.value = false
    ElMessage.success('搜索完成')
  }, 500)
}

// 重置搜索
const handleReset = () => {
  searchFilters.status = ''
  searchFilters.checkoutDate = ''
  searchFilters.keyword = ''
  handleSearch()
}

// 刷新数据
const handleRefresh = async () => {
  tableLoading.value = true
  try {
    await fetchData()
    ElMessage.success('数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  }
}

// 导出记录
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 办理退房
const handleCheckout = (row) => {
  formData.value = {
    ...row,
    actualCheckoutDate: new Date().toISOString().slice(0, 16).replace('T', ' '),
    otherCharges: '',
    depositReturn: 'full',
    depositDeductionReason: '',
    itemCheck: '',
    clientSignature: '',
  }
  formVisible.value = true
}

// 申请延期
const handleExtend = (row) => {
  Object.assign(extendForm, {
    id: row.id,
    customerName: row.customerName,
    roomNumber: row.roomNumber,
    originalCheckoutDate: row.originalCheckoutDate,
    newCheckoutDate: '',
    additionalFee: 0,
    reason: '',
  })
  showExtendDialog.value = true
}

// 查看详情
const handleView = (row) => {
  ElMessage.info(`查看 ${row.customerName} 的详细信息`)
}

// 打印结算单
const handlePrint = (row) => {
  ElMessage.success(`正在打印 ${row.customerName} 的结算单...`)
}

// 表单提交
const handleFormSubmit = () => {
  if (!formData.value.actualCheckoutDate) {
    ElMessage.warning('请选择实际退房日期')
    return
  }
  if (!formData.value.clientSignature) {
    ElMessage.warning('请确认客户签字')
    return
  }

  ElMessageBox.confirm('确认完成退房结算吗？此操作不可撤销。', '确认退房', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 更新数据状态
      const index = tableData.value.findIndex((item) => item.id === formData.value.id)
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          status: 'checked_out',
          actualCheckoutDate: formData.value.actualCheckoutDate,
        }
      }

      formVisible.value = false
      ElMessage.success('退房结算完成')
    })
    .catch(() => {
      ElMessage.info('已取消退房操作')
    })
}

// 打印结算单
const handlePrintSettlement = () => {
  ElMessage.success('正在生成结算单...')
}

// 确认延期
const handleConfirmExtend = () => {
  if (!extendForm.newCheckoutDate) {
    ElMessage.warning('请选择新的退房日期')
    return
  }
  if (extendDays.value <= 0) {
    ElMessage.warning('延期日期必须晚于原定退房日期')
    return
  }

  ElMessageBox.confirm(
    `确认将退房日期延期至 ${extendForm.newCheckoutDate} 吗？延期 ${extendDays.value} 天，续住费用 ¥${extendForm.additionalFee}`,
    '确认延期',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      // 更新数据状态
      const index = tableData.value.findIndex((item) => item.id === extendForm.id)
      if (index !== -1) {
        tableData.value[index] = {
          ...tableData.value[index],
          status: 'extended',
          originalCheckoutDate: extendForm.newCheckoutDate,
        }
      }

      showExtendDialog.value = false
      ElMessage.success('延期申请已确认')
    })
    .catch(() => {
      ElMessage.info('已取消延期申请')
    })
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.checkout-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.checkout-dialog :deep(.el-dialog__header) {
  display: none;
}

.checkout-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.form-section {
  transition: all 0.3s ease;
}

.form-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.checkout-form-content :deep(.el-form-item__label) {
  color: rgb(55 65 81);
  font-weight: 500;
}

.checkout-form-content :deep(.el-input__wrapper) {
  transition: all 0.2s;
}

.checkout-form-content :deep(.el-input__wrapper:hover) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.checkout-form-content :deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.checkout-form-content :deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

.checkout-form-content :deep(.el-date-picker:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

.extend-form :deep(.el-form-item__label) {
  color: rgb(55 65 81);
  font-weight: 500;
}

.extend-form :deep(.el-input__wrapper) {
  transition: all 0.2s;
}

.extend-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.extend-form :deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
