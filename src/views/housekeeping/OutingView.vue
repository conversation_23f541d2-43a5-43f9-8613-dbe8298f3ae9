<template>
  <div class="outing-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 外出管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理客户外出申请，跟踪外出状态</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            填写外出单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <!-- <OutingStats :stats="stats" /> -->

    <!-- 筛选区域 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 数据表格 -->
    <OutingTable
      :data="tableData"
      :loading="loading"
      :pagination="pagination"
      @edit="handleEdit"
      @approve="handleApprove"
      @reject="handleReject"
      @return="handleReturn"
      @delete="handleDelete"
      @pagination-change="handlePaginationChange"
      @row-click="handleRowClick"
    />

    <!-- 表单对话框 -->
    <OutingFormDialog
      v-model="showFormDialog"
      :mode="formMode"
      :item-id="itemId"
      @success="handleSubmitSuccess"
    />

    <!-- 返回登记模态框 -->
    <ReturnFormDialog v-model="showReturnModal" :item-id="itemId" @success="handleReturnSuccess" />

    <!-- 详情模态框 -->
    <OutingDetailDialog
      v-model="showDetailModal"
      :outing-id="selectedOutingId"
      @approve="handleApprove"
      @reject="handleReject"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElMessage, ElMessageBox, ElIcon } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { get, put } from '@/utils/request.js'
// import OutingStats from './components/OutingStats.vue'
import FilterPanel from '@/components/FilterPanel.vue'
import OutingTable from './components/OutingTable.vue'
import OutingFormDialog from './components/OutingFormDialog.vue'
import ReturnFormDialog from './components/ReturnFormDialog.vue'
import OutingDetailDialog from './components/OutingDetailDialog.vue'
import { OUTING_STATUS_OPTIONS, getOutingStatusText } from '@/utils/constants.js'

// 响应式数据
const loading = ref(false)
const showFormDialog = ref(false)
const showReturnModal = ref(false)
const showDetailModal = ref(false)
const selectedRecord = ref(null)
const selectedOutingId = ref(null)
const formMode = ref('add') // 'add' | 'edit'
const itemId = ref(null)

// 统计数据
const stats = ref({
  todayOut: 3,
  notReturned: 2,
  overdue: 1,
})

// 筛选条件
const filters = reactive({
  status: '',
  roomNumber: '',
  customerName: '',
  dateStart: '',
  dateEnd: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'aps',
    type: 'select',
    label: '审批状态',
    placeholder: '请选择审批状态',
    options: OUTING_STATUS_OPTIONS,
  },
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入客户姓名或房间号',
  },
]

// 分页数据
const pagination = reactive({
  page: 1,
  page_size: 10,
  total_count: 0,
  total_page: 1,
})

// 表格数据
const tableData = ref([])

// 转换API数据格式
const transformOutingData = (apiData) => {
  return apiData.map((item) => washOutingData(item))
}

const washOutingData = (item) => {
  // 根据API数据确定当前状态
  const currentStatus = determineCurrentStatus(item)

  return {
    oid: item.oid || Math.random(), // API数据可能没有id，临时生成
    roomNumber: item.room_number,
    customerName: item.maternity_name,
    outTime: item.outing_time,
    expectedReturnTime: item.expected_return_time,
    actualReturnTime: item.actual_return_time,
    reason: item.outing_reason,
    needAccompany: item.need_accompany,
    approvalStatus: item.approval_status,
    approvalStatusText: item.approval_status_label,
    outingStatus: item.outing_status,
    outingStatusText: item.outing_status_label,
    // 统一使用大写状态
    status: currentStatus,
    statusText: getOutingStatusText(currentStatus),
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 根据API数据确定当前状态（统一使用大写）
const determineCurrentStatus = (item) => {
  if (item.approval_status === 'PENDING') return 'PENDING'
  if (item.approval_status === 'REJECTED') return 'REJECTED'
  if (item.approval_status === 'APPROVED') {
    if (item.actual_return_time) return 'RETURNED'
    if (item.outing_status === 'OUT') {
      // 检查是否逾期
      const now = new Date()
      const expectedReturn = new Date(item.expected_return_time)
      if (now > expectedReturn) return 'OVERDUE'
      return 'OUT'
    }
    return 'APPROVED'
  }
  return 'PENDING'
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...filters,
      page: pagination.page,
      page_size: pagination.page_size,
    }
    console.log('params', params)
    const data = await get('customer-service/outing-application/list/', params)
    tableData.value = transformOutingData(data.list)

    // 更新分页信息
    pagination.page = data.page
    pagination.page_size = data.page_size
    pagination.total_count = data.total_count
    pagination.total_page = data.total_page

    // 更新统计数据（这里可以根据实际数据计算）
    updateStats(data.list)
  } catch (error) {
    console.error('获取外出申请列表失败:', error)
    ElMessage.error('获取外出申请列表失败')
    tableData.value = []
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStats = (list) => {
  const today = new Date().toDateString()

  stats.value = {
    todayOut: list.filter((item) => {
      const outDate = new Date(item.outing_time).toDateString()
      return outDate === today && item.outing_status === 'OUT'
    }).length,
    notReturned: list.filter(
      (item) => item.approval_status === 'APPROVED' && !item.actual_return_time,
    ).length,
    overdue: list.filter((item) => {
      if (item.actual_return_time) return false
      const now = new Date()
      const expectedReturn = new Date(item.expected_return_time)
      return now > expectedReturn && item.approval_status === 'APPROVED'
    }).length,
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 处理分页变化
const handlePaginationChange = ({ page, size }) => {
  pagination.page = page
  pagination.page_size = size
  loadData()
}

const handleRowClick = (record) => {
  selectedRecord.value = record
  selectedOutingId.value = record.oid
  showDetailModal.value = true
}

const handleApprove = async (record) => {
  try {
    await ElMessageBox.confirm('确定批准此外出申请吗？', '确认操作', {
      type: 'warning',
    })

    // 调用审批接口
    await put(`customer-service/outing-application/audit/${record.oid}/`, {
      result: true,
    })

    ElMessage.success('外出申请已批准')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批准外出申请失败:', error)
      ElMessage.error('批准操作失败，请稍后重试')
    }
  }
}

const handleReject = async (record) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入拒绝原因', '拒绝申请', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValidator: (value) => {
        if (!value) {
          return '请输入拒绝原因'
        }
        return true
      },
    })

    // 调用审批接口
    await put(`customer-service/outing-application/audit/${record.oid}/`, {
      result: false,
      reason: reason,
    })

    ElMessage.success('外出申请已拒绝')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒绝外出申请失败:', error)
      ElMessage.error('拒绝操作失败，请稍后重试')
    }
  }
}

const handleReturn = (record) => {
  selectedRecord.value = record
  itemId.value = record.oid
  showReturnModal.value = true
}

const handleCreate = () => {
  formMode.value = 'add'
  itemId.value = null
  showFormDialog.value = true
}

const handleSubmitSuccess = () => {
  showFormDialog.value = false
  itemId.value = null
  loadData()
}

const handleReturnSuccess = () => {
  ElMessage.success('返回登记成功')
  loadData()
}

const handleCloseDetail = () => {
  selectedOutingId.value = null
  selectedRecord.value = null
  showDetailModal.value = false
}

const handleDelete = (deletedRecord) => {
  // 从外出申请列表中移除被删除的记录
  const index = tableData.value.findIndex((record) => record.oid === deletedRecord.oid)
  if (index !== -1) {
    tableData.value.splice(index, 1)
    // 更新总数
    pagination.total_count = Math.max(0, pagination.total_count - 1)
  }
}

const handleEdit = (record) => {
  console.log('record', record)
  formMode.value = 'edit'
  itemId.value = record.oid
  showFormDialog.value = true
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.outing-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
