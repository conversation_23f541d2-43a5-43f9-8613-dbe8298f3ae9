<template>
  <div class="checkin-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">房务管理 - 入住管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理客户入住预约，办理入住流程</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            v-permission="'cus.housekeeping.edit'"
            @click="handleCreateCheckin"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            新增预约/登记
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" />

    <!-- 入住列表表格 -->
    <CheckinTable
      :data="tableData"
      :filters="filters"
      :loading="tableLoading"
      :pagination="paginationData"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
      @extend="handleExtend"
      @cancel="handleCancel"
      @row-click="handleRowClick"
      @pagination-change="handlePaginationChange"
      @refresh="fetchData"
    />

    <!-- 入住办理表单弹窗 -->
    <CheckinFormDialog
      v-model="formVisible"
      :checkin-id="currentCheckinId"
      :mode="formMode"
      @submit="handleFormSubmit"
    />

    <!-- 详情查看对话框 -->
    <CheckinDetailDialog
      v-model="showDetailDialog"
      :checkin-id="currentCheckinId"
      @checkin="handleCheckinFromDetail"
      @close="handleCloseDetail"
    />

    <!-- 办理入住/续住对话框 -->
    <CheckinRenewDialog
      v-model="showRenewDialog"
      :mode="renewMode"
      :checkin-id="currentCheckinId"
      @success="handleRenewSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import CheckinTable from '@/views/housekeeping/components/CheckinTable.vue'
import CheckinFormDialog from '@/views/housekeeping/components/CheckinFormDialog.vue'
import CheckinDetailDialog from '@/views/housekeeping/components/CheckinDetailDialog.vue'
import CheckinRenewDialog from '@/views/housekeeping/components/CheckinRenewDialog.vue'
import { get } from '@/utils/request'
import { CHECK_IN_STATUS_OPTIONS } from '@/utils/constants'

// 响应式数据
const showDetailDialog = ref(false)
const showRenewDialog = ref(false)
const renewMode = ref('checkin') // checkin 办理入住, renew 续住
const currentCheckinId = ref(null)

// 当前过滤条件
const filters = reactive({
  cis: '',
  sk: '',
  ecid: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'cis',
    type: 'select',
    label: '状态',
    placeholder: '选择状态',
    options: CHECK_IN_STATUS_OPTIONS,
  },
  {
    key: 'ecid',
    type: 'date',
    label: '预计入住日期',
    placeholder: '选择日期',
  },
  {
    key: 'sk',
    type: 'input',
    label: '产妇姓名',
    placeholder: '输入产妇姓名',
  },
]

// 表格数据
const tableData = ref([])
const tableLoading = ref(false)

// 分页数据
const paginationData = ref({
  page: 1,
  page_size: 10,
  total_count: 0,
  total_page: 1,
})

// 表单相关
const formVisible = ref(false)
const formMode = ref('create') // create, edit

// 搜索
const handleSearch = () => {
  paginationData.value.page = 1
  fetchData()
}

// 分页变化处理
const handlePaginationChange = ({ page, size }) => {
  paginationData.value.page = page
  paginationData.value.page_size = size
  // 这里可以调用API重新获取数据
  fetchData()
}

// 获取数据
const fetchData = async () => {
  tableLoading.value = true
  try {
    const params = {
      page: paginationData.value.page,
      page_size: paginationData.value.page_size,
      ...filters,
    }
    const response = await get('customer-service/maternity-admission/list/', params)
    tableData.value = response.list || []

    // 更新分页数据
    paginationData.value = {
      page: response.page || 1,
      page_size: response.page_size || 10,
      total_count: response.total_count || 0,
      total_page: response.total_page || 1,
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
    tableData.value = []
    paginationData.value = {
      page: 1,
      page_size: 10,
      total_count: 0,
      total_page: 1,
    }
  } finally {
    tableLoading.value = false
  }
}

// 新增预约/登记
const handleCreateCheckin = () => {
  formMode.value = 'create'
  formVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  currentCheckinId.value = row.aid
  formMode.value = 'edit'
  formVisible.value = true
}

// 查看详情
const handleView = (row) => {
  currentCheckinId.value = row.aid
  showDetailDialog.value = true
}

// 行点击
const handleRowClick = () => {
  // 可以在这里添加行点击逻辑
}

// 从详情对话框办理入住
const handleCheckinFromDetail = () => {
  showDetailDialog.value = false
  renewMode.value = 'checkin'
  showRenewDialog.value = true
}

// 办理续住
const handleExtend = (row) => {
  currentCheckinId.value = row.aid
  renewMode.value = 'renew'
  showRenewDialog.value = true
}

// 取消预约
const handleCancel = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要取消 ${row.maternity} 的预约吗？`, '确认取消', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    ElMessage.success('预约已取消')
    fetchData()
  } catch {
    // 用户取消操作
  }
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除 ${row.maternity} 的记录吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    ElMessage.success('删除成功')
    fetchData()
  } catch {
    // 用户取消操作
  }
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentCheckinId.value = null
}

// 办理入住/续住成功处理
const handleRenewSuccess = () => {
  fetchData() // 刷新表格数据
}

// 表单提交
const handleFormSubmit = (formData) => {
  console.log('表单提交:', formData)
  formVisible.value = false
  fetchData()
}

// 初始化
fetchData()
</script>

<style scoped>
.checkin-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
