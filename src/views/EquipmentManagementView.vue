<template>
  <div class="equipment-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">设备管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理医疗设备和设施，维护设备使用记录</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            @click="handleExport"
            class="text-gray-600 border-gray-300 hover:text-pink-500 hover:border-pink-300"
          >
            <el-icon class="mr-2">
              <Download />
            </el-icon>
            导出设备列表
          </el-button>
          <el-button
            type="primary"
            @click="handleCreateEquipment"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            添加新设备
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel :fields="filterFields" :filters="currentFilters" @search="handleSearch" />

    <!-- 设备列表表格 -->
    <EquipmentTable
      ref="equipmentTableRef"
      :filters="currentFilters"
      @edit="handleEdit"
      @delete="handleDelete"
      @view="handleView"
      @row-click="handleRowClick"
    />

    <!-- 设备表单弹窗 -->
    <EquipmentFormDialog
      v-model="formVisible"
      :form-data="formData"
      :mode="formMode"
      @submit="handleFormSubmit"
    />

    <!-- 详情查看对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="设备详情"
      width="800px"
      :before-close="handleCloseDetail"
    >
      <div v-loading="detailLoading" class="detail-content">
        <div v-if="!detailLoading && currentEquipment" class="equipment-detail">
          <!-- 设备图片 -->
          <div class="detail-section mb-6" v-if="currentEquipment.image_url">
            <h3 class="section-title">设备图片</h3>
            <div class="flex justify-center">
              <el-image
                :src="currentEquipment.image_url"
                :alt="currentEquipment.name"
                class="detail-image"
                fit="cover"
              >
                <template #error>
                  <div class="image-error">
                    <el-icon><Picture /></el-icon>
                    <span>图片加载失败</span>
                  </div>
                </template>
              </el-image>
            </div>
          </div>

          <!-- 设备基本信息 -->
          <div class="detail-section mb-6">
            <h3 class="section-title">基本信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>设备名称：</label>
                <span>{{ currentEquipment.name }}</span>
              </div>
              <div class="detail-item">
                <label>设备类型：</label>
                <span>{{ currentEquipment.type_display }}</span>
              </div>
              <div class="detail-item">
                <label>状态：</label>
                <el-tag :type="getEquipmentStatusTagType(currentEquipment.status)">
                  {{ currentEquipment.status_display }}
                </el-tag>
              </div>
              <div class="detail-item">
                <label>设备功能：</label>
                <span>{{ currentEquipment.function || '暂无' }}</span>
              </div>
            </div>
          </div>

          <!-- 使用信息 -->
          <div class="detail-section mb-6">
            <h3 class="section-title">使用信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>使用方式：</label>
                <span>{{ currentEquipment.usage_method || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>使用时长：</label>
                <span>{{ currentEquipment.usage_duration || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>消毒方式：</label>
                <span>{{ currentEquipment.disinfection_method || '暂无' }}</span>
              </div>
            </div>
          </div>

          <!-- 系统信息 -->
          <div class="detail-section mb-6">
            <h3 class="section-title">系统信息</h3>
            <div class="grid grid-cols-2 gap-4">
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ currentEquipment.created_at || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>更新时间：</label>
                <span>{{ currentEquipment.updated_at || '暂无' }}</span>
              </div>
              <div class="detail-item">
                <label>创建人：</label>
                <span>{{ currentEquipment.creator_name || '暂无' }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 暂无数据提示 -->
        <div v-else-if="!detailLoading" class="no-data">
          <p class="text-gray-500 text-center">暂无数据</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button
            @click="handleEditFromDetail"
            type="primary"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            编辑设备
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download, Picture } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'
import { getEquipmentStatusTagType } from '@/utils/constants.js'
import FilterPanel from '@/components/FilterPanel.vue'
import EquipmentTable from '@/views/equipment/components/EquipmentTable.vue'
import EquipmentFormDialog from '@/views/equipment/components/EquipmentFormDialog.vue'

// 响应式数据
const showDetailDialog = ref(false)
const currentEquipment = ref(null)
const detailLoading = ref(false)

// 获取组件引用
const equipmentTableRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
  type: '',
  status: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '设备名称',
    placeholder: '输入设备名称',
  },
  {
    key: 'type',
    type: 'select',
    label: '设备类型',
    placeholder: '选择设备类型',
    options: [
      { label: '医疗设备', value: 'MEDICAL' },
      { label: '护理设备', value: 'NURING' },
      { label: '清洁设备', value: 'CLEAN' },
      { label: '厨房设备', value: 'KITCHEN' },
      { label: '其他设备', value: 'OTHER' },
    ],
  },
  {
    key: 'status',
    type: 'select',
    label: '设备状态',
    placeholder: '选择状态',
    options: [
      { label: '使用中', value: 'USING' },
      { label: '维护中', value: 'MAINTAINING' },
      { label: '停用', value: 'DISABLED' },
    ],
  },
]

// 表单相关
const formVisible = ref(false)
const formData = ref({})
const formMode = ref('create') // create, edit

// 导出记录
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  equipmentTableRef.value?.resetPagination()
}

// 新增设备
const handleCreateEquipment = () => {
  formMode.value = 'create'
  formData.value = {}
  formVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  formMode.value = 'edit'

  // 获取完整的设备详情用于编辑
  try {
    console.log('获取设备详情用于编辑，rid:', row.rid)
    const data = await get(`organizational-management/equipment/detail/${row.rid}/`)
    console.log('设备详情数据:', data)

    // 转换数据格式以适配表单
    formData.value = {
      rid: data.rid,
      name: data.name,
      image: data.image,
      type: data.type,
      function: data.function,
      status: data.status,
      status_display: data.status_display,
      usage_method: data.usage_method,
      usage_duration: data.usage_duration,
      disinfection_method: data.disinfection_method,
    }

    formVisible.value = true
  } catch (error) {
    console.error('获取设备详情失败:', error)
    ElMessage.error('获取设备详情失败')
  }
}

// 查看详情
const handleView = async (row) => {
  showDetailDialog.value = true
  detailLoading.value = true

  try {
    console.log('请求设备详情，rid:', row.rid)
    const data = await get(`organizational-management/equipment/detail/${row.rid}/`)
    console.log('设备详情响应:', data)
    currentEquipment.value = data
  } catch (error) {
    console.error('获取设备详情失败:', error)
    ElMessage.error('获取设备详情失败')
    currentEquipment.value = null
  } finally {
    detailLoading.value = false
  }
}

// 行点击
const handleRowClick = (row) => {
  // 可以在这里添加行点击逻辑
}

// 从详情对话框编辑
const handleEditFromDetail = () => {
  showDetailDialog.value = false
  formMode.value = 'edit'
  formData.value = { ...currentEquipment.value }
  formVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备"${row.name}"吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        cancelButtonClass: 'el-button--default',
        customClass: 'custom-message-box',
        showClose: false,
        center: false,
        roundButton: false,
        dangerouslyUseHTMLString: false,
      }
    )

    console.log('删除设备，rid:', row.rid)

    // 调用删除接口
    await del(`organizational-management/equipment/delete/${row.rid}/`)

    console.log('设备删除成功')
    ElMessage.success('设备删除成功')

    // 刷新列表
    equipmentTableRef.value?.refresh()

  } catch (error) {
    // 如果是用户取消操作，error会是'cancel'
    if (error === 'cancel') {
      console.log('用户取消删除操作')
      return
    }

    // 网络或其他错误
    console.error('删除设备失败:', error)
    ElMessage.error('删除设备失败，请重试')
  }
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentEquipment.value = null
  detailLoading.value = false
}

// 表单提交
const handleFormSubmit = (response) => {
  console.log('表单提交成功:', response)
  formVisible.value = false
  // 刷新列表
  equipmentTableRef.value?.refresh()
}
</script>

<style scoped>
.equipment-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}

.detail-image {
  width: 200px;
  height: 200px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.detail-image:hover {
  border-color: #ec4899;
  transform: scale(1.02);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 200px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #9ca3af;
}

.image-error span {
  margin-top: 8px;
  font-size: 14px;
}

.no-data {
  padding: 2rem;
  text-align: center;
}

/* 自定义删除确认对话框样式 */
:deep(.custom-message-box) {
  border-radius: 16px !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  border: none !important;
  padding: 0 !important;
  overflow: hidden !important;
}

:deep(.custom-message-box .el-message-box__header) {
  padding: 24px 24px 16px 24px !important;
  border-bottom: none !important;
  background: white !important;
}

:deep(.custom-message-box .el-message-box__title) {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  line-height: 1.4 !important;
}

:deep(.custom-message-box .el-message-box__content) {
  padding: 0 24px 24px 24px !important;
  color: #6b7280 !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

:deep(.custom-message-box .el-message-box__message) {
  margin-left: 0 !important;
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;
}

:deep(.custom-message-box .el-message-box__status) {
  margin-top: 2px !important;
  font-size: 20px !important;
  color: #f59e0b !important;
}

:deep(.custom-message-box .el-message-box__btns) {
  padding: 16px 24px 24px 24px !important;
  text-align: right !important;
  border-top: 1px solid #f3f4f6 !important;
  background: #fafafa !important;
  margin: 0 !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
}

:deep(.custom-message-box .el-button) {
  margin: 0 !important;
  padding: 10px 20px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
}

:deep(.custom-message-box .el-button--default) {
  background: white !important;
  border: 1px solid #d1d5db !important;
  color: #374151 !important;
}

:deep(.custom-message-box .el-button--default:hover) {
  background: #f9fafb !important;
  border-color: #9ca3af !important;
}

:deep(.custom-message-box .el-button--danger) {
  background: #ef4444 !important;
  border: 1px solid #ef4444 !important;
  color: white !important;
}

:deep(.custom-message-box .el-button--danger:hover) {
  background: #dc2626 !important;
  border-color: #dc2626 !important;
}
</style>
