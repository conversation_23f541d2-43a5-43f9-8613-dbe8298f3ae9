<template>
  <div class="dishes-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">膳食管理 - 菜品管理</h1>
            <p class="text-sm text-gray-600 mt-1">管理月子中心的菜品信息</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            添加菜品
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <DishTable ref="dishTableRef" @edit="handleEdit" @row-click="handleRowClick" />

    <!-- 表单对话框 -->
    <DishFormDialog
      v-model="showFormDialog"
      :mode="formMode"
      :item-id="currentRow?.rid"
      @success="handleSubmitSuccess"
    />

    <!-- 详情查看对话框 -->
    <DishDetailDialog
      v-model="showDetailDialog"
      :dish-id="currentRow?.rid"
      @edit="handleEditFromDetail"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import DishTable from './components/DishTable.vue'
import DishFormDialog from './components/DishFormDialog.vue'
import DishDetailDialog from './components/DishDetailDialog.vue'

// 响应式数据
const showFormDialog = ref(false)
const showDetailDialog = ref(false)
const currentRow = ref(null)
const formMode = ref('add') // 'add' | 'edit'

// 获取 table 组件引用
const dishTableRef = ref(null)

// 新增菜品
const handleCreate = () => {
  formMode.value = 'add'
  currentRow.value = null
  showFormDialog.value = true
}

// 编辑菜品
const handleEdit = (row) => {
  formMode.value = 'edit'
  currentRow.value = row
  showFormDialog.value = true
}

// 表单提交成功
const handleSubmitSuccess = () => {
  showFormDialog.value = false
  currentRow.value = null
  dishTableRef.value?.refresh() // 刷新表格数据
}

// 行点击 - 查看详情
const handleRowClick = (row) => {
  currentRow.value = row
  showDetailDialog.value = true
}

// 从详情对话框编辑
const handleEditFromDetail = (row) => {
  showDetailDialog.value = false
  handleEdit(row)
}

// 关闭详情对话框
const handleCloseDetail = () => {
  showDetailDialog.value = false
  currentRow.value = null
}
</script>

<style scoped>
.dishes-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
