<template>
  <div class="settings-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">膳食设置</h1>
            <p class="text-sm text-gray-600 mt-1">配置膳食相关参数和规则</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleSaveSettings"
            :loading="saving"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Check />
            </el-icon>
            保存设置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 设置卡片容器 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 基本设置 -->
      <DietBasicSettings v-model:settings="basicSettings" class="lg:col-span-1" />

      <!-- 膳食类型管理 -->
      <DietTypeManagement
        v-model:dietTypes="dietTypes"
        @add="handleAddDietType"
        @edit="handleEditDietType"
        @delete="handleDeleteDietType"
        class="lg:col-span-1"
      />

      <!-- 营养标准设置 -->
      <NutritionStandards v-model:standards="nutritionStandards" class="lg:col-span-1" />

      <!-- 过敏原管理 -->
      <AllergenManagement v-model:allergens="allergenSettings" class="lg:col-span-1" />
    </div>

    <!-- 膳食类型弹窗 -->
    <DietTypeDialog
      v-model:visible="dialogVisible"
      :diet-type="currentDietType"
      :mode="dialogMode"
      @save="handleSaveDietType"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElMessage, ElIcon, ElMessageBox } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import DietBasicSettings from './components/DietBasicSettings.vue'
import DietTypeManagement from './components/DietTypeManagement.vue'
import NutritionStandards from './components/NutritionStandards.vue'
import AllergenManagement from './components/AllergenManagement.vue'
import DietTypeDialog from './components/DietTypeDialog.vue'

// 响应式数据
const saving = ref(false)
const dialogVisible = ref(false)
const dialogMode = ref('add') // 'add' | 'edit'
const currentDietType = ref(null)

// 基本设置
const basicSettings = reactive({
  mealTimes: {
    breakfast: '07:30',
    lunch: '12:00',
    dinner: '18:00',
    snack: '15:30',
  },
  deliveryAdvanceTime: 15,
})

// 膳食类型
const dietTypes = ref([
  {
    id: '1',
    name: '标准月子餐',
    description: '适合大部分产妇的标准营养配餐',
    targetGroup: '产后妈妈',
    isDefault: true,
  },
  {
    id: '2',
    name: '素食月子餐',
    description: '适合素食主义者的营养配餐',
    targetGroup: '素食产妇',
    isDefault: false,
  },
  {
    id: '3',
    name: '特殊营养餐',
    description: '针对特殊营养需求的定制配餐',
    targetGroup: '特殊需求产妇',
    isDefault: false,
  },
])

// 营养标准
const nutritionStandards = reactive({
  calories: {
    min: 1800,
    max: 2200,
  },
  protein: {
    min: 20,
    max: 30,
  },
  carbohydrate: {
    min: 45,
    max: 55,
  },
})

// 过敏原设置
const allergenSettings = reactive({
  enabled: true,
  commonAllergens: [
    { name: '海鲜', enabled: true },
    { name: '花生', enabled: true },
    { name: '乳制品', enabled: true },
    { name: '坚果', enabled: true },
    { name: '大豆', enabled: true },
    { name: '小麦', enabled: true },
  ],
})

// 方法
const handleSaveSettings = async () => {
  saving.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))
    ElMessage.success('设置保存成功')
  } catch (error) {
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const handleAddDietType = () => {
  currentDietType.value = null
  dialogMode.value = 'add'
  dialogVisible.value = true
}

const handleEditDietType = (dietType) => {
  currentDietType.value = { ...dietType }
  dialogMode.value = 'edit'
  dialogVisible.value = true
}

const handleDeleteDietType = async (dietType) => {
  try {
    await ElMessageBox.confirm(`确定要删除膳食类型 "${dietType.name}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const index = dietTypes.value.findIndex((item) => item.id === dietType.id)
    if (index !== -1) {
      dietTypes.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const handleSaveDietType = (dietTypeData) => {
  if (dialogMode.value === 'add') {
    // 添加新类型
    dietTypes.value.push({
      ...dietTypeData,
      id: Date.now().toString(),
    })
    ElMessage.success('膳食类型添加成功')
  } else {
    // 更新类型
    const index = dietTypes.value.findIndex((item) => item.id === dietTypeData.id)
    if (index !== -1) {
      dietTypes.value[index] = dietTypeData
    }
    ElMessage.success('膳食类型更新成功')
  }
  dialogVisible.value = false
}

onMounted(() => {
  // 初始化加载数据
})
</script>

<style scoped>
.settings-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
