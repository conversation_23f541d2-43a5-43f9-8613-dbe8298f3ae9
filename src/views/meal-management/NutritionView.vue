<template>
  <div class="bg-gray-50 min-h-screen p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
      <div class="flex justify-between items-start">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 mb-2">营养分析</h1>
          <p class="text-gray-600">分析膳食营养成分和搭配，为产妇提供科学的营养指导</p>
        </div>
        <div class="flex gap-3">
          <el-button @click="exportReport">
            <el-icon class="mr-1">
              <Download />
            </el-icon>
            导出报告
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选组件 -->
    <div class="mb-6 animate-fade-in">
      <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" />
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 items-stretch">
      <!-- 营养摄入概览 -->
      <div class="animate-fade-in h-full" style="animation-delay: 0.1s">
        <NutritionOverview />
      </div>

      <!-- 营养均衡分析 -->
      <div class="animate-fade-in h-full" style="animation-delay: 0.2s">
        <NutritionBalance />
      </div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex flex-col items-center">
        <el-icon class="text-3xl text-pink-500 animate-spin mb-3">
          <Loading />
        </el-icon>
        <span class="text-gray-600">正在加载营养数据...</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElIcon, ElMessage } from 'element-plus'
import {
  Download,
  Refresh,
  DataAnalysis,
  Operation,
  Loading,
  User,
  Calendar,
  TrendCharts,
  Document,
  Setting,
  Upload,
} from '@element-plus/icons-vue'

// 导入子组件
import FilterPanel from '@/components/FilterPanel.vue'
import NutritionOverview from './components/NutritionOverview.vue'
import NutritionBalance from './components/NutritionBalance.vue'

// 响应式数据
const loading = ref(false)
const filters = ref({
  timeRange: '7days',
  dietType: '',
  nutritionType: '',
  startDate: '',
  endDate: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'timeRange',
    type: 'select',
    label: '时间范围',
    placeholder: '选择时间范围',
    options: [
      { label: '最近7天', value: '7days' },
      { label: '最近30天', value: '30days' },
      { label: '最近90天', value: '90days' },
      { label: '自定义', value: 'custom' },
    ],
  },
  {
    key: 'dietType',
    type: 'select',
    label: '膳食类型',
    placeholder: '选择膳食类型',
    options: [
      { label: '标准月子餐', value: 'standard' },
      { label: '素食月子餐', value: 'vegetarian' },
      { label: '特殊营养餐', value: 'special' },
    ],
  },
  {
    key: 'nutritionType',
    type: 'select',
    label: '营养指标',
    placeholder: '选择营养指标',
    options: [
      { label: '热量', value: 'calories' },
      { label: '蛋白质', value: 'protein' },
      { label: '碳水化合物', value: 'carbohydrate' },
      { label: '脂肪', value: 'fat' },
    ],
  },
]

// 汇总数据
const summaryData = reactive([
  {
    title: '分析人数',
    value: '126',
    icon: 'User',
    color: 'text-pink-500',
  },
  {
    title: '分析天数',
    value: '30',
    icon: 'Calendar',
    color: 'text-green-500',
  },
  {
    title: '平均评分',
    value: '8.5',
    icon: 'TrendCharts',
    color: 'text-blue-500',
  },
  {
    title: '生成报告',
    value: '45',
    icon: 'Document',
    color: 'text-purple-500',
  },
])

// 快速操作
const quickActions = [
  {
    title: '营养标准设置',
    icon: 'Setting',
    handler: () => {
      console.log('打开营养标准设置')
      ElMessage.info('功能开发中...')
    },
  },
  {
    title: '生成周报',
    icon: 'Document',
    handler: () => {
      console.log('生成营养分析周报')
      ElMessage.success('周报生成中，请稍候...')
    },
  },
  {
    title: '数据导入',
    icon: 'Upload',
    handler: () => {
      console.log('导入营养数据')
      ElMessage.info('功能开发中...')
    },
  },
]

// 搜索处理
const handleSearch = (searchFilters) => {
  console.log('搜索营养数据:', searchFilters)
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已更新')
  }, 1500)
}

// 导出报告
const exportReport = () => {
  console.log('导出营养分析报告')
  ElMessage.success('报告导出中，请稍候...')
}

// 刷新数据
const refreshData = () => {
  console.log('刷新营养数据')
  loading.value = true

  setTimeout(() => {
    loading.value = false
    ElMessage.success('数据已刷新')
  }, 1000)
}

// 页面挂载
onMounted(() => {
  console.log('营养分析页面已加载')
})
</script>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out forwards;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}
</style>
