<template>
  <div class="patient-meal-detail-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">产妇膳食详情</h1>
            <p class="text-sm text-gray-600 mt-1">查看产妇的膳食计划和营养信息</p>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="flex gap-3">
          <el-button
            type="primary"
            @click="handleCreate"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            <el-icon class="mr-2">
              <Plus />
            </el-icon>
            添加膳食
          </el-button>
        </div>
      </div>
    </div>

    <!-- 产妇基本信息卡片 -->
    <div v-if="maternityInfo" class="maternity-info-card bg-white rounded-lg shadow-sm p-6 mb-6">
      <div class="flex items-center">
        <el-avatar :size="60" class="mr-4 bg-pink-100 text-pink-600 text-xl">
          {{ maternityInfo.name?.charAt(0) }}
        </el-avatar>
        <div class="flex-1">
          <h2 class="text-xl font-semibold text-gray-800 mb-2">{{ maternityInfo.name }}</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
            <div>
              <span class="text-gray-500">联系电话：</span>
              {{ maternityInfo.phone }}
            </div>
            <div>
              <span class="text-gray-500">年龄：</span>
              {{ maternityInfo.age }}岁
            </div>
            <div>
              <span class="text-gray-500">性别：</span>
              {{ maternityInfo.gender_display }}
            </div>
            <div>
              <span class="text-gray-500">籍贯：</span>
              {{ maternityInfo.native_place }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 膳食计划卡片列表 -->
    <DietRecordCards
      ref="dietRecordCardsRef"
      :filters="currentFilters"
      :maternity-id="maternityId"
      @card-click="handleCardClick"
      @edit-click="handleEditClick"
      @maternity-info="setMaternityInfo"
      @existing-dates="setExistingDates"
    />

    <!-- 添加/编辑膳食对话框 -->
    <MealFormDialog
      v-model="showMealDialog"
      :maternity-id="maternityId"
      :disabled-dates="existingDates"
      :item-id="editingRecordId"
      @success="handleMealCreated"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { Plus } from '@element-plus/icons-vue'
import DietRecordCards from './components/DietRecordCards.vue'
import MealFormDialog from './components/MealFormDialog.vue'

// 获取路由参数中的产妇ID
const route = useRoute()
const maternityId = ref(route.params.id || '7') // 默认使用7作为测试

// 响应式数据
const maternityInfo = ref(null)
const existingDates = ref([])

// 获取 DietRecordCards 组件引用
const dietRecordCardsRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  date_range: '',
  meal_type: '',
  keyword: '',
})

// 显示添加膳食对话框
const showMealDialog = ref(false)

// 编辑记录ID
const editingRecordId = ref(null)

// 方法
const handleCreate = () => {
  editingRecordId.value = null
  showMealDialog.value = true
}

// 卡片点击事件
const handleCardClick = (record) => {
  console.log(record)
  // ElMessage.success(`查看 ${record.date} 的详细膳食计划`)
}

// 编辑点击事件
const handleEditClick = (record) => {
  editingRecordId.value = record.rid
  showMealDialog.value = true
}

// 设置产妇信息（由子组件回调设置）
const setMaternityInfo = (info) => {
  maternityInfo.value = info
}

// 设置已有记录的日期（由子组件回调设置）
const setExistingDates = (dates) => {
  existingDates.value = dates
}

// 处理膳食计划创建/更新成功
const handleMealCreated = () => {
  // 重置编辑状态
  editingRecordId.value = null
  // 刷新膳食计划列表
  if (dietRecordCardsRef.value) {
    dietRecordCardsRef.value.refresh()
  }
}

// 暴露方法给子组件调用
defineExpose({
  setMaternityInfo,
})

onMounted(() => {
  // 组件挂载后，数据加载由子组件处理
})
</script>

<style scoped>
.patient-meal-detail-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

.maternity-info-card {
  transition: all 0.3s ease;
}

.maternity-info-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
