<template>
  <el-dialog
    v-model="visible"
    :title="props.itemId ? '编辑菜品' : '添加菜品'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-form-item label="菜品名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入菜品名称"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="菜品描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入菜品描述（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="卡路里" prop="calorie">
            <el-input
              v-model.number="formData.calorie"
              placeholder="kcal"
              type="number"
              min="0"
              step="0.1"
            >
              <template #suffix>
                <span class="text-gray-400">kcal</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="蛋白质" prop="protein">
            <el-input
              v-model.number="formData.protein"
              placeholder="g"
              type="number"
              min="0"
              step="0.1"
            >
              <template #suffix>
                <span class="text-gray-400">g</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="碳水化合物" prop="carbohydrate">
            <el-input
              v-model.number="formData.carbohydrate"
              placeholder="g"
              type="number"
              min="0"
              step="0.1"
            >
              <template #suffix>
                <span class="text-gray-400">g</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="脂肪" prop="fat">
            <el-input
              v-model.number="formData.fat"
              placeholder="g"
              type="number"
              min="0"
              step="0.1"
            >
              <template #suffix>
                <span class="text-gray-400">g</span>
              </template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ props.itemId ? '保存' : '添加' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { get, post, put } from '@/utils/request.js'

const emit = defineEmits(['update:modelValue', 'success'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// 响应式数据
const loading = ref(false)
const formRef = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  calorie: null,
  protein: null,
  carbohydrate: null,
  fat: null,
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入菜品名称', trigger: 'blur' },
    { min: 1, max: 50, message: '菜品名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  calorie: [{ type: 'number', message: '卡路里必须为数字', trigger: 'blur' }],
  protein: [{ type: 'number', message: '蛋白质必须为数字', trigger: 'blur' }],
  carbohydrate: [{ type: 'number', message: '碳水化合物必须为数字', trigger: 'blur' }],
  fat: [{ type: 'number', message: '脂肪必须为数字', trigger: 'blur' }],
}

// 重置表单
const resetForm = () => {
  formData.name = ''
  formData.description = ''
  formData.calorie = null
  formData.protein = null
  formData.carbohydrate = null
  formData.fat = null

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 加载菜品详情
const loadDishDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const data = await get(`customer-service/dishes/detail/${props.itemId}/`)

    // 填充表单数据
    formData.name = data.name || ''
    formData.description = data.description || ''
    formData.calorie = data.calorie
    formData.protein = data.protein
    formData.carbohydrate = data.carbohydrate
    formData.fat = data.fat
  } catch (error) {
    console.error('获取菜品详情失败:', error)
    ElMessage.error('获取菜品详情失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  loading.value = true
  try {
    const submitData = {
      name: formData.name,
      description: formData.description || null,
      calorie: formData.calorie || null,
      protein: formData.protein || null,
      carbohydrate: formData.carbohydrate || null,
      fat: formData.fat || null,
    }

    if (!props.itemId) {
      await post('customer-service/dishes/create/', submitData)
      ElMessage.success('菜品添加成功')
    } else {
      await put(`customer-service/dishes/update/${props.itemId}/`, submitData)
      ElMessage.success('菜品更新成功')
    }

    emit('success')
    handleClose()
  } catch (error) {
    console.error('保存菜品失败:', error)
    ElMessage.error('保存菜品失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      resetForm()
      if (props.itemId) {
        loadDishDetail()
      }
    }
  },
  { immediate: true },
)
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input__suffix-inner .text-gray-400) {
  font-size: 12px;
}
</style>
