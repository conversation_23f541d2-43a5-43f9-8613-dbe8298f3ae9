<template>
  <div
    class="meal-plan-card bg-gray-50 rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-300"
  >
    <!-- 套餐标题 -->
    <div class="card-header flex items-center justify-between mb-4">
      <div class="flex items-center gap-3">
        <div class="plan-icon w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
          <el-icon class="text-pink-500" :size="20">
            <Dish />
          </el-icon>
        </div>
        <div>
          <h4 class="text-lg font-semibold text-gray-800">{{ planData.name }}</h4>
          <p class="text-sm text-gray-600">{{ planData.description }}</p>
        </div>
      </div>

      <div class="card-actions flex items-center gap-2">
        <el-button
          @click="handleEditPlan"
          circle
          size="small"
          class="text-gray-500 border-gray-300 hover:text-pink-500 hover:border-pink-300"
        >
          <el-icon><Edit /></el-icon>
        </el-button>
        <el-dropdown @command="handleMoreAction">
          <el-button
            circle
            size="small"
            class="text-gray-500 border-gray-300 hover:text-pink-500 hover:border-pink-300"
          >
            <el-icon><More /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="copy">复制套餐</el-dropdown-item>
              <el-dropdown-item command="template">保存为模板</el-dropdown-item>
              <el-dropdown-item command="delete" divided>删除套餐</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 菜品列表 -->
    <div class="meal-items mb-4">
      <div
        v-for="(item, index) in planData.items"
        :key="index"
        class="meal-item flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0"
      >
        <div class="item-info flex items-center gap-3 flex-1">
          <div
            class="item-icon w-8 h-8 bg-white rounded-full flex items-center justify-center border"
          >
            <el-icon class="text-pink-500" :size="14">
              <component :is="getItemIcon(item.type)" />
            </el-icon>
          </div>
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <span class="text-gray-800 font-medium">{{ item.name }}</span>
              <el-tag
                v-if="item.type"
                :type="getItemTypeColor(item.type)"
                size="small"
                effect="light"
              >
                {{ getItemTypeLabel(item.type) }}
              </el-tag>
            </div>
            <div v-if="item.description" class="text-sm text-gray-600 mt-1">
              {{ item.description }}
            </div>
          </div>
        </div>

        <div class="item-actions flex items-center gap-2">
          <el-button
            @click="handleEditItem(item, index)"
            link
            type="primary"
            size="small"
            class="text-pink-500 hover:text-pink-600"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
          <el-button @click="handleDeleteItem(index)" link type="danger" size="small">
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="!planData.items || planData.items.length === 0"
        class="empty-state text-center py-8 text-gray-500"
      >
        <el-icon :size="32" class="mb-2"><DocumentDelete /></el-icon>
        <p>暂无菜品，点击下方按钮添加</p>
      </div>
    </div>

    <!-- 营养信息 -->
    <div class="nutrition-info bg-white rounded-lg p-4 mb-4" v-if="planData.nutrition">
      <h5 class="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
        <el-icon class="text-pink-500"><TrendCharts /></el-icon>
        营养成分
      </h5>
      <div class="nutrition-grid grid grid-cols-3 gap-4">
        <div class="nutrition-item text-center">
          <div class="value text-xl font-bold text-pink-600">
            {{ planData.nutrition.calories || 0 }}
          </div>
          <div class="label text-xs text-gray-600">热量(kcal)</div>
        </div>
        <div class="nutrition-item text-center">
          <div class="value text-xl font-bold text-blue-600">
            {{ planData.nutrition.protein || 0 }}
          </div>
          <div class="label text-xs text-gray-600">蛋白质(g)</div>
        </div>
        <div class="nutrition-item text-center">
          <div class="value text-xl font-bold text-green-600">
            {{ planData.nutrition.carbs || 0 }}
          </div>
          <div class="label text-xs text-gray-600">碳水(g)</div>
        </div>
      </div>
    </div>

    <!-- 添加菜品按钮 -->
    <div class="add-item-btn">
      <el-button
        @click="handleAddItem"
        class="w-full text-pink-500 border-pink-200 hover:bg-pink-50 hover:border-pink-300"
        dashed
      >
        <el-icon class="mr-2"><Plus /></el-icon>
        添加菜品
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ElButton, ElIcon, ElTag, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import {
  Dish,
  Edit,
  Delete,
  Plus,
  More,
  DocumentDelete,
  TrendCharts,
  // 菜品类型图标
  Bowl,
  Coffee,
  IceTea,
  Chicken,
} from '@element-plus/icons-vue'

// 定义属性
const props = defineProps({
  planData: {
    type: Object,
    required: true,
    default: () => ({
      id: '',
      name: '',
      description: '',
      items: [],
      nutrition: {
        calories: 0,
        protein: 0,
        carbs: 0,
      },
    }),
  },
  mealType: {
    type: String,
    default: 'breakfast',
  },
})

// 定义事件
const emit = defineEmits(['edit-plan', 'edit-item', 'add-item', 'delete-item', 'more-action'])

// 方法
const handleEditPlan = () => {
  emit('edit-plan', props.planData)
}

const handleAddItem = () => {
  emit('add-item', props.planData)
}

const handleEditItem = (item, index) => {
  emit('edit-item', { item, index, planData: props.planData })
}

const handleDeleteItem = (index) => {
  emit('delete-item', { index, planData: props.planData })
}

const handleMoreAction = (command) => {
  emit('more-action', { command, planData: props.planData })
}

// 获取菜品类型图标
const getItemIcon = (type) => {
  const iconMap = {
    main: Bowl, // 主食
    side: Chicken, // 配菜
    soup: Coffee, // 汤品
    dessert: IceTea, // 甜点
    drink: Coffee, // 饮品
  }
  return iconMap[type] || Bowl
}

// 获取菜品类型颜色
const getItemTypeColor = (type) => {
  const colorMap = {
    main: 'primary',
    side: 'success',
    soup: 'warning',
    dessert: 'danger',
    drink: 'info',
  }
  return colorMap[type] || 'primary'
}

// 获取菜品类型标签
const getItemTypeLabel = (type) => {
  const labelMap = {
    main: '主食',
    side: '配菜',
    soup: '汤品',
    dessert: '甜点',
    drink: '饮品',
  }
  return labelMap[type] || '其他'
}
</script>

<style scoped>
.meal-plan-card {
  transition: all 0.3s ease;
  position: relative;
}

.meal-plan-card:hover {
  transform: translateY(-2px);
  border-color: rgb(251 207 232);
  box-shadow:
    0 10px 25px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.plan-icon {
  transition: all 0.2s ease;
}

.meal-plan-card:hover .plan-icon {
  transform: scale(1.1);
}

.meal-item {
  transition: all 0.2s ease;
}

.meal-item:hover {
  background-color: rgba(249, 168, 212, 0.05);
  border-radius: 8px;
  margin: 0 -8px;
  padding-left: 8px;
  padding-right: 8px;
}

.item-icon {
  transition: all 0.2s ease;
}

.meal-item:hover .item-icon {
  transform: scale(1.1);
  background-color: rgb(251 207 232);
}

.nutrition-item {
  transition: all 0.2s ease;
}

.nutrition-item:hover {
  transform: scale(1.05);
}

.empty-state {
  transition: all 0.2s ease;
}

.empty-state:hover {
  color: rgb(236 72 153);
}

:deep(.el-button.is-dashed) {
  border-style: dashed;
}
</style>
