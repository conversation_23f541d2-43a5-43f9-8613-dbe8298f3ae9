<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <el-icon class="text-pink-500 mr-2">
          <Bowl />
        </el-icon>
        <h3 class="text-lg font-semibold text-gray-800">膳食类型管理</h3>
      </div>
      <el-button
        type="primary"
        size="small"
        @click="$emit('add')"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        添加类型
      </el-button>
    </div>

    <div class="space-y-3">
      <div
        v-for="dietType in dietTypes"
        :key="dietType.id"
        class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-pink-200 transition-colors"
      >
        <div class="flex items-center space-x-3">
          <div class="w-2 h-2 rounded-full bg-pink-500"></div>
          <div class="flex-1">
            <div class="flex items-center space-x-2">
              <h4 class="font-medium text-gray-900">{{ dietType.name }}</h4>
              <el-tag v-if="dietType.isDefault" type="success" size="small" effect="plain">
                默认
              </el-tag>
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ dietType.description }}</p>
            <p class="text-xs text-gray-400 mt-1">适用人群：{{ dietType.targetGroup }}</p>
          </div>
        </div>

        <div class="flex items-center space-x-2">
          <el-button
            size="small"
            @click="$emit('edit', dietType)"
            class="text-gray-600 hover:text-pink-500"
          >
            <el-icon>
              <Edit />
            </el-icon>
          </el-button>
          <el-button
            size="small"
            @click="$emit('delete', dietType)"
            class="text-gray-600 hover:text-red-500"
            :disabled="dietType.isDefault"
          >
            <el-icon>
              <Delete />
            </el-icon>
          </el-button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!dietTypes || dietTypes.length === 0" class="text-center py-8 text-gray-500">
        <el-icon class="text-4xl mb-2">
          <Bowl />
        </el-icon>
        <p>暂无膳食类型</p>
        <el-button @click="$emit('add')" class="mt-2 text-pink-500"> 添加第一个膳食类型 </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElButton, ElIcon, ElTag } from 'element-plus'
import { Bowl, Plus, Edit, Delete } from '@element-plus/icons-vue'

// Props
defineProps({
  dietTypes: {
    type: Array,
    default: () => [],
  },
})

// Emits
defineEmits(['update:dietTypes', 'add', 'edit', 'delete'])
</script>

<style scoped>
.hover\:border-pink-200:hover {
  border-color: #fce7f3;
}
</style>
