<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center mb-6">
      <el-icon class="text-pink-500 mr-2">
        <TrendCharts />
      </el-icon>
      <h3 class="text-lg font-semibold text-gray-800">营养标准设置</h3>
    </div>

    <div class="space-y-6">
      <!-- 每日热量摄入 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"> 每日热量摄入 (kcal) </label>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-xs text-gray-500 mb-1">最低</label>
            <el-input-number
              v-model="standards.calories.min"
              :min="1000"
              :max="3000"
              :step="50"
              controls-position="right"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">最高</label>
            <el-input-number
              v-model="standards.calories.max"
              :min="1000"
              :max="3000"
              :step="50"
              controls-position="right"
              class="w-full"
            />
          </div>
        </div>
      </div>

      <!-- 蛋白质摄入比例 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"> 蛋白质摄入比例 (%) </label>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-xs text-gray-500 mb-1">最低</label>
            <el-input-number
              v-model="standards.protein.min"
              :min="10"
              :max="40"
              :step="1"
              controls-position="right"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">最高</label>
            <el-input-number
              v-model="standards.protein.max"
              :min="10"
              :max="40"
              :step="1"
              controls-position="right"
              class="w-full"
            />
          </div>
        </div>
      </div>

      <!-- 碳水化合物摄入比例 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3"> 碳水化合物摄入比例 (%) </label>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-xs text-gray-500 mb-1">最低</label>
            <el-input-number
              v-model="standards.carbohydrate.min"
              :min="30"
              :max="70"
              :step="1"
              controls-position="right"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">最高</label>
            <el-input-number
              v-model="standards.carbohydrate.max"
              :min="30"
              :max="70"
              :step="1"
              controls-position="right"
              class="w-full"
            />
          </div>
        </div>
      </div>

      <!-- 建议提示 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
          <el-icon class="text-blue-500 mt-0.5 mr-2">
            <InfoFilled />
          </el-icon>
          <div class="text-sm text-blue-700">
            <p class="font-medium mb-1">营养标准建议</p>
            <ul class="space-y-1 text-xs">
              <li>• 产妇每日热量需求通常在1800-2200kcal之间</li>
              <li>• 蛋白质应占总热量的20-30%，支持产后恢复</li>
              <li>• 碳水化合物应占总热量的45-55%，提供充足能量</li>
              <li>• 脂肪应占总热量的25-30%，有助于激素合成</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElInputNumber, ElIcon } from 'element-plus'
import { TrendCharts, InfoFilled } from '@element-plus/icons-vue'

// Props
defineProps({
  standards: {
    type: Object,
    required: true,
  },
})

// Emits
defineEmits(['update:standards'])
</script>

<style scoped>
:deep(.el-input-number) {
  width: 100%;
}
</style>
