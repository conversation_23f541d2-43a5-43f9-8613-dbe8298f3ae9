<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="meal-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" class="meal-form">
        <!-- 日期选择器 -->
        <el-form-item label="记录日期" prop="date" class="mb-6">
          <MealDateFilter
            v-model="form.date"
            :disabled-dates="disabledDates"
            :disabled="!!props.itemId"
            class="w-full"
          />
          <div v-if="props.itemId" class="text-sm text-gray-500 mt-1">编辑模式下日期不可修改</div>
        </el-form-item>

        <!-- 每日备注 -->
        <el-form-item label="每日备注" prop="daily_remark" class="mb-6">
          <el-input
            v-model="form.daily_remark"
            type="textarea"
            :rows="2"
            placeholder="请输入当日膳食的整体备注"
          />
        </el-form-item>

        <div v-for="mealType in MEAL_TYPE_OPTIONS" :key="mealType.value" class="meal-section mb-6">
          <h5 class="meal-title text-lg font-medium text-gray-800 mb-3">
            {{ mealType.label }}
          </h5>

          <div class="grid grid-cols-1 gap-3">
            <el-form-item :label="`${mealType.label}菜品`" class="mb-3">
              <el-select
                v-model="form.daily_meals[mealType.value].dishes"
                multiple
                placeholder="请选择菜品"
                class="w-full"
                :loading="dishesLoading"
                filterable
                clearable
              >
                <el-option
                  v-for="dish in dishOptions"
                  :key="dish.rid"
                  :label="dish.name"
                  :value="dish.rid"
                >
                  <div class="dish-option">
                    <div class="dish-name">{{ dish.name }}</div>
                    <div class="dish-desc text-sm text-gray-500">{{ dish.description }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item :label="`${mealType.label}备注`" class="mb-0">
              <el-input
                v-model="form.daily_meals[mealType.value].remark"
                type="textarea"
                :rows="2"
                :placeholder="`请输入${mealType.label}备注`"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '保存记录' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { MEAL_TYPE_OPTIONS } from '@/utils/constants.js'
import MealDateFilter from './MealDateFilter.vue'
import { getNextAvailableDate } from '@/utils/utils.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  maternityId: {
    type: [String, Number],
    default: null,
  },
  disabledDates: {
    type: Array,
    default: () => [],
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)
const dishesLoading = ref(false)
const dishOptions = ref([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑膳食计划' : '添加膳食计划'
})

// 表单数据
const form = reactive({
  id: '',
  date: '',
  daily_remark: '',
  daily_meals: {},
})

// 初始化每餐数据结构
const initializeMealData = () => {
  MEAL_TYPE_OPTIONS.forEach((mealType) => {
    form.daily_meals[mealType.value] = {
      dishes: [],
      remark: '',
    }
  })
}

// 表单验证规则
const rules = computed(() => ({
  date: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
}))

// 获取菜品列表
const fetchDishes = async () => {
  dishesLoading.value = true
  try {
    const response = await get('customer-service/dishes/list/', {
      page: 1,
      page_size: 100,
    })

    if (response && response.list) {
      dishOptions.value = response.list
    }
  } catch (error) {
    console.error('获取菜品列表失败:', error)
    ElMessage.error('获取菜品列表失败')
  } finally {
    dishesLoading.value = false
  }
}

// 获取膳食计划详情数据（用于编辑模式）
const fetchMealDetail = async (recordId) => {
  if (!recordId) return

  loading.value = true
  try {
    const response = await get(`customer-service/maternity-diet-records/detail/${recordId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到膳食计划详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取膳食计划详情失败:', error)
    ElMessage.error('获取膳食计划详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  const formData = {
    id: apiData.rid,
    date: apiData.date,
    daily_remark: apiData.daily_remark || '',
    daily_meals: {},
  }

  // 转换每餐数据：从详情格式转换为表单格式
  MEAL_TYPE_OPTIONS.forEach((mealType) => {
    const mealDetail = apiData.daily_meals_detail[mealType.value]
    formData.daily_meals[mealType.value] = {
      dishes: mealDetail && mealDetail.dishes ? mealDetail.dishes.map((dish) => dish.rid) : [],
      remark: mealDetail ? mealDetail.remark || '' : '',
    }
  })

  return formData
}

// 滚动到顶部
const scrollToTop = async () => {
  await nextTick()
  const scrollContainer = document.querySelector('.meal-dialog .max-h-\\[70vh\\]')
  if (scrollContainer) {
    scrollContainer.scrollTop = 0
  }
}

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      // 先重置表单
      await resetForm()
      await scrollToTop()

      if (props.itemId) {
        // 编辑模式：获取详情数据
        await fetchMealDetail(props.itemId)
      } else {
        // 新增模式：设置默认日期为下一个可用日期
        form.date = getNextAvailableDate(props.disabledDates)
      }

      // 获取菜品列表
      await fetchDishes()
    }
  },
)

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  form.id = ''
  form.date = ''
  form.daily_remark = ''

  // 重新初始化每餐数据
  initializeMealData()

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()

    if (!props.maternityId) {
      ElMessage.error('产妇信息不存在')
      return
    }

    submitting.value = true

    // 构造提交数据
    const submitData = {
      daily_meals: form.daily_meals,
      daily_remark: form.daily_remark || '',
    }

    // 新增模式下才包含日期
    if (!props.itemId) {
      submitData.date = form.date
    }

    let res
    if (!props.itemId) {
      res = await post(`customer-service/maternity-diet-records/create/${props.maternityId}/`, submitData)
    } else {
      res = await put(`customer-service/maternity-diet-records/update/${form.id}/`, submitData)
    }

    ElMessage.success(props.itemId ? '膳食计划更新成功' : '膳食计划添加成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    console.error(props.itemId ? '更新膳食计划失败:' : '添加膳食计划失败:', error)
    ElMessage.error(props.itemId ? '更新膳食计划失败，请稍后重试' : '添加膳食计划失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 组件挂载时初始化数据结构
onMounted(() => {
  initializeMealData()
})
</script>

<style scoped>
.meal-title {
  position: relative;
  padding-left: 0.75rem;
}

.meal-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  border-radius: 0.125rem;
}

.dish-option {
  padding: 4px 0;
}

.dish-name {
  font-weight: 500;
  color: #374151;
}

.dish-desc {
  margin-top: 2px;
  line-height: 1.3;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
