<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="plan-form-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form ref="formRef" :model="formData" :rules="rules" class="plan-form">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="套餐名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入套餐名称" />
            </el-form-item>
            <el-form-item label="套餐描述" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="3"
                placeholder="请输入套餐描述..."
              />
            </el-form-item>
          </div>
        </div>

        <!-- 营养目标 -->
        <div class="form-section mb-6">
          <h4 class="section-title">营养目标</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item prop="nutrition.calories" label="热量(kcl)">
              <el-input-number
                v-model="formData.nutrition.calories"
                :min="0"
                placeholder="目标热量"
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item prop="nutrition.protein" label="蛋白质(g)">
              <el-input-number
                v-model="formData.nutrition.protein"
                :min="0"
                :precision="1"
                placeholder="目标蛋白质"
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
            <el-form-item prop="nutrition.carbs" label="碳水(g)">
              <el-input-number
                v-model="formData.nutrition.carbs"
                :min="0"
                :precision="1"
                placeholder="目标碳水"
                class="w-full"
                controls-position="right"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '创建套餐' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElInput, ElInputNumber, ElButton } from 'element-plus'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  planData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
  mealType: {
    type: String,
    default: 'breakfast',
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  description: '',
  mealType: '',
  nutrition: {
    calories: 0,
    protein: 0,
    carbs: 0,
  },
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  const mealNames = {
    breakfast: '早餐',
    lunch: '午餐',
    dinner: '晚餐',
    snack: '加餐',
  }
  const mealName = mealNames[props.mealType] || '餐次'
  return props.mode === 'add' ? `创建${mealName}套餐` : `编辑${mealName}套餐`
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入套餐名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入套餐描述', trigger: 'blur' }],
}

// 监听数据变化
watch([() => props.visible, () => props.planData], ([newVisible, newPlanData]) => {
  if (newVisible) {
    formData.mealType = props.mealType

    if (newPlanData) {
      // 编辑模式，填充数据
      Object.assign(formData, newPlanData)
    } else {
      // 新增模式，重置表单
      resetForm()
    }
  }
})

// 方法
const handleOpen = () => {
  if (props.mode === 'add') {
    resetForm()
  }
}

const handleClose = () => {
  visible.value = false
}

const handleSave = async () => {
  try {
    await formRef.value.validate()
    saving.value = true

    const saveData = {
      ...formData,
      createdAt: new Date().toISOString(),
    }

    emit('save', saveData)
    saving.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const resetForm = () => {
  const mealDefaults = {
    breakfast: { calories: 350, protein: 15, carbs: 45 },
    lunch: { calories: 500, protein: 25, carbs: 60 },
    dinner: { calories: 450, protein: 20, carbs: 55 },
    snack: { calories: 200, protein: 8, carbs: 25 },
  }

  const defaults = mealDefaults[props.mealType] || mealDefaults.breakfast

  Object.assign(formData, {
    id: '',
    name: '',
    description: '',
    mealType: props.mealType,
    nutrition: {
      calories: defaults.calories,
      protein: defaults.protein,
      carbs: defaults.carbs,
    },
  })
}
</script>

<style scoped>
.plan-form-dialog :deep(.el-dialog__header) {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.plan-form {
  padding: 0 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #fce7f3;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 16px;
  background-color: #ec4899;
  border-radius: 2px;
}

.form-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #f0f0f0;
}
</style>
