<template>
  <div class="bg-white rounded-lg shadow p-6 h-full flex flex-col">
    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
      <el-icon class="mr-2 text-pink-500">
        <TrendCharts />
      </el-icon>
      营养摄入概览
    </h3>

    <!-- 统计数据 -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <div
        v-for="stat in nutritionStats"
        :key="stat.label"
        class="bg-gray-50 rounded-lg p-4 text-center transition-all duration-300 hover:bg-gray-100 hover:scale-105"
      >
        <div class="text-2xl font-bold text-pink-500 mb-1">{{ stat.value }}</div>
        <div class="text-sm text-gray-600 mb-2">{{ stat.label }}</div>
        <div class="flex items-center justify-center text-xs">
          <el-icon :class="['mr-1', stat.trend > 0 ? 'text-green-500' : 'text-red-500']">
            <ArrowUp v-if="stat.trend > 0" />
            <ArrowDown v-else />
          </el-icon>
          <span :class="[stat.trend > 0 ? 'text-green-500' : 'text-red-500']">
            {{ Math.abs(stat.trend) }}%
          </span>
        </div>
      </div>
    </div>

    <!-- 趋势图表 -->
    <div class="h-80">
      <div class="flex justify-between items-center mb-4">
        <h4 class="text-md font-medium text-gray-700">营养摄入趋势</h4>
        <div class="flex gap-2">
          <el-button
            v-for="option in chartOptions"
            :key="option.value"
            :type="selectedChart === option.value ? 'primary' : ''"
            :class="
              selectedChart === option.value ? 'bg-pink-500 hover:bg-pink-600 border-pink-500' : ''
            "
            size="small"
            @click="selectedChart = option.value"
          >
            {{ option.label }}
          </el-button>
        </div>
      </div>

      <!-- 这里使用占位图表，实际项目中需要集成真实的图表库 -->
      <div class="bg-gray-50 rounded-lg h-64 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <el-icon class="text-4xl mb-2">
            <TrendCharts />
          </el-icon>
          <p>{{ selectedChart }} 趋势图表</p>
          <p class="text-sm mt-1">集成 ECharts 或 Chart.js 显示实时数据</p>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="mt-6">
      <h4 class="text-md font-medium text-gray-700 mb-3">详细数据</h4>
      <el-table :data="detailData" stripe class="w-full">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="calories" label="热量 (kcal)" align="center" />
        <el-table-column prop="protein" label="蛋白质 (g)" align="center" />
        <el-table-column prop="carbohydrate" label="碳水 (g)" align="center" />
        <el-table-column prop="fat" label="脂肪 (g)" align="center" />
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <el-button
              size="small"
              @click="viewDetail(scope.row)"
              class="text-pink-500 hover:text-pink-600"
            >
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElIcon, ElButton, ElTable, ElTableColumn } from 'element-plus'
import { TrendCharts, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// 营养统计数据
const nutritionStats = reactive([
  { label: '平均热量 (kcal)', value: '2,150', trend: 5.2 },
  { label: '平均蛋白质', value: '85g', trend: 3.1 },
  { label: '平均碳水', value: '280g', trend: -2.4 },
  { label: '平均脂肪', value: '65g', trend: 1.8 },
])

// 图表选项
const chartOptions = [
  { label: '热量', value: 'calories' },
  { label: '蛋白质', value: 'protein' },
  { label: '碳水', value: 'carbohydrate' },
  { label: '脂肪', value: 'fat' },
]

// 选中的图表类型
const selectedChart = ref('calories')

// 详细数据
const detailData = reactive([
  {
    date: '2024-01-15',
    calories: 2100,
    protein: 82,
    carbohydrate: 275,
    fat: 63,
  },
  {
    date: '2024-01-14',
    calories: 2150,
    protein: 84,
    carbohydrate: 280,
    fat: 65,
  },
  {
    date: '2024-01-13',
    calories: 2200,
    protein: 85,
    carbohydrate: 285,
    fat: 67,
  },
  {
    date: '2024-01-12',
    calories: 2180,
    protein: 83,
    carbohydrate: 278,
    fat: 64,
  },
  {
    date: '2024-01-11',
    calories: 2250,
    protein: 86,
    carbohydrate: 290,
    fat: 68,
  },
])

// 查看详情
const viewDetail = (row) => {
  console.log('查看详情:', row)
  // 这里可以打开详情弹窗或跳转到详情页面
}
</script>

<style scoped>
.hover\:scale-105:hover {
  transform: scale(1.05);
}
</style>
