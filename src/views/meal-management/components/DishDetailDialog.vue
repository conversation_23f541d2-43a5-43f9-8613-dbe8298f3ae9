<template>
  <el-dialog
    v-model="visible"
    title="菜品详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>菜品名称：</label>
              <span class="font-medium">{{ detailData.name }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ detailData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ detailData.updated_at }}</span>
            </div>
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="detailData.is_active ? 'success' : 'danger'">
                {{ detailData.is_active ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>

          <!-- 菜品描述 -->
          <div v-if="detailData.description" class="mt-4">
            <label class="text-sm font-medium text-gray-700 mb-2 block">菜品描述：</label>
            <div class="reason-content p-4 bg-gray-50 rounded-lg">
              {{ detailData.description }}
            </div>
          </div>
        </div>

        <!-- 营养信息 -->
        <div class="detail-section">
          <h3 class="section-title">营养信息</h3>
          <div class="nutrition-grid grid grid-cols-2 gap-4">
            <div class="nutrition-card p-4 bg-orange-50 rounded-lg border border-orange-200">
              <div class="flex items-center justify-between">
                <span class="text-orange-700 font-medium">卡路里</span>
                <span class="text-2xl font-bold text-orange-600">
                  {{ detailData.calorie || '-' }}
                  <span v-if="detailData.calorie" class="text-sm font-normal text-orange-500"
                    >kcal</span
                  >
                </span>
              </div>
            </div>
            <div class="nutrition-card p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div class="flex items-center justify-between">
                <span class="text-blue-700 font-medium">蛋白质</span>
                <span class="text-2xl font-bold text-blue-600">
                  {{ detailData.protein || '-' }}
                  <span v-if="detailData.protein" class="text-sm font-normal text-blue-500">g</span>
                </span>
              </div>
            </div>
            <div class="nutrition-card p-4 bg-purple-50 rounded-lg border border-purple-200">
              <div class="flex items-center justify-between">
                <span class="text-purple-700 font-medium">碳水化合物</span>
                <span class="text-2xl font-bold text-purple-600">
                  {{ detailData.carbohydrate || '-' }}
                  <span v-if="detailData.carbohydrate" class="text-sm font-normal text-purple-500"
                    >g</span
                  >
                </span>
              </div>
            </div>
            <div class="nutrition-card p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <div class="flex items-center justify-between">
                <span class="text-yellow-700 font-medium">脂肪</span>
                <span class="text-2xl font-bold text-yellow-600">
                  {{ detailData.fat || '-' }}
                  <span v-if="detailData.fat" class="text-sm font-normal text-yellow-500">g</span>
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 月子中心信息 -->
        <div v-if="detailData.maternity_center" class="detail-section">
          <h3 class="section-title">所属月子中心</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>中心名称：</label>
              <span class="font-medium">{{ detailData.maternity_center.name }}</span>
            </div>
            <div class="detail-item">
              <label>容量：</label>
              <span>{{ detailData.maternity_center.capacity }}人</span>
            </div>
            <div class="detail-item">
              <label>联系人：</label>
              <span>{{ detailData.maternity_center.contact }}</span>
            </div>
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="detailData.maternity_center.status === 1 ? 'success' : 'danger'">
                {{ detailData.maternity_center.status === 1 ? '正常' : '停用' }}
              </el-tag>
            </div>
          </div>

          <!-- 地址信息 -->
          <div class="mt-4">
            <label class="text-sm font-medium text-gray-700 mb-2 block">中心地址：</label>
            <div class="reason-content p-4 bg-gray-50 rounded-lg">
              {{ detailData.maternity_center.address }}
            </div>
          </div>
        </div>

        <!-- 操作记录 -->
        <div class="detail-section">
          <h3 class="section-title">操作记录</h3>
          <div class="space-y-3">
            <div class="p-3 bg-gray-50 rounded-lg">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">创建时间：</span>
                  <span>{{ detailData.created_at }}</span>
                </div>
                <div>
                  <span class="text-gray-600">最后更新：</span>
                  <span>{{ detailData.updated_at }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          @click="handleEdit"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-2">
            <Edit />
          </el-icon>
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  dishId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.dishId) {
    fetchDetail()
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.dishId) return

  loading.value = true
  try {
    const response = await get(`customer-service/dishes/detail/${props.dishId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取菜品详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
  detailData.value = null
}

// 编辑菜品
const handleEdit = () => {
  emit('edit', detailData.value)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.reason-content {
  color: #374151;
  line-height: 1.625;
}

.nutrition-grid {
  gap: 16px;
}

.nutrition-card {
  transition: all 0.2s ease;
}

.nutrition-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
