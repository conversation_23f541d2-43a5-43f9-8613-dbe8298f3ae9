<template>
  <div
    class="maternity-card-list-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 卡片列表标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          在住产妇列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 卡片列表内容 -->
    <div v-loading="loading" class="card-list-content">
      <div v-if="filteredData.length === 0 && !loading" class="empty-state text-center py-12">
        <el-icon size="48" class="text-gray-400 mb-4">
          <Document />
        </el-icon>
        <p class="text-gray-600">暂无数据</p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
        <div
          v-for="item in filteredData"
          :key="item.id"
          class="maternity-card bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-200 hover:border-pink-300"
          @click="handleCardClick(item)"
        >
          <!-- 卡片头部 -->
          <div class="card-header p-4 border-b border-gray-100">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <el-avatar :size="40" class="bg-pink-500 text-white font-medium">
                  {{ item.maternity.charAt(0) }}
                </el-avatar>
                <div class="ml-3">
                  <div class="flex items-center gap-2">
                    <h4 class="font-semibold text-gray-900">{{ item.maternity }}</h4>
                    <el-tag v-if="item.needAttention" type="danger" size="small"> 需要关注 </el-tag>
                    <el-tag v-if="item.isMultipleBirth" type="warning" size="small"> 多胎 </el-tag>
                  </div>
                  <p class="text-sm text-gray-500 mt-1">{{ item.phone }}</p>
                </div>
              </div>
              <div class="text-right">
                <el-tag type="info" size="small"> 房间 {{ item.roomNumber }} </el-tag>
              </div>
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="card-content p-4">
            <div class="grid grid-cols-2 gap-4">
              <!-- 住院信息 -->
              <div>
                <div class="flex items-center mb-2">
                  <el-icon class="text-blue-500 mr-2" :size="16">
                    <Calendar />
                  </el-icon>
                  <span class="text-sm font-medium text-gray-700">入住天数</span>
                </div>
                <span class="text-lg font-bold text-blue-600">{{ item.stayDays }}</span>
                <span class="text-sm text-gray-500 ml-1">天</span>
              </div>

              <!-- 主管护士 -->
              <div>
                <div class="flex items-center mb-2">
                  <el-icon class="text-green-500 mr-2" :size="16">
                    <Avatar />
                  </el-icon>
                  <span class="text-sm font-medium text-gray-700">主管护士</span>
                </div>
                <span class="text-sm text-gray-900 font-medium">{{ item.mainNurse }}</span>
              </div>
            </div>
          </div>

          <!-- 卡片底部 -->
          <div class="card-footer px-4 py-3 bg-gray-50 border-t border-gray-100">
            <div class="flex items-center justify-end">
              <el-button
                @click.stop="handleViewDetails(item)"
                type="primary"
                size="small"
                class="bg-pink-500 hover:bg-pink-600 border-pink-500"
              >
                制定计划
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[12, 24, 48, 96]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { List, Document, Calendar, Avatar } from '@element-plus/icons-vue'
import { get } from '@/utils/request.js'

const emit = defineEmits(['card-click', 'view-details'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/maternity-admission/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(12)
const total = computed(() => totalCount.value)

// 转换API数据格式
const transformMaternityData = (apiData) => {
  return apiData.map((item) => washMaternityData(item))
}

const washMaternityData = (item) => {
  return {
    id: item.aid,
    maternity: item.maternity,
    phone: item.phone,
    isMultipleBirth: item.is_multiple_birth,
    needAttention: item.need_attention,
    checkInStatus: item.check_in_status,
    newborns: item.newborns,
    roomNumber: item.room_number,
    expectedCheckInDate: item.expected_check_in_date,
    actualCheckInDate: item.actual_check_in_date,
    expectedCheckOutDate: item.expected_check_out_date,
    actualCheckOutDate: item.actual_check_out_date,
    mainNurse: item.main_nurse,
    stayDays: item.stay_days,
    recordsCount: item.records_count,
    lastUpdateTime: item.last_update_time,
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      cis: 'CHECKED_IN', // 只获取在住产妇
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = transformMaternityData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取产妇信息列表失败:', error)
    ElMessage.error('获取产妇信息列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 直接使用内部数据
const filteredData = computed(() => tableData.value)

// 事件处理
const handleCardClick = (item) => {
  emit('card-click', item)
}

const handleViewDetails = (item) => {
  emit('view-details', item)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.maternity-card-list-container {
  transition: all 0.3s ease;
  width: 100%;
}

.maternity-card-list-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.maternity-card {
  transition: all 0.2s ease;
}

.maternity-card:hover {
  transform: translateY(-2px);
  border-color: rgb(249 168 212);
}

.card-list-content {
  min-height: 400px;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
