<template>
  <div class="dish-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Bowl />
          </el-icon>
          菜品列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >

      <el-table-column prop="name" label="菜品名称" width="200">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              <el-icon>
                <Bowl />
              </el-icon>
            </el-avatar>
            <span class="font-medium">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="description" label="描述" min-width="150">
        <template #default="{ row }">
          <el-tooltip
            :content="row.description || '暂无描述'"
            placement="top"
            :disabled="!row.description || row.description.length <= 30"
          >
            <span class="text-gray-700">
              {{
                row.description
                  ? row.description.length > 30
                    ? row.description.substring(0, 30) + '...'
                    : row.description
                  : '暂无描述'
              }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column prop="calorie" label="卡路里(kcal)" width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.calorie || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="protein" label="蛋白质(g)" width="120">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.protein || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="carbohydrate" label="碳水化合物(g)" width="140">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.carbohydrate || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="fat" label="脂肪(g)" width="100">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.fat || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.created_at) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.created_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              @click.stop="handleEdit(row)"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              编辑
            </el-button>
            <el-button @click.stop="handleDelete(row)" type="danger" size="small"> 删除 </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Bowl } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'

const emit = defineEmits(['edit', 'row-click'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/dishes/list/',
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const requestParams = {
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = data.list
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取菜品列表失败:', error)
    ElMessage.error('获取菜品列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 时间格式化
const formatDate = (dateTime) => {
  return dateTime.split(' ')[0]
}

const formatTime = (dateTime) => {
  return dateTime.split(' ')[1]
}

// 事件处理
const handleEdit = (row) => {
  emit('edit', row)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除菜品"${row.name}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 调用删除接口
    await del(`customer-service/dishes/delete/${row.rid}/`)
    ElMessage.success('删除成功')

    // 刷新数据
    refresh()
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除菜品失败:', error)
    ElMessage.error('删除菜品失败')
  }
}

const handleRowClick = (row) => {
  emit('row-click', row)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.dish-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.dish-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
