<template>
  <el-dialog
    v-model="dialogVisible"
    :title="mode === 'add' ? '添加膳食类型' : '编辑膳食类型'"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="类型名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入膳食类型名称"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          placeholder="请输入膳食类型描述"
          :rows="3"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="适用人群" prop="targetGroup">
        <el-input
          v-model="formData.targetGroup"
          placeholder="请输入适用人群"
          maxlength="30"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="设置">
        <el-checkbox
          v-model="formData.isDefault"
          :disabled="mode === 'edit' && dietType?.isDefault"
        >
          设为默认膳食类型
        </el-checkbox>
        <div class="text-xs text-gray-500 ml-1">
          {{
            formData.isDefault ? '默认类型会在创建新膳食计划时自动选中' : '可在需要时手动选择此类型'
          }}
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="flex justify-end space-x-3">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ mode === 'add' ? '添加' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElCheckbox,
  ElMessage,
} from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  dietType: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

// Emits
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const dialogVisible = ref(false)
const saving = ref(false)
const formRef = ref()

// 表单数据
const formData = reactive({
  id: null,
  name: '',
  description: '',
  targetGroup: '',
  isDefault: false,
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入膳食类型名称', trigger: 'blur' },
    { min: 2, max: 20, message: '名称长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入膳食类型描述', trigger: 'blur' },
    { min: 5, max: 100, message: '描述长度在 5 到 100 个字符', trigger: 'blur' },
  ],
  targetGroup: [
    { required: true, message: '请输入适用人群', trigger: 'blur' },
    { min: 2, max: 30, message: '适用人群长度在 2 到 30 个字符', trigger: 'blur' },
  ],
}

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
    if (newVal) {
      initFormData()
    }
  },
)

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 初始化表单数据
const initFormData = () => {
  if (props.mode === 'edit' && props.dietType) {
    Object.assign(formData, props.dietType)
  } else {
    // 重置表单
    formData.id = null
    formData.name = ''
    formData.description = ''
    formData.targetGroup = ''
    formData.isDefault = false
  }

  // 清除表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 保存
const handleSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    saving.value = true

    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500))

    // 触发保存事件
    emit('save', { ...formData })

    // 关闭弹窗
    dialogVisible.value = false
  } catch (error) {
    console.log('验证失败:', error)
  } finally {
    saving.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style scoped>
:deep(.el-dialog__header) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px 20px;
}
</style>
