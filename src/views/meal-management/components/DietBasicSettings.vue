<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center mb-6">
      <el-icon class="text-pink-500 mr-2">
        <Setting />
      </el-icon>
      <h3 class="text-lg font-semibold text-gray-800">基本设置</h3>
    </div>

    <div class="space-y-6">
      <!-- 默认餐次时间 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">默认餐次时间</label>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-xs text-gray-500 mb-1">早餐</label>
            <el-time-picker
              v-model="settings.mealTimes.breakfast"
              format="HH:mm"
              value-format="HH:mm"
              placeholder="选择时间"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">午餐</label>
            <el-time-picker
              v-model="settings.mealTimes.lunch"
              format="HH:mm"
              value-format="HH:mm"
              placeholder="选择时间"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">晚餐</label>
            <el-time-picker
              v-model="settings.mealTimes.dinner"
              format="HH:mm"
              value-format="HH:mm"
              placeholder="选择时间"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-xs text-gray-500 mb-1">加餐</label>
            <el-time-picker
              v-model="settings.mealTimes.snack"
              format="HH:mm"
              value-format="HH:mm"
              placeholder="选择时间"
              class="w-full"
            />
          </div>
        </div>
      </div>

      <!-- 送餐时间设置 -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-3">默认送餐时间（提前）</label>
        <div class="flex items-center space-x-2">
          <el-input-number
            v-model="settings.deliveryAdvanceTime"
            :min="0"
            :max="60"
            :step="5"
            controls-position="right"
            class="w-32"
          />
          <span class="text-sm text-gray-500">分钟</span>
        </div>
        <p class="text-xs text-gray-400 mt-1">系统将在设定的餐次时间提前指定分钟数安排送餐</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElTimePicker, ElInputNumber, ElIcon } from 'element-plus'
import { Setting } from '@element-plus/icons-vue'

// Props
defineProps({
  settings: {
    type: Object,
    required: true,
  },
})

// Emits
defineEmits(['update:settings'])
</script>

<style scoped>
:deep(.el-time-picker) {
  width: 100%;
}
</style>
