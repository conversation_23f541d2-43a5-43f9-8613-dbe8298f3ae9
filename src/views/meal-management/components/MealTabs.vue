<template>
  <div class="meal-tabs-container">
    <el-tabs
      v-model="activeMealLocal"
      @tab-change="handleTabChange"
      class="meal-tabs"
      type="border-card"
    >
      <el-tab-pane v-for="tab in mealTabs" :key="tab.value" :label="tab.label" :name="tab.value">
        <template #label>
          <div class="flex items-center">
            <el-icon class="mr-2">
              <component :is="tab.icon" />
            </el-icon>
            {{ tab.label }}
            <el-badge
              v-if="getDishesCount(tab.value) > 0"
              :value="getDishesCount(tab.value)"
              :max="99"
              type="primary"
              class="ml-2"
            />
          </div>
        </template>

        <!-- 菜品列表内容 -->
        <div class="dishes-content pt-4" v-loading="loading">
          <!-- 菜品操作栏 -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
              <el-icon class="mr-2 text-pink-500">
                <component :is="tab.icon" />
              </el-icon>
              {{ tab.label }}菜品
              <span class="text-sm text-gray-500 ml-2"
                >共 {{ getCurrentDishesTotal(tab.value) }} 道菜品</span
              >
            </h3>
            <div class="flex gap-3">
              <el-button
                @click="handleAddMealPlan(tab.value)"
                type="primary"
                plain
                class="text-pink-500 border-pink-200 hover:bg-pink-50"
              >
                <el-icon class="mr-2"><Plus /></el-icon>
                添加套餐
              </el-button>
            </div>
          </div>

          <!-- 套餐卡片网格 -->
          <div class="meal-plans-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            <MealPlanCard
              v-for="plan in getCurrentMealPlans(tab.value)"
              :key="plan.id"
              :plan-data="plan"
              :meal-type="tab.value"
              @edit-plan="handleEditPlan"
              @edit-item="handleEditItem"
              @add-item="handleAddItem"
              @delete-item="handleDeleteItem"
              @more-action="handleMoreAction"
            />

            <!-- 空状态 -->
            <div
              v-if="getCurrentMealPlans(tab.value).length === 0 && !loading"
              class="empty-state col-span-full bg-gray-50 rounded-lg border border-gray-200 p-12 text-center"
            >
              <el-icon class="text-gray-400 mb-4" :size="48">
                <component :is="tab.icon" />
              </el-icon>
              <h4 class="text-lg font-medium text-gray-600 mb-2">暂无{{ tab.label }}套餐</h4>
              <p class="text-gray-500 mb-6">开始创建您的第一个{{ tab.label }}套餐计划</p>
              <el-button
                @click="handleAddMealPlan(tab.value)"
                type="primary"
                class="bg-pink-500 hover:bg-pink-600 border-pink-500"
              >
                <el-icon class="mr-2"><Plus /></el-icon>
                创建套餐
              </el-button>
            </div>
          </div>

          <!-- 分页（如果有数据且总数大于页面大小） -->
          <div
            v-if="getCurrentDishesTotal(tab.value) > pageSize"
            class="pagination-container mt-6 flex justify-end"
          >
            <el-pagination
              v-model:current-page="currentPage[tab.value]"
              v-model:page-size="pageSize"
              :total="getCurrentDishesTotal(tab.value)"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="(size) => handleSizeChange(size, tab.value)"
              @current-change="(page) => handleCurrentChange(page, tab.value)"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, markRaw, onMounted } from 'vue'
import { ElTabs, ElTabPane, ElIcon, ElBadge, ElButton, ElPagination, ElMessage } from 'element-plus'
import {
  Sunny, // 早餐
  Cloudy, // 午餐
  Moon, // 晚餐
  Coffee, // 加餐
  Plus,
} from '@element-plus/icons-vue'
import { get } from '@/utils/request.js'
import MealPlanCard from './MealPlanCard.vue'

// 定义属性
const props = defineProps({
  activeMeal: {
    type: String,
    default: 'breakfast',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
  apiUrl: {
    type: String,
    default: 'customer-service/dishes/list/',
  },
})

// 定义事件
const emit = defineEmits([
  'update:activeMeal',
  'add-meal-plan',
  'edit-plan',
  'edit-item',
  'add-item',
  'delete-item',
  'more-action',
])

// 响应式数据
const loading = ref(false)
const activeMealLocal = ref(props.activeMeal)

// 菜品数据存储 - 按餐次分组
const dishesData = reactive({
  breakfast: { list: [], total: 0 },
  lunch: { list: [], total: 0 },
  dinner: { list: [], total: 0 },
  snack: { list: [], total: 0 },
})

// 分页配置 - 为每个餐次单独维护分页状态
const currentPage = reactive({
  breakfast: 1,
  lunch: 1,
  dinner: 1,
  snack: 1,
})
const pageSize = ref(20)

// 餐次标签配置
const mealTabs = [
  {
    value: 'breakfast',
    label: '早餐',
    icon: markRaw(Sunny),
  },
  {
    value: 'lunch',
    label: '午餐',
    icon: markRaw(Cloudy),
  },
  {
    value: 'dinner',
    label: '晚餐',
    icon: markRaw(Moon),
  },
  {
    value: 'snack',
    label: '加餐',
    icon: markRaw(Coffee),
  },
]

// 数据转换函数
const transformDishData = (apiData) => {
  return apiData.map((item) => ({
    id: item.id,
    name: item.name,
    description: item.description,
    calorie: item.calorie || 0,
    protein: item.protein || 0,
    carbohydrate: item.carbohydrate || 0,
    fat: item.fat || 0,
    createdAt: item.created_at,
    updatedAt: item.updated_at,
    originalData: item,
  }))
}

// 将菜品数据转换为套餐格式（临时方案，后续会有专门的套餐接口）
const convertDishesToMealPlans = (dishes, mealType) => {
  if (!dishes || dishes.length === 0) return []

  // 这里暂时将菜品按营养成分分组，实际应该由后端提供套餐数据
  const plans = []
  const chunkSize = 3 // 每个套餐包含3道菜

  for (let i = 0; i < dishes.length; i += chunkSize) {
    const dishChunk = dishes.slice(i, i + chunkSize)
    const totalCalorie = dishChunk.reduce((sum, dish) => sum + (dish.calorie || 0), 0)
    const totalProtein = dishChunk.reduce((sum, dish) => sum + (dish.protein || 0), 0)
    const totalCarbs = dishChunk.reduce((sum, dish) => sum + (dish.carbohydrate || 0), 0)

    plans.push({
      id: `${mealType}_plan_${Math.floor(i / chunkSize) + 1}`,
      name: `${getMealTypeLabel(mealType)}套餐 ${Math.floor(i / chunkSize) + 1}`,
      description: `精选${dishChunk.length}道菜品的营养搭配`,
      items: dishChunk.map((dish) => ({
        id: dish.id,
        name: dish.name,
        type: 'main',
        description: dish.description,
      })),
      nutrition: {
        calories: totalCalorie,
        protein: totalProtein,
        carbs: totalCarbs,
      },
    })
  }

  return plans
}

const getMealTypeLabel = (mealType) => {
  const typeMap = {
    breakfast: '早餐',
    lunch: '午餐',
    dinner: '晚餐',
    snack: '加餐',
  }
  return typeMap[mealType] || '套餐'
}

// 加载菜品数据
const loadDishesData = async (mealType) => {
  loading.value = true
  try {
    const requestParams = {
      ...props.filters,
      meal_type: mealType, // 假设后端支持按餐次筛选
      page: currentPage[mealType],
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    const transformedData = transformDishData(data.list)

    dishesData[mealType] = {
      list: transformedData,
      total: data.total_count,
    }
  } catch (error) {
    console.error(`获取${getMealTypeLabel(mealType)}菜品列表失败:`, error)
    ElMessage.error(`获取${getMealTypeLabel(mealType)}菜品列表失败`)
    dishesData[mealType] = { list: [], total: 0 }
  } finally {
    loading.value = false
  }
}

// 监听属性变化
watch(
  () => props.activeMeal,
  (newVal) => {
    activeMealLocal.value = newVal
    // 当切换餐次时，如果该餐次数据为空则加载数据
    if (dishesData[newVal].list.length === 0) {
      loadDishesData(newVal)
    }
  },
)

watch(
  () => props.filters,
  () => {
    // 过滤条件变化时重新加载当前餐次数据
    if (activeMealLocal.value) {
      resetPagination(activeMealLocal.value)
    }
  },
  { deep: true },
)

// 计算属性和方法
const getDishesCount = (mealType) => {
  return dishesData[mealType].list.length
}

const getCurrentDishesTotal = (mealType) => {
  return dishesData[mealType].total
}

const getCurrentMealPlans = (mealType) => {
  return convertDishesToMealPlans(dishesData[mealType].list, mealType)
}

// 事件处理
const handleTabChange = (tab) => {
  emit('update:activeMeal', tab)
  // 如果该餐次数据为空则加载数据
  if (dishesData[tab].list.length === 0) {
    loadDishesData(tab)
  }
}

const handleSizeChange = (size, mealType) => {
  pageSize.value = size
  currentPage[mealType] = 1 // 切换页码大小时重置到第一页
  loadDishesData(mealType)
}

const handleCurrentChange = (page, mealType) => {
  currentPage[mealType] = page
  loadDishesData(mealType)
}

// 重置分页到第一页
const resetPagination = (mealType) => {
  if (mealType) {
    currentPage[mealType] = 1
    loadDishesData(mealType)
  } else {
    // 重置所有餐次
    Object.keys(currentPage).forEach((type) => {
      currentPage[type] = 1
    })
    loadDishesData(activeMealLocal.value)
  }
}

// 刷新当前餐次数据
const refresh = (mealType) => {
  const typeToRefresh = mealType || activeMealLocal.value
  loadDishesData(typeToRefresh)
}

// 套餐相关事件处理
const handleAddMealPlan = (mealType) => {
  emit('add-meal-plan', mealType)
}

const handleEditPlan = (plan) => {
  emit('edit-plan', plan)
}

const handleEditItem = (data) => {
  emit('edit-item', data)
}

const handleAddItem = (plan) => {
  emit('add-item', plan)
}

const handleDeleteItem = (data) => {
  emit('delete-item', data)
}

const handleMoreAction = (data) => {
  emit('more-action', data)
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后加载默认餐次数据
onMounted(() => {
  loadDishesData(activeMealLocal.value)
})
</script>

<style scoped>
.meal-tabs-container {
  transition: all 0.3s ease;
}

.meal-tabs :deep(.el-tabs__content) {
  padding: 1rem;
}

.dishes-content {
  min-height: 300px;
}

.meal-plans-grid {
  min-height: 200px;
}

.empty-state {
  transition: all 0.3s ease;
}

.empty-state:hover {
  box-shadow:
    0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 1px 2px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.pagination-container {
  padding: 1rem 0;
  border-top: 1px solid #e5e7eb;
  margin-top: 1.5rem;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
