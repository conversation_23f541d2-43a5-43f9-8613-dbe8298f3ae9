<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="flex items-center mb-6">
      <el-icon class="text-pink-500 mr-2">
        <WarningFilled />
      </el-icon>
      <h3 class="text-lg font-semibold text-gray-800">过敏原管理</h3>
    </div>

    <div class="space-y-6">
      <!-- 过敏原提醒开关 -->
      <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 class="text-sm font-medium text-gray-900">启用过敏原提醒</h4>
          <p class="text-xs text-gray-500 mt-1">开启后，系统会在配餐时提醒注意过敏原</p>
        </div>
        <el-switch
          v-model="allergens.enabled"
          size="default"
          active-color="#ec4899"
          inactive-color="#d1d5db"
        />
      </div>

      <!-- 常见过敏原设置 -->
      <div v-if="allergens.enabled">
        <label class="block text-sm font-medium text-gray-700 mb-3">常见过敏原</label>
        <div class="grid grid-cols-2 gap-3">
          <div
            v-for="allergen in allergens.commonAllergens"
            :key="allergen.name"
            class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-pink-200 transition-colors"
          >
            <div class="flex items-center space-x-3">
              <div
                class="w-3 h-3 rounded-full"
                :class="allergen.enabled ? 'bg-red-500' : 'bg-gray-300'"
              ></div>
              <span class="text-sm text-gray-900">{{ allergen.name }}</span>
            </div>
            <el-switch
              v-model="allergen.enabled"
              size="small"
              active-color="#ef4444"
              inactive-color="#d1d5db"
            />
          </div>
        </div>

        <!-- 添加自定义过敏原 -->
        <div class="my-4">
          <div class="flex items-center space-x-2">
            <el-input v-model="customAllergen" placeholder="添加自定义过敏原" class="flex-1" />
            <el-button
              type="primary"
              @click="addCustomAllergen"
              :disabled="!customAllergen.trim()"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              添加
            </el-button>
          </div>
        </div>

        <!-- 提示信息 -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="flex items-start">
            <el-icon class="text-yellow-500 mt-0.5 mr-2">
              <WarningFilled />
            </el-icon>
            <div class="text-sm text-yellow-700">
              <p class="font-medium mb-1">过敏原提醒</p>
              <ul class="space-y-1 text-xs">
                <li>• 启用的过敏原会在配餐时显示警告标识</li>
                <li>• 建议根据入住客户的过敏史调整过敏原设置</li>
                <li>• 可添加自定义过敏原以满足特殊需求</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 禁用状态提示 -->
      <div v-else class="text-center py-8 text-gray-500">
        <el-icon class="text-4xl mb-2">
          <WarningFilled />
        </el-icon>
        <p>过敏原提醒已禁用</p>
        <p class="text-xs mt-1">启用后可配置常见过敏原</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElSwitch, ElInput, ElButton, ElIcon, ElMessage } from 'element-plus'
import { WarningFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  allergens: {
    type: Object,
    required: true,
  },
})

// Emits
defineEmits(['update:allergens'])

// 自定义过敏原输入
const customAllergen = ref('')

// 添加自定义过敏原
const addCustomAllergen = () => {
  const name = customAllergen.value.trim()
  if (!name) return

  // 检查是否已存在
  const exists = props.allergens.commonAllergens.some((item) => item.name === name)
  if (exists) {
    ElMessage.warning('该过敏原已存在')
    return
  }

  // 添加到列表
  props.allergens.commonAllergens.push({
    name,
    enabled: true,
  })

  customAllergen.value = ''
  ElMessage.success('过敏原添加成功')
}
</script>

<style scoped>
.hover\:border-pink-200:hover {
  border-color: #fce7f3;
}
</style>
