<template>
  <div class="diet-record-cards-container">
    <!-- 卡片容器标题 -->
    <div class="cards-header border border-gray-200 rounded-t-lg px-6 py-4 border-b bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Calendar />
          </el-icon>
          膳食计划列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 卡片列表容器 -->
    <div class="cards-content bg-white border-x border-gray-200 px-6 py-4" v-loading="loading">
      <div v-if="dietRecords.length === 0 && !loading" class="text-center py-12 text-gray-500">
        <el-icon class="text-4xl mb-2 text-gray-400">
          <Document />
        </el-icon>
        <p>暂无膳食计划</p>
      </div>

      <!-- 卡片列表 - 使用flex布局 -->
      <div v-else class="flex flex-wrap gap-6 items-start">
        <div
          v-for="record in dietRecords"
          :key="record.rid"
          class="diet-record-card bg-gradient-to-br from-white to-pink-50 rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 hover:border-pink-300 flex flex-col w-full lg:w-[calc(50%-12px)] xl:w-[calc(33.333%-16px)]"
        >
          <!-- 卡片头部 -->
          <div class="card-header flex items-center justify-between mb-4 p-6 pb-0">
            <div class="flex items-center">
              <el-icon class="text-2xl text-pink-500 mr-3">
                <Calendar />
              </el-icon>
              <div>
                <h4 class="text-lg font-semibold text-gray-800">{{ record.date }}</h4>
              </div>
            </div>
          </div>

          <!-- 卡片内容区 - 可扩展 -->
          <div class="card-content flex-1 px-6 cursor-pointer" @click="handleCardClick(record)">
            <!-- 餐次概览 -->
            <div
              v-if="mealTypes.some((mealType) => getMealDishCount(record, mealType.key) > 0)"
              class="meals-overview mb-4"
            >
              <div class="grid grid-cols-1 gap-3">
                <template v-for="mealType in mealTypes" :key="mealType.key">
                  <div
                    v-if="getMealDishCount(record, mealType.key) > 0"
                    class="meal-item bg-white rounded-md p-3 border border-gray-100"
                  >
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-700">{{ mealType.label }}</span>
                      <span class="text-xs text-pink-600">
                        {{ getMealDishCount(record, mealType.key) }}道菜
                      </span>
                    </div>
                    <!-- 菜品名称列表 -->
                    <div class="dish-list">
                      <div class="flex flex-wrap gap-1">
                        <el-tag
                          v-for="dish in getMealDishes(record, mealType.key)"
                          :key="dish.rid"
                          size="small"
                          type="info"
                          effect="plain"
                          class="text-xs"
                        >
                          {{ dish.name }}
                        </el-tag>
                      </div>
                      <!-- 餐次备注 -->
                      <div v-if="getMealRemark(record, mealType.key)" class="mt-2">
                        <p class="text-xs text-gray-500 italic">
                          <el-icon class="mr-1">
                            <ChatLineRound />
                          </el-icon>
                          {{ getMealRemark(record, mealType.key) }}
                        </p>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>

            <!-- 营养信息概览 -->
            <div class="bg-white rounded-md p-3 border border-gray-100">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">营养概览</span>
                <el-icon class="text-pink-500">
                  <DataAnalysis />
                </el-icon>
              </div>
              <div class="grid grid-cols-2 gap-2 text-xs text-gray-600">
                <div>总热量: {{ getDailyCalories(record) }}卡</div>
                <div>蛋白质: {{ getDailyProtein(record) }}g</div>
                <div>碳水: {{ getDailyCarbs(record) }}g</div>
                <div>脂肪: {{ getDailyFat(record) }}g</div>
              </div>
            </div>

            <!-- 每日备注 -->
            <div v-if="record.daily_remark" class="daily-remark mt-4">
              <div class="bg-yellow-50 border border-yellow-200 rounded-md p-2">
                <p class="text-xs text-gray-600">
                  <el-icon class="mr-1 text-yellow-600">
                    <ChatDotRound />
                  </el-icon>
                  {{ record.daily_remark }}
                </p>
              </div>
            </div>
          </div>

          <!-- 卡片底部操作按钮 -->
          <div class="card-footer p-6 pt-0 mt-4">
            <div class="flex gap-3">
              <el-button type="danger" class="flex-1" @click.stop="handleDeleteClick(record)">
                <el-icon class="mr-1">
                  <Delete />
                </el-icon>
                删除
              </el-button>
              <el-button type="primary" class="flex-1" @click.stop="handleEditClick(record)">
                <el-icon class="mr-1">
                  <Edit />
                </el-icon>
                编辑
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div
      class="pagination-container border border-gray-200 rounded-b-lg px-6 py-4 border-t bg-gray-50"
    >
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Calendar,
  Document,
  DataAnalysis,
  ChatDotRound,
  ChatLineRound,
  Edit,
  Delete,
} from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'

const emit = defineEmits(['card-click', 'edit-click', 'maternity-info', 'existing-dates'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'customer-service/maternity-diet-records/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
  maternityId: {
    type: String,
    default: '7',
  },
})

// 餐次类型定义
const mealTypes = [
  { key: 'BREAKFAST', label: '早餐' },
  { key: 'EARLY_SNACK', label: '早加餐' },
  { key: 'LUNCH', label: '午餐' },
  { key: 'NOON_SNACK', label: '午加餐' },
  { key: 'DINNER', label: '晚餐' },
  { key: 'LATE_SNACK', label: '晚加餐' },
]

// 内部状态管理
const loading = ref(false)
const dietRecords = ref([])
const maternityInfo = ref(null)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(12) // 卡片布局使用较大的页码
const total = ref(0)

// 转换API数据格式
const transformDietData = (apiResponse) => {
  return {
    maternityInfo: apiResponse.content.maternity_info,
    dietRecords: apiResponse.content.diet_records,
    totalCount: apiResponse.total_count,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的date_range字段
    const processedFilters = { ...props.filters }

    // 处理日期范围过滤
    if (processedFilters.date_range && Array.isArray(processedFilters.date_range)) {
      processedFilters.start_date = processedFilters.date_range[0]
      processedFilters.end_date = processedFilters.date_range[1]
      delete processedFilters.date_range
    }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const apiUrl = `${props.apiUrl}${props.maternityId}/`
    const data = await get(apiUrl, requestParams)

    const transformedData = transformDietData(data)
    dietRecords.value = transformedData.dietRecords
    maternityInfo.value = transformedData.maternityInfo
    total.value = transformedData.totalCount

    // 通知父组件设置产妇信息
    if (maternityInfo.value) {
      emit('maternity-info', maternityInfo.value)
    }

    // 提取已有记录的日期并通知父组件
    const existingDates = dietRecords.value.map((record) => record.date)
    emit('existing-dates', existingDates)
  } catch (error) {
    console.error('获取膳食计划失败:', error)
    ElMessage.error('获取膳食计划失败')
    dietRecords.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 获取某餐次的菜品数量
const getMealDishCount = (record, mealType) => {
  const meal = record.daily_meals_detail[mealType]
  return meal && meal.dishes ? meal.dishes.length : 0
}

// 获取某餐次的菜品列表
const getMealDishes = (record, mealType) => {
  const meal = record.daily_meals_detail[mealType]
  return meal && meal.dishes ? meal.dishes : []
}

// 获取某餐次的备注
const getMealRemark = (record, mealType) => {
  const meal = record.daily_meals_detail[mealType]
  return meal ? meal.remark : ''
}

// 计算每日总热量
const getDailyCalories = (record) => {
  let total = 0
  mealTypes.forEach((mealType) => {
    const meal = record.daily_meals_detail[mealType.key]
    if (meal && meal.dishes) {
      meal.dishes.forEach((dish) => {
        if (dish.calorie) total += dish.calorie
      })
    }
  })
  return total.toFixed(1)
}

// 计算每日总蛋白质
const getDailyProtein = (record) => {
  let total = 0
  mealTypes.forEach((mealType) => {
    const meal = record.daily_meals_detail[mealType.key]
    if (meal && meal.dishes) {
      meal.dishes.forEach((dish) => {
        if (dish.protein) total += dish.protein
      })
    }
  })
  return total.toFixed(1)
}

// 计算每日总碳水化合物
const getDailyCarbs = (record) => {
  let total = 0
  mealTypes.forEach((mealType) => {
    const meal = record.daily_meals_detail[mealType.key]
    if (meal && meal.dishes) {
      meal.dishes.forEach((dish) => {
        if (dish.carbohydrate) total += dish.carbohydrate
      })
    }
  })
  return total.toFixed(1)
}

// 计算每日总脂肪
const getDailyFat = (record) => {
  let total = 0
  mealTypes.forEach((mealType) => {
    const meal = record.daily_meals_detail[mealType.key]
    if (meal && meal.dishes) {
      meal.dishes.forEach((dish) => {
        if (dish.fat) total += dish.fat
      })
    }
  })
  return total.toFixed(1)
}

// 事件处理
const handleCardClick = (record) => {
  console.log(record)
  emit('card-click', record)
}

const handleEditClick = (record) => {
  emit('edit-click', record)
}

const handleDeleteClick = async (record) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${record.date} 的膳食计划吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const deleteUrl = `customer-service/maternity-diet-records/delete/${record.rid}/`
    await del(deleteUrl)

    ElMessage.success('删除成功')

    // 刷新数据
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除膳食计划失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 监听过滤条件变化，自动重新加载数据
watch(
  () => props.filters,
  () => {
    currentPage.value = 1
    loadData()
  },
  { deep: true },
)

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.diet-record-cards-container {
  transition: all 0.3s ease;
}

.diet-record-card {
  transition: all 0.3s ease;
}

.diet-record-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.meal-item {
  transition: all 0.2s ease;
}

.meal-item:hover {
  background-color: #fef7f0;
  border-color: #ec4899;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}

/* 加载状态 */
:deep(.el-loading-mask) {
  border-radius: 0.5rem;
}
</style>
