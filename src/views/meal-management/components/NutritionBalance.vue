<template>
  <div class="bg-white rounded-lg shadow p-6 h-full flex flex-col">
    <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
      <el-icon class="mr-2 text-pink-500">
        <PieChart />
      </el-icon>
      营养均衡分析
    </h3>

    <!-- 营养比例饼图 - 占一整行 -->
    <div class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-4">营养成分比例</h4>
      <!-- 占位饼图 -->
      <div class="bg-gray-50 rounded-lg h-64 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <el-icon class="text-4xl mb-2">
            <PieChart />
          </el-icon>
          <p>营养成分比例饼图</p>
          <p class="text-sm mt-1">蛋白质 30% | 碳水 50% | 脂肪 20%</p>
        </div>
      </div>

      <!-- 图例 -->
      <div class="flex justify-center mt-4 gap-6">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-pink-500 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">蛋白质 30%</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">碳水 50%</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
          <span class="text-sm text-gray-600">脂肪 20%</span>
        </div>
      </div>
    </div>

    <!-- 营养目标完成度 - 每2个item占一行 -->
    <div>
      <h4 class="text-md font-medium text-gray-700 mb-4">营养目标完成度</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          v-for="nutrition in nutritionTargets"
          :key="nutrition.name"
          class="bg-gray-50 rounded-lg p-4"
        >
          <div class="flex justify-between items-center mb-2">
            <span class="font-medium text-gray-700">{{ nutrition.name }}</span>
            <span class="text-sm text-gray-500">
              {{ nutrition.current }}{{ nutrition.unit }} / {{ nutrition.target
              }}{{ nutrition.unit }}
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="h-2 rounded-full transition-all duration-300"
              :class="getProgressColor(nutrition.percentage)"
              :style="{ width: nutrition.percentage + '%' }"
            ></div>
          </div>
          <div class="flex justify-between items-center mt-1">
            <span class="text-xs text-gray-500">{{ nutrition.percentage }}% 完成</span>
            <el-tag :type="getTagType(nutrition.percentage)" size="small">
              {{ getStatus(nutrition.percentage) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElIcon, ElTag } from 'element-plus'
import { PieChart } from '@element-plus/icons-vue'

// 营养目标数据
const nutritionTargets = reactive([
  {
    name: '蛋白质',
    current: 85,
    target: 90,
    unit: 'g',
    percentage: 94,
  },
  {
    name: '碳水化合物',
    current: 280,
    target: 300,
    unit: 'g',
    percentage: 93,
  },
  {
    name: '脂肪',
    current: 65,
    target: 70,
    unit: 'g',
    percentage: 92,
  },
  {
    name: '维生素C',
    current: 90,
    target: 100,
    unit: 'mg',
    percentage: 90,
  },
  {
    name: '钙',
    current: 950,
    target: 1200,
    unit: 'mg',
    percentage: 79,
  },
  {
    name: '铁',
    current: 18,
    target: 20,
    unit: 'mg',
    percentage: 90,
  },
])

// 根据完成度获取进度条颜色
const getProgressColor = (percentage) => {
  if (percentage >= 90) return 'bg-green-500'
  if (percentage >= 70) return 'bg-yellow-500'
  return 'bg-red-500'
}

// 根据完成度获取标签类型
const getTagType = (percentage) => {
  if (percentage >= 90) return 'success'
  if (percentage >= 70) return 'warning'
  return 'danger'
}

// 根据完成度获取状态文本
const getStatus = (percentage) => {
  if (percentage >= 90) return '优秀'
  if (percentage >= 70) return '良好'
  return '待改善'
}
</script>

<style scoped>
.transition-all {
  transition: all 0.3s ease;
}
</style>
