<template>
  <div class="planning-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">餐饮管理 - 配餐计划</h1>
            <p class="text-sm text-gray-600 mt-1">管理产妇信息和配餐计划</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索过滤区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="currentFilters"
      @search="handleSearch"
      class="mb-6"
    />

    <!-- 卡片列表 -->
    <MaternityCardList
      ref="maternityCardListRef"
      :filters="currentFilters"
      @card-click="handleCardClick"
      @view-details="handleViewDetails"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import FilterPanel from '@/components/FilterPanel.vue'
import MaternityCardList from '@/views/meal-management/components/MaternityCardList.vue'

const router = useRouter()

// 响应式数据
const currentRow = ref(null)

// 获取卡片列表组件引用
const maternityCardListRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  sk: '',
})

// 过滤器字段配置
const filterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '关键字',
    placeholder: '请输入姓名或房间号',
  },
]

// 搜索 - 点击搜索按钮时触发
const handleSearch = () => {
  // 重置到第一页并重新加载数据
  maternityCardListRef.value?.resetPagination()
}

// 卡片点击事件
const handleCardClick = (item) => {
  currentRow.value = item
  // TODO: 实现卡片点击后的操作，比如显示详情或进入配餐计划页面
}

// 查看详情
const handleViewDetails = (item) => {
  currentRow.value = item

  // 跳转到产妇膳食详情页面
  if (!item?.id) {
    ElMessage.error('产妇信息不完整，无法跳转')
    return
  }

  router
    .push({
      name: 'patient-meal-detail',
      params: { id: item.id },
      query: { name: item.maternity || '未知产妇' },
    })
    .catch((error) => {
      console.error('路由跳转失败:', error)
      ElMessage.error('页面跳转失败')
    })
}
</script>

<style scoped>
.planning-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
