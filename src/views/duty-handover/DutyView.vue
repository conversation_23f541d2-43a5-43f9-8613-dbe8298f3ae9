<template>
  <div class="duty-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">值班与交接班管理 - 值班信息查看</h1>
            <p class="text-sm text-gray-600 mt-1">查看当前值班人员和值班信息，管理班次安排</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 视图切换和内容 -->
    <div class="mb-6">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="" type="border-card">
        <el-tab-pane label="列表视图" name="table">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <List />
              </el-icon>
              列表视图
            </div>
          </template>
          <!-- 列表视图 -->
          <DutyListView
            v-if="activeTab === 'table'"
            ref="listViewRef"
            :filters="filters"
            @view="handleViewDuty"
            @edit="handleEditDuty"
            @delete="handleDeleteDuty"
          />
        </el-tab-pane>

        <el-tab-pane label="时间线视图" name="timeline">
          <template #label>
            <div class="flex items-center">
              <el-icon class="mr-2">
                <Clock />
              </el-icon>
              时间线视图
            </div>
          </template>
          <!-- 时间线视图 -->
          <DutyTimeline v-if="activeTab === 'timeline'" ref="timelineViewRef" :filters="filters" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 值班详情对话框 -->
    <DutyDetailDialog
      v-model="detailDialogVisible"
      :item-id="selectedDutyId"
      @edit="handleShowEditDialog"
      @delete="handleDeleteDuty"
      @close="handleCloseDetailDialog"
    />

    <!-- 值班编辑对话框 -->
    <DutyFormDialog
      v-model="editDialogVisible"
      :item-id="selectedDutyId"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElIcon, ElTabs, ElTabPane } from 'element-plus'
import { List, Clock } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import DutyListView from './components/DutyListView.vue'
import DutyTimeline from './components/DutyTimeline.vue'
import DutyDetailDialog from './components/DutyDetailDialog.vue'
import DutyFormDialog from './components/DutyFormDialog.vue'
import { useBaseDataStore } from '@/stores/baseData.js'
import { SHIFT_TYPE_OPTIONS } from '@/utils/constants.js'
import { getTodayString } from '@/utils/utils.js'

// 响应式数据
const activeTab = ref('table')

// 基础数据store
const baseDataStore = useBaseDataStore()

// 移除员工相关状态（已删除员工筛选功能）

// 子组件引用
const listViewRef = ref(null)
const timelineViewRef = ref(null)

// 对话框状态
const detailDialogVisible = ref(false)
const editDialogVisible = ref(false)
const selectedDutyId = ref(null)

// 筛选条件
const filters = reactive({
  department_rid: '',
  duty_date: getTodayString(), // 值班日期
  shift_type: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'department_rid',
    type: 'select',
    label: '部门筛选',
    placeholder: '选择部门',
    options: baseDataStore.departments.getOptions(),
    loading: baseDataStore.departments.isLoading() || baseDataStore.departments.searchLoading.value,
  },
  {
    key: 'duty_date',
    type: 'date',
    label: '值班日期',
    placeholder: '选择值班日期',
    required: true,
  },
  {
    key: 'shift_type',
    type: 'select',
    label: '班次筛选',
    placeholder: '选择班次',
    options: SHIFT_TYPE_OPTIONS,
  },
])

// 方法
const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

const handleViewDuty = (duty) => {
  selectedDutyId.value = duty.rid
  detailDialogVisible.value = true
}

const handleEditDuty = (duty) => {
  selectedDutyId.value = duty.rid
  editDialogVisible.value = true
}

const handleDeleteDuty = () => {
  // 删除值班记录后刷新数据
  handleSearch()
}

// 对话框相关方法
const handleShowEditDialog = () => {
  detailDialogVisible.value = false
  editDialogVisible.value = true
}

const handleCloseDetailDialog = () => {
  detailDialogVisible.value = false
  selectedDutyId.value = null
}

const handleEditSuccess = () => {
  editDialogVisible.value = false
  selectedDutyId.value = null
  // 刷新数据
  handleSearch()
}

const handleSearch = () => {
  // 触发子组件刷新数据
  if (activeTab.value === 'table') {
    // 列表视图：重置分页并刷新
    if (listViewRef.value) {
      listViewRef.value.resetPagination()
    }
  } else {
    // 时间线视图：直接刷新数据
    if (timelineViewRef.value) {
      timelineViewRef.value.refreshData?.()
    }
  }
}

// 员工相关代码已删除（不再需要员工筛选功能）

onMounted(() => {
  // 初始化基础数据
  baseDataStore.departments.fetch()
  baseDataStore.staffs.fetch()
})
</script>

<style scoped>
.duty-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}

/* 自定义 tabs 样式 */
:deep(.el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

:deep(.el-tabs__content) {
  padding: 24px;
}

:deep(.el-tabs__item) {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
}

:deep(.el-tabs__item:hover) {
  color: #ec4899;
}
</style>
