<template>
  <div class="handover-management-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">交班管理 - 交班报告</h1>
            <p class="text-sm text-gray-600 mt-1">查看和管理员工交班报告</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 日期筛选组件 -->
    <MealDateFilter
      v-model="currentFilters.schedule_date"
      class="mb-6"
      :is-center="false"
      title="交班日期"
    />

    <!-- 交班报告网格组件 -->
    <HandoverReportGrid
      ref="handoverReportGridRef"
      :filters="currentFilters"
      @add-report="handleAddReport"
      @edit-report="handleEditReport"
    />

    <!-- 交班报告详情弹窗 -->
    <HandoverReportDetailDialog
      v-model="detailVisible"
      :report-id="currentReportId"
      @success="handleDetailClose"
    />

    <!-- 交班报告表单弹窗 -->
    <HandoverReportFormDialog
      v-model="formVisible"
      :schedule-data="currentScheduleData"
      :item-id="currentFormItemId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import MealDateFilter from '@/views/meal-management/components/MealDateFilter.vue'
import HandoverReportGrid from './components/HandoverReportGrid.vue'
import HandoverReportDetailDialog from './components/HandoverReportDetailDialog.vue'
import HandoverReportFormDialog from './components/HandoverReportFormDialog.vue'

// 响应式数据
const detailVisible = ref(false)
const currentReportId = ref(null)
const formVisible = ref(false)
const currentScheduleData = ref({})
const currentFormItemId = ref(null)

// 获取组件引用
const handoverReportGridRef = ref(null)

// 当前过滤条件
const currentFilters = reactive({
  schedule_date: new Date().toISOString().split('T')[0], // 默认为今天
})

// 监听日期变化，自动刷新数据
watch(
  () => currentFilters.schedule_date,
  () => {
    // 日期变化时自动刷新表格数据
    handoverReportGridRef.value?.resetPagination()
  },
)

// 方法
const handleAddReport = (scheduleData) => {
  currentScheduleData.value = scheduleData
  currentFormItemId.value = null
  formVisible.value = true
}

const handleEditReport = (reportData) => {
  currentScheduleData.value = reportData
  currentFormItemId.value = reportData.rid
  formVisible.value = true
}

const handleDetailClose = () => {
  detailVisible.value = false
  currentReportId.value = null
}

const handleFormSuccess = () => {
  formVisible.value = false
  currentScheduleData.value = {}
  currentFormItemId.value = null
  // 刷新表格数据
  handoverReportGridRef.value?.refresh()
}
</script>

<style scoped>
.handover-management-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
