<template>
  <div class="duty-timeline-container">
    <div v-loading="loading" class="timeline-wrapper">
      <!-- 时间线头部说明 -->
      <div class="timeline-header mb-6">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-lg font-semibold text-gray-800">值班时间线</h3>
            <p class="text-sm text-gray-500 mt-1">{{ currentDateText }}</p>
          </div>
          <div class="text-sm text-gray-500">按时间顺序展示各班次值班安排</div>
        </div>
      </div>

      <!-- 时间线内容 -->
      <div class="timeline-content">
        <div
          v-for="(shiftGroup, index) in timelineData"
          :key="shiftGroup.shift"
          class="timeline-item"
          :class="{ 'is-last': index === timelineData.length - 1 }"
        >
          <!-- 时间线节点 -->
          <div class="timeline-node">
            <div class="timeline-dot" :class="getShiftDotClass(shiftGroup.shift)">
              <el-icon>
                <component :is="getShiftIcon(shiftGroup.shift)" />
              </el-icon>
            </div>
            <div v-if="index !== timelineData.length - 1" class="timeline-line"></div>
          </div>

          <!-- 时间线内容 -->
          <div class="timeline-card">
            <div class="card-header">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                  <el-tag
                    :type="getShiftTypeTagType(shiftGroup.shift)"
                    class="mr-3 font-medium"
                    size="default"
                  >
                    {{ shiftGroup.shiftText }}
                  </el-tag>
                  <span class="text-lg font-semibold text-gray-800">
                    {{ shiftGroup.time }}
                  </span>
                </div>
                <div class="text-sm text-gray-500">{{ shiftGroup.duties.length }} 人值班</div>
              </div>
            </div>

            <!-- 值班人员列表 -->
            <div class="staff-list">
              <div v-for="duty in shiftGroup.duties" :key="duty.rid" class="staff-item">
                <div class="staff-avatar">
                  <div class="avatar-circle">
                    <span class="avatar-text">{{ duty.staff_name.charAt(0) }}</span>
                  </div>
                  <div class="status-indicator" :class="getStatusIndicatorClass(duty.status)"></div>
                </div>

                <div class="staff-info flex-1">
                  <div class="staff-name-row">
                    <span class="staff-name">{{ duty.staff_name }}</span>
                    <span class="staff-position">{{ duty.position }}</span>
                  </div>
                  <div class="staff-details">
                    <span class="department-tag">
                      {{ duty.department_name || '未分配部门' }}
                    </span>
                    <el-tag
                      :type="getOrgScheduleStatusTagType(duty.status)"
                      size="small"
                      class="ml-2"
                    >
                      {{ duty.status_display }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="timelineData.length === 0 && !loading" class="empty-state">
        <div class="empty-icon">
          <el-icon size="48" class="text-gray-300"><Clock /></el-icon>
        </div>
        <p class="empty-text">{{ currentDateText }} 暂无值班安排</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElTag, ElIcon } from 'element-plus'
import { Clock, Sunny as Sunrise, Sunny, Moon } from '@element-plus/icons-vue'
import { get } from '@/utils/request.js'
import {
  getShiftTypeTagType,
  getOrgScheduleStatusTagType,
  SHIFT_TYPE_MAP,
} from '@/utils/constants.js'
import { getTodayString } from '@/utils/utils.js'

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 响应式数据
const loading = ref(false)
const dutyData = ref([])

// 当前查看的日期文本
const currentDateText = computed(() => {
  const date = props.filters.duty_date || getTodayString()
  const dateObj = new Date(date)

  // 判断是否是今天
  const isToday = date === getTodayString()

  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weekday = weekdays[dateObj.getDay()]

  if (isToday) {
    return `今天 ${date} ${weekday}`
  } else {
    return `${date} ${weekday}`
  }
})

// 加载值班数据
const loadTimelineData = async () => {
  loading.value = true
  try {
    // 处理筛选参数，将 duty_date 转换为 start_date 和 end_date
    const processedFilters = { ...props.filters }

    // 如果没有指定日期，使用当天
    if (!processedFilters.duty_date) {
      processedFilters.duty_date = getTodayString()
    }

    processedFilters.start_date = processedFilters.duty_date
    processedFilters.end_date = processedFilters.duty_date
    delete processedFilters.duty_date

    const response = await get('organizational-management/schedule/query/', processedFilters)

    if (response && Array.isArray(response)) {
      dutyData.value = response
    } else {
      dutyData.value = []
    }
  } catch (error) {
    console.error('获取值班数据失败:', error)
    dutyData.value = []
  } finally {
    loading.value = false
  }
}

// 将值班数据按班次分组并排序
const timelineData = computed(() => {
  // 按班次分组
  const groupedByShift = dutyData.value.reduce((acc, duty) => {
    const shiftType = duty.shift_type
    if (!acc[shiftType]) {
      acc[shiftType] = {
        shift: shiftType,
        shiftText: duty.shift_type_display,
        time: getShiftTimeText(shiftType),
        duties: [],
      }
    }
    acc[shiftType].duties.push(duty)
    return acc
  }, {})

  // 按早中晚顺序排序班次
  const shiftOrder = ['MORNING', 'AFTERNOON', 'NIGHT', 'ALL_DAY']
  return shiftOrder.filter((shift) => groupedByShift[shift]).map((shift) => groupedByShift[shift])
})

// 获取班次时间文本
const getShiftTimeText = (shiftType) => {
  return SHIFT_TYPE_MAP[shiftType]?.time || '时间待定'
}

// 获取班次节点样式
const getShiftDotClass = (shift) => {
  const classMap = {
    MORNING: 'dot-morning',
    AFTERNOON: 'dot-afternoon',
    NIGHT: 'dot-night',
    ALL_DAY: 'dot-allday',
  }
  return classMap[shift] || 'dot-default'
}

// 获取班次图标
const getShiftIcon = (shift) => {
  const iconMap = {
    MORNING: Sunrise,
    AFTERNOON: Sunny,
    NIGHT: Moon,
    ALL_DAY: Clock,
  }
  return iconMap[shift] || Clock
}

// 获取状态指示器样式
const getStatusIndicatorClass = (status) => {
  const classMap = {
    ACTIVE: 'status-active',
    INACTIVE: 'status-inactive',
    ON_LEAVE: 'status-break',
  }
  return classMap[status] || 'status-default'
}

// 移除联系功能（根据需求删除）

// 刷新数据方法（供父组件调用）
const refreshData = () => {
  loadTimelineData()
}

// 移除自动监听 filters 变化，改为手动触发

// 暴露方法给父组件
defineExpose({
  refreshData,
})

onMounted(() => {
  loadTimelineData()
})
</script>

<style scoped>
.duty-timeline-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.timeline-wrapper {
  min-height: 300px;
}

.timeline-header {
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.timeline-content {
  position: relative;
  padding-top: 20px;
}

.timeline-item {
  position: relative;
  display: flex;
  margin-bottom: 32px;
}

.timeline-item.is-last {
  margin-bottom: 0;
}

.timeline-node {
  position: relative;
  flex-shrink: 0;
  width: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-dot {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dot-morning {
  background: linear-gradient(135deg, #10b981, #34d399);
}

.dot-afternoon {
  background: linear-gradient(135deg, #f59e0b, #fbbf24);
}

.dot-night {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}

.dot-allday {
  background: linear-gradient(135deg, #ec4899, #f472b6);
}

.timeline-line {
  width: 2px;
  background: linear-gradient(to bottom, #e5e7eb, #f3f4f6);
  flex: 1;
  margin-top: 8px;
}

.timeline-card {
  min-width: 300px;
  max-width: 600px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-left: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

.timeline-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-color: #ec4899;
}

.staff-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.staff-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.staff-item:hover {
  background: #fef7f7;
}

.staff-avatar {
  position: relative;
  margin-right: 12px;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ec4899, #f472b6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-active {
  background-color: #10b981;
}

.status-inactive {
  background-color: #ef4444;
}

.status-break {
  background-color: #f59e0b;
}

.staff-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.staff-name-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.staff-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.staff-position {
  font-size: 12px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.staff-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.department-tag {
  background: #fce7f3;
  color: #be185d;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

/* 移除联系按钮相关样式 */

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
}

.empty-text {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}
</style>
