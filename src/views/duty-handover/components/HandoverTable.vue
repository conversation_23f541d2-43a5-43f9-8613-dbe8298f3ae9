<template>
  <div class="handover-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Document />
          </el-icon>
          交接班日志列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      @row-click="handleRowClick"
    >
      <el-table-column prop="staff_department_name" label="交班部门" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-icon class="mr-2 text-pink-500">
              <House />
            </el-icon>
            <span class="font-medium">{{ row.staff_department_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="staff_name" label="交班人" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.staff_name.charAt(0) }}
            </el-avatar>
            <span class="font-medium">{{ row.staff_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="schedule_date" label="值班日期" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.schedule_date }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="shift_time" label="交班时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.shift_time) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.shift_time) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="schedule_shift_type_display" label="班次" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getShiftTypeTagType(row.schedule_shift_type)" size="small" effect="light">
            {{ row.schedule_shift_type_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="work_summary" label="工作总结" min-width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tooltip
            :content="row.work_summary"
            placement="top"
            :disabled="row.work_summary.length <= 50"
          >
            <span class="text-gray-700">
              {{
                row.work_summary.length > 50
                  ? row.work_summary.substring(0, 50) + '...'
                  : row.work_summary
              }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="120" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
              @click="handleViewDetail(row)"
            >
              查看
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, House } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { get } from '@/utils/request.js'
import { getShiftTypeTagType } from '@/utils/constants.js'

const emit = defineEmits(['view', 'viewDetail'])

const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理filters中的shift_date字段，如果存在且为日期格式，转换为日期格式
    const processedFilters = { ...props.filters }
    if (processedFilters.shift_date && /^\d{4}-\d{2}-\d{2}$/.test(processedFilters.shift_date)) {
      // 保持 yyyy-MM-dd 格式
    }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...processedFilters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get('organizational-management/handover-report/list/', requestParams)
    tableData.value = data.list || []
    totalCount.value = data.total_count || 0
  } catch (error) {
    console.error('获取交接班列表失败:', error)
    ElMessage.error('获取交接班列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm')
}

// 事件处理
const handleRowClick = (row) => {
  emit('view', row)
}

const handleViewDetail = (row) => {
  emit('viewDetail', row)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.handover-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.handover-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}
</style>
