<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="handover-report-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="handover-report-form"
      >
        <!-- 班次信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">班次信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-form-item label="班次日期">
              <el-input :value="form.schedule_date" readonly class="w-full" />
            </el-form-item>
            <el-form-item label="班次类型">
              <el-input :value="form.schedule_shift_type_display" readonly class="w-full" />
            </el-form-item>
          </div>
        </div>

        <!-- 交班信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">交班信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="交班时间" prop="shift_time">
              <el-date-picker
                v-model="form.shift_time"
                type="datetime"
                placeholder="请选择交班时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                class="w-full"
              />
              <div class="text-sm text-gray-500 mt-1">交班时间必须晚于班次开始时间</div>
            </el-form-item>

            <el-form-item label="工作总结" prop="work_summary">
              <el-input
                v-model="form.work_summary"
                type="textarea"
                :rows="4"
                placeholder="请简要总结本班次的工作内容和完成情况"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 特殊情况记录 -->
        <div class="form-section mb-6">
          <h4 class="section-title">特殊情况记录</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="特殊情况跟进" prop="special_situation_follow_up">
              <el-input
                v-model="form.special_situation_follow_up"
                type="textarea"
                :rows="3"
                placeholder="记录本班次内发生的特殊情况及处理跟进情况"
              />
            </el-form-item>

            <el-form-item label="设备异常情况" prop="equipment_abnormal_situation">
              <el-input
                v-model="form.equipment_abnormal_situation"
                type="textarea"
                :rows="3"
                placeholder="记录设备故障、异常或维修情况"
              />
            </el-form-item>

            <el-form-item label="未完成工作" prop="unfinished_work">
              <el-input
                v-model="form.unfinished_work"
                type="textarea"
                :rows="3"
                placeholder="记录本班次未能完成的工作事项，需要下班次继续处理"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 交接信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">交接信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="物品交接" prop="item_handover">
              <el-input
                v-model="form.item_handover"
                type="textarea"
                :rows="3"
                placeholder="记录钥匙、设备、用品等物品的交接情况"
              />
            </el-form-item>

            <el-form-item label="其他重要提醒" prop="other_important_notice">
              <el-input
                v-model="form.other_important_notice"
                type="textarea"
                :rows="3"
                placeholder="其他需要特别提醒下班次注意的重要事项"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '提交报告' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElInput, ElDatePicker, ElButton } from 'element-plus'
import { ElMessage } from 'element-plus'
import { post, put } from '@/utils/request.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'
import { formatDateForInput, formatDateForAPI } from '@/utils/dateUtils.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  scheduleData: {
    type: Object,
    default: () => ({}),
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑交班报告' : '新增交班报告'
})

// 表单数据
const form = reactive({
  schedule_rid: '',
  schedule_date: '',
  schedule_shift_type: '',
  schedule_shift_type_display: '',
  shift_time: '',
  work_summary: '',
  special_situation_follow_up: '',
  equipment_abnormal_situation: '',
  item_handover: '',
  other_important_notice: '',
  unfinished_work: '',
})

// 表单验证规则
const rules = computed(() => ({
  shift_time: [{ required: true, message: '请选择交班时间', trigger: 'change' }],
  work_summary: [{ required: true, message: '请输入工作总结', trigger: 'blur' }],
  special_situation_follow_up: [{ required: true, message: '请输入特殊情况跟进', trigger: 'blur' }],
  equipment_abnormal_situation: [
    { required: true, message: '请输入设备异常情况', trigger: 'blur' },
  ],
  item_handover: [{ required: true, message: '请输入物品交接情况', trigger: 'blur' }],
  other_important_notice: [{ required: true, message: '请输入其他重要提醒', trigger: 'blur' }],
  unfinished_work: [{ required: true, message: '请输入未完成工作', trigger: 'blur' }],
}))

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      console.log('props.scheduleData', props.scheduleData)
      // 填充班次基础信息
      if (props.scheduleData) {
        form.schedule_rid = props.scheduleData.schedule_rid
        form.schedule_date = props.scheduleData.schedule_date
        form.schedule_shift_type = props.scheduleData.schedule_shift_type
        form.schedule_shift_type_display = props.scheduleData.schedule_shift_type_display

        // 如果是编辑模式且有报告数据，填充报告字段
        if (props.itemId && props.scheduleData.rid) {
          form.shift_time = formatDateForInput(props.scheduleData.shift_time)
          form.work_summary = props.scheduleData.work_summary || ''
          form.special_situation_follow_up = props.scheduleData.special_situation_follow_up || ''
          form.equipment_abnormal_situation = props.scheduleData.equipment_abnormal_situation || ''
          form.item_handover = props.scheduleData.item_handover || ''
          form.other_important_notice = props.scheduleData.other_important_notice || ''
          form.unfinished_work = props.scheduleData.unfinished_work || ''
        }
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    schedule_rid: '',
    schedule_date: '',
    schedule_shift_type: '',
    schedule_shift_type_display: '',
    shift_time: '',
    work_summary: '',
    special_situation_follow_up: '',
    equipment_abnormal_situation: '',
    item_handover: '',
    other_important_notice: '',
    unfinished_work: '',
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      shift_time: formatDateForAPI(form.shift_time),
      work_summary: form.work_summary,
      special_situation_follow_up: form.special_situation_follow_up,
      equipment_abnormal_situation: form.equipment_abnormal_situation,
      item_handover: form.item_handover,
      other_important_notice: form.other_important_notice,
      unfinished_work: form.unfinished_work,
    }

    let res
    if (!props.itemId) {
      // 新建报告
      res = await post(
        `organizational-management/handover-report/staff/create/${props.scheduleData.schedule_rid}/`,
        submitData,
      )
    } else {
      // 更新报告
      res = await put(
        `organizational-management/handover-report/staff/update/${props.itemId}/`,
        submitData,
      )
    }

    ElMessage.success(props.itemId ? '交班报告更新成功' : '交班报告提交成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>
