<template>
  <div
    class="handover-report-grid-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Document />
          </el-icon>
          交班报告列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="cards-content px-6 py-4" v-loading="loading">
      <div v-if="tableData.length === 0 && !loading" class="text-center py-12 text-gray-500">
        <el-icon class="text-4xl mb-2 text-gray-400">
          <Document />
        </el-icon>
        <p>暂无交班报告数据</p>
      </div>

      <!-- 交班报告卡片网格 -->
      <div v-else class="handover-grid grid grid-cols-1 md:grid-cols-2 gap-6">
        <div
          v-for="report in tableData"
          :key="report.rid"
          class="handover-card bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-all duration-300 hover:border-pink-300 flex flex-col cursor-pointer overflow-hidden"
          @click="handleCardClick(report)"
        >
          <!-- 卡片头部 -->
          <div class="card-header p-4 border-b border-gray-100">
            <div class="flex items-center justify-between mb-3">
              <div class="flex items-center gap-2">
                <el-icon class="text-pink-500">
                  <User />
                </el-icon>
                <span class="font-semibold text-gray-800">{{ report.staff_name }}</span>
              </div>
              <el-tag :type="getShiftTypeTagType(report.schedule_shift_type)" size="small">
                {{ report.schedule_shift_type_display }}
              </el-tag>
            </div>
            <div class="text-sm text-gray-600">
              {{ report.staff_department_name }}
            </div>
          </div>

          <!-- 卡片内容区 -->
          <div class="card-content flex-1 p-4">
            <!-- 如果有报告数据，显示完整报告内容 -->
            <div v-if="report.rid" class="report-content">
              <!-- 基本信息 -->
              <div class="info-section mb-3">
                <div class="flex items-center justify-between text-sm text-gray-600">
                  <div class="flex items-center gap-1">
                    <el-icon class="info-icon text-pink-500">
                      <Calendar />
                    </el-icon>
                    <span>{{ formatDate(report.schedule_date) }}</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <el-icon class="info-icon text-pink-500">
                      <Clock />
                    </el-icon>
                    <span>{{ report.shift_time }}</span>
                  </div>
                </div>
              </div>

              <!-- 工作总结 -->
              <div class="field-section mb-2">
                <h5 class="field-label">工作总结</h5>
                <p class="field-content">{{ report.work_summary || '-' }}</p>
              </div>

              <!-- 特殊情况跟进 -->
              <div class="field-section mb-2">
                <h5 class="field-label">特殊情况跟进</h5>
                <p class="field-content">{{ report.special_situation_follow_up || '-' }}</p>
              </div>

              <!-- 设备异常情况 -->
              <div class="field-section mb-2">
                <h5 class="field-label">设备异常情况</h5>
                <p class="field-content">{{ report.equipment_abnormal_situation || '-' }}</p>
              </div>

              <!-- 物品交接 -->
              <div class="field-section mb-2">
                <h5 class="field-label">物品交接</h5>
                <p class="field-content">{{ report.item_handover || '-' }}</p>
              </div>

              <!-- 其他重要提醒 -->
              <div class="field-section mb-2">
                <h5 class="field-label">其他重要提醒</h5>
                <p class="field-content">{{ report.other_important_notice || '-' }}</p>
              </div>

              <!-- 未完成工作 -->
              <div class="field-section">
                <h5 class="field-label">未完成工作</h5>
                <p class="field-content">{{ report.unfinished_work || '-' }}</p>
              </div>
            </div>

            <!-- 如果没有报告数据，显示提示信息 -->
            <div v-else class="no-report-content">
              <div class="info-section mb-4">
                <div class="info-item mb-2">
                  <el-icon class="info-icon text-pink-500">
                    <Calendar />
                  </el-icon>
                  <span class="text-sm text-gray-600">{{ formatDate(report.schedule_date) }}</span>
                </div>
              </div>

              <div class="text-center py-8 text-gray-500">
                <el-icon class="text-3xl mb-2 text-gray-400">
                  <Document />
                </el-icon>
                <p class="text-sm">暂未提交交班报告</p>
                <p class="text-xs mt-1">请点击下方按钮添加报告</p>
              </div>
            </div>
          </div>

          <!-- 卡片底部操作按钮 -->
          <div class="card-footer p-4 pt-0">
            <!-- 如果没有报告，显示添加按钮 -->
            <el-button
              v-if="!report.rid"
              type="primary"
              size="small"
              class="w-full bg-pink-500 hover:bg-pink-600 border-pink-500"
              @click.stop="handleAddReport(report)"
            >
              <el-icon class="mr-1">
                <Plus />
              </el-icon>
              添加报告
            </el-button>
            <!-- 如果有报告，显示编辑按钮 -->
            <el-button
              v-else
              type="primary"
              size="small"
              class="w-full bg-pink-500 hover:bg-pink-600 border-pink-500"
              @click.stop="handleEditReport(report)"
            >
              <el-icon class="mr-1">
                <Edit />
              </el-icon>
              编辑报告
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[12, 24, 48, 96]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Edit, Plus, User, Calendar, Clock } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { get } from '@/utils/request.js'
import { getShiftTypeTagType } from '@/utils/constants.js'

const emit = defineEmits(['add-report', 'edit-report'])

const props = defineProps({
  apiUrl: {
    type: String,
    default: 'organizational-management/handover-report/staff/list/',
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 转换API数据格式
const transformReportData = (apiData) => {
  return apiData.map((item) => washReportData(item))
}

const washReportData = (item) => {
  return {
    rid: item.rid,
    schedule_rid: item.schedule_rid,
    schedule_shift_type: item.schedule_shift_type,
    schedule_shift_type_display: item.schedule_shift_type_display,
    schedule_date: item.schedule_date,
    staff_department_name: item.staff_department_name,
    staff_name: item.staff_name,
    shift_time: item.shift_time,
    work_summary: item.work_summary,
    special_situation_follow_up: item.special_situation_follow_up,
    equipment_abnormal_situation: item.equipment_abnormal_situation,
    item_handover: item.item_handover,
    other_important_notice: item.other_important_notice,
    unfinished_work: item.unfinished_work,
    created_at: item.created_at,
    updated_at: item.updated_at,
    // 保留原始数据以备后用
    originalData: item,
  }
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 检查必需的筛选条件
    if (!props.filters.schedule_date) {
      tableData.value = []
      totalCount.value = 0
      loading.value = false
      return
    }

    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get(props.apiUrl, requestParams)
    tableData.value = transformReportData(data.list)
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取交班报告列表失败:', error)
    ElMessage.error('获取交班报告列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 时间格式化
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'MM-dd', { locale: zhCN })
}

// 事件处理

const handleCardClick = () => {
  // 点击卡片时不做任何操作，只通过按钮操作
}

const handleAddReport = (report) => {
  emit('add-report', report)
}

const handleEditReport = (report) => {
  emit('edit-report', report)
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.handover-report-grid-container {
  transition: all 0.3s ease;
  width: 100%;
}

.handover-report-grid-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.handover-card {
  transition: all 0.3s ease;
  min-height: 280px;
}

.handover-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* 截断文本样式 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 3;
}

/* 字段样式 */
.field-section {
  padding-bottom: 0.5rem;
}

.field-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.field-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
}

.field-label::before {
  content: '';
  width: 3px;
  height: 12px;
  background-color: #ec4899;
  margin-right: 6px;
  border-radius: 2px;
}

.field-content {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.3;
  word-wrap: break-word;
  max-height: 2.6rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-clamp: 2;
}

.no-report-content {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .handover-grid {
    grid-template-columns: 1fr;
  }

  .table-header {
    padding: 1rem;
  }

  .cards-content {
    padding: 1rem;
  }
}

/* 自定义分页样式 */
:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}

/* 加载状态 */
:deep(.el-loading-mask) {
  border-radius: 0.5rem;
}
</style>
