<template>
  <el-dialog
    v-model="dialogVisible"
    title="接班确认"
    width="50%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="handover-confirm-dialog"
  >
    <div class="confirm-content">
      <!-- 交接班信息展示 -->
      <div class="handover-info mb-6">
        <h4 class="info-title">交接班信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">交班部门：</span>
            <span class="value">{{ handoverData?.department }}</span>
          </div>
          <div class="info-item">
            <span class="label">交班人：</span>
            <span class="value">{{ handoverData?.giver }}</span>
          </div>
          <div class="info-item">
            <span class="label">交班时间：</span>
            <span class="value">{{ handoverData?.handoverTime }}</span>
          </div>
          <div class="info-item">
            <span class="label">班次：</span>
            <span class="value">{{ handoverData?.shift }}</span>
          </div>
        </div>
      </div>

      <!-- 工作总结 -->
      <div class="content-section mb-4">
        <h5 class="section-label">本班工作总结</h5>
        <div class="section-content">
          {{ handoverData?.summary || '暂无内容' }}
        </div>
      </div>

      <!-- 重点事项 -->
      <div class="content-section mb-4">
        <h5 class="section-label">重点事项记录</h5>
        <div class="section-content">
          {{ handoverData?.keyPoints || '暂无内容' }}
        </div>
      </div>

      <!-- 未完成工作 -->
      <div class="content-section mb-6">
        <h5 class="section-label">未完成工作</h5>
        <div class="section-content">
          {{ handoverData?.unfinished || '暂无内容' }}
        </div>
      </div>

      <!-- 接班确认表单 -->
      <div class="confirm-form">
        <h4 class="form-title">接班确认区</h4>
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px">
          <div class="grid grid-cols-2 gap-4 mb-4">
            <el-form-item label="接班人" prop="receiverName" required>
              <el-input v-model="formData.receiverName" placeholder="当前登录用户（自动填写）" />
            </el-form-item>

            <el-form-item label="确认时间">
              <el-input :value="confirmTime" disabled placeholder="系统自动记录" />
            </el-form-item>
          </div>

          <el-form-item label="接班备注">
            <el-input
              v-model="formData.receiverNotes"
              type="textarea"
              :rows="4"
              placeholder="如对交班内容有疑问或需要补充说明的情况，请在此备注..."
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel"> 取消 </el-button>
        <el-button
          type="warning"
          @click="handleQuestion"
          class="bg-orange-500 hover:bg-orange-600 border-orange-500"
        >
          <el-icon class="mr-1"><QuestionFilled /></el-icon>
          标记有疑问
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          class="bg-green-500 hover:bg-green-600 border-green-500"
        >
          <el-icon class="mr-1"><Check /></el-icon>
          确认交接无误
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElIcon, ElMessage } from 'element-plus'
import { Check, QuestionFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  handoverData: {
    type: Object,
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const confirmTime = computed(() => {
  return new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
})

// 表单数据
const formData = reactive({
  receiverName: '当前用户', // 这里应该从用户信息获取
  receiverNotes: '',
  confirmTime: '',
  status: 'confirmed', // 'confirmed' | 'questioned'
})

// 表单验证规则
const formRules = {
  receiverName: [{ required: true, message: '请输入接班人姓名', trigger: 'blur' }],
}

// 监听弹窗显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      resetForm()
    }
  },
)

// 重置表单
const resetForm = () => {
  formData.receiverName = '当前用户'
  formData.receiverNotes = ''
  formData.status = 'confirmed'
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 确认交接
const handleConfirm = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      const confirmData = {
        id: props.handoverData?.id,
        receiverName: formData.receiverName,
        receiverNotes: formData.receiverNotes,
        confirmTime: confirmTime.value,
        status: 'confirmed',
      }
      emit('save', confirmData)
      ElMessage.success('交接确认成功')
    }
  })
}

// 标记有疑问
const handleQuestion = () => {
  formRef.value?.validate((valid) => {
    if (valid) {
      const confirmData = {
        id: props.handoverData?.id,
        receiverName: formData.receiverName,
        receiverNotes: formData.receiverNotes || '接班人对交接内容有疑问',
        confirmTime: confirmTime.value,
        status: 'questioned',
      }
      emit('save', confirmData)
      ElMessage.warning('已标记为有疑问，请与交班人沟通确认')
    }
  })
}
</script>

<style scoped>
.handover-confirm-dialog {
  margin-top: 8vh !important;
}

.confirm-content {
  max-height: 60vh;
  overflow-y: auto;
}

.handover-info {
  background-color: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.info-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #e77fa1;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #64748b;
  margin-right: 8px;
  min-width: 80px;
}

.info-item .value {
  color: #1f2937;
  font-weight: 500;
}

.content-section {
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.section-label {
  margin: 0;
  padding: 12px 16px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.section-content {
  padding: 16px;
  background-color: white;
  color: #4b5563;
  line-height: 1.6;
  min-height: 60px;
  white-space: pre-wrap;
}

.confirm-form {
  background-color: #f0f9ff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.form-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #0369a1;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
