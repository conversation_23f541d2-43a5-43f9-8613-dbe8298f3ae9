<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
    @open="handleOpen"
    align-center
    :close-on-click-modal="false"
    class="handover-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="handover-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="交班部门" prop="department">
              <el-select
                v-model="formData.department"
                placeholder="请选择部门"
                class="w-full"
                :disabled="isViewMode"
              >
                <el-option label="护理部" value="护理部" />
                <el-option label="产康部" value="产康部" />
                <el-option label="营养部" value="营养部" />
                <el-option label="客服部" value="客服部" />
              </el-select>
            </el-form-item>

            <el-form-item label="班次" prop="shift">
              <el-select
                v-model="formData.shift"
                placeholder="请选择班次"
                class="w-full"
                :disabled="isViewMode"
              >
                <el-option label="早班" value="早班" />
                <el-option label="中班" value="中班" />
                <el-option label="晚班" value="晚班" />
                <el-option label="夜班" value="夜班" />
              </el-select>
            </el-form-item>

            <el-form-item label="交班人" prop="giver">
              <el-input
                v-model="formData.giver"
                placeholder="当前登录用户（自动填写）"
                :disabled="isViewMode"
              />
            </el-form-item>

            <el-form-item label="预计接班人" prop="receiver">
              <el-input
                v-model="formData.receiver"
                placeholder="选择或输入接班人"
                :disabled="isViewMode"
              />
            </el-form-item>

            <el-form-item label="交班时间" prop="handoverTime">
              <el-date-picker
                v-model="formData.handoverTime"
                type="datetime"
                placeholder="选择交班时间"
                :disabled="isViewMode"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 工作总结 -->
        <div class="form-section mb-6">
          <h4 class="section-title">本班工作总结</h4>
          <el-form-item prop="summary">
            <el-input
              v-model="formData.summary"
              type="textarea"
              :rows="4"
              placeholder="记录本班次主要工作完成情况..."
              :disabled="isViewMode"
            />
          </el-form-item>
        </div>

        <!-- 重点事项记录 -->
        <div class="form-section mb-6">
          <h4 class="section-title">重点事项记录</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="母婴特殊情况跟进">
              <el-input
                v-model="formData.keyPoints"
                type="textarea"
                :rows="3"
                placeholder="可链接到具体母婴档案，如：301房产妇情绪波动，需关注..."
                :disabled="isViewMode"
              />
            </el-form-item>

            <el-form-item label="设备异常情况">
              <el-input
                v-model="formData.equipmentIssues"
                type="textarea"
                :rows="2"
                placeholder="如：2楼治疗仪故障..."
                :disabled="isViewMode"
              />
            </el-form-item>

            <el-form-item label="物品交接">
              <el-input
                v-model="formData.itemsHandover"
                type="textarea"
                :rows="2"
                placeholder="如：钥匙、药品（数量）..."
                :disabled="isViewMode"
              />
            </el-form-item>

            <el-form-item label="其他重要通知">
              <el-input
                v-model="formData.otherNotices"
                type="textarea"
                :rows="2"
                placeholder="其他需要特别关注的事项..."
                :disabled="isViewMode"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 未完成工作 -->
        <div class="form-section mb-6">
          <h4 class="section-title">未完成工作</h4>
          <el-form-item prop="unfinished">
            <el-input
              v-model="formData.unfinished"
              type="textarea"
              :rows="4"
              placeholder="列出需下一班继续处理的事项..."
              :disabled="isViewMode"
            />
          </el-form-item>
        </div>

        <!-- 附件上传 -->
        <div class="form-section mb-6" v-if="!isViewMode">
          <h4 class="section-title">附件上传（可选）</h4>
          <el-form-item>
            <el-upload
              class="upload-demo"
              drag
              action="#"
              multiple
              :auto-upload="false"
              :file-list="fileList"
              @change="handleFileChange"
            >
              <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip">支持 jpg/png/pdf 等格式文件，且不超过 5MB</div>
              </template>
            </el-upload>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ isViewMode ? '关闭' : '取消' }}
        </el-button>
        <el-button v-if="!isViewMode" type="info" @click="handleSaveDraft" :loading="saving">
          保存草稿
        </el-button>
        <el-button
          v-if="!isViewMode"
          type="primary"
          @click="handleSubmit"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '提交交班' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
  ElUpload,
  ElIcon,
  ElMessage,
} from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  handoverData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'view'
  },
})

// Emits
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const fileList = ref([])
const saving = ref(false)

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const isViewMode = computed(() => props.mode === 'view')

const dialogTitle = computed(() => {
  if (props.mode === 'view') {
    return `查看交接班日志 (${props.handoverData?.id || ''})`
  }
  return '发起新交班'
})

// 表单数据
const formData = reactive({
  id: '',
  department: '',
  giver: '当前用户', // 这里应该从用户信息获取
  receiver: '',
  handoverTime: '',
  shift: '',
  summary: '',
  keyPoints: '',
  equipmentIssues: '',
  itemsHandover: '',
  otherNotices: '',
  unfinished: '',
})

// 表单验证规则
const formRules = {
  department: [{ required: true, message: '请选择交班部门', trigger: 'change' }],
  shift: [{ required: true, message: '请选择班次', trigger: 'change' }],
  giver: [{ required: true, message: '请输入交班人', trigger: 'blur' }],
  handoverTime: [{ required: true, message: '请选择交班时间', trigger: 'change' }],
  summary: [{ required: true, message: '请填写本班工作总结', trigger: 'blur' }],
  unfinished: [{ required: true, message: '请填写未完成工作', trigger: 'blur' }],
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    department: '',
    giver: '当前用户',
    receiver: '',
    handoverTime: '',
    shift: '',
    summary: '',
    keyPoints: '',
    equipmentIssues: '',
    itemsHandover: '',
    otherNotices: '',
    unfinished: '',
  })
  fileList.value = []
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听数据变化
watch(
  () => props.handoverData,
  (newData) => {
    if (newData) {
      Object.assign(formData, {
        id: newData.id || '',
        department: newData.department || '',
        giver: newData.giver || '当前用户',
        receiver: newData.receiver || '',
        handoverTime: newData.handoverTime || '',
        shift: newData.shift || '',
        summary: newData.summary || '',
        keyPoints: newData.keyPoints || '',
        equipmentIssues: newData.equipmentIssues || '',
        itemsHandover: newData.itemsHandover || '',
        otherNotices: newData.otherNotices || '',
        unfinished: newData.unfinished || '',
      })
    }
  },
  { deep: true, immediate: true },
)

// 方法
const handleOpen = () => {
  if (props.mode === 'add') {
    resetForm()
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

// 文件上传处理
const handleFileChange = (file, files) => {
  fileList.value = files
}

// 保存草稿
const handleSaveDraft = () => {
  ElMessage.success('草稿保存成功')
  // TODO: 调用保存草稿API
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    saving.value = true

    const submitData = {
      ...formData,
      attachments: fileList.value.map((file) => file.name),
    }

    // 模拟保存延迟
    setTimeout(() => {
      emit('save', submitData)
      saving.value = false
    }, 500)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-upload-dragger) {
  border: 2px dashed #e77fa1;
  border-radius: 6px;
  width: 100%;
  height: 120px;
}

:deep(.el-upload-dragger:hover) {
  border-color: #d3608a;
}
</style>
