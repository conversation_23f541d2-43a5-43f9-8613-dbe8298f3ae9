<template>
  <el-dialog
    v-model="visible"
    title="交班报告详情"
    width="800px"
    :before-close="handleClose"
    class="handover-report-detail-dialog"
  >
    <div v-loading="loading" class="dialog-content">
      <div v-if="reportData" class="report-detail-container">
        <!-- 基本信息卡片 -->
        <div class="info-card mb-6">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <User />
              </el-icon>
              基本信息
            </h3>
          </div>
          <div class="card-content">
            <div class="info-grid">
              <div class="info-item">
                <label>排班日期：</label>
                <span class="font-medium">{{ formatDate(reportData.schedule_date) }}</span>
              </div>
              <div class="info-item">
                <label>班次：</label>
                <el-tag :type="getShiftTypeTagType(reportData.schedule_shift_type)" size="small">
                  {{ reportData.schedule_shift_type_display }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>部门：</label>
                <span>{{ reportData.staff_department_name }}</span>
              </div>
              <div class="info-item">
                <label>员工姓名：</label>
                <span class="font-medium">{{ reportData.staff_name }}</span>
              </div>
              <div class="info-item">
                <label>交班时间：</label>
                <span>{{ formatDateTime(reportData.shift_time) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 工作总结卡片 -->
        <div class="info-card mb-6">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <Document />
              </el-icon>
              工作总结
            </h3>
          </div>
          <div class="card-content">
            <div class="content-text">
              {{ reportData.work_summary || '无' }}
            </div>
          </div>
        </div>

        <!-- 特殊情况跟进卡片 -->
        <div class="info-card mb-6">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <Warning />
              </el-icon>
              特殊情况跟进
            </h3>
          </div>
          <div class="card-content">
            <div class="content-text">
              {{ reportData.special_situation_follow_up || '无特殊情况' }}
            </div>
          </div>
        </div>

        <!-- 设备异常情况卡片 -->
        <div class="info-card mb-6">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <Tools />
              </el-icon>
              设备异常情况
            </h3>
          </div>
          <div class="card-content">
            <div class="content-text">
              {{ reportData.equipment_abnormal_situation || '设备正常' }}
            </div>
          </div>
        </div>

        <!-- 物品交接卡片 -->
        <div class="info-card mb-6">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <Box />
              </el-icon>
              物品交接
            </h3>
          </div>
          <div class="card-content">
            <div class="content-text">
              {{ reportData.item_handover || '无物品交接' }}
            </div>
          </div>
        </div>

        <!-- 其他重要事项卡片 -->
        <div class="info-card mb-6">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <Bell />
              </el-icon>
              其他重要事项
            </h3>
          </div>
          <div class="card-content">
            <div class="content-text">
              {{ reportData.other_important_notice || '无其他重要事项' }}
            </div>
          </div>
        </div>

        <!-- 未完成工作卡片 -->
        <div class="info-card mb-6">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <Clock />
              </el-icon>
              未完成工作
            </h3>
          </div>
          <div class="card-content">
            <div class="content-text">
              {{ reportData.unfinished_work || '所有工作已完成' }}
            </div>
          </div>
        </div>

        <!-- 创建和更新时间 -->
        <div class="info-card">
          <div class="card-header">
            <h3 class="card-title">
              <el-icon class="text-pink-500">
                <Calendar />
              </el-icon>
              记录信息
            </h3>
          </div>
          <div class="card-content">
            <div class="info-grid">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDateTime(reportData.created_at) }}</span>
              </div>
              <div class="info-item">
                <label>更新时间：</label>
                <span>{{ formatDateTime(reportData.updated_at) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Document, Warning, Tools, Box, Bell, Clock, Calendar } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { get } from '@/utils/request.js'
import { getShiftTypeTagType } from '@/utils/constants.js'

const emit = defineEmits(['update:modelValue', 'success'])

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  reportId: {
    type: String,
    default: '',
  },
})

// 响应式数据
const loading = ref(false)
const reportData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 时间格式化
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return format(new Date(dateString), 'yyyy-MM-dd', { locale: zhCN })
}

const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '-'
  return format(new Date(dateTimeString), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
}

// 加载报告详情
const loadReportDetail = async () => {
  if (!props.reportId) return

  loading.value = true
  try {
    const data = await get(
      `organizational-management/handover-report/staff/detail/${props.reportId}/`,
    )
    reportData.value = data
  } catch (error) {
    console.error('获取交班报告详情失败:', error)
    ElMessage.error('获取交班报告详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  reportData.value = null
  emit('success')
}

// 监听对话框打开
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && props.reportId) {
      loadReportDetail()
    }
  },
  { immediate: true },
)
</script>

<style scoped>
.handover-report-detail-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 4px;
}

.report-detail-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.card-header {
  background: #f9fafb;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.card-content {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item label {
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.content-text {
  color: #374151;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
  min-height: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .card-content {
    padding: 16px;
  }

  .card-header {
    padding: 12px 16px;
  }
}

/* 自定义滚动条 */
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 对话框样式覆盖 */
:deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}
</style>
