<template>
  <div class="handover-view-container bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div>
            <h1 class="text-2xl font-bold text-gray-800">值班与交接班管理 - 交接班报告</h1>
            <p class="text-sm text-gray-600 mt-1">进行班次交接和信息传递，记录重要事项</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 交接班列表组件 -->
    <HandoverTable
      ref="handoverTableRef"
      :filters="filters"
      @view="handleViewHandover"
      @viewDetail="handleViewDetail"
    />

    <!-- 确认交接弹窗 -->
    <HandoverConfirmDialog
      v-model:visible="confirmVisible"
      :handover-data="currentHandover"
      @save="handleSaveConfirm"
    />

    <!-- 交接班详情弹窗 -->
    <HandoverDetailDialog
      v-model="detailVisible"
      :item-id="currentHandoverId"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import FilterPanel from '@/components/FilterPanel.vue'
import HandoverTable from './components/HandoverTable.vue'
import HandoverConfirmDialog from './components/HandoverConfirmDialog.vue'
import HandoverDetailDialog from './components/HandoverDetailDialog.vue'
import { useBaseDataStore } from '@/stores/baseData.js'
import { getTodayString } from '@/utils/utils.js'

// 基础数据store
const baseDataStore = useBaseDataStore()

// 响应式数据
const formVisible = ref(false)
const confirmVisible = ref(false)
const detailVisible = ref(false)
const formMode = ref('add') // 'add' | 'view'
const currentHandover = ref(null)
const currentHandoverId = ref(null)

// 获取 table 组件引用
const handoverTableRef = ref(null)

// 筛选条件
const filters = reactive({
  department_rid: '',
  shift_date: null,
  sk: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'department_rid',
    type: 'select',
    label: '交班部门',
    placeholder: '选择部门',
    options: baseDataStore.departments.getOptions(),
    loading: baseDataStore.departments.isLoading() || baseDataStore.departments.searchLoading.value,
  },
  {
    key: 'shift_date',
    type: 'date',
    label: '交班日期',
    placeholder: '选择日期',
  },
  {
    key: 'sk',
    type: 'input',
    label: '员工姓名',
    placeholder: '搜索员工姓名',
  },
])

const handleViewHandover = (handover) => {
  currentHandover.value = { ...handover }
  formMode.value = 'view'
  formVisible.value = true
}

const handleSearch = () => {
  // 重置到第一页并重新加载数据
  handoverTableRef.value?.resetPagination()
}

const handleViewDetail = (handover) => {
  currentHandoverId.value = handover.rid
  detailVisible.value = true
}

const handleCloseDetail = () => {
  detailVisible.value = false
  currentHandoverId.value = null
}

const handleSaveConfirm = () => {
  confirmVisible.value = false
  ElMessage.success('交接确认成功')
  // 刷新表格数据
  handoverTableRef.value?.refresh()
}

onMounted(() => {
  // 初始化基础数据
  baseDataStore.departments.fetch()
})
</script>

<style scoped>
.handover-view-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.page-header {
  transition: all 0.3s ease;
}
</style>
