<template>
  <el-container class="h-screen">
    <!-- 侧边栏 -->
    <Sidebar />

    <el-container>
      <!-- 顶部导航 -->
      <el-header
        class="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4"
      >
        <div class="flex items-center space-x-4">
          <!-- <el-button
            text
            @click="menuStore.toggleCollapse()"
            class="text-gray-600 hover:text-gray-900"
          >
            <ArrowRight v-if="menuStore.collapsed" class="w-5 h-5" />
            <ArrowLeft v-else class="w-5 h-5" />
          </el-button> -->

          <!-- 面包屑 -->
          <el-breadcrumb v-if="breadcrumbs.length > 0" separator="/" class="hidden md:block">
            <el-breadcrumb-item v-for="item in breadcrumbs" :key="item.path" :to="item.path">
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="flex items-center space-x-4">

          <!-- 更新日志 -->
          <el-badge class="cursor-pointer" @click="showChangelog">
            <Refresh class="w-5 h-5 text-gray-500" />
          </el-badge>

          <!-- 通知 -->
          <el-badge :value="5" class="cursor-pointer">
            <Bell class="w-5 h-5 text-gray-500" />
          </el-badge>

          <!-- 用户信息 -->
          <el-dropdown @command="handleUserMenuCommand">
            <div
              class="flex items-center space-x-2 cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-md"
            >
              <el-avatar :size="32" :src="authStore.user?.avatar" />
              <div class="hidden md:block text-left">
                <div class="text-sm font-medium text-gray-900">{{ authStore.user?.name }}</div>
                <div class="text-xs text-gray-500">
                  {{ authStore.userRole }}
                </div>
              </div>
              <ArrowDown class="w-4 h-4 text-gray-400" />
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <User class="w-4 h-4 mr-2" />
                  档案
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <Setting class="w-4 h-4 mr-2" />
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <SwitchButton class="w-4 h-4 mr-2" />
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容区 -->
      <el-main class="bg-gray-50 px-10!">
        <RouterView />
      </el-main>
    </el-container>

    <!-- 更新日志对话框 -->
    <ChangelogDialog v-model="changelogVisible" />
  </el-container>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useMenuStore } from '@/stores/menu'
import { ElMessage, ElMessageBox } from 'element-plus'
import Sidebar from '@/components/Sidebar.vue'
import ChangelogDialog from '@/views/changelog/ChangelogDialog.vue'
import { User, Bell, ArrowDown, SwitchButton, Setting, Refresh } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const menuStore = useMenuStore()

// 更新日志对话框状态
const changelogVisible = ref(false)

// 面包屑导航
const breadcrumbs = computed(() => {
  const path = route.path
  const segments = path.split('/').filter(Boolean)
  const crumbs = []

  // 使用 filteredMenus 而不是 allMenus，这样只会显示用户有权限的菜单
  const menus = menuStore.filteredMenus || []

  if (!Array.isArray(menus) || menus.length === 0) {
    console.log('菜单为空或不是数组')
    return crumbs
  }

  // 检查是否有自定义面包屑配置
  if (route.meta?.breadcrumbs && Array.isArray(route.meta.breadcrumbs)) {
    const breadcrumbConfig = route.meta.breadcrumbs

    breadcrumbConfig.forEach((crumb) => {
      if (!crumb || typeof crumb !== 'object') return // 安全检查

      if (
        crumb.dynamic &&
        crumb.title &&
        crumb.title.startsWith('{') &&
        crumb.title.endsWith('}')
      ) {
        // 处理动态参数
        const paramKey = crumb.title.slice(1, -1)
        const value = route.query[paramKey] || route.params[paramKey] || '详情'
        crumbs.push({
          title: value,
          path: crumb.path || route.path,
        })
      } else if (crumb.title) {
        // 静态面包屑项
        crumbs.push({
          title: crumb.title,
          path: crumb.path || undefined, // 避免传递 null
        })
      }
    })
    return crumbs
  }

  // 根据路径生成面包屑
  segments.forEach((segment, index) => {
    const currentPath = '/' + segments.slice(0, index + 1).join('/')
    const menu = findMenuByPath(currentPath, menus)
    // console.log(`查找路径 ${currentPath}:`, menu)
    if (menu) {
      crumbs.push({
        title: menu.title,
        path: currentPath,
      })
    }
  })

  // console.log('生成的面包屑:', crumbs)
  return crumbs
})

// 查找菜单项
const findMenuByPath = (path, menus) => {
  // 确保 menus 是数组
  if (!Array.isArray(menus)) {
    return null
  }

  for (const menu of menus) {
    if (menu.path === path) {
      return menu
    }
    if (menu.children && Array.isArray(menu.children)) {
      const found = findMenuByPath(path, menu.children)
      if (found) return found
    }
  }
  return null
}

// 显示更新日志
const showChangelog = () => {
  changelogVisible.value = true
}

// 处理用户菜单命令
const handleUserMenuCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('Profile page coming soon!')
      break
    case 'settings':
      ElMessage.info('Settings page coming soon!')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('Are you sure you want to logout?', 'Confirm Logout', {
          confirmButtonText: 'Yes',
          cancelButtonText: 'Cancel',
          type: 'warning',
        })
        authStore.logout()
        router.push('/login')
        ElMessage.success('Logged out successfully')
      } catch {
        // 用户取消
      }
      break
  }
}

onMounted(() => {
  // 初始化用户信息
  authStore.initUser()
})
</script>

<style scoped></style>
