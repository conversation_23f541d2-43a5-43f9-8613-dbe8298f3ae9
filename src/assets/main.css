@import 'tailwindcss';

/* Element Plus 主题变量覆盖 */
:root {
  /* 主色调 */
  --el-color-primary: rgb(231, 127, 161);
  --el-color-primary-light-3: rgba(231, 127, 161, 0.7);
  --el-color-primary-light-5: rgba(231, 127, 161, 0.5);
  --el-color-primary-light-7: rgba(231, 127, 161, 0.3);
  --el-color-primary-light-8: rgba(231, 127, 161, 0.2);
  --el-color-primary-light-9: rgba(231, 127, 161, 0.1);
  --el-color-primary-dark-2: rgb(207, 114, 145);

  --el-menu-bg-color: transparent;

  /* 自定义变量 */
  --primary-color: rgb(231, 127, 161);
  --primary-color-hover: rgba(231, 127, 161, 0.8);
  --primary-color-light: rgba(231, 127, 161, 0.1);
  --el-menu-item-height: 45px;
}
.el-input-number {
  width: auto;
  &.w-full {
    width: 100%;
  }
}
.el-date-editor.el-input,
.el-date-editor.el-input__wrapper {
  &.w-full {
    width: 100%;
  }
}
.el-menu-item {
  &.menu-item-child {
    /* padding-left: 0 !important; */
  }
  &.is-active {
    background-color: var(--el-color-primary-light-3);
    color: white;
  }
}
