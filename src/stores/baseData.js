import { defineStore } from 'pinia'
import { ref, reactive, computed, readonly } from 'vue'
import { get } from '@/utils/request.js'

export const useBaseDataStore = defineStore('baseData', () => {
  // 通用数据缓存结构
  const dataCache = reactive({})
  const loadingStates = reactive({})
  const loadedStates = reactive({})
  const errorStates = reactive({}) // 新增：错误状态管理
  const lastRequestTime = reactive({}) // 新增：最后请求时间
  const retryCount = reactive({}) // 新增：重试次数

  // 搜索数据缓存
  const searchCache = reactive({})
  const searchLoadingStates = reactive({})

  // 请求间隔限制（毫秒）
  const REQUEST_INTERVAL = 5000 // 5秒内不重复请求
  const MAX_RETRY_COUNT = 3 // 最大重试次数
  const RETRY_DELAY = 2000 // 重试延迟时间

  // 数据类型配置
  const dataTypes = {
    roomTypes: {
      url: 'customer-service/room/type/list/',
      formatter: (data) => data.map((type) => ({ label: type, value: type })),
    },
    floors: {
      url: 'customer-service/room/floor/list/',
      formatter: (data) => data.map((floor) => ({ label: `${floor}楼`, value: floor })),
    },
    packages: {
      url: 'organizational-management/packages/list/?page=1&page_size=1000&status=ACTIVE',
      formatter: (data) => data.map((item) => ({ label: item.name, value: item.rid, origin: item })),
    },
    rooms: {
      url: 'customer-service/room/list/?aps=AVAILABLE&page=1&page_size=1000',
      formatter: (data) =>
        data.map((item) => ({ label: item.room_number, value: item.rid, origin: item })),
    },
    nurses: {
      url: 'customer-service/staff/select/list/',
      formatter: (data) => data.map((item) => ({ label: item.name, value: item.sid, origin: item })),
      searchParam: 'sk', // 搜索参数字段名
      searchable: true, // 标记为可搜索
    },
    staffs: {
      url: 'organizational-management/staff/list/?page=1&page_size=1000',
      formatter: (data) => data.map((item) => ({ label: item.name, value: item.sid, origin: item })),
      searchParam: 'sk', // 搜索参数字段名
      searchable: true, // 标记为可搜索
    },
    departments: {
      url: 'organizational-management/department/list/',
      formatter: (data) => data.map((item) => ({ label: item.name, value: item.rid, origin: item })),
      searchParam: 'sk', // 搜索参数字段名
      searchable: true, // 标记为可搜索
    },
    newMothers: {
      url: 'customer-service/maternity-admission/in-house/select/list/',
      formatter: (data) =>
        data.map((item) => ({ label: item.maternity_name, value: item.aid, origin: item })),
      searchParam: 'sk', // 搜索参数字段名
      searchable: true, // 标记为可搜索
    },
    // 入住选择用的
    maternitys: {
      url: 'customer-service/maternity/select/list/',
      formatter: (data) =>
        data.map((item) => ({ label: item.name, value: item.uid, origin: item })),
      searchParam: 'sk', // 搜索参数字段名
      searchable: true, // 标记为可搜索
    },
    disinfectionDocs: {
      url: 'customer-service/disinfection-document/list/?status=IN_EFFECT',
      formatter: (data) =>
        data.map((item) => ({ label: item.name, value: item.rid, origin: item })),
      searchParam: 'sk', // 搜索参数字段名
      searchable: true, // 标记为可搜索
    },
    activityTypes: {
      url: 'customer-service/activity/type/',
      formatter: (data) => data.map((item) => ({ label: item, value: item })),
    },
    roles: {
      url: 'permission/role-list/',
      formatter: (data) => data.map((item) => ({ label: item.name, value: item.rid })),
    },
  }

  // 响应式数据更新回调
  const updateCallbacks = reactive({})

  // 通用数据获取方法
  const fetchData = async (key, url, force = false) => {
    // 如果已经加载过且不强制刷新，直接返回
    if (loadedStates[key] && !force) {
      return dataCache[key] || []
    }

    // 如果正在加载中，返回空数组（避免重复请求）
    if (loadingStates[key]) {
      return dataCache[key] || []
    }

    // 检查重试次数
    if (!force && retryCount[key] >= MAX_RETRY_COUNT) {
      console.warn(`${key}数据请求失败次数过多，已停止重试`)
      return dataCache[key] || []
    }

    // 检查是否在错误状态且未超过重试间隔（但排除自动重试的情况）
    const now = Date.now()
    if (
      !force &&
      errorStates[key] &&
      lastRequestTime[key] &&
      now - lastRequestTime[key] < REQUEST_INTERVAL &&
      retryCount[key] === 0
    ) {
      console.warn(`${key}数据请求过于频繁，请稍后再试`)
      return dataCache[key] || []
    }

    loadingStates[key] = true
    lastRequestTime[key] = now

    try {
      // if (retryCount[key] == undefined) {
      //   throw 'test err'
      // }
      // 如果是重试，添加延迟
      if (retryCount[key] > 0) {
        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY))
      }

      const response = await get(url)

      // 处理不同类型的API响应结构
      let data
      if (response && typeof response === 'object') {
        // 如果响应包含list字段，说明是分页结构，提取list数据
        if (Object.prototype.hasOwnProperty.call(response, 'list') && Array.isArray(response.list)) {
          data = response.list
        }
        // 如果响应包含data字段，提取data数据
        else if (Object.prototype.hasOwnProperty.call(response, 'data')) {
          data = Array.isArray(response.data) ? response.data : response
        }
        // 如果响应本身就是数组，直接使用
        else if (Array.isArray(response)) {
          data = response
        }
        // 其他情况，保持原响应数据
        else {
          data = response
        }
      } else {
        data = response || []
      }

      dataCache[key] = data
      loadedStates[key] = true
      errorStates[key] = false // 清除错误状态
      retryCount[key] = 0 // 重置重试次数

      // 触发响应式数据更新回调
      if (updateCallbacks[key]) {
        updateCallbacks[key](data)
      }

      return dataCache[key]
    } catch (error) {
      console.error(`获取${key}数据失败:`, error)

      // 增加重试次数
      retryCount[key] = (retryCount[key] || 0) + 1
      errorStates[key] = true

      // 如果还没达到最大重试次数，自动重试
      if (retryCount[key] < MAX_RETRY_COUNT) {
        console.log(`${key}数据获取失败，${RETRY_DELAY / 1000}秒后进行第${retryCount[key]}次重试`)
        loadingStates[key] = false // 先重置加载状态

        // 延迟后自动重试
        setTimeout(async () => {
          try {
            // 重试时重置时间限制，允许重试请求通过
            lastRequestTime[key] = 0
            await fetchData(key, url, false)
          } catch (retryError) {
            console.error(`${key}数据重试失败:`, retryError)
          }
        }, RETRY_DELAY)

        // 返回当前缓存数据
        if (!dataCache[key]) {
          dataCache[key] = []
        }
        return dataCache[key]
      } else {
        // ElMessage.error(`获取${key}数据失败，请检查网络连接或稍后重试`)

        // 保持之前的缓存数据，避免返回空数组导致界面异常
        if (!dataCache[key]) {
          dataCache[key] = []
        }
        return dataCache[key]
      }
    } finally {
      // 只有在不需要重试时才重置加载状态
      if (!retryCount[key] || retryCount[key] >= MAX_RETRY_COUNT || loadedStates[key]) {
        loadingStates[key] = false
      }
    }
  }

  // 搜索数据方法
  const searchData = async (key, keyword) => {
    const config = dataTypes[key]
    if (!config || !config.searchable) {
      return []
    }

    // 清理关键词，移除空格
    const cleanKeyword = keyword?.trim() || ''
    if (!cleanKeyword) {
      return []
    }

    const searchKey = `${key}_search_${cleanKeyword}`

    // 如果已经在搜索中，返回缓存的数据
    if (searchLoadingStates[searchKey]) {
      return searchCache[searchKey] || []
    }

    // 如果已有缓存且关键词相同，直接返回
    if (searchCache[searchKey]) {
      return searchCache[searchKey]
    }

    searchLoadingStates[searchKey] = true

    try {
      const searchParam = config.searchParam || 'sk'
      const separator = config.url.includes('?') ? '&' : '?'
      const searchUrl = `${config.url}${separator}${searchParam}=${encodeURIComponent(cleanKeyword)}`

      const response = await get(searchUrl)

      // 处理不同类型的API响应结构
      let data
      if (response && typeof response === 'object') {
        if (Object.prototype.hasOwnProperty.call(response, 'list') && Array.isArray(response.list)) {
          data = response.list
        } else if (Object.prototype.hasOwnProperty.call(response, 'data')) {
          data = Array.isArray(response.data) ? response.data : response
        } else if (Array.isArray(response)) {
          data = response
        } else {
          data = response
        }
      } else {
        data = response || []
      }

      searchCache[searchKey] = data
      return data
    } catch (error) {
      console.error(`搜索${key}数据失败:`, error)
      return []
    } finally {
      searchLoadingStates[searchKey] = false
    }
  }

  // 动态创建数据管理器
  const createDataManager = (key, config) => {
    const data = ref([])

    // 搜索相关状态
    const searchKeyword = ref('')
    const searchResults = ref([])
    const isSearching = ref(false)
    let searchTimeout = null

    // 注册更新回调
    updateCallbacks[key] = (newData) => {
      data.value = newData
    }

    // 监听数据变化，自动更新响应式数据
    const updateData = async (force = false) => {
      const result = await fetchData(key, config.url, force)
      data.value = result
      return result
    }

    // 获取格式化选项
    const getOptions = () => {
      // 如果数据未加载且不在错误状态，触发异步加载
      if (!loadedStates[key] && !loadingStates[key] && !errorStates[key]) {
        updateData()
      }
      return config.formatter ? config.formatter(data.value || []) : data.value || []
    }

    // 清理搜索状态
    const clearSearch = () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout)
        searchTimeout = null
      }
      searchKeyword.value = ''
      searchResults.value = []
      isSearching.value = false
    }

    // 防抖搜索方法
    const performSearch = async (query) => {
      const trimmedQuery = (query || '').trim()

      // 如果搜索关键词与当前相同，不需要重复搜索
      if (searchKeyword.value === trimmedQuery) {
        return
      }

      searchKeyword.value = trimmedQuery

      // 清除之前的搜索定时器
      if (searchTimeout) {
        clearTimeout(searchTimeout)
      }

      if (!trimmedQuery) {
        searchResults.value = []
        isSearching.value = false
        return
      }

      if (!config.searchable) {
        // 如果不支持远程搜索，使用本地过滤
        const allOptions = getOptions()
        searchResults.value = allOptions.filter((option) =>
          option.label.toLowerCase().includes(trimmedQuery.toLowerCase()),
        )
        return
      }

      // 设置防抖延迟
      isSearching.value = true
      searchTimeout = setTimeout(async () => {
        try {
          const results = await searchData(key, trimmedQuery)
          // 只有当搜索关键词没有变化时才更新结果
          if (searchKeyword.value === trimmedQuery) {
            const formattedResults = config.formatter ? config.formatter(results) : results
            searchResults.value = formattedResults
          }
        } catch (error) {
          console.error(`搜索${key}数据失败:`, error)
          if (searchKeyword.value === trimmedQuery) {
            searchResults.value = []
          }
        } finally {
          // 只有当搜索关键词没有变化时才重置加载状态
          if (searchKeyword.value === trimmedQuery) {
            isSearching.value = false
          }
        }
      }, 300) // 300ms 防抖延迟
    }

    // 获取显示选项（搜索结果或默认选项）- 使用computed确保响应式
    const displayOptions = computed(() => {
      // 如果有搜索关键词，显示搜索结果（即使结果为空也要显示，避免显示全部选项）
      if (searchKeyword.value && searchKeyword.value.trim() !== '') {
        return searchResults.value
      }
      // 没有搜索关键词时，显示默认选项
      return getOptions()
    })

    const getDisplayOptions = () => displayOptions.value

    // 搜索方法（保持向后兼容）
    const search = async (keyword) => {
      if (!config.searchable) {
        // 如果不支持搜索，使用本地过滤
        const options = getOptions()
        return options.filter((option) =>
          option.label.toLowerCase().includes(keyword.toLowerCase()),
        )
      }

      const searchResult = await searchData(key, keyword)
      return config.formatter ? config.formatter(searchResult) : searchResult
    }

    // 获取搜索状态
    const isSearchingKeyword = (keyword) => {
      const cleanKeyword = keyword?.trim() || ''
      if (!cleanKeyword) return false
      const searchKey = `${key}_search_${cleanKeyword}`
      return searchLoadingStates[searchKey] || false
    }

    return {
      data,
      fetch: updateData,
      getOptions,
      search,
      isSearching: isSearchingKeyword,
      isLoading: () => loadingStates[key] || false,
      isLoaded: () => loadedStates[key] || false,
      isError: () => errorStates[key] || false,
      isSearchable: () => config.searchable || false,
      retry: () => {
        errorStates[key] = false
        retryCount[key] = 0
        lastRequestTime[key] = 0
        return updateData(true)
      },
      reset: () => {
        dataCache[key] = []
        loadedStates[key] = false
        errorStates[key] = false
        retryCount[key] = 0
        lastRequestTime[key] = 0
        data.value = []
        // 清空搜索缓存
        Object.keys(searchCache).forEach((cacheKey) => {
          if (cacheKey.startsWith(`${key}_search_`)) {
            delete searchCache[cacheKey]
          }
        })
        // 清空搜索加载状态
        Object.keys(searchLoadingStates).forEach((loadingKey) => {
          if (loadingKey.startsWith(`${key}_search_`)) {
            delete searchLoadingStates[loadingKey]
          }
        })
        // 清理搜索状态
        clearSearch()
      },
      // 新的搜索接口
      performSearch,
      clearSearch,
      getDisplayOptions,
      searchKeyword: readonly(searchKeyword),
      searchResults: readonly(searchResults),
      searchLoading: readonly(isSearching),
    }
  }

  // 创建所有数据管理器
  const managers = {}
  Object.keys(dataTypes).forEach((key) => {
    managers[key] = createDataManager(key, dataTypes[key])
  })

  // 清理搜索缓存（定期调用以避免内存泄漏）
  const cleanupSearchCache = () => {
    Object.keys(searchCache).forEach((cacheKey) => {
      // 这里可以添加时间戳检查，暂时简单清理
      if (Math.random() < 0.1) {
        // 10%概率清理缓存
        delete searchCache[cacheKey]
        delete searchLoadingStates[cacheKey]
      }
    })
  }

  // 重置所有数据
  const resetAllData = () => {
    Object.keys(managers).forEach((key) => {
      managers[key].reset()
    })
    // 清理搜索缓存
    cleanupSearchCache()
  }

  // 注册新的数据类型（供其他模块动态添加）
  const registerDataType = (key, url, formatter = null) => {
    dataTypes[key] = { url, formatter }
    managers[key] = createDataManager(key, dataTypes[key])
    return managers[key]
  }

  return {
    // 数据管理器
    ...managers,

    // 通用方法
    resetAllData,
    registerDataType,
  }
})
