import { defineStore } from 'pinia'
import { ref, computed, markRaw } from 'vue'
import { useAuthStore } from './auth'
import {
  TrendCharts,
  House,
  User,
  CreditCard,
  CircleCheck,
  Switch,
  Bowl,
  Calendar,
  Reading,
  Clock,
  ChatLineRound,
  Setting,
  Document,
  Tools,
  Plus,
  Money,
  Lock,
  Coin,
  Star,
  Headset,
  View,
  Right,
  SwitchButton,
  Rank,
  Location,
  KnifeFork,
  Cpu,
  InfoFilled,
  ChatDotRound,
  Iphone,
  Promotion,
  Management,
  Monitor,
} from '@element-plus/icons-vue'

// 使用 markRaw 包装图标组件，防止被响应式系统包装
const icons = {
  TrendCharts: markRaw(TrendCharts),
  House: markRaw(House),
  User: markRaw(User),
  CreditCard: markRaw(CreditCard),
  CircleCheck: markRaw(CircleCheck),
  Switch: markRaw(Switch),
  Bowl: markRaw(Bowl),
  Calendar: markRaw(Calendar),
  Reading: markRaw(Reading),
  Clock: markRaw(Clock),
  ChatLineRound: markRaw(ChatLineRound),
  Setting: markRaw(Setting),
  Document: markRaw(Document),
  Tools: markRaw(Tools),
  Plus: markRaw(Plus),
  Money: markRaw(Money),
  Lock: markRaw(Lock),
  Coin: markRaw(Coin),
  Star: markRaw(Star),
  Headset: markRaw(Headset),
  View: markRaw(View),
  Right: markRaw(Right),
  SwitchButton: markRaw(SwitchButton),
  Rank: markRaw(Rank),
  Location: markRaw(Location),
  KnifeFork: markRaw(KnifeFork),
  Cpu: markRaw(Cpu),
  InfoFilled: markRaw(InfoFilled),
  ChatDotRound: markRaw(ChatDotRound),
  Iphone: markRaw(Iphone),
  Promotion: markRaw(Promotion),
  Management: markRaw(Management),
  Monitor: markRaw(Monitor),
}

export const useMenuStore = defineStore('menu', () => {
  const collapsed = ref(false)

  // 完整菜单配置 - 添加分组层级
  const allMenus = ref([
    {
      id: 'dashboard-group',
      title: '运营总览',
      isGroup: true,
      children: [
        {
          id: 'dashboard',
          title: '运营总览',
          icon: icons.TrendCharts,
          path: '/dashboard',
          permissions: ['main.dashboard.view'],
          children: [],
        },
        {
          id: 'todo-list',
          title: '待办事项',
          icon: icons.Document,
          path: '/todo-list',
          permissions: [],
          children: [],
        },
        {
          id: 'todo-management',
          title: '待办事项管理',
          icon: icons.Management,
          path: '/todo-management',
          permissions: ['todo.edit'],
          children: [],
        },
      ],
    },
    {
      id: 'customer-service-group',
      title: '客服中心',
      isGroup: true,
      children: [
        {
          id: 'housekeeping',
          title: '房务管理',
          icon: icons.House,
          path: '/housekeeping',
          children: [
            {
              id: 'housekeeping-overview',
              title: '房态总览',
              icon: icons.View,
              path: '/housekeeping/overview',
              permissions: ['cus.housekeeping.view'],
            },
            {
              id: 'maternal-management-housekeeping',
              title: '产妇管理',
              icon: icons.User,
              path: '/housekeeping/maternal-management',
              permissions: ['cus.maternal.core.record.view'],
            },
            {
              id: 'housekeeping-checkin',
              title: '入住管理',
              icon: icons.Right,
              path: '/housekeeping/checkin',
              permissions: ['cus.housekeeping.view'],
            },
            {
              id: 'housekeeping-checkout',
              title: '退房管理',
              icon: icons.SwitchButton,
              path: '/housekeeping/checkout',
              permissions: ['cus.housekeeping.view'],
            },
            {
              id: 'housekeeping-rooms',
              title: '房间信息管理',
              icon: icons.House,
              path: '/housekeeping/rooms',
              permissions: ['cus.housekeeping.view'],
            },
            {
              id: 'housekeeping-transfer',
              title: '换房管理',
              icon: icons.Rank,
              path: '/housekeeping/transfer',
              permissions: ['cus.housekeeping.view'],
            },
            {
              id: 'housekeeping-outing',
              title: '外出管理',
              icon: icons.Location,
              path: '/housekeeping/outing',
              permissions: ['cus.housekeeping.edit'],
            },
          ],
        },
        {
          id: 'internal-referral',
          title: '院内转诊',
          icon: icons.Switch,
          path: '/internal-referral',
          permissions: ['cus.in_hospital_transfer.view'],
          children: [],
        },
        {
          id: 'meal-management',
          title: '膳食管理',
          icon: icons.Bowl,
          path: '/meal-management',
          children: [
            {
              id: 'dish-management',
              title: '菜品管理',
              icon: icons.Bowl,
              path: '/meal-management/dishes',
              permissions: ['cus.diet.view'],
            },
            {
              id: 'meal-planning',
              title: '菜单规划',
              icon: icons.KnifeFork,
              path: '/meal-management/planning',
              permissions: ['cus.diet.view'],
            },
            // {
            //   id: 'meal-settings',
            //   title: '膳食设置',
            //   icon: icons.Setting,
            //   path: '/meal-management/settings',
            //   permissions: ['cus.diet.view'],
            // },
            {
              id: 'nutrition-analysis',
              title: '营养分析',
              icon: icons.Reading,
              path: '/meal-management/nutrition',
              permissions: ['cus.diet.view'],
            },
          ],
        },
        {
          id: 'maternal-records',
          title: '母婴核心记录',
          icon: icons.User,
          path: '/maternal-records',
          children: [
            {
              id: 'maternal-customers',
              title: '客户列表',
              icon: icons.User,
              path: '/maternal-records/customers',
              permissions: ['cus.maternal.core.record.view'],
            },
            {
              id: 'maternal-management',
              title: '产妇管理',
              icon: icons.User,
              path: '/maternal-records/maternal-management',
              permissions: ['cus.maternal.core.record.view'],
            },
          ],
        },
        {
          id: 'activity-management',
          title: '活动管理',
          icon: icons.Calendar,
          path: '/activity-management',
          children: [
            {
              id: 'activity-list',
              title: '活动列表',
              icon: icons.Calendar,
              path: '/activity-management/list',
              permissions: ['cus.activity.view'],
            },
            // {
            //   id: 'activity-settings',
            //   title: '活动设置',
            //   icon: icons.Setting,
            //   path: '/activity-management/settings',
            //   permissions: ['cus.activity.view'],
            // },
          ],
        },
        {
          id: 'health-education',
          title: '健康宣教管理',
          icon: icons.ChatLineRound,
          path: '/health-education',
          permissions: ['cus.health.education.view'],
          children: [],
        },
        {
          id: 'visitor-management',
          title: '访客管理',
          icon: icons.User,
          path: '/visitor-management',
          permissions: ['cus.visitor.view'],
          children: [],
        },
        {
          id: 'visitor-appointment',
          title: '预约参观(小程序)',
          icon: icons.User,
          path: '/visitor-appointment',
          permissions: ['cus.visitor.view'],
          children: [],
        },
        {
          id: 'disinfection-management',
          title: '消毒管理',
          icon: icons.CircleCheck,
          path: '/disinfection-management',
          children: [
            {
              id: 'disinfection-records',
              title: '清洁消毒记录',
              icon: icons.Document,
              path: '/disinfection-management/records',
              permissions: ['cus.disinfection.view'],
            },
            {
              id: 'disinfection-standards',
              title: '消毒规范文库',
              icon: icons.Reading,
              path: '/disinfection-management/standards',
              permissions: ['cus.disinfection.view'],
            },
          ],
        },
        {
          id: 'postpartum-rehabilitation',
          title: '产后康复',
          icon: icons.Promotion,
          path: '/postpartum-rehabilitation',
          children: [
            {
              id: 'postpartum-records',
              title: '产康记录',
              icon: icons.Document,
              path: '/postpartum-rehabilitation/records',
              permissions: ['cus.postpartum.view'],
            },
            {
              id: 'postpartum-project-management',
              title: '产康项目',
              icon: icons.Management,
              path: '/postpartum-rehabilitation/project-management',
              permissions: ['cus.postpartum.view'],
            },
          ],
        },
        {
          id: 'ward-round-management',
          title: '查房管理',
          icon: icons.Monitor,
          path: '/ward-round-management',
          children: [
            {
              id: 'maternity-ward-round',
              title: '产妇查房',
              icon: icons.User,
              path: '/ward-round-management/maternity',
              permissions: ['cus.wardround.view'],
            },
            {
              id: 'newborn-ward-round',
              title: '新生儿查房',
              icon: icons.Star,
              path: '/ward-round-management/newborn',
              permissions: ['cus.wardround.view'],
            },
          ],
        },
      ],
    },
    {
      id: 'organization-group',
      title: '机构管理',
      isGroup: true,
      children: [
        {
          id: 'staff-scheduling',
          title: '人员与排班管理',
          icon: icons.Calendar,
          path: '/staff-scheduling',
          children: [
            {
              id: 'staff-info',
              title: '员工信息管理',
              icon: icons.User,
              path: '/staff-scheduling/staff',
              permissions: ['ogm.staff_schedule.view'],
            },
            {
              id: 'scheduling',
              title: '排班管理',
              icon: icons.CircleCheck,
              path: '/staff-scheduling/scheduling',
              permissions: ['ogm.staff_schedule.view'],
            },
          ],
        },
        {
          id: 'duty-handover',
          title: '值班与交接班管理',
          icon: icons.Clock,
          path: '/duty-handover',
          children: [
            {
              id: 'duty-info',
              title: '值班信息查看',
              icon: icons.View,
              path: '/duty-handover/duty',
              permissions: ['ogm.duty_shift.view'],
            },
            {
              id: 'handover',
              title: '交接班报告',
              icon: icons.Switch,
              path: '/duty-handover/handover',
              permissions: ['ogm.duty_shift.view'],
            },
            {
              id: 'handover-management',
              title: '交接班报告管理',
              icon: icons.Management,
              path: '/duty-handover/management',
              permissions: ['ogm.duty_shift.edit'],
            },
          ],
        },
        {
          id: 'billing',
          title: '收费管理',
          icon: icons.CreditCard,
          path: '/billing',
          children: [
            {
              id: 'billing-packages',
              title: '套餐价格管理',
              icon: icons.Money,
              path: '/billing/packages',
              permissions: ['ogm.charge.view'],
            },
            {
              id: 'billing-statements',
              title: '结算单管理',
              icon: icons.Document,
              path: '/billing/statements',
              permissions: ['ogm.charge.view'],
            },
            {
              id: 'billing-statistics',
              title: '财务统计',
              icon: icons.TrendCharts,
              path: '/billing/statistics',
              permissions: ['ogm.charge.view'],
            },
          ],
        },
        {
          id: 'infection-control',
          title: '院感与环境管理',
          icon: icons.CircleCheck,
          path: '/infection-control',
          permissions: ['ogm.infection_control_environment.view'],
          children: [],
        },
        {
          id: 'customer-feedback',
          title: '客户反馈/满意度管理',
          icon: icons.Star,
          path: '/customer-feedback',
          permissions: ['ogm.customer.feedback.view'],
          children: [],
        },
        {
          id: 'equipment-management',
          title: '设备管理',
          icon: icons.Tools,
          path: '/equipment-management',
          permissions: ['ogm.equipment.view'],
          children: [],
        },
        {
          id: 'report-export',
          title: '报表与导出中心',
          icon: icons.Document,
          path: '/report-export',
          permissions: ['ogm.report_export_center.view'],
          children: [],
        },
      ],
    },
    {
      id: 'system-group',
      title: '后台管理',
      isGroup: true,
      children: [
        {
          id: 'role-permission',
          title: '角色权限管理',
          icon: icons.Lock,
          path: '/system-management/roles',
          permissions: ['sys.backend.management.view'],
        },
        {
          id: 'audit-log',
          title: '审计日志',
          icon: icons.TrendCharts,
          path: '/system-management/audit',
          permissions: ['sys.backend.management.view'],
        },
        {
          id: 'his-config',
          title: 'HIS对接配置',
          icon: icons.Coin,
          path: '/system-management/his-config',
          permissions: ['sys.backend.management.view'],
        },
        {
          id: 'wechat-app',
          title: '微信小程序',
          icon: icons.Iphone,
          path: '/system-management/wechat-app',
          permissions: ['sys.backend.management.view'],
        },
      ],
    },
  ])

  // 根据用户权限过滤菜单
  const filteredMenus = computed(() => {
    const authStore = useAuthStore()

    if (!authStore.isLoggedIn) return []

    const filterMenu = (menus) => {
      const result = []

      for (const menu of menus) {
        // 检查权限访问
        const hasPermissionAccess = (menuItem) => {
          // 如果没有permissions字段或permissions为空数组，表示不需要权限
          if (!menuItem.permissions || menuItem.permissions.length === 0) {
            return true
          }
          // 检查用户是否拥有任一权限
          return authStore.hasAnyPermission(menuItem.permissions)
        }

        // 如果菜单有子菜单，检查是否有子菜单可访问
        let hasAccessibleChildren = false
        let filteredChildren = []
        if (menu.children && menu.children.length > 0) {
          filteredChildren = filterMenu(menu.children)
          hasAccessibleChildren = filteredChildren.length > 0
        }

        // 对于分组菜单，只要有可访问的子菜单就显示
        // 对于有子菜单的父菜单，只要有可访问的子菜单就显示
        // 对于没有子菜单的菜单，检查权限
        const shouldShowMenu = menu.isGroup
          ? hasAccessibleChildren
          : menu.children && menu.children.length > 0
            ? hasAccessibleChildren
            : hasPermissionAccess(menu)

        if (shouldShowMenu) {
          // 创建新的菜单对象
          const newMenu = {
            id: menu.id,
            title: menu.title,
            path: menu.path,
            permissions: menu.permissions,
            isGroup: menu.isGroup,
            children: filteredChildren,
          }

          // 只有非分组菜单才添加icon
          if (!menu.isGroup && menu.icon) {
            newMenu.icon = menu.icon
          }

          result.push(newMenu)
        }
      }

      return result
    }

    return filterMenu(allMenus.value)
  })

  // 获取指定路径的菜单权限要求
  const getMenuPermissions = (path) => {
    const findMenuByPath = (menus, targetPath) => {
      for (const menu of menus) {
        // 如果是分组菜单，在其子菜单中查找
        if (menu.isGroup && menu.children) {
          const found = findMenuByPath(menu.children, targetPath)
          if (found) return found
        } else {
          // 普通菜单项或非分组菜单
          if (menu.path === targetPath) {
            return menu
          }
          if (menu.children && menu.children.length > 0) {
            const found = findMenuByPath(menu.children, targetPath)
            if (found) return found
          }
        }
      }
      return null
    }

    const menu = findMenuByPath(allMenus.value, path)
    return menu?.permissions || []
  }

  // 检查用户是否有访问指定路径的权限
  const canAccessPath = (path) => {
    const authStore = useAuthStore()
    const requiredPermissions = getMenuPermissions(path)

    // 特殊处理 dashboard 路径
    if (path === '/dashboard') {
      return authStore.isLoggedIn
    }

    return authStore.hasAnyPermission(requiredPermissions)
  }

  // 切换菜单折叠状态
  const toggleCollapse = () => {
    collapsed.value = !collapsed.value
  }

  // 设置菜单折叠状态
  const setCollapse = (value) => {
    collapsed.value = value
  }

  return {
    collapsed,
    allMenus,
    filteredMenus,
    getMenuPermissions,
    canAccessPath,
    toggleCollapse,
    setCollapse,
  }
})
