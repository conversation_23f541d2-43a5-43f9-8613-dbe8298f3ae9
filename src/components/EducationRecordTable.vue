<template>
  <div class="record-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <List />
          </el-icon>
          宣教活动记录
        </h3>
        <div class="text-sm text-gray-600">共 {{ data.length }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="paginatedData"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column type="index" label="序号" min-width="80" fixed="left" />

      <el-table-column prop="topic" label="宣教主题" min-width="180">
        <template #default="{ row }">
          <span class="font-medium text-gray-800">{{ row.content_title }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="target" label="宣教对象" min-width="120">
        <template #default="{ row }">
          <span class="text-blue-600 font-medium">{{ row.maternity_name }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="method" label="宣教方式" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getMethodType(row.method)" size="small" effect="plain">
            {{ row.way_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="datetime" label="时间" min-width="160">
        <template #default="{ row }">
          <div class="text-gray-600 text-sm">
            <div>{{ formatDate(row.edu_time) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.edu_time) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="executor" label="执行人" min-width="100">
        <template #default="{ row }">
          <span class="text-gray-700">{{ row.executor }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="location" label="地点" min-width="120">
        <template #default="{ row }">
          <span class="text-gray-600 text-sm">{{ row.edu_place }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="240" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="info"
              size="small"
              @click="$emit('view', row)"
              class="bg-gray-500 hover:bg-gray-600 border-gray-500 hover:border-gray-600"
            >
              查看
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="$emit('edit', row)"
              class="bg-blue-500 hover:bg-blue-600 border-blue-500 hover:border-blue-600"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="$emit('delete', row)"
              class="bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="pagination.total_count"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElTable, ElTableColumn, ElTag, ElButton, ElPagination, ElIcon } from 'element-plus'
import { List } from '@element-plus/icons-vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },

  pagination: {
    type: Object,
    default: () => ({
      page: 1,
      page_size: 10,
      total_count: 0,
      total_page: 0,
    }),
  },
})

const emit = defineEmits(['view', 'edit', 'delete', 'size-change', 'current-change'])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 分页数据计算
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return props.data.slice(start, end)
})

const getMethodType = (method) => {
  const methodMap = {
    一对一指导: 'success',
    '一对一指导/演示': 'success',
    '讲座/资料发放': 'warning',
    小组讨论: 'info',
    视频播放: 'primary',
  }
  return methodMap[method] || 'info'
}

const formatDate = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime.replace(' ', 'T'))
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  })
}

const formatTime = (datetime) => {
  if (!datetime) return ''
  const date = new Date(datetime.replace(' ', 'T'))
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  emit('size-change', size)
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('current-change', page)
}
</script>

<style scoped>
.record-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.record-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table th) {
  padding: 12px 0;
}

:deep(.el-table .el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  padding: 4px 12px;
  font-size: 12px;
  min-width: auto;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
