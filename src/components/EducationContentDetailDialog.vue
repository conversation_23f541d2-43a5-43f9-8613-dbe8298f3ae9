<template>
  <el-dialog
    v-model="visible"
    title="健康宣教内容详情"
    width="800px"
    align-center
    :close-on-click-modal="false"
    class="content-detail-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <div v-if="contentData" class="content-detail">
        <!-- 基本信息 -->
        <div class="detail-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="info-item">
              <label>标题：</label>
              <span>{{ contentData.title }}</span>
            </div>
            <div class="info-item">
              <label>分类：</label>
              <span>{{ contentData.category }}</span>
            </div>
            <div class="info-item">
              <label>关键词：</label>
              <span>{{ contentData.keywords }}</span>
            </div>
            <div class="info-item">
              <label>格式：</label>
              <div class="format-display">
                <el-icon class="format-icon" :class="getFormatIconClass(contentData.format)">
                  <component :is="getFormatIcon(contentData.format)" />
                </el-icon>
                <span class="format-text">{{ getFormatLabel(contentData.format) }}</span>
              </div>
            </div>
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ contentData.created_at }}</span>
            </div>
            <div class="info-item">
              <label>创建人：</label>
              <span>{{ contentData.creator_name }}</span>
            </div>
          </div>
        </div>

        <!-- 内容详情 -->
        <div class="detail-section mb-6">
          <h4 class="section-title">内容详情</h4>
          <div class="content-info">
            <div class="info-item mb-4">
              <label>内容摘要：</label>
              <div class="content-text">{{ contentData.summary }}</div>
            </div>
            <div class="info-item">
              <label>详细内容：</label>
              <div class="content-text">{{ contentData.content }}</div>
            </div>
          </div>
        </div>

        <!-- 附件信息 -->
        <div class="detail-section mb-6" v-if="contentData.attachment">
          <h4 class="section-title">相关附件</h4>
          <div class="attachment-info">
            <div class="attachment-item">
              <el-link :href="contentData.attachment" target="_blank" type="primary">
                <el-icon class="mr-1"><Document /></el-icon>
                查看附件
              </el-link>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit">
          编辑内容
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  Document,
  Picture,
  VideoPlay,
  Headset,
  Files,
  ChatLineSquare
} from '@element-plus/icons-vue'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  contentData: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'edit'])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 格式相关方法
const getFormatType = (format) => {
  const formatMap = {
    TEXT: 'info',
    DOCUMENT: 'primary',
    VIDEO: 'warning',
    IMAGE: 'success',
    AUDIO: 'danger',
    PPT: 'warning',
    OTHER: 'info',
  }
  return formatMap[format] || 'info'
}

const getFormatLabel = (format) => {
  const formatMap = {
    TEXT: '文字',
    DOCUMENT: '文档',
    VIDEO: '视频',
    IMAGE: '图片',
    AUDIO: '音频',
    PPT: 'PPT',
    OTHER: '其他',
  }
  return formatMap[format] || format
}

// 获取格式图标
const getFormatIcon = (format) => {
  const iconMap = {
    TEXT: ChatLineSquare,
    DOCUMENT: Document,
    VIDEO: VideoPlay,
    IMAGE: Picture,
    AUDIO: Headset,
    PPT: Files,
    OTHER: Document,
  }
  return iconMap[format] || Document
}

// 获取格式图标样式类
const getFormatIconClass = (format) => {
  const classMap = {
    TEXT: 'text-blue-500',
    DOCUMENT: 'text-green-500',
    VIDEO: 'text-orange-500',
    IMAGE: 'text-purple-500',
    AUDIO: 'text-red-500',
    PPT: 'text-yellow-500',
    OTHER: 'text-gray-500',
  }
  return classMap[format] || 'text-gray-500'
}

// 编辑处理
const handleEdit = () => {
  emit('edit', props.contentData)
  visible.value = false
}
</script>

<style scoped>
.detail-section {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #f3f4f6;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.info-item label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  margin-right: 12px;
}

.info-item span {
  color: #1f2937;
  flex: 1;
}

.content-text {
  color: #1f2937;
  line-height: 1.6;
  white-space: pre-wrap;
  background-color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  flex: 1;
}

.attachment-item {
  padding: 12px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.format-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  width: fit-content;
}

.format-icon {
  font-size: 16px;
}

.format-text {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}
</style>
