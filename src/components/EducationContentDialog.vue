<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="content-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="content-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">内容基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="主题/标题" prop="title">
              <el-input v-model="formData.title" placeholder="例如：新生儿抚触技巧" />
            </el-form-item>
            <el-form-item label="分类" prop="category">
              <el-input v-model="formData.category" placeholder="例如：产后护理、新生儿护理" />
            </el-form-item>
            <el-form-item label="关键词" prop="keywords">
              <el-input v-model="formData.keywords" placeholder="用逗号分隔关键词" />
            </el-form-item>
            <el-form-item label="格式" prop="format">
              <el-select v-model="formData.format" placeholder="请选择格式" class="w-full">
                <el-option label="文字" value="TEXT" />
                <el-option label="文档" value="DOCUMENT" />
                <el-option label="视频" value="VIDEO" />
                <el-option label="图片" value="IMAGE" />
                <el-option label="音频" value="AUDIO" />
                <el-option label="PPT" value="PPT" />
                <el-option label="其他" value="OTHER" />
              </el-select>
            </el-form-item>
            <el-form-item label="适用人群">
              <el-input
                v-model="formData.targetAudience"
                placeholder="例如：所有产妇、剖腹产产妇等"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 内容详情 -->
        <div class="form-section mb-6">
          <h4 class="section-title">详细内容</h4>
          <el-form-item label="内容摘要" prop="summary">
            <el-input
              v-model="formData.summary"
              type="textarea"
              :rows="3"
              placeholder="简述内容要点"
            />
          </el-form-item>
          <el-form-item label="详细内容" prop="content">
            <el-input
              v-model="formData.content"
              type="textarea"
              :rows="8"
              placeholder="输入详细的宣教内容..."
            />
          </el-form-item>
        </div>

        <!-- 附件上传 -->
        <div class="form-section mb-6">
          <h4 class="section-title">相关附件</h4>
          <el-form-item label="上传附件">
            <!-- 文件预览区域 -->
            <div v-if="selectedFile || formData.attachment_url" class="file-preview-container mb-4">
              <!-- 图片预览 -->
              <div v-if="isImageFile(selectedFile || formData.attachment_url)" class="image-preview">
                <el-image
                  :src="getFilePreviewUrl()"
                  fit="cover"
                  class="preview-image"
                  :preview-src-list="[getFilePreviewUrl()]"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>图片加载失败</span>
                    </div>
                  </template>
                </el-image>
                <div class="file-actions">
                  <el-button size="small" @click="handleReupload">重新上传</el-button>
                  <el-button size="small" type="danger" @click="handleRemoveFile">删除</el-button>
                </div>
              </div>

              <!-- 非图片文件显示 -->
              <div v-else class="file-info">
                <div class="file-main-content">
                  <div class="file-icon-container">
                    <el-icon class="file-icon" :class="getFileIconClass()">
                      <component :is="getFileIcon()" />
                    </el-icon>
                    <div class="file-details">
                      <div class="file-name">{{ getFileName() }}</div>
                      <div class="file-size" v-if="selectedFile">{{ formatFileSize(selectedFile.size) }}</div>
                    </div>
                  </div>
                </div>
                <div class="file-actions">
                  <el-button size="small" @click="handleReupload">重新上传</el-button>
                  <el-button size="small" type="danger" @click="handleRemoveFile">删除</el-button>
                </div>
              </div>
            </div>

            <!-- 上传区域 -->
            <div class="upload-container" v-show="!selectedFile && !formData.attachment_url">
              <el-upload
                ref="uploadRef"
                :auto-upload="false"
                :show-file-list="false"
                :limit="1"
                :on-change="handleFileChange"
                :before-upload="beforeUpload"
                accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.ppt,.pptx,.mp4,.mp3,.wav,.flac,.aac,.m4a,.txt"
                drag
                class="full-width-upload"
              >
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  <em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持上传图片、文档、音视频文件等
                  </div>
                </template>
              </el-upload>
            </div>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="saveDraft" :loading="saving">保存草稿</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '提交内容' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  UploadFilled,
  Document,
  Picture,
  VideoPlay,
  Headset,
  Files,
  Folder
} from '@element-plus/icons-vue'
import { post, put } from '@/utils/request.js'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  contentData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const uploadRef = ref()
const saving = ref(false)
const selectedFile = ref(null)

// 表单数据
const formData = reactive({
  rid: '',
  title: '',
  category: '',
  keywords: '',
  format: '',
  summary: '',
  content: '',
  attachment: '',
  attachment_url: '',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新增宣教内容' : '编辑宣教内容'
})

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入主题/标题', trigger: 'blur' }],
  category: [{ required: true, message: '请输入分类', trigger: 'blur' }],
  format: [{ required: true, message: '请选择格式', trigger: 'change' }],
  summary: [{ required: true, message: '请输入内容摘要', trigger: 'blur' }],
  content: [{ required: true, message: '请输入详细内容', trigger: 'blur' }],
}

// 监听内容数据变化
watch(
  () => props.contentData,
  (newData) => {
    if (newData && props.mode === 'edit') {
      // 编辑模式，填充数据
      Object.assign(formData, {
        rid: newData.rid,
        title: newData.title || '',
        category: newData.category || '',
        keywords: newData.keywords || '',
        format: newData.format || '',
        summary: newData.summary || '',
        content: newData.content || '',
        attachment: '',
        attachment_url: newData.attachment || '',
      })
    }
  },
  { immediate: true },
)

// 方法
const handleOpen = () => {
  if (props.mode === 'add') {
    resetForm()
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    rid: '',
    title: '',
    category: '',
    keywords: '',
    format: '',
    summary: '',
    content: '',
    attachment: '',
    attachment_url: '',
  })
  selectedFile.value = null
  uploadRef.value?.clearFiles()
  formRef.value?.clearValidate()
}

// 文件处理方法
const handleFileChange = (file) => {
  selectedFile.value = file.raw || file
  // 清除已有附件URL，因为选择了新文件
  if (selectedFile.value) {
    formData.attachment_url = ''
  }
}

const handleRemoveFile = () => {
  selectedFile.value = null
  formData.attachment_url = ''
  uploadRef.value?.clearFiles()
}

const handleReupload = () => {
  uploadRef.value?.clearFiles()
  selectedFile.value = null
  formData.attachment_url = ''
}

const beforeUpload = (file) => {
  const isValidSize = file.size / 1024 / 1024 < 50 // 50MB
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过50MB!')
    return false
  }
  return true
}

// 文件类型判断和处理
const isImageFile = (file) => {
  if (!file) return false

  if (typeof file === 'string') {
    // URL字符串
    return /\.(jpg|jpeg|png|gif|webp)$/i.test(file)
  } else {
    // File对象
    return file.type?.startsWith('image/')
  }
}

const getFilePreviewUrl = () => {
  if (selectedFile.value) {
    return URL.createObjectURL(selectedFile.value)
  }
  return formData.attachment_url
}

const getFileName = () => {
  if (selectedFile.value) {
    return selectedFile.value.name
  }
  if (formData.attachment_url) {
    const url = formData.attachment_url
    const fileName = url.substring(url.lastIndexOf('/') + 1)
    return fileName.split('?')[0] || '附件文件'
  }
  return '未知文件'
}

const getFileIcon = () => {
  const fileName = getFileName().toLowerCase()

  if (/\.(jpg|jpeg|png|gif|webp)$/i.test(fileName)) {
    return Picture
  } else if (/\.(mp4|avi|mov|wmv|flv)$/i.test(fileName)) {
    return VideoPlay
  } else if (/\.(mp3|wav|flac|aac)$/i.test(fileName)) {
    return Headset
  } else if (/\.(pdf|doc|docx|txt)$/i.test(fileName)) {
    return Document
  } else if (/\.(ppt|pptx)$/i.test(fileName)) {
    return Files
  } else {
    return Folder
  }
}

const getFileIconClass = () => {
  const fileName = getFileName().toLowerCase()

  if (/\.(jpg|jpeg|png|gif|webp)$/i.test(fileName)) {
    return 'icon-image'
  } else if (/\.(mp4|avi|mov|wmv|flv)$/i.test(fileName)) {
    return 'icon-video'
  } else if (/\.(mp3|wav|flac|aac)$/i.test(fileName)) {
    return 'icon-audio'
  } else if (/\.(pdf)$/i.test(fileName)) {
    return 'icon-pdf'
  } else if (/\.(doc|docx)$/i.test(fileName)) {
    return 'icon-word'
  } else if (/\.(ppt|pptx)$/i.test(fileName)) {
    return 'icon-ppt'
  } else {
    return 'icon-default'
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const saveDraft = () => {
  saving.value = true

  setTimeout(() => {
    ElMessage.success('草稿保存成功！')
    saving.value = false
  }, 500)
}

const handleSave = async () => {
  try {
    await formRef.value.validate()

    saving.value = true

    // 准备提交数据
    const submitData = new FormData()
    submitData.append('title', formData.title)
    submitData.append('category', formData.category)
    submitData.append('keywords', formData.keywords)
    submitData.append('format', formData.format)
    submitData.append('summary', formData.summary)
    submitData.append('content', formData.content)

    // 如果有选择的新文件，添加到FormData
    if (selectedFile.value) {
      submitData.append('attachment', selectedFile.value)
    }

    let response
    if (props.mode === 'add') {
      response = await post('customer-service/health-education/content/create/', submitData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      ElMessage.success('健康宣教内容创建成功')
    } else {
      response = await put(`customer-service/health-education/content/update/${formData.rid}/`, submitData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      ElMessage.success('健康宣教内容更新成功')
    }

    emit('save', response)
    visible.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

/* 文件预览和上传样式 */
.file-preview-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background-color: #f9fafb;
  width: 100%;
}

.upload-container {
  width: 100%;
}

.full-width-upload {
  width: 100%;
}

.full-width-upload :deep(.el-upload) {
  width: 100%;
}

.full-width-upload :deep(.el-upload-dragger) {
  width: 100%;
}

.image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.preview-image {
  width: 200px;
  height: 150px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: #9ca3af;
  font-size: 14px;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.file-main-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.file-icon {
  font-size: 32px;
}

.icon-image { color: #10b981; }
.icon-video { color: #f59e0b; }
.icon-audio { color: #8b5cf6; }
.icon-pdf { color: #ef4444; }
.icon-word { color: #3b82f6; }
.icon-ppt { color: #f97316; }
.icon-default { color: #6b7280; }

.file-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.file-name {
  font-weight: 500;
  color: #1f2937;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
}

.file-size {
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  background-color: #fafafa;
  text-align: center;
  padding: 40px;
  transition: all 0.2s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #ec4899;
  background-color: #fdf2f8;
}
</style>
