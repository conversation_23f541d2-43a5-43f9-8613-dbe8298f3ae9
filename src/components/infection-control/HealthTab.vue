<template>
  <div class="health-tab-container">
    <!-- 介绍说明 -->
    <div class="info-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <el-icon class="mr-2 text-pink-500">
          <User />
        </el-icon>
        健康档案与防控培训
      </h3>
      <div class="text-gray-600 space-y-2">
        <p><strong>此模块整合健康档案、院感防控培训、健康证管理等内容。</strong></p>
        <p>
          <strong>关键信息包括:</strong> 员工姓名, 健康证状态及有效期,
          体检与培训记录，院感防控知识考核等。
        </p>
        <p class="mt-4">
          <el-link href="#" type="primary" @click="handleJumpToStaffInfo" class="text-pink-500">
            点击此处跳转至员工信息管理查看详细健康档案
          </el-link>
        </p>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
      <div class="flex justify-end items-center">
        <el-button
          type="success"
          @click="handleExportHealthList"
          class="bg-green-500 hover:bg-green-600 border-green-500"
        >
          <el-icon class="mr-2">
            <Download />
          </el-icon>
          导出健康档案列表 (PDF)
        </el-button>
      </div>
    </div>

    <!-- 健康档案列表 -->
    <div class="health-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
      <!-- 表格标题 -->
      <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <el-icon class="mr-2 text-pink-500">
              <UserFilled />
            </el-icon>
            员工健康档案
          </h3>
          <div class="text-sm text-gray-600">共 {{ healthRecords.length }} 名员工</div>
        </div>
      </div>

      <!-- 表格内容 -->
      <el-table
        :data="healthRecords"
        v-loading="loading"
        stripe
        class="w-full"
        :header-cell-style="{
          backgroundColor: '#f9fafb',
          color: '#374151',
          fontWeight: '600',
          borderBottom: '1px solid #e5e7eb',
          textAlign: 'center',
        }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column prop="name" label="员工姓名" width="100" fixed="left">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <el-icon class="mr-2 text-blue-500">
                <User />
              </el-icon>
              <span class="font-medium">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="position" label="岗位" width="120" />

        <el-table-column prop="healthCertNo" label="健康证号" width="120" />

        <el-table-column label="健康证有效期" width="130">
          <template #default="{ row }">
            <div class="text-sm">
              <div :class="getValidityClass(row.healthCertExpiry)">
                {{ row.healthCertExpiry }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="lastCheckupDate" label="最近体检日期" width="130" />

        <el-table-column prop="checkupResult" label="体检结果摘要" width="120">
          <template #default="{ row }">
            <el-tag :type="row.checkupResult === '合格' ? 'success' : 'warning'" size="small">
              {{ row.checkupResult }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="infectionTraining"
          label="院感培训"
          min-width="200"
          show-overflow-tooltip
        />

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              查看详细
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElButton, ElIcon, ElTable, ElTableColumn, ElTag, ElLink, ElMessage } from 'element-plus'
import { User, UserFilled, Download } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)

// 健康档案列表
const healthRecords = ref([
  {
    id: '1',
    name: '保洁员A',
    position: '客房清洁',
    healthCertNo: 'HC00123',
    healthCertExpiry: '2025-10-31',
    lastCheckupDate: '2025-04-15',
    checkupResult: '合格',
    infectionTraining: '2025-03-01 院感知识考核合格',
  },
  {
    id: '2',
    name: '保洁员B',
    position: '公共区域清洁',
    healthCertNo: 'HC00456',
    healthCertExpiry: '2026-01-20',
    lastCheckupDate: '2025-01-10',
    checkupResult: '合格',
    infectionTraining: '2025-03-01 院感知识考核合格',
  },
  {
    id: '3',
    name: '护士长A',
    position: '护理管理',
    healthCertNo: 'HC00789',
    healthCertExpiry: '2025-08-15',
    lastCheckupDate: '2025-02-20',
    checkupResult: '合格',
    infectionTraining: '2025-02-15 院感管理培训优秀',
  },
])

// 方法
const handleJumpToStaffInfo = () => {
  ElMessage.info('跳转到员工信息管理模块...')
}

const handleExportHealthList = () => {
  ElMessage.info('健康档案列表导出功能开发中...')
}

const handleViewDetail = (record) => {
  ElMessage.info(`查看 ${record.name} 的详细健康档案`)
}

// 获取有效期样式类
const getValidityClass = (expiry) => {
  if (!expiry) return ''

  const expiryDate = new Date(expiry)
  const today = new Date()
  const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24))

  if (daysUntilExpiry < 0) {
    return 'text-red-600 font-medium' // 已过期
  } else if (daysUntilExpiry < 30) {
    return 'text-orange-600 font-medium' // 即将过期
  } else {
    return 'text-green-600' // 正常
  }
}
</script>

<style scoped>
.health-tab-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-section,
.action-section {
  transition: all 0.3s ease;
}

.info-section:hover,
.action-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}
</style>
