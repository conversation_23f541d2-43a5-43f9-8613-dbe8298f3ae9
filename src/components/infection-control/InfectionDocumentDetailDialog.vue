<template>
  <el-dialog
    v-model="visible"
    title="制度文档详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 文档基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">文档信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>文档ID：</label>
              <span class="font-mono text-gray-600">{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <label>制度名称：</label>
              <span class="font-medium">{{ detailData.name }}</span>
            </div>
            <div class="detail-item">
              <label>版本号：</label>
              <el-tag type="info" size="small">{{ detailData.version }}</el-tag>
            </div>
            <div class="detail-item">
              <label>发布日期：</label>
              <span>{{ detailData.publish_date }}</span>
            </div>
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ detailData.creator_name }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ detailData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ detailData.updated_at }}</span>
            </div>
          </div>
        </div>

        <!-- 文档描述 -->
        <div class="detail-section" v-if="detailData.description">
          <h3 class="section-title">制度描述</h3>
          <div class="description-content p-4 bg-gray-50 rounded-lg">
            {{ detailData.description }}
          </div>
        </div>

        <!-- 文档文件 -->
        <div class="detail-section" v-if="detailData.file_url">
          <h3 class="section-title">制度文件</h3>
          <FileDisplayList :file-list="detailData.file_url" max-height="300px" />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          @click="handleEdit"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑文档
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import FileDisplayList from '@/components/FileDisplayList.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(
      `organizational-management/infection-document/detail/${props.itemId}/`,
    )
    detailData.value = response
  } catch (error) {
    console.error('获取制度文档详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 编辑文档
const handleEdit = () => {
  emit('edit', detailData.value)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.description-content {
  color: #374151;
  line-height: 1.625;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
