<template>
  <div class="organization-tab-container">
    <!-- 介绍说明 -->
    <div class="info-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <InfoFilled />
          </el-icon>
          院感管理组织与制度
        </h3>
        <el-button
          type="primary"
          @click="handleUploadDoc"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-2">
            <Upload />
          </el-icon>
          新增院感管理制度
        </el-button>
      </div>
      <ul class="space-y-2 text-gray-600">
        <li class="flex items-center">
          <el-icon class="mr-2 text-green-500"><Check /></el-icon>
          已建立院感管理小组，明确职责分工
        </li>
        <li class="flex items-center">
          <el-icon class="mr-2 text-green-500"><Check /></el-icon>
          制定院感管理相关制度、流程与SOP（如消毒隔离、手卫生、健康监测等）
        </li>
        <li class="flex items-center">
          <el-icon class="mr-2 text-green-500"><Check /></el-icon>
          定期组织院感会议，持续改进管理
        </li>
      </ul>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 制度文档列表 -->
    <InfectionDocumentTable
      ref="tableRef"
      :filters="filters"
      @view="handleViewDocument"
      @edit="handleEditDocument"
      @row-click="handleRowClick"
    />

    <!-- 文档上传弹窗 -->
    <InfectionDocumentFormDialog
      v-model="uploadVisible"
      :item-id="currentDocument?.rid"
      @success="handleSaveDocument"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { InfoFilled, Check, Upload } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import InfectionDocumentTable from './InfectionDocumentTable.vue'
import InfectionDocumentFormDialog from './InfectionDocumentFormDialog.vue'

// 响应式数据
const uploadVisible = ref(false)
const currentDocument = ref(null)
const tableRef = ref(null)

// 过滤器配置
const filterFields = [
  {
    key: 'sk',
    label: '关键字',
    type: 'input',
    placeholder: '输入制度名称或版本号',
  },
]

// 过滤器数据
const filters = reactive({
  sk: '',
})

// 方法
const handleUploadDoc = () => {
  currentDocument.value = null
  uploadVisible.value = true
}

const handleViewDocument = (doc) => {
  console.log('查看文档:', doc)
}

const handleEditDocument = (doc) => {
  // 设置要编辑的文档
  currentDocument.value = doc
  uploadVisible.value = true
}

const handleRowClick = (row) => {
  console.log('点击行:', row)
  // 可以在这里处理行点击事件，比如展开详情等
}

const handleSaveDocument = () => {
  uploadVisible.value = false

  // 刷新表格数据
  if (tableRef.value) {
    tableRef.value.refresh()
  }
}

const handleSearch = () => {
  // 重置分页并触发搜索
  if (tableRef.value) {
    tableRef.value.resetPagination()
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.organization-tab-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-section,
.action-section {
  transition: all 0.3s ease;
}

.info-section:hover,
.action-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}
</style>
