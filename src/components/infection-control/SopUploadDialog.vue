<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    align-center
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="sop-upload-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px" class="sop-form">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="文档标题" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="例如：客房终末消毒规范"
                :disabled="mode === 'view'"
              />
            </el-form-item>
            <el-form-item label="版本号" prop="version">
              <el-input
                v-model="formData.version"
                placeholder="例如：v1.2"
                :disabled="mode === 'view'"
              />
            </el-form-item>
            <el-form-item label="适用范围" prop="scope">
              <el-input
                v-model="formData.scope"
                placeholder="例如：所有客房、特定区域"
                :disabled="mode === 'view'"
              />
            </el-form-item>
            <el-form-item label="发布日期" prop="publishDate">
              <el-date-picker
                v-model="formData.publishDate"
                type="date"
                placeholder="选择发布日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled="mode === 'view'"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 文件信息 -->
        <div class="form-section mb-6" v-if="mode !== 'view'">
          <h4 class="section-title">文件上传</h4>
          <el-form-item label="选择文件" prop="file">
            <FileUpload
              v-model="uploadedFile"
              :file-types="['pdf', 'doc', 'docx']"
              :max-size="10"
              upload-class="w-full"
              @change="handleFileChange"
            />
          </el-form-item>
        </div>

        <!-- 文件信息显示（查看模式） -->
        <div class="form-section mb-6" v-if="mode === 'view'">
          <h4 class="section-title">文件信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="文件名">
              <el-input v-model="formData.fileName" disabled />
            </el-form-item>
            <el-form-item label="文件大小">
              <el-input v-model="formData.fileSize" disabled />
            </el-form-item>
          </div>
        </div>

        <!-- 描述信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">描述信息</h4>
          <el-form-item label="文档描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请描述该SOP文档的主要内容和使用场景..."
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </div>

        <!-- 状态设置 -->
        <div class="form-section mb-6">
          <h4 class="section-title">状态设置</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="文档状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择文档状态"
                class="w-full"
                :disabled="mode === 'view'"
              >
                <el-option label="生效中" value="active" />
                <el-option label="草稿" value="draft" />
                <el-option label="已停用" value="inactive" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建人" prop="creator">
              <el-input
                v-model="formData.creator"
                placeholder="请输入创建人姓名"
                :disabled="mode === 'view'"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ mode === 'view' ? '关闭' : '取消' }}</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '上传文档' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElDatePicker,
  ElMessage,
} from 'element-plus'
import FileUpload from '../FileUpload.vue'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  sopData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit' | 'view'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const uploadedFile = ref(null)

// 表单数据
const formData = reactive({
  id: '',
  title: '',
  version: '',
  scope: '',
  publishDate: '',
  description: '',
  status: 'active',
  creator: '',
  fileName: '',
  fileSize: '',
  file: null,
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  const titleMap = {
    add: '上传新SOP文档',
    edit: '编辑SOP文档信息',
    view: '查看SOP文档信息',
  }
  return titleMap[props.mode] || 'SOP文档'
})

// 表单验证规则
const rules = computed(() => {
  if (props.mode === 'view') return {}

  return {
    title: [{ required: true, message: '请输入文档标题', trigger: 'blur' }],
    version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
    scope: [{ required: true, message: '请输入适用范围', trigger: 'blur' }],
    publishDate: [{ required: true, message: '请选择发布日期', trigger: 'change' }],
    status: [{ required: true, message: '请选择文档状态', trigger: 'change' }],
    creator: [{ required: true, message: '请输入创建人', trigger: 'blur' }],
    file: [
      {
        validator: (rule, value, callback) => {
          if (props.mode === 'add' && !uploadedFile.value) {
            callback(new Error('请选择要上传的文件'))
          } else {
            callback()
          }
        },
        trigger: 'change',
      },
    ],
  }
})

// 监听SOP数据变化
watch(
  () => props.sopData,
  (newData) => {
    if (newData) {
      // 编辑或查看模式，填充数据
      Object.assign(formData, newData)
      if (newData.fileName) {
        uploadedFile.value = {
          name: newData.fileName,
          size: newData.fileSize,
          fileSize: newData.fileSize,
          fileType: 'PDF', // 默认类型，可以根据扩展名判断
        }
      }
    }
  },
  { immediate: true },
)

// 方法
const handleOpen = () => {
  if (props.mode === 'add') {
    resetForm()
  }
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  uploadedFile.value = null
  Object.assign(formData, {
    id: '',
    title: '',
    version: '',
    scope: '',
    publishDate: '',
    description: '',
    status: 'active',
    creator: '',
    fileName: '',
    fileSize: '',
    file: null,
  })
  formRef.value?.clearValidate()
}

const handleFileChange = (file) => {
  // 文件信息已经由 FileUpload 组件处理过了
  formData.file = file.raw || file
  formData.fileName = file.name
  formData.fileSize = file.fileSize
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    // 模拟保存过程
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const saveData = {
      ...formData,
      ...(props.mode === 'add' && {
        id: 'SOP' + String(Date.now()).slice(-3).padStart(3, '0'),
        name: formData.fileName,
      }),
    }

    emit('save', saveData)
    saving.value = false
  } catch (error) {
    saving.value = false
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  color: #909399;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  color: #909399;
}

:deep(.el-select.is-disabled .el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
}
</style>
