<template>
  <div>
    <!-- 表格容器 -->
    <div
      class="infection-detection-table-container bg-white border border-gray-200 rounded-lg overflow-hidden"
    >
      <!-- 表格标题 -->
      <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <el-icon class="mr-2 text-pink-500">
              <Monitor />
            </el-icon>
            感染监测记录列表
          </h3>
          <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
        </div>
      </div>

      <!-- 表格内容 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        class="w-full"
        style="width: 100%"
        :header-cell-style="{
          backgroundColor: '#f9fafb',
          color: '#374151',
          fontWeight: '600',
          borderBottom: '1px solid #e5e7eb',
          textAlign: 'center',
        }"
        :row-style="{ cursor: 'pointer' }"
        :cell-style="{ textAlign: 'center' }"
        @row-click="handleRowClick"
      >
        <el-table-column prop="rid" label="记录ID" min-width="160" fixed="left">
          <template #default="{ row }">
            <span class="font-mono text-gray-600">{{ row.rid }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="detection_date" label="监测日期" min-width="120">
          <template #default="{ row }">
            <div class="text-sm text-gray-600">{{ row.detection_date }}</div>
          </template>
        </el-table-column>

        <el-table-column
          prop="detection_content"
          label="监测内容"
          min-width="200"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <el-icon class="mr-2 text-pink-500">
                <Monitor />
              </el-icon>
              <span class="font-medium">{{ row.detection_content }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="problem" label="发现问题" min-width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span>{{ row.problem || '--' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="report_status" label="上报状态" min-width="100">
          <template #default="{ row }">
            <el-tag :type="getReportStatusTagType(row.report_status)" size="small">
              {{ getReportStatusText(row.report_status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          prop="processing_result"
          label="处理结果"
          min-width="150"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <span>{{ row.processing_result || '--' }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="creator_name" label="创建人" min-width="100">
          <template #default="{ row }">
            <span>{{ row.creator_name }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="190" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="default"
                size="small"
                @click.stop="handleView(row)"
                class="text-pink-600 border-pink-200 hover:bg-pink-50"
              >
                查看
              </el-button>
              <el-button
                type="default"
                size="small"
                @click.stop="handleEdit(row)"
                class="text-orange-600 border-orange-200 hover:bg-orange-50"
              >
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click.stop="handleDelete(row)"
                class="text-red-600 border-red-200 hover:bg-red-50"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-end"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElButton,
  ElPagination,
  ElIcon,
  ElMessageBox,
  ElMessage,
} from 'element-plus'
import { Monitor } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'
import { getReportStatusText, getReportStatusTagType } from '@/utils/constants.js'

// 定义属性
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// 定义事件
const emit = defineEmits(['view', 'edit', 'row-click'])

// 内部状态管理
const loading = ref(false)
const tableData = ref([])
const totalCount = ref(0)

// 详情对话框相关
const detailDialogVisible = ref(false)
const selectedItemId = ref(null)

// 分页相关 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const total = computed(() => totalCount.value)

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 合并过滤条件和分页参数
    const requestParams = {
      ...props.filters,
      page: currentPage.value,
      page_size: pageSize.value,
    }

    const data = await get('organizational-management/infection-detection/list/', requestParams)
    tableData.value = data.list
    totalCount.value = data.total_count
  } catch (error) {
    console.error('获取感染监测记录列表失败:', error)
    ElMessage.error('获取感染监测记录列表失败')
    tableData.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

// 查看记录
const handleView = (record) => {
  selectedItemId.value = record.rid
  detailDialogVisible.value = true
  emit('view', record)
}

// 编辑记录
const handleEdit = (record) => {
  emit('edit', record)
}

// 行点击处理
const handleRowClick = (row) => {
  selectedItemId.value = row.rid
  detailDialogVisible.value = true
  emit('row-click', row)
}

// 删除记录
const handleDelete = async (record) => {
  try {
    await ElMessageBox.confirm(`确定要删除这条感染监测记录吗？删除后无法恢复。`, '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger',
    })

    const deleteUrl = `organizational-management/infection-detection/delete/${record.rid}/`
    await del(deleteUrl)

    ElMessage.success('删除成功')

    // 重新加载数据
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      showErrorTip(error)
    }
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadData()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadData()
}

// 重置分页到第一页（供父组件调用，比如搜索时）
const resetPagination = () => {
  currentPage.value = 1
  loadData()
}

// 刷新当前页数据
const refresh = () => {
  loadData()
}

// 暴露方法给父组件
defineExpose({
  resetPagination,
  refresh,
})

// 组件挂载后自动加载第一页数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.infection-detection-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.infection-detection-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
