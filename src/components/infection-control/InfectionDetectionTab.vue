<template>
  <div class="infection-detection-tab-container">
    <!-- 介绍说明 -->
    <div class="info-section bg-white border border-gray-200 rounded-lg p-6 mb-6">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <InfoFilled />
          </el-icon>
          感染监测与报告
        </h3>
        <el-button
          type="primary"
          @click="handleAddInfectionDetection"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          <el-icon class="mr-2">
            <Plus />
          </el-icon>
          新增监测/上报记录
        </el-button>
      </div>
      <ul class="space-y-2 text-gray-600">
        <li class="flex items-center">
          <el-icon class="mr-2 text-green-500"><Check /></el-icon>
          定期开展感染风险评估与监测，填写监测记录
        </li>
        <li class="flex items-center">
          <el-icon class="mr-2 text-green-500"><Check /></el-icon>
          发现疑似感染事件及时上报，启动应急处理流程
        </li>
        <li class="flex items-center">
          <el-icon class="mr-2 text-green-500"><Check /></el-icon>
          组织院感相关培训与应急演练
        </li>
      </ul>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 监测记录列表 -->
    <InfectionDetectionTable
      ref="tableRef"
      :filters="filters"
      @view="handleViewRecord"
      @edit="handleEditRecord"
      @row-click="handleRowClick"
    />

    <!-- 监测记录表单弹窗 -->
    <InfectionDetectionFormDialog
      v-model="formVisible"
      :item-id="currentRecord?.rid"
      @success="handleSaveRecord"
    />

    <!-- 监测记录详情弹窗 -->
    <InfectionDetectionDetailDialog
      v-model="detailVisible"
      :item-id="selectedRecordId"
      @edit="handleEditFromDetail"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElButton, ElIcon } from 'element-plus'
import { InfoFilled, Check, Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import InfectionDetectionTable from './InfectionDetectionTable.vue'
import InfectionDetectionFormDialog from './InfectionDetectionFormDialog.vue'
import InfectionDetectionDetailDialog from './InfectionDetectionDetailDialog.vue'

// 响应式数据
const formVisible = ref(false)
const detailVisible = ref(false)
const currentRecord = ref(null)
const selectedRecordId = ref(null)
const tableRef = ref(null)

// 过滤器配置
const filterFields = [
  {
    key: 'sk',
    label: '关键字',
    type: 'input',
    placeholder: '输入监测内容或问题描述',
  },
]

// 过滤器数据
const filters = reactive({
  sk: '',
})

// 方法
const handleAddInfectionDetection = () => {
  currentRecord.value = null
  formVisible.value = true
}

const handleViewRecord = (record) => {
  selectedRecordId.value = record.rid
  detailVisible.value = true
}

const handleEditRecord = (record) => {
  currentRecord.value = record
  formVisible.value = true
}

const handleRowClick = (row) => {
  selectedRecordId.value = row.rid
  detailVisible.value = true
}

const handleSaveRecord = () => {
  formVisible.value = false

  // 刷新表格数据
  if (tableRef.value) {
    tableRef.value.refresh()
  }
}

const handleEditFromDetail = (record) => {
  detailVisible.value = false
  currentRecord.value = record
  formVisible.value = true
}

const handleDetailClose = () => {
  detailVisible.value = false
  selectedRecordId.value = null
}

const handleSearch = () => {
  // 重置分页并触发搜索
  if (tableRef.value) {
    tableRef.value.resetPagination()
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.infection-detection-tab-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-section,
.action-section {
  transition: all 0.3s ease;
}

.info-section:hover,
.action-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}
</style>
