<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="infection-detection-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="infection-detection-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="监测日期" prop="detection_date">
              <el-date-picker
                v-model="formData.detection_date"
                type="date"
                placeholder="选择监测日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
            <el-form-item label="上报状态" prop="report_status">
              <el-select
                v-model="formData.report_status"
                placeholder="请选择上报状态"
                class="w-full"
              >
                <el-option
                  v-for="option in reportStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 监测内容 -->
        <div class="form-section mb-6">
          <h4 class="section-title">监测内容</h4>
          <el-form-item label="监测内容" prop="detection_content">
            <el-input
              v-model="formData.detection_content"
              type="textarea"
              :rows="3"
              placeholder="请输入监测内容，如体温异常监测、皮肤感染监测等..."
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 问题发现 -->
        <div class="form-section mb-6">
          <h4 class="section-title">问题发现</h4>
          <el-form-item label="发现问题" prop="problem">
            <el-input
              v-model="formData.problem"
              type="textarea"
              :rows="3"
              placeholder="请描述发现的问题，如无问题请输入'无'..."
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 处理结果 -->
        <div class="form-section mb-6">
          <h4 class="section-title">处理结果</h4>
          <el-form-item label="处理结果" prop="processing_result">
            <el-input
              v-model="formData.processing_result"
              type="textarea"
              :rows="3"
              placeholder="请描述处理结果和后续措施..."
              maxlength="300"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 详细描述 -->
        <div class="form-section mb-6">
          <h4 class="section-title">详细描述</h4>
          <el-form-item label="详细描述" prop="detailed_description">
            <el-input
              v-model="formData.detailed_description"
              type="textarea"
              :rows="4"
              placeholder="请输入详细描述，包括监测过程、具体情况、处理措施等..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, nextTick } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElDatePicker,
  ElSelect,
  ElOption,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'
import { REPORT_STATUS_OPTIONS } from '@/utils/constants.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const loading = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  detection_date: '',
  detection_content: '',
  problem: '',
  report_status: '',
  processing_result: '',
  detailed_description: '',
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑感染监测记录' : '新建感染监测记录'
})

// 上报状态选项
const reportStatusOptions = REPORT_STATUS_OPTIONS

// 表单验证规则
const rules = {
  detection_date: [{ required: true, message: '请选择监测日期', trigger: 'change' }],
  detection_content: [{ required: true, message: '请输入监测内容', trigger: 'blur' }],
  problem: [{ required: true, message: '请输入发现的问题', trigger: 'blur' }],
  report_status: [{ required: true, message: '请选择上报状态', trigger: 'change' }],
  processing_result: [{ required: true, message: '请输入处理结果', trigger: 'blur' }],
  detailed_description: [{ required: true, message: '请输入详细描述', trigger: 'blur' }],
}

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(() => {
      resetForm()
    }, 100)
    if (visible) {
      await scrollToTop()

      if (props.itemId) {
        // 编辑模式：获取详情数据
        await fetchRecordDetail(props.itemId)
      } else {
        // 新增模式：设置默认值
        const today = new Date()
        formData.detection_date = today.toISOString().slice(0, 10)
        formData.report_status = 'NO_NEED_REPORT'
      }
    }
  },
)

// 方法
// 滚动到顶部
const scrollToTop = async () => {
  await nextTick()
  const scrollContainer = document.querySelector('.infection-detection-dialog .max-h-\\[70vh\\]')
  if (scrollContainer) {
    scrollContainer.scrollTop = 0
  }
}

// 获取记录详情数据（用于编辑模式）
const fetchRecordDetail = async (recordId) => {
  if (!recordId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/infection-detection/detail/${recordId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(formData, processedData)
    } else {
      ElMessage.error('未获取到记录详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取感染监测记录详情失败:', error)
    ElMessage.error('获取记录详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    id: apiData.rid,
    detection_date: apiData.detection_date || '',
    detection_content: apiData.detection_content || '',
    problem: apiData.problem || '',
    report_status: apiData.report_status || '',
    processing_result: apiData.processing_result || '',
    detailed_description: apiData.detailed_description || '',
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = async () => {
  Object.assign(formData, {
    id: '',
    detection_date: '',
    detection_content: '',
    problem: '',
    report_status: '',
    processing_result: '',
    detailed_description: '',
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSave = async () => {
  if (!formRef.value) return

  try {
    // 验证表单
    await formRef.value.validate()

    saving.value = true

    // 准备提交数据
    const submitData = {
      detection_date: formData.detection_date,
      detection_content: formData.detection_content,
      problem: formData.problem,
      report_status: formData.report_status,
      processing_result: formData.processing_result,
      detailed_description: formData.detailed_description,
    }

    let response
    if (props.itemId) {
      // 编辑模式
      response = await put(
        `organizational-management/infection-detection/update/${props.itemId}/`,
        submitData,
      )
      ElMessage.success('感染监测记录更新成功')
    } else {
      // 新增模式
      response = await post('organizational-management/infection-detection/create/', submitData)
      ElMessage.success('感染监测记录创建成功')
    }

    emit('success', response)
    visible.value = false
  } catch (error) {
    if (error?.errors) {
      // 处理字段验证错误
      const firstErrorField = Object.keys(error.errors)[0]
      const firstErrorMessage = error.errors[firstErrorField][0]
      ElMessage.error(`${firstErrorField}: ${firstErrorMessage}`)
    } else {
      showErrorTip(error)
    }
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.form-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-textarea__inner) {
  border-radius: 0.375rem;
}

:deep(.el-select) {
  width: 100%;
}
</style>
