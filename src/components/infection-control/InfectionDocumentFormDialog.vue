<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="document-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="document-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="制度标题" prop="name">
              <el-input v-model="formData.name" placeholder="请输入院感管理制度标题" />
            </el-form-item>
            <el-form-item label="版本号" prop="version">
              <el-input v-model="formData.version" placeholder="如：1.0" />
            </el-form-item>
            <el-form-item label="发布日期" prop="publish_date">
              <el-date-picker
                v-model="formData.publish_date"
                type="date"
                placeholder="选择发布日期"
                class="w-full"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 制度描述 -->
        <div class="form-section mb-6">
          <h4 class="section-title">制度描述</h4>
          <el-form-item label="详细描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请输入制度的详细描述，包括主要内容、适用范围等..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 文件上传 -->
        <div class="form-section mb-6">
          <h4 class="section-title">文件上传</h4>
          <el-form-item label="制度文件" prop="file">
            <FileUpload
              v-model="formData.file"
              :urls="formData.file_url"
              :file-types="['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']"
              :max-size="10"
              :multiple="false"
              :limit="1"
              action="file/infection-management/file/upload/"
              field="infection_management_file"
              upload-text="上传制度文件"
              custom-tip-text="支持 PDF、Word、图片格式，文件大小不超过 10MB"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch, nextTick } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElInput, ElButton, ElDatePicker } from 'element-plus'
import { ElMessage } from 'element-plus'
import FileUpload from '@/components/FileUpload.vue'
import { post, put, get } from '@/utils/request.js'
import { showErrorTip } from '@/utils/utils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const loading = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  name: '',
  version: '',
  publish_date: '',
  description: '',
  file: '',
})

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑院感管理制度' : '新建院感管理制度'
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入制度标题', trigger: 'blur' }],
  version: [{ required: true, message: '请输入版本号', trigger: 'blur' }],
  publish_date: [{ required: true, message: '请选择发布日期', trigger: 'change' }],
  description: [{ required: true, message: '请输入制度描述', trigger: 'blur' }],
  file: [{ required: true, message: '请上传制度文件', trigger: 'change' }],
}

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(() => {
      resetForm()
    }, 100)
    if (visible) {
      await scrollToTop()

      if (props.itemId) {
        // 编辑模式：获取详情数据
        await fetchDocumentDetail(props.itemId)
      } else {
        // 新增模式：设置默认值
        const today = new Date()
        formData.publish_date = today.toISOString().slice(0, 10)
        formData.version = '1.0'
      }
    }
  },
)

// 方法
// 滚动到顶部
const scrollToTop = async () => {
  await nextTick()
  const scrollContainer = document.querySelector('.document-dialog .max-h-\\[70vh\\]')
  if (scrollContainer) {
    scrollContainer.scrollTop = 0
  }
}

// 获取文档详情数据（用于编辑模式）
const fetchDocumentDetail = async (documentId) => {
  if (!documentId) return

  loading.value = true
  try {
    const response = await get(`organizational-management/infection-document/detail/${documentId}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(formData, processedData)
    } else {
      ElMessage.error('未获取到制度详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取制度详情失败:', error)
    ElMessage.error('获取制度详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  // 处理日期：优先使用publish_date，如果为null则使用created_at，都没有则使用今天
  let publishDate = ''
  if (apiData.publish_date) {
    publishDate = apiData.publish_date
  } else if (apiData.created_at) {
    publishDate = apiData.created_at.slice(0, 10)
  } else {
    publishDate = new Date().toISOString().slice(0, 10)
  }

  return {
    id: apiData.id || apiData.rid,
    name: apiData.name || '',
    version: apiData.version || '',
    publish_date: publishDate,
    description: apiData.description || '',
    file: apiData.file || '',
  }
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = async () => {
  Object.assign(formData, {
    id: '',
    name: '',
    version: '',
    publish_date: '',
    description: '',
    file: '',
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSave = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (saving.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    // 构造提交数据
    const submitData = {
      name: formData.name,
      version: formData.version,
      publish_date: formData.publish_date,
      description: formData.description,
      file: formData.file,
    }

    let res
    if (!props.itemId) {
      res = await post('organizational-management/infection-document/create/', submitData)
    } else {
      res = await put(
        `organizational-management/infection-document/update/${props.itemId}/`,
        submitData,
      )
    }

    ElMessage.success(props.itemId ? '制度更新成功' : '制度创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor) {
  width: 100%;
}
</style>
