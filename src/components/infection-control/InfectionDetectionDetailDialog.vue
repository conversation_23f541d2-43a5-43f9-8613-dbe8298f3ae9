<template>
  <el-dialog
    v-model="visible"
    title="感染监测记录详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>记录ID：</label>
              <span class="font-mono text-gray-600">{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <label>监测日期：</label>
              <span>{{ detailData.detection_date }}</span>
            </div>
            <div class="detail-item">
              <label>上报状态：</label>
              <el-tag :type="getReportStatusTagType(detailData.report_status)" size="small">
                {{ detailData.report_status_display }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ detailData.creator_name }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ detailData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ detailData.updated_at }}</span>
            </div>
          </div>
        </div>

        <!-- 监测内容 -->
        <div class="detail-section">
          <h3 class="section-title">监测内容</h3>
          <div class="content-box">
            {{ detailData.detection_content }}
          </div>
        </div>

        <!-- 发现问题 -->
        <div class="detail-section">
          <h3 class="section-title">发现问题</h3>
          <div class="content-box">
            {{ detailData.problem || '--' }}
          </div>
        </div>

        <!-- 处理结果 -->
        <div class="detail-section">
          <h3 class="section-title">处理结果</h3>
          <div class="content-box">
            {{ detailData.processing_result || '--' }}
          </div>
        </div>

        <!-- 详细描述 -->
        <div class="detail-section" v-if="detailData.detailed_description">
          <h3 class="section-title">详细描述</h3>
          <div class="content-box">
            {{ detailData.detailed_description }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          @click="handleEdit"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          编辑记录
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { ElDialog, ElButton, ElTag } from 'element-plus'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import { getReportStatusTagType } from '@/utils/constants.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'edit', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(
      `organizational-management/infection-detection/detail/${props.itemId}/`,
    )
    detailData.value = response
  } catch (error) {
    console.error('获取感染监测记录详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 编辑记录
const handleEdit = () => {
  emit('edit', detailData.value)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.content-box {
  color: #374151;
  line-height: 1.625;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
