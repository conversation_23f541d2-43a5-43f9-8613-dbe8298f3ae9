<template>
  <div class="cleaning-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Brush />
          </el-icon>
          清洁消毒记录
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="records"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
    >
      <el-table-column prop="area" label="清洁区域" width="140" fixed="left">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-icon class="mr-2 text-blue-500">
              <Location />
            </el-icon>
            <span class="font-medium">{{ row.area }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="type" label="清洁类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getTypeTagType(row.type)" size="small">
            {{ row.type }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="sop" label="SOP规范" width="160" show-overflow-tooltip>
        <template #default="{ row }">
          <el-link
            type="primary"
            @click="handleViewSOP(row.sop)"
            class="text-pink-500 hover:text-pink-700"
          >
            {{ row.sop }}
          </el-link>
        </template>
      </el-table-column>

      <el-table-column prop="planTime" label="计划/执行时间" width="160">
        <template #default="{ row }">
          <div class="text-sm">
            <div class="font-medium">{{ formatDateTime(row.planTime) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="executor" label="执行人" width="100">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-icon class="mr-1 text-gray-500">
              <User />
            </el-icon>
            <span>{{ row.executor }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        prop="disinfectant"
        label="消毒剂/用量"
        min-width="180"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.disinfectant }}</span>
        </template>
      </el-table-column>

      <el-table-column label="结果" width="100">
        <template #default="{ row }">
          <el-tag :type="row.result === '合格' ? 'success' : 'danger'" size="small">
            {{ row.result }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="supervisor" label="监督人" width="100" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-gray-600">{{ row.supervisor || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="240" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="info"
              size="small"
              @click="handleView(row)"
              class="bg-gray-500 hover:bg-gray-600 border-gray-500 hover:border-gray-600"
            >
              <el-icon class="mr-1">
                <View />
              </el-icon>
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(row)"
              class="bg-orange-500 hover:bg-orange-600 border-orange-500 hover:border-orange-600"
            >
              <el-icon class="mr-1">
                <Edit />
              </el-icon>
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
              class="bg-red-500 hover:bg-red-600 border-red-500 hover:border-red-600"
            >
              <el-icon class="mr-1">
                <Delete />
              </el-icon>
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElTag,
  ElButton,
  ElPagination,
  ElIcon,
  ElLink,
  ElMessageBox,
  ElMessage,
} from 'element-plus'
import { Brush, Location, User, View, Edit, Delete } from '@element-plus/icons-vue'

// 定义属性
const props = defineProps({
  records: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['view', 'edit', 'delete', 'pagination-change'])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = computed(() => props.records.length)

// 获取清洁类型标签颜色
const getTypeTagType = (type) => {
  const typeMap = {
    日常清洁: 'info',
    终末消毒: 'warning',
    空气消毒: 'success',
    深度清洁: 'danger',
  }
  return typeMap[type] || 'info'
}

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-'
  try {
    const date = new Date(dateTimeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return dateTimeStr
  }
}

// 查看SOP文档
const handleViewSOP = (sop) => {
  ElMessage.info(`正在打开SOP文档: ${sop}`)
}

// 查看记录
const handleView = (record) => {
  emit('view', record)
}

// 编辑记录
const handleEdit = (record) => {
  emit('edit', record)
}

// 删除记录
const handleDelete = (record) => {
  ElMessageBox.confirm(`确定要删除区域"${record.area}"的清洁记录吗？此操作不可撤销。`, '删除确认', {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning',
    confirmButtonClass: 'el-button--danger',
  })
    .then(() => {
      emit('delete', record)
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  emit('pagination-change', { page: currentPage.value, size })
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  emit('pagination-change', { page, size: pageSize.value })
}
</script>

<style scoped>
.cleaning-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.cleaning-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
}

.action-buttons .el-button {
  font-size: 12px;
  padding: 6px 8px;
}

:deep(.el-table .el-table__body-wrapper .el-table__body .el-table__row:hover) {
  background-color: #f8fafc;
}

:deep(.el-table .el-table__body-wrapper .el-table__body .el-table__row:hover td) {
  background-color: transparent;
}

:deep(.el-link) {
  font-weight: 500;
}
</style>
