<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="cleaning-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="140px"
        class="cleaning-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="清洁区域" prop="area">
              <el-input
                v-model="formData.area"
                placeholder="例如: VIP01房间"
                :disabled="mode === 'view'"
              />
            </el-form-item>
            <el-form-item label="清洁类型" prop="type">
              <el-select
                v-model="formData.type"
                placeholder="请选择清洁类型"
                class="w-full"
                :disabled="mode === 'view'"
              >
                <el-option label="日常清洁" value="日常清洁" />
                <el-option label="终末消毒" value="终末消毒" />
                <el-option label="空气消毒" value="空气消毒" />
                <el-option label="深度清洁" value="深度清洁" />
              </el-select>
            </el-form-item>
            <el-form-item label="参照SOP规范" prop="sop">
              <el-input
                v-model="formData.sop"
                placeholder="可链接到SOP文档或填写名称"
                :disabled="mode === 'view'"
              />
            </el-form-item>
            <el-form-item label="执行时间" prop="planTime">
              <el-date-picker
                v-model="formData.planTime"
                type="datetime"
                placeholder="选择执行时间"
                class="w-full"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm"
                :disabled="mode === 'view'"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 执行信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">执行信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="执行人" prop="executor">
              <el-input
                v-model="formData.executor"
                placeholder="填写执行人姓名"
                :disabled="mode === 'view'"
              />
            </el-form-item>
            <el-form-item label="监督/检查人" prop="supervisor">
              <el-input
                v-model="formData.supervisor"
                placeholder="填写监督人姓名 (若有)"
                :disabled="mode === 'view'"
              />
            </el-form-item>
            <el-form-item label="执行结果" prop="result">
              <el-select
                v-model="formData.result"
                placeholder="请选择执行结果"
                class="w-full"
                :disabled="mode === 'view'"
              >
                <el-option label="合格" value="合格" />
                <el-option label="不合格" value="不合格" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 消毒信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">消毒信息</h4>
          <el-form-item label="消毒剂名称/浓度/用量" prop="disinfectant">
            <el-input
              v-model="formData.disinfectant"
              type="textarea"
              :rows="3"
              placeholder="例如: 84消毒液, 1:100, 5升"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </div>

        <!-- 过程描述 -->
        <div class="form-section mb-6">
          <h4 class="section-title">过程描述</h4>
          <el-form-item label="清洁/消毒过程简述" prop="process">
            <el-input
              v-model="formData.process"
              type="textarea"
              :rows="4"
              placeholder="简要描述操作步骤"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </div>

        <!-- 备注信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">备注信息</h4>
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="其他需要说明的信息..."
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ mode === 'view' ? '关闭' : '取消' }}</el-button>
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '保存记录' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElDatePicker,
} from 'element-plus'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  recordData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit' | 'view'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  area: '',
  type: '',
  sop: '',
  planTime: '',
  executor: '',
  supervisor: '',
  result: '合格',
  disinfectant: '',
  process: '',
  remarks: '',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  const titleMap = {
    add: '新增清洁消毒执行记录',
    edit: '编辑清洁消毒执行记录',
    view: '查看清洁消毒执行记录',
  }
  return titleMap[props.mode] || '清洁消毒执行记录'
})

// 表单验证规则
const rules = computed(() => {
  if (props.mode === 'view') return {}

  return {
    area: [{ required: true, message: '请输入清洁区域', trigger: 'blur' }],
    type: [{ required: true, message: '请选择清洁类型', trigger: 'change' }],
    planTime: [{ required: true, message: '请选择执行时间', trigger: 'change' }],
    executor: [{ required: true, message: '请输入执行人姓名', trigger: 'blur' }],
    result: [{ required: true, message: '请选择执行结果', trigger: 'change' }],
    disinfectant: [{ required: true, message: '请输入消毒剂信息', trigger: 'blur' }],
    process: [{ required: true, message: '请描述清洁/消毒过程', trigger: 'blur' }],
  }
})

// 监听记录数据变化
watch(
  () => props.recordData,
  (newData) => {
    if (newData) {
      // 编辑或查看模式，填充数据
      Object.assign(formData, newData)
    }
  },
  { immediate: true },
)

// 打开弹窗
const handleOpen = () => {
  if (props.mode === 'add') {
    // 重置表单
    resetForm()
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    area: '',
    type: '',
    sop: '',
    planTime: '',
    executor: '',
    supervisor: '',
    result: '合格',
    disinfectant: '',
    process: '',
    remarks: '',
  })
  formRef.value?.clearValidate()
}

// 保存
const handleSave = async () => {
  if (props.mode === 'view') return
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    // 模拟保存过程
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const saveData = {
      ...formData,
      ...(props.mode === 'add' && {
        id: 'CR' + String(Date.now()).slice(-3).padStart(3, '0'),
      }),
    }

    emit('save', saveData)
    saving.value = false
  } catch (error) {
    saving.value = false
    console.error('表单验证失败:', error)
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  color: #909399;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  color: #909399;
}

:deep(.el-select.is-disabled .el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
