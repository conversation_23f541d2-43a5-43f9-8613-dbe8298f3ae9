<template>
  <div class="statistics-tab-container">
    <!-- 筛选条件区域 -->
    <FilterPanel
      :fields="filterFields"
      :filters="filters"
      @search="handleGenerateReport"
      class="mb-6"
    />

    <!-- 统计信息概览 -->
    <div
      v-if="statisticsData"
      class="overview-section bg-white border border-gray-200 rounded-lg p-6 mb-6"
    >
      <h3 class="text-lg font-semibold text-gray-800 mb-4">统计概览</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="stat-card">
          <div class="stat-label">总回复数</div>
          <div class="stat-value text-pink-600">{{ statisticsData.response_count }}</div>
        </div>
        <div class="stat-card">
          <div class="stat-label">问题数量</div>
          <div class="stat-value text-blue-600">
            {{ statisticsData.questions_stats?.length || 0 }}
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-label">数据时间范围</div>
          <div class="stat-value text-green-600">{{ formatDateRange() }}</div>
        </div>
      </div>
    </div>

    <!-- 问题统计详情 -->
    <div v-if="statisticsData?.questions_stats" class="questions-section">
      <div
        v-for="(question, index) in statisticsData.questions_stats"
        :key="question.question_rid"
        class="question-card bg-white border border-gray-200 rounded-lg p-6 mb-6"
      >
        <h4 class="text-md font-semibold text-gray-800 mb-4">
          {{ index + 1 }}. {{ question.content }}
          <el-tag :type="getQuestionTypeTagType(question.type)" size="small" class="ml-2">
            {{ getQuestionTypeText(question.type) }}
          </el-tag>
        </h4>

        <!-- 单选题统计 -->
        <div v-if="question.type.toUpperCase() === 'SINGLE_CHOICE' && question.choice_stats">
          <div class="choice-stats">
            <div
              v-for="choice in question.choice_stats"
              :key="choice.choice_rid"
              class="choice-item"
            >
              <div class="choice-content">{{ choice.content }}</div>
              <div class="choice-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{
                      width: getChoicePercentage(choice.count, statisticsData.response_count) + '%',
                    }"
                  ></div>
                </div>
                <div class="choice-stats-text">
                  {{ choice.count }} ({{
                    getChoicePercentage(choice.count, statisticsData.response_count)
                  }}%)
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 多选题统计 -->
        <div v-else-if="question.type.toUpperCase() === 'MULTIPLE_CHOICE' && question.choice_stats">
          <div class="choice-stats">
            <div
              v-for="choice in question.choice_stats"
              :key="choice.choice_rid"
              class="choice-item"
            >
              <div class="choice-content">{{ choice.content }}</div>
              <div class="choice-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill bg-blue-500"
                    :style="{
                      width: getChoicePercentage(choice.count, statisticsData.response_count) + '%',
                    }"
                  ></div>
                </div>
                <div class="choice-stats-text">
                  {{ choice.count }} ({{
                    getChoicePercentage(choice.count, statisticsData.response_count)
                  }}%)
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 是非题统计 -->
        <div v-else-if="question.type.toUpperCase() === 'TRUE_FALSE'">
          <div class="true-false-stats">
            <div class="tf-item">
              <div class="tf-label">是</div>
              <div class="tf-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill bg-green-500"
                    :style="{
                      width:
                        getChoicePercentage(question.true_count, statisticsData.response_count) +
                        '%',
                    }"
                  ></div>
                </div>
                <div class="tf-stats-text">
                  {{ question.true_count }} ({{
                    getChoicePercentage(question.true_count, statisticsData.response_count)
                  }}%)
                </div>
              </div>
            </div>
            <div class="tf-item">
              <div class="tf-label">否</div>
              <div class="tf-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill bg-red-500"
                    :style="{
                      width:
                        getChoicePercentage(question.false_count, statisticsData.response_count) +
                        '%',
                    }"
                  ></div>
                </div>
                <div class="tf-stats-text">
                  {{ question.false_count }} ({{
                    getChoicePercentage(question.false_count, statisticsData.response_count)
                  }}%)
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 星级评分统计 -->
        <div v-else-if="question.type.toUpperCase() === 'STAR_RATING' && question.details">
          <div class="star-rating-stats">
            <div class="star-overview mb-4">
              <div class="average-rating">
                <span class="rating-label">平均评分：</span>
                <el-rate
                  :model-value="calculateAverageRating(question.details)"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}分"
                />
              </div>
              <div class="total-responses">总评分次数：{{ question.details.total_count }}</div>
            </div>
            <div class="star-distribution">
              <div
                class="star-item"
                v-for="star in [5, 4.5, 4, 3.5, 3, 2.5, 2, 1.5, 1, 0.5]"
                :key="star"
              >
                <div class="star-label">{{ star }}星</div>
                <div class="star-progress">
                  <div class="progress-bar">
                    <div
                      class="progress-fill bg-yellow-500"
                      :style="{ width: getStarPercentage(star, question.details) + '%' }"
                    ></div>
                  </div>
                  <div class="star-count">{{ getStarCount(star, question.details) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 文本回答统计 -->
        <div v-else-if="question.type.toUpperCase() === 'TEXT' && question.text_answers">
          <div class="text-answers">
            <div class="answers-summary mb-4">
              <span class="answers-count">共收到 {{ question.text_answers.length }} 条回答</span>
            </div>
            <div class="answers-list">
              <div
                v-for="(answer, answerIndex) in question.text_answers"
                :key="answerIndex"
                class="answer-item"
              >
                <div class="answer-index">{{ answerIndex + 1 }}.</div>
                <div class="answer-content">{{ answer }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 暂无数据状态 -->
    <div
      v-else-if="!loading"
      class="no-data bg-white border border-gray-200 rounded-lg p-12 text-center"
    >
      <el-icon class="text-6xl text-gray-400 mb-4">
        <TrendCharts />
      </el-icon>
      <div class="text-lg text-gray-500 mb-2">暂无统计数据</div>
      <div class="text-sm text-gray-400">请选择问卷和时间范围后点击"生成报告"</div>
    </div>

    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="loading-state bg-white border border-gray-200 rounded-lg p-12 text-center"
    >
      <el-icon class="text-6xl text-pink-500 mb-4 animate-spin">
        <TrendCharts />
      </el-icon>
      <div class="text-lg text-gray-600">正在生成统计报告...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElRate, ElTag, ElIcon, ElMessage } from 'element-plus'
import { TrendCharts } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import { get } from '@/utils/request.js'
import { getQuestionTypeText, getQuestionTypeTagType } from '@/utils/constants.js'

// 响应式数据
const filters = reactive({
  questionnaire_qid: '',
  start_date: '',
  end_date: '',
})

// 问卷相关数据
const questionnaires = ref([])
const questionnairesLoading = ref(false)
const questionnaireSearchOptions = ref([])
const questionnaireSearchLoading = ref(false)

// 统计数据
const statisticsData = ref(null)
const loading = ref(false)

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'questionnaire_qid',
    type: 'select',
    label: '选择问卷',
    placeholder: '输入问卷标题搜索',
    filterable: true,
    remote: true,
    remoteMethod: searchQuestionnaire,
    options:
      questionnaireSearchOptions.value.length > 0
        ? questionnaireSearchOptions.value
        : questionnaires.value.map((questionnaire) => ({
            label: questionnaire.title,
            value: questionnaire.qid,
          })),
    loading: questionnaireSearchLoading.value || questionnairesLoading.value,
    required: true,
  },
  {
    key: 'start_date',
    type: 'date',
    label: '开始日期',
    placeholder: '选择开始日期',
  },
  {
    key: 'end_date',
    type: 'date',
    label: '结束日期',
    placeholder: '选择结束日期',
  },
])

// 方法
const fetchQuestionnaires = async (options = {}) => {
  const { query, isSearch = false } = options

  const loadingRef = isSearch ? questionnaireSearchLoading : questionnairesLoading
  loadingRef.value = true

  try {
    const params = {
      page: 1,
      page_size: isSearch ? 50 : 100,
    }

    if (query && query.trim()) {
      params.search = query.trim()
    }

    const response = await get('organizational-management/questionnaires/list/', params)

    if (response?.list) {
      if (isSearch) {
        questionnaireSearchOptions.value = response.list.map((questionnaire) => ({
          label: questionnaire.title,
          value: questionnaire.qid,
        }))
      } else {
        questionnaires.value = response.list
        // 默认选择第一个问卷并请求统计数据
        if (response.list.length > 0 && !filters.questionnaire_qid) {
          filters.questionnaire_qid = response.list[0].qid
          // 初始化时请求一次统计数据
          fetchStatistics()
        }
      }
    } else {
      if (isSearch) {
        questionnaireSearchOptions.value = []
      } else {
        questionnaires.value = []
      }
    }
  } catch (error) {
    console.error(isSearch ? '搜索问卷失败:' : '获取问卷列表失败:', error)
    if (!isSearch) {
      ElMessage.error('获取问卷列表失败')
    }

    if (isSearch) {
      questionnaireSearchOptions.value = []
    } else {
      questionnaires.value = []
    }
  } finally {
    loadingRef.value = false
  }
}

const searchQuestionnaire = async (query) => {
  if (!query || query.trim() === '') {
    questionnaireSearchOptions.value = []
    return
  }

  await fetchQuestionnaires({
    query: query,
    isSearch: true,
  })
}

const fetchStatistics = async () => {
  if (!filters.questionnaire_qid) {
    ElMessage.warning('请选择问卷')
    return
  }

  loading.value = true
  try {
    const params = {}

    if (filters.start_date) {
      params.start_date = filters.start_date
    }
    if (filters.end_date) {
      params.end_date = filters.end_date
    }

    const response = await get(
      `organizational-management/questionnaires/${filters.questionnaire_qid}/statistics/`,
      params,
    )
    statisticsData.value = response
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
    statisticsData.value = null
  } finally {
    loading.value = false
  }
}

const handleGenerateReport = () => {
  fetchStatistics()
}

// 计算选择项百分比
const getChoicePercentage = (count, total) => {
  if (total === 0) return 0
  return Math.round((count / total) * 100)
}

// 计算星级评分平均值
const calculateAverageRating = (details) => {
  if (!details || details.total_count === 0) return 0

  const totalScore =
    details.star_0_5_count * 0.5 +
    details.star_1_0_count * 1.0 +
    details.star_1_5_count * 1.5 +
    details.star_2_0_count * 2.0 +
    details.star_2_5_count * 2.5 +
    details.star_3_0_count * 3.0 +
    details.star_3_5_count * 3.5 +
    details.star_4_0_count * 4.0 +
    details.star_4_5_count * 4.5 +
    details.star_5_0_count * 5.0

  return Math.round((totalScore / details.total_count) * 10) / 10
}

// 获取星级计数
const getStarCount = (star, details) => {
  const starKey = `star_${star.toString().replace('.', '_')}_count`
  return details[starKey] || 0
}

// 获取星级百分比
const getStarPercentage = (star, details) => {
  if (details.total_count === 0) return 0
  const count = getStarCount(star, details)
  return Math.round((count / details.total_count) * 100)
}

// 格式化日期范围
const formatDateRange = () => {
  if (filters.start_date && filters.end_date) {
    return `${filters.start_date} ~ ${filters.end_date}`
  }
  return '全部时间'
}

onMounted(() => {
  // 获取问卷列表
  fetchQuestionnaires()
})

// 移除监听问卷选择变化的watch
// watch(
//   () => filters.questionnaire_qid,
//   (newQid) => {
//     if (newQid) {
//       fetchStatistics()
//     }
//   },
// )
</script>

<style scoped>
.statistics-tab-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-section {
  transition: all 0.3s ease;
}

.action-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

.stat-card {
  text-align: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
}

.question-card {
  transition: all 0.3s ease;
}

.question-card:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.choice-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.choice-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.choice-content {
  min-width: 120px;
  font-weight: 500;
  color: #374151;
}

.choice-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 20px;
  background-color: #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to right, #ec4899, #f472b6);
  transition: width 0.3s ease;
}

.choice-stats-text {
  min-width: 80px;
  text-align: right;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.true-false-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tf-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.tf-label {
  min-width: 60px;
  font-weight: 500;
  color: #374151;
}

.tf-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.tf-stats-text {
  min-width: 80px;
  text-align: right;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.star-rating-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.star-overview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.average-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-label {
  font-weight: 500;
  color: #374151;
}

.total-responses {
  font-size: 14px;
  color: #6b7280;
}

.star-distribution {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.star-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.star-label {
  min-width: 60px;
  font-weight: 500;
  color: #374151;
}

.star-progress {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.star-count {
  min-width: 40px;
  text-align: right;
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.text-answers {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.answers-summary {
  padding: 12px;
  background-color: #f3f4f6;
  border-radius: 8px;
}

.answers-count {
  font-weight: 500;
  color: #374151;
}

.answers-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.answer-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.answer-index {
  min-width: 24px;
  font-weight: 500;
  color: #6b7280;
}

.answer-content {
  flex: 1;
  color: #374151;
  line-height: 1.5;
}

.no-data,
.loading-state {
  transition: all 0.3s ease;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
