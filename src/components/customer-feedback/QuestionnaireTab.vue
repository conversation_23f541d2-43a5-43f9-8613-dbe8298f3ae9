<template>
  <div class="questionnaire-tab-container">
    <!-- 操作按钮区域 -->
    <div class="mb-6 flex justify-end">
      <el-button
        type="primary"
        @click="handleCreateQuestionnaire"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-2">
          <Plus />
        </el-icon>
        创建新问卷
      </el-button>
    </div>

    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 问卷表格 -->
    <QuestionnaireTable
      ref="tableRef"
      :filters="filters"
      @view="handleViewQuestionnaire"
      @edit="handleEditQuestionnaire"
      @refresh="handleSearch"
    />

    <!-- 问卷详情对话框 -->
    <QuestionnaireDetailDialog
      v-model="detailDialogVisible"
      :qid="selectedQuestionnaireQid"
      @close="handleCloseDetailDialog"
      @refresh="handleSearch"
    />

    <!-- 问卷编辑弹窗 -->
    <QuestionnaireFormDialog
      v-model="formDialogVisible"
      :item-id="currentQuestionnaireQid"
      @success="handleSaveQuestionnaire"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElButton, ElIcon, ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import FilterPanel from '@/components/FilterPanel.vue'
import QuestionnaireTable from './QuestionnaireTable.vue'
import QuestionnaireDetailDialog from './QuestionnaireDetailDialog.vue'
import QuestionnaireFormDialog from './QuestionnaireFormDialog.vue'
import { useBaseDataStore } from '@/stores/baseData.js'
import { get } from '@/utils/request.js'
import {
  QUESTIONNAIRE_TYPE_OPTIONS,
  QUESTIONNAIRE_AVAILABLE_STAGE_OPTIONS,
} from '@/utils/constants.js'

// 使用基础数据存储
const baseDataStore = useBaseDataStore()

// 响应式数据
const tableRef = ref(null)
const detailDialogVisible = ref(false)
const selectedQuestionnaireQid = ref(null)

// 员工选项和加载状态
const staffs = ref([])
const staffsLoading = ref(false)
const staffSearchOptions = ref([]) // 搜索结果选项
const staffSearchLoading = ref(false)

// 筛选条件
const filters = reactive({
  search: '',
  questionnaire_type: '',
  questionnaire_available_stage: '',
  is_active: '',
  creator_sid: '',
  creator_department_rid: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'search',
    type: 'input',
    label: '关键字',
    placeholder: '问卷标题/描述',
  },
  {
    key: 'questionnaire_type',
    type: 'select',
    label: '问卷类型',
    placeholder: '选择问卷类型',
    options: QUESTIONNAIRE_TYPE_OPTIONS,
  },
  {
    key: 'questionnaire_available_stage',
    type: 'select',
    label: '适用阶段',
    placeholder: '选择适用阶段',
    options: QUESTIONNAIRE_AVAILABLE_STAGE_OPTIONS,
  },
  {
    key: 'is_active',
    type: 'select',
    label: '启用状态',
    placeholder: '选择启用状态',
    options: [
      { label: '启用', value: true },
      { label: '停用', value: false },
    ],
  },
  {
    key: 'creator_department_rid',
    type: 'select',
    label: '创建者部门',
    placeholder: '选择部门',
    options: baseDataStore.departments.getOptions(),
    loading: baseDataStore.departments.isLoading() || baseDataStore.departments.searchLoading.value,
  },
  {
    key: 'creator_sid',
    type: 'select',
    label: '创建者',
    placeholder: '输入员工姓名搜索',
    filterable: true,
    remote: true,
    remoteMethod: searchStaff,
    options:
      staffSearchOptions.value.length > 0
        ? staffSearchOptions.value
        : staffs.value.map((staff) => ({
            label: staff.name,
            value: staff.sid,
          })),
    loading: staffSearchLoading.value || staffsLoading.value,
  },
])

// 弹窗相关
const formDialogVisible = ref(false)
const currentQuestionnaireQid = ref(null)

// 方法
const handleSearch = () => {
  // 触发表格刷新数据
  if (tableRef.value) {
    tableRef.value.resetPagination()
  }
}

const handleViewQuestionnaire = (questionnaire) => {
  selectedQuestionnaireQid.value = questionnaire.qid
  detailDialogVisible.value = true
}

const handleEditQuestionnaire = (questionnaire) => {
  currentQuestionnaireQid.value = questionnaire.qid
  formDialogVisible.value = true
}

const handleCreateQuestionnaire = () => {
  currentQuestionnaireQid.value = null
  formDialogVisible.value = true
}

const handleSaveQuestionnaire = () => {
  // 处理保存成功后刷新表格数据
  if (tableRef.value) {
    tableRef.value.refresh()
  }
  formDialogVisible.value = false
}

// 对话框相关方法
const handleCloseDetailDialog = () => {
  detailDialogVisible.value = false
  selectedQuestionnaireQid.value = null
}

// 获取员工列表（支持搜索）
const fetchStaffs = async (options = {}) => {
  const { department, query, isSearch = false } = options

  const loading = isSearch ? staffSearchLoading : staffsLoading
  loading.value = true

  try {
    const params = {
      page: 1,
      page_size: isSearch ? 50 : 100,
    }

    // 如果指定了部门，添加部门参数
    if (department) {
      params.department = department
    }

    // 如果是搜索模式，添加搜索关键词
    if (query && query.trim()) {
      params.search = query.trim()
    }

    const response = await get('organizational-management/staff/list/', params)

    if (response?.list) {
      if (isSearch) {
        // 搜索模式：更新搜索选项
        staffSearchOptions.value = response.list.map((staff) => ({
          label: staff.name,
          value: staff.sid,
        }))
      } else {
        // 普通模式：更新员工列表
        staffs.value = response.list
      }
    } else {
      if (isSearch) {
        staffSearchOptions.value = []
      } else {
        staffs.value = []
      }
    }
  } catch (error) {
    console.error(isSearch ? '搜索员工失败:' : '获取员工列表失败:', error)
    if (!isSearch) {
      ElMessage.error('获取员工列表失败')
    }

    if (isSearch) {
      staffSearchOptions.value = []
    } else {
      staffs.value = []
    }
  } finally {
    loading.value = false
  }
}

// 搜索员工（使用统一的 fetchStaffs 函数）
const searchStaff = async (query) => {
  if (!query || query.trim() === '') {
    staffSearchOptions.value = []
    return
  }

  await fetchStaffs({
    department: filters.creator_department_rid,
    query: query,
    isSearch: true,
  })
}

// 监听部门选择变化
watch(
  () => filters.creator_department_rid,
  (newDepartment) => {
    // 清空员工选择和搜索结果
    filters.creator_sid = ''
    staffSearchOptions.value = []

    // 获取员工列表
    fetchStaffs({ department: newDepartment })
  },
)

onMounted(() => {
  // 初始化数据
  baseDataStore.departments.fetch()

  // 初始加载所有员工
  fetchStaffs()
})
</script>

<style scoped>
.questionnaire-tab-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-section {
  transition: all 0.3s ease;
}

.action-section:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}
</style>
