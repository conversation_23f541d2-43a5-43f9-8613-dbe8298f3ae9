<template>
  <div
    class="questionnaire-table-container bg-white border border-gray-200 rounded-lg overflow-hidden"
  >
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Edit />
          </el-icon>
          问卷列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="questionnaireList"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      empty-text="暂无问卷数据"
      @row-click="handleRowClick"
    >
      <el-table-column prop="qid" label="问卷编号" min-width="180" fixed="left">
        <template #default="{ row }">
          <span class="font-mono text-sm">{{ row.qid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="title" label="问卷标题" min-width="200">
        <template #default="{ row }">
          <div class="text-center">
            <div class="font-medium text-gray-900">{{ row.title }}</div>
            <div class="text-sm text-gray-500 mt-1" v-if="row.description">
              <el-tooltip
                :content="row.description"
                placement="top"
                :disabled="row.description.length <= 50"
              >
                {{
                  row.description.length > 50
                    ? row.description.substring(0, 50) + '...'
                    : row.description
                }}
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="questionnaire_type" label="问卷类型" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getQuestionnaireTypeTagType(row.questionnaire_type)" size="small">
            {{ getQuestionnaireTypeText(row.questionnaire_type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="available_stage" label="适用阶段" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getQuestionnaireAvailableStageTagType(row.available_stage)" size="small">
            {{
              row.available_stage_display || getQuestionnaireAvailableStageText(row.available_stage)
            }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="is_active" label="启用状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
            {{ row.is_active ? '启用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="creator_name" label="创建者" min-width="120">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="28" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.creator_name ? row.creator_name.charAt(0) : '?' }}
            </el-avatar>
            <span class="font-medium">{{ row.creator_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.created_at) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.created_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="240" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              @click.stop="handleView(row)"
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleEdit(row)"
              type="primary"
              size="small"
              class="bg-orange-500 hover:bg-orange-600 border-orange-500"
            >
              编辑
            </el-button>
            <el-button
              @click.stop="handleToggleStatus(row)"
              :type="row.is_active ? 'warning' : 'success'"
              size="small"
              :loading="toggleStatusLoading.has(row.qid)"
              :class="
                row.is_active
                  ? 'bg-yellow-500 hover:bg-yellow-600 border-yellow-500'
                  : 'bg-green-500 hover:bg-green-600 border-green-500'
              "
            >
              {{ row.is_active ? '停用' : '启用' }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElTag,
  ElPagination,
  ElMessage,
  ElAvatar,
  ElIcon,
  ElTooltip,
  ElMessageBox,
} from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import {
  getQuestionnaireTypeText,
  getQuestionnaireTypeTagType,
  getQuestionnaireAvailableStageText,
  getQuestionnaireAvailableStageTagType,
} from '@/utils/constants.js'
import { get, post } from '@/utils/request.js'

// Props
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// Emits
const emit = defineEmits(['view', 'edit', 'refresh'])

// 响应式数据
const loading = ref(false)
const questionnaireList = ref([])
const toggleStatusLoading = ref(new Set()) // 存储正在处理状态切换的问卷ID

// 分页数据 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const total = computed(() => totalCount.value)

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm')
}

// 方法
const loadQuestionnaireList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...props.filters,
    }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('/organizational-management/questionnaires/list/', params)

    questionnaireList.value = response.list || []
    totalCount.value = response.total_count || 0
  } catch (error) {
    console.error('加载问卷列表失败:', error)
    ElMessage.error('加载问卷列表失败')
    questionnaireList.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

const handleView = (row) => {
  emit('view', row)
}

const handleEdit = (row) => {
  emit('edit', row)
}

const handleRowClick = (row) => {
  emit('view', row)
}

const handleToggleStatus = async (row) => {
  const action = row.is_active ? '停用' : '启用'
  const endpoint = row.is_active ? 'unpublish' : 'publish'

  try {
    await ElMessageBox.confirm(`确定要${action}问卷 "${row.title}" 吗？`, `${action}问卷`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    // 添加loading状态
    toggleStatusLoading.value.add(row.qid)

    await post(`/organizational-management/questionnaires/${row.qid}/${endpoint}/`)

    ElMessage.success(`${action}成功`)
    loadQuestionnaireList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}问卷失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  } finally {
    // 移除loading状态
    toggleStatusLoading.value.delete(row.qid)
  }
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadQuestionnaireList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadQuestionnaireList()
}

// 重置分页并刷新数据
const resetPagination = () => {
  currentPage.value = 1
  loadQuestionnaireList()
}

// 刷新当前页数据
const refresh = () => {
  loadQuestionnaireList()
}

// 暴露给父组件的方法
defineExpose({
  resetPagination,
  refresh,
})

// 初始化
onMounted(() => {
  loadQuestionnaireList()
})
</script>

<style scoped>
.questionnaire-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.questionnaire-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
