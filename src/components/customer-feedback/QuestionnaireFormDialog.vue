<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="1000px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="questionnaire-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="questionnaire-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-2 gap-4">
            <el-form-item label="问卷标题" prop="title">
              <el-input
                v-model="form.title"
                placeholder="请输入问卷标题"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="问卷类型" prop="questionnaire_type">
              <el-select
                v-model="form.questionnaire_type"
                placeholder="请选择问卷类型"
                class="w-full"
              >
                <el-option
                  v-for="option in QUESTIONNAIRE_TYPE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="适用阶段" prop="available_stage">
              <el-select v-model="form.available_stage" placeholder="请选择适用阶段" class="w-full">
                <el-option
                  v-for="option in QUESTIONNAIRE_AVAILABLE_STAGE_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="启用状态" prop="is_active">
              <el-switch
                v-model="form.is_active"
                active-text="启用"
                inactive-text="停用"
                active-color="#ec4899"
                inactive-color="#d1d5db"
              />
            </el-form-item>
          </div>

          <el-form-item label="问卷描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="请输入问卷描述，简要说明问卷的目的和用途"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 问题设置 -->
        <div class="form-section mb-6">
          <h4 class="section-title">问题设置</h4>
          <div class="questions-container">
            <div
              v-for="(question, index) in form.questions"
              :key="question.tempId || question.rid"
              class="question-item border border-gray-200 rounded-lg p-4 mb-4"
            >
              <div class="question-header flex items-center justify-between mb-3">
                <div class="question-number text-sm font-medium text-pink-600">
                  问题 {{ index + 1 }}
                </div>
                <div class="question-actions">
                  <el-button
                    v-if="form.questions.length > 1"
                    @click="removeQuestion(index)"
                    type="danger"
                    size="small"
                    :icon="Delete"
                    text
                  >
                    删除
                  </el-button>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <el-form-item
                  :prop="`questions.${index}.content`"
                  :rules="[{ required: true, message: '请输入问题内容', trigger: 'blur' }]"
                  label="问题内容"
                >
                  <el-input
                    v-model="question.content"
                    placeholder="请输入问题内容"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>

                <el-form-item
                  :prop="`questions.${index}.question_type`"
                  :rules="[{ required: true, message: '请选择问题类型', trigger: 'change' }]"
                  label="问题类型"
                >
                  <el-select
                    v-model="question.question_type"
                    placeholder="请选择问题类型"
                    class="w-full"
                                         @change="handleQuestionTypeChange(question)"
                  >
                    <el-option
                      v-for="option in QUESTION_TYPE_OPTIONS"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </el-form-item>
              </div>

              <div class="flex items-center mb-3">
                <el-checkbox v-model="question.is_required">必填项</el-checkbox>
              </div>

              <!-- 选择题选项 -->
              <div v-if="questionNeedsChoices(question.question_type)" class="choices-container">
                <div class="choices-header mb-2">
                  <span class="text-sm font-medium text-gray-700">选项设置</span>
                  <el-button
                    @click="addChoice(question)"
                    type="primary"
                    size="small"
                    :icon="Plus"
                    text
                    class="ml-2"
                  >
                    添加选项
                  </el-button>
                </div>

                <div
                  v-for="(choice, choiceIndex) in question.choices"
                  :key="choice.tempId || choice.rid"
                  class="choice-item flex items-center gap-2 mb-2"
                >
                  <span class="choice-prefix text-sm text-gray-500 w-8">
                    {{ getChoicePrefix(question.question_type, choiceIndex) }}
                  </span>
                  <el-input
                    v-model="choice.content"
                    placeholder="请输入选项内容"
                    class="flex-1"
                    maxlength="100"
                  />
                  <el-button
                    v-if="question.choices.length > 1"
                    @click="removeChoice(question, choiceIndex)"
                    type="danger"
                    size="small"
                    :icon="Delete"
                    text
                  />
                </div>
              </div>
            </div>

            <div class="add-question-btn text-center">
              <el-button
                @click="addQuestion"
                type="primary"
                :icon="Plus"
                class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
              >
                添加问题
              </el-button>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.itemId ? '保存修改' : '创建问卷' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElSwitch,
  ElCheckbox,
} from 'element-plus'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { post, put, get } from '@/utils/request.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { showErrorTip } from '@/utils/utils'
import {
  QUESTIONNAIRE_TYPE_OPTIONS,
  QUESTIONNAIRE_AVAILABLE_STAGE_OPTIONS,
  QUESTION_TYPE_OPTIONS,
  questionNeedsChoices,
  getChoicePrefix,
} from '@/utils/constants.js'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  return props.itemId ? '编辑问卷' : '新增问卷'
})

// 生成临时ID
const generateTempId = () => {
  return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// 表单数据
const form = reactive({
  qid: '',
  title: '',
  description: '',
  questionnaire_type: '',
  available_stage: '',
  is_active: true,
  questions: [
    {
      tempId: generateTempId(),
      content: '',
      question_type: '',
      is_required: true,
      order: 1,
      choices: [],
    },
  ],
})

// 表单验证规则
const rules = computed(() => ({
  title: [{ required: true, message: '请输入问卷标题', trigger: 'blur' }],
  questionnaire_type: [{ required: true, message: '请选择问卷类型', trigger: 'change' }],
  available_stage: [{ required: true, message: '请选择适用阶段', trigger: 'change' }],
}))

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    setTimeout(resetForm, 5)
    if (visible) {
      if (props.itemId) {
        // 编辑模式：获取详情数据
        await fetchQuestionnaireDetail(props.itemId)
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 获取问卷详情数据（用于编辑模式）
const fetchQuestionnaireDetail = async (qid) => {
  if (!qid) return

  loading.value = true
  try {
    const response = await get(`/organizational-management/questionnaires/detail/${qid}/`)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到问卷详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取问卷详情失败:', error)
    ElMessage.error('获取问卷详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  // 处理问题数据，为每个问题和选项添加临时ID
  const processedQuestions = (apiData.questions || []).map((question, index) => ({
    ...question,
    tempId: question.rid || generateTempId(),
    order: index + 1,
    choices: (question.choices || []).map((choice, choiceIndex) => ({
      ...choice,
      tempId: choice.rid || generateTempId(),
      order: choiceIndex + 1,
    })),
  }))

  return {
    qid: apiData.qid,
    title: apiData.title,
    description: apiData.description || '',
    questionnaire_type: apiData.questionnaire_type,
    available_stage: apiData.available_stage,
    is_active: apiData.is_active,
    questions:
      processedQuestions.length > 0
        ? processedQuestions
        : [
            {
              tempId: generateTempId(),
              content: '',
              question_type: '',
              is_required: true,
              order: 1,
              choices: [],
            },
          ],
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    qid: '',
    title: '',
    description: '',
    questionnaire_type: '',
    available_stage: '',
    is_active: true,
    questions: [
      {
        tempId: generateTempId(),
        content: '',
        question_type: '',
        is_required: true,
        order: 1,
        choices: [],
      },
    ],
  })

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

// 问题类型改变处理
const handleQuestionTypeChange = (question) => {
  // 如果是选择题类型，初始化选项
  if (questionNeedsChoices(question.question_type)) {
    if (!question.choices || question.choices.length === 0) {
      question.choices = [
        { tempId: generateTempId(), content: '', order: 1 },
        { tempId: generateTempId(), content: '', order: 2 },
      ]
    }
  } else {
    // 非选择题类型，清空选项
    question.choices = []
  }
}

// 添加问题
const addQuestion = () => {
  const newQuestion = {
    tempId: generateTempId(),
    content: '',
    question_type: '',
    is_required: true,
    order: form.questions.length + 1,
    choices: [],
  }
  form.questions.push(newQuestion)
}

// 删除问题
const removeQuestion = (index) => {
  form.questions.splice(index, 1)
  // 重新排序
  form.questions.forEach((question, idx) => {
    question.order = idx + 1
  })
}

// 添加选项
const addChoice = (question) => {
  const newChoice = {
    tempId: generateTempId(),
    content: '',
    order: question.choices.length + 1,
  }
  question.choices.push(newChoice)
}

// 删除选项
const removeChoice = (question, index) => {
  question.choices.splice(index, 1)
  // 重新排序
  question.choices.forEach((choice, idx) => {
    choice.order = idx + 1
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()

    // 验证问题数据
    if (form.questions.length === 0) {
      ElMessage.error('至少需要添加一个问题')
      return
    }

    for (let i = 0; i < form.questions.length; i++) {
      const question = form.questions[i]
      if (!question.content.trim()) {
        ElMessage.error(`第${i + 1}个问题的内容不能为空`)
        return
      }
      if (!question.question_type) {
        ElMessage.error(`第${i + 1}个问题需要选择问题类型`)
        return
      }
      if (questionNeedsChoices(question.question_type)) {
        if (!question.choices || question.choices.length < 2) {
          ElMessage.error(`第${i + 1}个问题至少需要2个选项`)
          return
        }
        for (let j = 0; j < question.choices.length; j++) {
          if (!question.choices[j].content.trim()) {
            ElMessage.error(`第${i + 1}个问题的第${j + 1}个选项内容不能为空`)
            return
          }
        }
      }
    }

    submitting.value = true

    // 构造提交数据
    const submitData = {
      title: form.title,
      description: form.description,
      questionnaire_type: form.questionnaire_type,
      available_stage: form.available_stage,
      is_active: form.is_active,
      questions: form.questions.map((question, index) => {
        const questionData = {
          content: question.content,
          question_type: question.question_type,
          is_required: question.is_required,
          order: index + 1,
        }

        // 只有选择题才包含choices
        if (questionNeedsChoices(question.question_type)) {
          questionData.choices = question.choices.map((choice, choiceIndex) => ({
            content: choice.content,
            order: choiceIndex + 1,
          }))
        }

        return questionData
      }),
    }

    let res
    if (!props.itemId) {
      res = await post('/organizational-management/questionnaires/create/', submitData)
    } else {
      res = await put(`/organizational-management/questionnaires/detail/${form.qid}/`, submitData)
    }

    ElMessage.success(props.itemId ? '问卷更新成功' : '问卷创建成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    showErrorTip(error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.question-item {
  background-color: #fafafa;
  transition: all 0.2s;
}

.question-item:hover {
  background-color: #f5f5f5;
  border-color: #ec4899;
}

.question-number {
  background-color: #ec4899;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.choice-prefix {
  min-width: 2rem;
  text-align: center;
  font-weight: 500;
}

.questions-container {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  background-color: #ffffff;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-switch.is-checked .el-switch__core) {
  background-color: #ec4899;
  border-color: #ec4899;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #ec4899;
  border-color: #ec4899;
}
</style>
