<template>
  <el-dialog
    v-model="visible"
    title="问卷回复详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>问卷标题：</label>
              <span class="font-medium">{{ questionnaireData?.title || '加载中...' }}</span>
            </div>
            <div class="detail-item">
              <label>问卷类型：</label>
              <el-tag
                v-if="questionnaireData"
                :type="getQuestionnaireTypeTagType(questionnaireData.questionnaire_type)"
                size="small"
              >
                {{
                  questionnaireData.questionnaire_type_display ||
                  getQuestionnaireTypeText(questionnaireData.questionnaire_type)
                }}
              </el-tag>
              <span v-else>加载中...</span>
            </div>
            <div class="detail-item">
              <label>问卷编号：</label>
              <span class="font-mono text-xs">{{ detailData.questionnaire_qid }}</span>
            </div>
            <div class="detail-item">
              <label>回复编号：</label>
              <span class="font-mono text-xs">{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <label>客户ID：</label>
              <span>{{ detailData.customer_uid }}</span>
            </div>
            <div class="detail-item">
              <label>提交时间：</label>
              <span>{{ formatDateTime(detailData.submitted_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ formatDateTime(detailData.updated_at) }}</span>
            </div>
          </div>

          <!-- 问卷描述 -->
          <div v-if="questionnaireData?.description" class="mt-4 p-3 bg-gray-50 rounded-lg">
            <label class="text-sm font-medium text-gray-700">问卷描述：</label>
            <p class="mt-1 text-sm text-gray-600">{{ questionnaireData.description }}</p>
          </div>
        </div>

        <!-- 问卷回答详情 -->
        <div class="detail-section">
          <h3 class="section-title">问卷回答详情</h3>
          <div class="space-y-4">
            <div
              v-for="(answer, index) in detailData.answers"
              :key="answer.rid"
              class="answer-item p-4 bg-gray-50 rounded-lg"
            >
              <div class="flex items-start justify-between mb-3">
                <h4 class="text-sm font-medium text-gray-800">
                  问题 {{ index + 1 }}：{{ answer.question_content }}
                </h4>
                <el-tag size="small" :type="getQuestionTypeTagType(answer.question_type)">
                  {{ getQuestionTypeText(answer.question_type) }}
                </el-tag>
              </div>

              <!-- 单选题答案 -->
              <div
                v-if="answer.question_type === 'SINGLE_CHOICE' && answer.selected_choice"
                class="answer-content"
              >
                <div class="answer-label">选择答案：</div>
                <el-tag type="success" effect="plain">{{
                  getChoiceText(answer.selected_choice, answer.question)
                }}</el-tag>
              </div>

              <!-- 多选题答案 -->
              <div
                v-else-if="
                  answer.question_type === 'MULTIPLE_CHOICE' && answer.selected_choices?.length
                "
                class="answer-content"
              >
                <div class="answer-label">选择答案：</div>
                <div class="flex flex-wrap gap-2">
                  <el-tag
                    v-for="choice in answer.selected_choices"
                    :key="choice"
                    type="success"
                    effect="plain"
                    size="small"
                  >
                    {{ getChoiceText(choice, answer.question) }}
                  </el-tag>
                </div>
              </div>

              <!-- 判断题答案 -->
              <div
                v-else-if="answer.question_type === 'TRUE_FALSE' && answer.boolean_answer !== null"
                class="answer-content"
              >
                <div class="answer-label">选择答案：</div>
                <el-tag :type="answer.boolean_answer ? 'success' : 'danger'" effect="plain">
                  {{ answer.boolean_answer ? '是' : '否' }}
                </el-tag>
              </div>

              <!-- 星级评分答案 -->
              <div
                v-else-if="
                  answer.question_type === 'STAR_RATING' && answer.half_star_count !== null
                "
                class="answer-content"
              >
                <div class="answer-label">评分：</div>
                <div class="flex items-center">
                  <el-rate
                    v-model="answer.half_star_count"
                    :max="10"
                    allow-half
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value} 分"
                  />
                </div>
              </div>

              <!-- 文本题答案 -->
              <div
                v-else-if="answer.question_type === 'TEXT' && answer.text_answer"
                class="answer-content"
              >
                <div class="answer-label">回答内容：</div>
                <div class="text-answer p-3 bg-white rounded border border-gray-200">
                  {{ answer.text_answer }}
                </div>
              </div>

              <!-- 未回答 -->
              <div v-else class="answer-content">
                <div class="text-gray-400 text-sm italic">未回答此问题</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import { format } from 'date-fns'
import {
  getQuestionTypeText,
  getQuestionTypeTagType,
  getQuestionnaireTypeText,
  getQuestionnaireTypeTagType,
} from '@/utils/constants.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)
const questionnaireData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
      questionnaireData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    // 先获取问卷回复详情
    const response = await get(
      `organizational-management/questionnaires/responses/detail/${props.itemId}/`,
    )
    detailData.value = response

    // 获取问卷详情
    if (response.questionnaire_qid) {
      const questionnaireResponse = await get(
        `organizational-management/questionnaires/detail/${response.questionnaire_qid}/`,
      )
      questionnaireData.value = questionnaireResponse
    }
  } catch (error) {
    console.error('获取问卷回复详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 时间格式化
const formatDateTime = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 根据选项ID获取选项文本
const getChoiceText = (choiceId, questionId) => {
  if (!questionnaireData.value?.questions || !choiceId) return choiceId

  const question = questionnaireData.value.questions.find((q) => q.rid === questionId)
  if (!question?.choices) return choiceId

  const choice = question.choices.find((c) => c.rid === choiceId)
  return choice?.content || choiceId
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.answer-item {
  transition: all 0.3s ease;
}

.answer-item:hover {
  background-color: #f3f4f6;
}

.answer-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.answer-content {
  margin-top: 0.75rem;
}

.text-answer {
  color: #374151;
  line-height: 1.625;
  white-space: pre-wrap;
  word-break: break-word;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}

:deep(.el-rate) {
  height: auto;
}

:deep(.el-rate__text) {
  color: #ec4899;
  font-weight: 500;
}
</style>
