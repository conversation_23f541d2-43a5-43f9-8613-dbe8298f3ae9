<template>
  <el-dialog
    v-model="visible"
    title="问卷详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="questionnaireData" class="space-y-6">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h3 class="section-title">基础信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>问卷编号：</label>
              <span class="font-mono text-sm">{{ questionnaireData.qid }}</span>
            </div>
            <div class="detail-item">
              <label>问卷标题：</label>
              <span class="font-medium">{{ questionnaireData.title }}</span>
            </div>
            <div class="detail-item">
              <label>问卷类型：</label>
              <el-tag
                :type="getQuestionnaireTypeTagType(questionnaireData.questionnaire_type)"
                size="small"
              >
                {{ getQuestionnaireTypeText(questionnaireData.questionnaire_type) }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>适用阶段：</label>
              <el-tag
                :type="getQuestionnaireAvailableStageTagType(questionnaireData.available_stage)"
                size="small"
              >
                {{
                  questionnaireData.available_stage_display ||
                  getQuestionnaireAvailableStageText(questionnaireData.available_stage)
                }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>启用状态：</label>
              <el-tag :type="questionnaireData.is_active ? 'success' : 'danger'" size="small">
                {{ questionnaireData.is_active ? '启用' : '停用' }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>创建者：</label>
              <span>{{ questionnaireData.creator_name }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ formatDateTime(questionnaireData.created_at) }}</span>
            </div>
          </div>
        </div>

        <!-- 问卷描述 -->
        <div v-if="questionnaireData.description" class="detail-section">
          <h3 class="section-title">问卷描述</h3>
          <div class="description-content p-4 bg-gray-50 rounded-lg">
            {{ questionnaireData.description }}
          </div>
        </div>

        <!-- 问题列表 -->
        <div class="detail-section">
          <h3 class="section-title">
            问题列表 ({{ questionnaireData.questions?.length || 0 }} 题)
          </h3>
          <div
            v-if="questionnaireData.questions && questionnaireData.questions.length > 0"
            class="space-y-4"
          >
            <div
              v-for="(question, index) in sortedQuestions"
              :key="question.rid"
              class="question-item p-4 bg-gray-50 rounded-lg"
            >
              <div class="question-header flex items-start justify-between mb-3">
                <div class="question-title flex-1">
                  <span class="question-number text-pink-600 font-semibold mr-2">
                    {{ question.order || index + 1 }}.
                  </span>
                  <span class="text-gray-900 font-medium">{{ question.content }}</span>
                  <el-tag v-if="question.is_required" type="danger" size="small" class="ml-2">
                    必答
                  </el-tag>
                </div>
                <div class="question-type">
                  <el-tag :type="getQuestionTypeTagType(question.question_type)" size="small">
                    {{ getQuestionTypeText(question.question_type) }}
                  </el-tag>
                </div>
              </div>

              <!-- 选择题选项 -->
              <div v-if="isChoiceQuestion(question.question_type)" class="question-choices">
                <div class="choices-list space-y-2">
                  <div
                    v-for="choice in sortedChoices(question.choices)"
                    :key="choice.rid"
                    class="choice-item flex items-center text-sm text-gray-700"
                  >
                    <span class="choice-label mr-2 text-gray-500">
                      {{ getChoicePrefix(question.question_type, (choice.order || 1) - 1) }}
                    </span>
                    <span>{{ choice.content }}</span>
                  </div>
                </div>
              </div>

              <!-- 特殊题型说明 -->
              <div
                v-else-if="isTextQuestion(question.question_type)"
                class="question-note text-sm text-gray-500"
              >
                <el-icon class="mr-1"><Edit /></el-icon>
                文本输入题，用户可自由填写答案
              </div>
              <div
                v-else-if="isTrueFalseQuestion(question.question_type)"
                class="question-note text-sm text-gray-500"
              >
                <el-icon class="mr-1"><Check /></el-icon>
                是非判断题，用户选择"是"或"否"
              </div>
              <div
                v-else-if="isStarRatingQuestion(question.question_type)"
                class="question-note text-sm text-gray-500"
              >
                <el-icon class="mr-1"><Star /></el-icon>
                星级评分题，用户给出1-5星评分
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <el-empty description="暂无问题" :image-size="100" />
          </div>
        </div>
      </div>
    </div>

    <template #footer> </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElDialog, ElTag, ElEmpty, ElMessage, ElIcon } from 'element-plus'
import { Edit, Check, Star } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import {
  getQuestionnaireTypeText,
  getQuestionnaireTypeTagType,
  getQuestionnaireAvailableStageText,
  getQuestionnaireAvailableStageTagType,
  getQuestionTypeText,
  getQuestionTypeTagType,
  isChoiceQuestion,
  isTextQuestion,
  isTrueFalseQuestion,
  isStarRatingQuestion,
  getChoicePrefix,
} from '@/utils/constants.js'
import { get } from '@/utils/request.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  qid: {
    type: String,
    default: '',
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'refresh'])

// 响应式数据
const loading = ref(false)
const questionnaireData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 排序后的问题列表
const sortedQuestions = computed(() => {
  if (!questionnaireData.value?.questions) return []
  return [...questionnaireData.value.questions].sort((a, b) => (a.order || 0) - (b.order || 0))
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.qid) {
    loadQuestionnaireDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      questionnaireData.value = null
    }, 300)
  }
})

// 获取详情数据
const loadQuestionnaireDetail = async () => {
  if (!props.qid) return

  loading.value = true
  try {
    const response = await get(`/organizational-management/questionnaires/detail/${props.qid}/`)
    questionnaireData.value = response
  } catch (error) {
    console.error('加载问卷详情失败:', error)
    ElMessage.error('加载问卷详情失败')
    questionnaireData.value = null
  } finally {
    loading.value = false
  }
}

// 排序选项
const sortedChoices = (choices) => {
  if (!choices) return []
  return [...choices].sort((a, b) => (a.order || 0) - (b.order || 0))
}

// 时间格式化
const formatDateTime = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.description-content {
  color: #374151;
  line-height: 1.625;
}

.question-item {
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.question-item:hover {
  border-color: #ec4899;
  box-shadow: 0 2px 4px rgba(236, 72, 153, 0.1);
}

.question-number {
  min-width: 1.5rem;
}

.question-choices {
  padding-left: 1.5rem;
  margin-top: 0.5rem;
}

.choice-item {
  padding: 0.25rem 0;
}

.choice-label {
  min-width: 1.5rem;
  font-weight: 500;
}

.question-note {
  display: flex;
  align-items: center;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background-color: #f9fafb;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
