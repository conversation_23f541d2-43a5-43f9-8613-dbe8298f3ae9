<template>
  <el-dialog
    v-model="visible"
    title="反馈处理"
    width="500px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="feedback-process-dialog"
  >
    <template #header>
      <div class="dialog-header flex items-center">
        <el-icon class="mr-3 text-pink-500 text-xl">
          <Edit />
        </el-icon>
        <div>
          <h3 class="text-lg font-semibold text-gray-800">反馈处理</h3>
        </div>
      </div>
    </template>

    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="process-form"
      >
        <el-form-item label="处理意见" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="4"
            placeholder="请填写处理意见..."
            class="w-full"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose"> 取消 </el-button>
        <el-button
          @click="handleSubmit"
          type="primary"
          :loading="loading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          提交处理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import { put } from '@/utils/request'

const emit = defineEmits(['update:visible', 'save'])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  feedbackData: {
    type: Object,
    default: () => null,
  },
})

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  content: '',
})

// 表单验证规则
const formRules = {
  content: [{ required: true, message: '请填写处理意见', trigger: 'blur' }],
}

// 监听对话框打开，重置表单
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      formData.content = ''
    }
  },
)

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (!props.feedbackData?.fid) {
      ElMessage.error('反馈ID不能为空')
      return
    }

    loading.value = true

    const url = `organizational-management/feedback/process/${props.feedbackData.fid}/`

    // 调用API
    await put(url, {
      content: formData.content,
    })

    ElMessage.success('处理提交成功')
    emit('save', {
      content: formData.content,
    })

    visible.value = false
  } catch (error) {
    console.error('处理提交失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  if (loading.value) {
    ElMessage.warning('操作进行中，请稍候...')
    return
  }

  visible.value = false
}
</script>

<style scoped>
.feedback-process-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-header {
  padding: 0;
}

.dialog-content {
  padding: 1rem 0;
}

.process-form {
  padding: 0 1rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 0;
}

:deep(.el-form-item__label) {
  color: rgb(55 65 81);
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 0;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
  padding-bottom: 0;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}
</style>
