<template>
  <div class="feedback-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <ChatDotRound />
          </el-icon>
          客户反馈列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="feedbackList"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      empty-text="暂无反馈数据"
      @row-click="handleRowClick"
    >
      <el-table-column prop="fid" label="反馈编号" min-width="150" fixed="left">
        <template #default="{ row }">
          <span class="font-mono">{{ row.fid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="maternity_info" label="产妇信息" min-width="200">
        <template #default="{ row }">
          <div class="flex items-center justify-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.maternity_info ? row.maternity_info.charAt(0) : '?' }}
            </el-avatar>
            <span class="font-medium">{{ row.maternity_info }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="feedback_time" label="反馈时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.feedback_time) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.feedback_time) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="feedback_type_display" label="反馈类型" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getFeedbackTypeTagType(row.feedback_type)" size="small">
            {{ row.feedback_type_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="status_display" label="处理状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getFeedbackStatusTagType(row.status)" size="small">
            {{ row.status_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="content" label="反馈内容" min-width="200">
        <template #default="{ row }">
          <el-tooltip :content="row.content" placement="top" :disabled="row.content.length <= 30">
            <span class="text-gray-700">
              {{ row.content.length > 30 ? row.content.substring(0, 30) + '...' : row.content }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="180" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              @click.stop="handleView(row)"
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
            >
              查看
            </el-button>
            <el-button
              @click.stop="handleProcess(row)"
              :disabled="row.status === 'RESOLVED'"
              type="primary"
              size="small"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              处理
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 处理弹框 -->
    <FeedbackProcessDialog
      v-model:visible="processDialogVisible"
      :feedback-data="currentFeedback"
      @save="handleProcessSave"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElTag,
  ElPagination,
  ElMessage,
  ElAvatar,
  ElIcon,
  ElTooltip,
} from 'element-plus'
import { ChatDotRound } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { getFeedbackTypeTagType, getFeedbackStatusTagType } from '@/utils/constants.js'
import { get } from '@/utils/request.js'
import FeedbackProcessDialog from './FeedbackProcessDialog.vue'

// Props
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// Emits
const emit = defineEmits(['view', 'refresh'])

// 响应式数据
const loading = ref(false)
const feedbackList = ref([])

// 分页数据 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const total = computed(() => totalCount.value)

// 处理弹框相关
const processDialogVisible = ref(false)
const currentFeedback = ref(null)

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm')
}

// 方法
const loadFeedbackList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...props.filters,
    }

    // if (params.fd_time && /^\d{4}-\d{2}-\d{2}$/.test(params.fd_time)) {
    //   params.fd_time = `${params.fd_time} 00:00:00`
    // }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('/organizational-management/feedback/list/', params)

    feedbackList.value = response.list || []
    totalCount.value = response.total_count || 0
  } catch (error) {
    console.error('加载反馈列表失败:', error)
    ElMessage.error('加载反馈列表失败')
    feedbackList.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

const handleView = (row) => {
  emit('view', row)
}

const handleRowClick = (row) => {
  emit('view', row)
}

const handleProcess = (row) => {
  currentFeedback.value = row
  processDialogVisible.value = true
}

const handleProcessSave = () => {
  // 处理成功后刷新表格数据
  loadFeedbackList()
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadFeedbackList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadFeedbackList()
}

// 重置分页并刷新数据
const resetPagination = () => {
  currentPage.value = 1
  loadFeedbackList()
}

// 刷新当前页数据
const refresh = () => {
  loadFeedbackList()
}

// 暴露给父组件的方法
defineExpose({
  resetPagination,
  refresh,
})

// 初始化
onMounted(() => {
  loadFeedbackList()
})
</script>

<style scoped>
.feedback-table-container {
  transition: all 0.3s ease;
  width: 100%;
}

.feedback-table-container:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.el-table__row:hover) {
  background-color: rgb(253 242 248);
}

:deep(.el-table__row) {
  transition: background-color 0.2s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.action-buttons .el-button {
  flex-shrink: 0;
  white-space: nowrap;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 确保表格占满宽度 */
:deep(.el-table) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  width: 100%;
}

:deep(.el-pagination) {
  --el-pagination-button-color: #374151;
  --el-pagination-hover-color: #ec4899;
}

:deep(.el-pagination .btn-next),
:deep(.el-pagination .btn-prev) {
  border-color: #d1d5db;
}

:deep(.el-pagination .btn-next:hover),
:deep(.el-pagination .btn-prev:hover) {
  color: #ec4899;
  border-color: #ec4899;
}
</style>
