<template>
  <div class="response-table-container bg-white border border-gray-200 rounded-lg overflow-hidden">
    <!-- 表格标题 -->
    <div class="table-header px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-pink-500">
            <Edit />
          </el-icon>
          问卷回复列表
        </h3>
        <div class="text-sm text-gray-600">共 {{ total }} 条记录</div>
      </div>
    </div>

    <!-- 表格内容 -->
    <el-table
      :data="responseList"
      v-loading="loading"
      stripe
      class="w-full"
      style="width: 100%"
      :header-cell-style="{
        backgroundColor: '#f9fafb',
        color: '#374151',
        fontWeight: '600',
        borderBottom: '1px solid #e5e7eb',
        textAlign: 'center',
      }"
      :row-style="{ cursor: 'pointer' }"
      :cell-style="{ textAlign: 'center' }"
      empty-text="暂无问卷回复数据"
      @row-click="handleRowClick"
    >
      <el-table-column prop="questionnaire_qid" label="问卷编号" min-width="180" fixed="left">
        <template #default="{ row }">
          <span class="font-mono text-xs">{{ row.questionnaire_qid }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="questionnaire_title" label="问卷标题" min-width="200">
        <template #default="{ row }">
          <div class="text-center">
            <div class="font-medium text-gray-900">{{ row.questionnaire_title }}</div>
            <div class="text-xs text-gray-500 mt-1" v-if="row.questionnaire_description">
              {{ row.questionnaire_description }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="questionnaire_type_display" label="问卷类型" min-width="120">
        <template #default="{ row }">
          <el-tag :type="getQuestionnaireTypeTagType(row.questionnaire_type)" size="small">
            {{ row.questionnaire_type_display }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="customer_name" label="产妇信息" min-width="150">
        <template #default="{ row }">
          <div class="flex items-center">
            <el-avatar :size="32" class="mr-2 bg-pink-100 text-pink-600">
              {{ row.customer_name ? row.customer_name.charAt(0) : '?' }}
            </el-avatar>
            <span class="font-medium">{{ row.customer_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="submitted_at" label="提交时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.submitted_at) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.submitted_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="updated_at" label="更新时间" min-width="160">
        <template #default="{ row }">
          <div class="text-sm text-gray-600">
            <div>{{ formatDate(row.updated_at) }}</div>
            <div class="text-xs text-gray-400">{{ formatTime(row.updated_at) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="120" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              @click="handleView(row)"
              type="default"
              size="small"
              class="text-pink-600 border-pink-200 hover:bg-pink-50"
            >
              查看
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container px-6 py-4 border-t border-gray-200 bg-gray-50">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="justify-end"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 详情弹框 -->
    <QuestionnaireResponseDetailDialog
      v-model="showDetailDialog"
      :item-id="selectedItemId"
      @close="handleDetailClose"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElTag,
  ElPagination,
  ElMessage,
  ElAvatar,
  ElIcon,
} from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import { format } from 'date-fns'
import { getQuestionnaireTypeTagType } from '@/utils/constants.js'
import { get } from '@/utils/request.js'
import QuestionnaireResponseDetailDialog from './QuestionnaireResponseDetailDialog.vue'

// Props
const props = defineProps({
  filters: {
    type: Object,
    default: () => ({}),
  },
})

// Emits - 暂时移除未使用的emit
// const emit = defineEmits(['refresh'])

// 响应式数据
const loading = ref(false)
const responseList = ref([])
const showDetailDialog = ref(false)
const selectedItemId = ref(null)

// 分页数据 - 内部管理
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const total = computed(() => totalCount.value)

// 时间格式化
const formatDate = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd')
}

const formatTime = (dateTime) => {
  return format(new Date(dateTime), 'HH:mm')
}

// 方法
const loadResponseList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...props.filters,
    }

    // 过滤空值
    Object.keys(params).forEach((key) => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await get('organizational-management/questionnaires/responses/', params)

    responseList.value = response.list || []
    totalCount.value = response.total_count || 0
  } catch (error) {
    console.error('加载问卷回复列表失败:', error)
    ElMessage.error('加载问卷回复列表失败')
    responseList.value = []
    totalCount.value = 0
  } finally {
    loading.value = false
  }
}

const handleView = (row) => {
  selectedItemId.value = row.rid
  showDetailDialog.value = true
}

const handleRowClick = (row) => {
  selectedItemId.value = row.rid
  showDetailDialog.value = true
}

const handleDetailClose = () => {
  showDetailDialog.value = false
  selectedItemId.value = null
}

// 分页事件处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // 切换页码大小时重置到第一页
  loadResponseList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadResponseList()
}

// 重置分页并刷新数据
const resetPagination = () => {
  currentPage.value = 1
  loadResponseList()
}

// 刷新当前页数据
const refresh = () => {
  loadResponseList()
}

// 暴露给父组件的方法
defineExpose({
  resetPagination,
  refresh,
})

// 监听筛选条件变化
const loadData = () => {
  resetPagination()
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})

// 移除自动监听filters变化，改为手动触发搜索
</script>

<style scoped>
.response-table-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-header {
  transition: all 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

:deep(.el-table__row:hover) {
  background-color: #fdf2f8;
}

:deep(.el-pagination) {
  justify-content: flex-end;
}

.pagination-container {
  background-color: #f9fafb;
}
</style>
