<template>
  <div class="questionnaire-response-tab-container">
    <!-- 筛选搜索组件 -->
    <FilterPanel :fields="filterFields" :filters="filters" @search="handleSearch" class="mb-6" />

    <!-- 问卷回复表格 -->
    <QuestionnaireResponseTable ref="tableRef" :filters="filters" @refresh="handleSearch" />

    <!-- 详情对话框现在由QuestionnaireResponseTable组件内部管理 -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import FilterPanel from '@/components/FilterPanel.vue'
import QuestionnaireResponseTable from './QuestionnaireResponseTable.vue'
import { useBaseDataStore } from '@/stores/baseData.js'
import { QUESTIONNAIRE_TYPE_OPTIONS } from '@/utils/constants.js'
import { get } from '@/utils/request.js'

// 使用基础数据存储
const baseDataStore = useBaseDataStore()

// 响应式数据
const tableRef = ref(null)
// 移除detailDialogVisible等状态，现在由QuestionnaireResponseTable组件内部管理

// 问卷相关数据
const questionnaires = ref([])
const questionnairesLoading = ref(false)
const questionnaireSearchOptions = ref([])
const questionnaireSearchLoading = ref(false)

// 筛选条件
const filters = reactive({
  questionnaire_qid: '',
  customer_uid: '',
  search: '',
  questionnaire_type: '',
})

// 过滤器字段配置
const filterFields = computed(() => [
  {
    key: 'search',
    type: 'input',
    label: '关键字',
    placeholder: '搜索问卷标题/描述',
  },
  {
    key: 'questionnaire_qid',
    type: 'select',
    label: '选择问卷',
    placeholder: '输入问卷标题搜索',
    filterable: true,
    remote: true,
    remoteMethod: searchQuestionnaire,
    options:
      questionnaireSearchOptions.value.length > 0
        ? questionnaireSearchOptions.value
        : questionnaires.value.map((questionnaire) => ({
            label: questionnaire.title,
            value: questionnaire.qid,
          })),
    loading: questionnaireSearchLoading.value || questionnairesLoading.value,
    clearable: true,
    'reserve-keyword': true,
    'remote-show-suffix': true,
    'clear-method': handleClearQuestionnaire,
  },
  {
    key: 'customer_uid',
    type: 'select',
    label: '产妇',
    placeholder: '选择产妇',
    filterable: true,
    remote: true,
    remoteMethod: baseDataStore.newMothers.performSearch,
    options: baseDataStore.newMothers.getDisplayOptions(),
    loading: baseDataStore.newMothers.isLoading() || baseDataStore.newMothers.searchLoading,
    clearable: true,
    'reserve-keyword': true,
    'remote-show-suffix': true,
    'clear-method': handleClearCustomer,
  },
  {
    key: 'questionnaire_type',
    type: 'select',
    label: '问卷类型',
    placeholder: '选择问卷类型',
    options: QUESTIONNAIRE_TYPE_OPTIONS,
  },
])

// 方法
const handleSearch = () => {
  // 触发表格刷新数据
  if (tableRef.value) {
    tableRef.value.resetPagination()
  }
}

// 移除handleViewResponse方法，现在由QuestionnaireResponseTable内部处理详情查看

// 移除对话框相关方法，现在由QuestionnaireResponseTable组件内部管理

// 获取问卷列表（支持搜索）
const fetchQuestionnaires = async (options = {}) => {
  const { query, isSearch = false } = options

  const loadingRef = isSearch ? questionnaireSearchLoading : questionnairesLoading
  loadingRef.value = true

  try {
    const params = {
      page: 1,
      page_size: isSearch ? 50 : 100,
    }

    if (query && query.trim()) {
      params.search = query.trim()
    }

    const response = await get('organizational-management/questionnaires/list/', params)

    if (response?.list) {
      if (isSearch) {
        questionnaireSearchOptions.value = response.list.map((questionnaire) => ({
          label: questionnaire.title,
          value: questionnaire.qid,
        }))
      } else {
        questionnaires.value = response.list
      }
    } else {
      if (isSearch) {
        questionnaireSearchOptions.value = []
      } else {
        questionnaires.value = []
      }
    }
  } catch (error) {
    console.error(isSearch ? '搜索问卷失败:' : '获取问卷列表失败:', error)
    if (!isSearch) {
      ElMessage.error('获取问卷列表失败')
    }

    if (isSearch) {
      questionnaireSearchOptions.value = []
    } else {
      questionnaires.value = []
    }
  } finally {
    loadingRef.value = false
  }
}

// 搜索问卷
const searchQuestionnaire = async (query) => {
  if (!query || query.trim() === '') {
    questionnaireSearchOptions.value = []
    return
  }

  await fetchQuestionnaires({
    query: query,
    isSearch: true,
  })
}

// 添加清除搜索的方法
const handleClearCustomer = () => {
  baseDataStore.newMothers.clearSearch()
}

const handleClearQuestionnaire = () => {
  questionnaireSearchOptions.value = []
}

onMounted(() => {
  // 初始化数据
  baseDataStore.newMothers.fetch()
  fetchQuestionnaires()
})
</script>

<style scoped>
.questionnaire-response-tab-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
