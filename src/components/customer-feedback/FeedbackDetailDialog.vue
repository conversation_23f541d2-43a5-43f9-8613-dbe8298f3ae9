<template>
  <el-dialog
    v-model="visible"
    title="反馈详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="feedbackData" class="space-y-6">
        <!-- 基础信息 -->
        <div class="detail-section">
          <h3 class="section-title">基础信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>反馈编号：</label>
              <span>{{ feedbackData.fid }}</span>
            </div>
            <div class="detail-item">
              <label>产妇信息：</label>
              <span>{{ feedbackData.maternity_info }}</span>
            </div>
            <div class="detail-item">
              <label>反馈时间：</label>
              <span>{{ feedbackData.feedback_time }}</span>
            </div>
            <div class="detail-item">
              <label>反馈类型：</label>
              <el-tag :type="getFeedbackTypeTagType(feedbackData.feedback_type)" size="small">
                {{ feedbackData.feedback_type_display }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>处理状态：</label>
              <el-tag :type="getFeedbackStatusTagType(feedbackData.status)" size="small">
                {{ feedbackData.status_display }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ feedbackData.created_at }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ feedbackData.updated_at }}</span>
            </div>
          </div>
        </div>

        <!-- 反馈内容 -->
        <div class="detail-section">
          <h3 class="section-title">反馈内容</h3>
          <div class="reason-content p-4 bg-gray-50 rounded-lg">
            {{ feedbackData.content }}
          </div>
        </div>

        <!-- 处理记录 -->
        <div
          v-if="feedbackData.process_records && feedbackData.process_records.length > 0"
          class="detail-section"
        >
          <h3 class="section-title">处理记录</h3>
          <div class="space-y-3">
            <div
              v-for="record in feedbackData.process_records"
              :key="record.fid"
              class="p-3 bg-gray-50 rounded-lg"
            >
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">处理人：</span>
                  <span>{{ record.creator_name }}</span>
                </div>
                <div>
                  <span class="text-gray-600">处理时间：</span>
                  <span>{{ record.process_time }}</span>
                </div>
              </div>
              <div class="mt-2 text-sm text-gray-600">
                <span class="text-gray-600">处理内容：</span>{{ record.content }}
              </div>
            </div>
          </div>
        </div>

        <!-- 无处理记录时的提示 -->
        <div v-else class="detail-section">
          <h3 class="section-title">处理记录</h3>
          <div class="text-center py-8">
            <el-empty description="暂无处理记录" :image-size="100" />
          </div>
        </div>
      </div>
    </div>

    <template #footer> </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElDialog, ElTag, ElEmpty, ElMessage } from 'element-plus'
import { getFeedbackTypeTagType, getFeedbackStatusTagType } from '@/utils/constants.js'
import { get } from '@/utils/request.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: String,
    default: '',
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'refresh'])

// 响应式数据
const loading = ref(false)
const feedbackData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    loadFeedbackDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      feedbackData.value = null
    }, 300)
  }
})

// 获取详情数据
const loadFeedbackDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    const response = await get(`/organizational-management/feedback/${props.itemId}/`)
    feedbackData.value = response
  } catch (error) {
    console.error('加载反馈详情失败:', error)
    ElMessage.error('加载反馈详情失败')
    feedbackData.value = null
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.reason-content {
  color: #374151;
  line-height: 1.625;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
