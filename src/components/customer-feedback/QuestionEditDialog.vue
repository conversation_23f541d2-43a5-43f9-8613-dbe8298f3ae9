<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :before-close="handleClose"
    class="question-edit-dialog"
  >
    <div class="question-form">
      <div class="form-group">
        <label>题目类型：</label>
        <el-tag :type="getTypeTagType(questionType)" size="large">
          {{ getTypeLabel(questionType) }}
        </el-tag>
      </div>

      <div class="form-group">
        <label>题目内容：<span class="required">*</span></label>
        <el-input
          v-model="formData.question"
          type="textarea"
          :rows="3"
          placeholder="请输入题目内容"
          :maxlength="200"
          show-word-limit
        />
      </div>

      <div class="form-group">
        <label>是否必填：</label>
        <el-switch v-model="formData.required" active-text="必填" inactive-text="选填" />
      </div>

      <!-- 单选题/多选题选项 -->
      <div v-if="questionType === 'single' || questionType === 'multiple'" class="form-group">
        <label>选项设置：<span class="required">*</span></label>
        <div class="options-editor">
          <div v-for="(option, index) in formData.options" :key="index" class="option-item">
            <el-input
              v-model="formData.options[index]"
              placeholder="请输入选项内容"
              class="option-input"
            />
            <el-button
              type="danger"
              link
              @click="removeOption(index)"
              :disabled="formData.options.length <= 2"
            >
              删除
            </el-button>
          </div>
          <el-button type="primary" link @click="addOption" class="add-option-btn">
            <el-icon><Plus /></el-icon>
            添加选项
          </el-button>
        </div>
      </div>

      <!-- 评分题设置 -->
      <div v-if="questionType === 'rating'" class="form-group">
        <label>评分范围：</label>
        <div class="rating-config">
          <span>1 分 到 </span>
          <el-input-number
            v-model="formData.scale"
            :min="3"
            :max="10"
            size="small"
            style="width: 80px; margin: 0 8px"
          />
          <span> 分</span>
        </div>
      </div>

      <!-- 文本题设置 -->
      <div v-if="questionType === 'text'" class="form-group">
        <label>文本框提示：</label>
        <el-input v-model="formData.placeholder" placeholder="请输入文本框提示信息" />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          :disabled="!isFormValid"
        >
          {{ mode === 'create' ? '添加题目' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, reactive, watch } from 'vue'
import {
  ElDialog,
  ElInput,
  ElInputNumber,
  ElButton,
  ElSwitch,
  ElTag,
  ElIcon,
  ElMessage,
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  questionData: {
    type: Object,
    default: null,
  },
  questionType: {
    type: String,
    default: '',
  },
  mode: {
    type: String,
    default: 'create', // 'create' | 'edit'
  },
})

// Emits
const emit = defineEmits(['update:visible', 'save'])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  const typeLabel = getTypeLabel(props.questionType)
  return props.mode === 'create' ? `添加${typeLabel}` : `编辑${typeLabel}`
})

const isFormValid = computed(() => {
  if (!formData.question.trim()) return false

  if (props.questionType === 'single' || props.questionType === 'multiple') {
    return formData.options.length >= 2 && formData.options.every((opt) => opt.trim())
  }

  if (props.questionType === 'rating') {
    return formData.scale >= 3 && formData.scale <= 10
  }

  return true
})

// 响应式数据
const formData = reactive({
  question: '',
  required: true,
  options: ['', ''],
  scale: 5,
  placeholder: '',
})

// 监听题目数据变化
watch(
  () => props.questionData,
  (newData) => {
    if (newData) {
      formData.question = newData.question || ''
      formData.required = newData.required !== false
      formData.options = newData.options || ['', '']
      formData.scale = newData.scale || 5
      formData.placeholder = newData.placeholder || ''
    } else {
      // 重置表单
      formData.question = ''
      formData.required = true
      formData.options = ['', '']
      formData.scale = 5
      formData.placeholder = ''
    }
  },
  { immediate: true },
)

// 方法
const getTypeLabel = (type) => {
  const typeMap = {
    single: '单选题',
    multiple: '多选题',
    rating: '评分题',
    text: '文本题',
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    single: 'primary',
    multiple: 'success',
    rating: 'warning',
    text: 'info',
  }
  return typeMap[type] || 'info'
}

const addOption = () => {
  formData.options.push('')
}

const removeOption = (index) => {
  if (formData.options.length > 2) {
    formData.options.splice(index, 1)
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleSave = () => {
  if (!isFormValid.value) {
    ElMessage.warning('请填写完整的题目信息')
    return
  }

  const questionData = {
    question: formData.question.trim(),
    type: props.questionType,
    required: formData.required,
  }

  // 根据题目类型添加特定字段
  if (props.questionType === 'single' || props.questionType === 'multiple') {
    questionData.options = formData.options.filter((opt) => opt.trim()).map((opt) => opt.trim())
  } else if (props.questionType === 'rating') {
    questionData.scale = formData.scale
  } else if (props.questionType === 'text') {
    questionData.placeholder = formData.placeholder
  }

  // 如果是编辑模式，保留原有ID
  if (props.mode === 'edit' && props.questionData?.id) {
    questionData.id = props.questionData.id
  }

  emit('save', questionData)
}
</script>

<style scoped>
.question-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.required {
  color: #ef4444;
}

.options-editor {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.option-input {
  flex: 1;
}

.add-option-btn {
  align-self: flex-start;
  margin-top: 8px;
}

.rating-config {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #6b7280;
}

:deep(.question-edit-dialog) {
  .el-dialog__header {
    background-color: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
  }

  .el-dialog__title {
    color: #374151;
    font-weight: 600;
  }

  .el-dialog__body {
    padding: 24px;
  }

  .el-dialog__footer {
    background-color: #f8fafc;
    border-top: 1px solid #e5e7eb;
    padding: 16px 24px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
