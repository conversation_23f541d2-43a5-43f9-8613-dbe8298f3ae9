<template>
  <div class="health-education-form">
    <div class="form-header mb-6">
      <h3
        :class="
          mode == 'page'
            ? 'text-2xl font-bold text-gray-900 mb-2'
            : 'text-lg font-semibold text-gray-800 mb-2'
        "
      >
        健康宣教记录
      </h3>
      <p class="text-sm text-gray-600">健康教育内容库管理和宣教活动记录</p>
    </div>

    <!-- 标签页导航 -->
    <el-tabs
      v-model="activeTab"
      :class="mode == 'page' ? 'education-tabs bg-white rounded-lg shadow p-6' : 'education-tabs'"
    >
      <!-- 健康宣教内容库 -->
      <el-tab-pane label="健康宣教内容库" name="contentLibrary">
        <div class="content-library-section">
          <!-- 筛选区域 -->
          <FilterPanel
            v-if="mode == 'page'"
            :fields="contentFilterFields"
            :filters="contentFilters"
            @search="searchContent"
          />

          <!-- 操作按钮 -->
          <div class="action-buttons flex justify-between items-center mb-4">
            <el-button
              type="primary"
              @click="showContentForm"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              <Plus class="w-4 h-4 mr-2" />
              新增宣教内容
            </el-button>
            <el-button @click="exportContentList">
              <Download class="w-4 h-4 mr-2" />
              导出内容列表 (PDF)
            </el-button>
          </div>

          <!-- 内容库列表 -->
          <EducationContentTable
            :data="contentLibrary"
            :loading="contentLoading"
            :pagination="contentPagination"
            @view="viewContent"
            @edit="editContent"
            @delete="deleteContent"
            @size-change="handleContentSizeChange"
            @current-change="handleContentCurrentChange"
          />
        </div>
      </el-tab-pane>

      <!-- 宣教活动记录 -->
      <el-tab-pane label="宣教活动记录" name="records">
        <div class="records-section">
          <!-- 筛选区域 -->
          <FilterPanel
            :fields="recordFilterFields"
            :filters="recordFilters"
            @search="searchRecords"
          />

          <!-- 操作按钮 -->
          <div class="action-buttons flex justify-between items-center mb-4">
            <el-button
              type="primary"
              @click="showRecordForm"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              <Plus class="w-4 h-4 mr-2" />
              新增宣教活动记录
            </el-button>
            <el-button @click="exportRecords">
              <Download class="w-4 h-4 mr-2" />
              导出活动记录 (PDF)
            </el-button>
          </div>

          <!-- 记录列表 -->
          <EducationRecordTable
            :data="educationRecords"
            :loading="recordLoading"
            :pagination="educationRecordsPagination"
            @size-change="handleRecordSizeChange"
            @current-change="handleRecordCurrentChange"
            @view="viewRecord"
            @edit="editRecord"
            @delete="deleteRecord"
          />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 内容库对话框 -->
    <EducationContentDialog
      v-model:visible="showContentDialog"
      :content-data="currentContentData"
      :mode="contentDialogMode"
      @save="handleContentSave"
    />

    <!-- 内容详情对话框 -->
    <EducationContentDetailDialog
      v-model="showContentDetailDialog"
      :content-data="currentContentData"
      :loading="detailLoading"
      @edit="editContentFromDetail"
    />

    <!-- 记录对话框 -->
    <EducationRecordDialog
      v-model:visible="showRecordDialog"
      :record-data="currentRecordData"
      :mode="recordDialogMode"
      @save="handleRecordSave"
    />

    <!-- 记录详情对话框 -->
    <EducationRecordDetailDialog
      v-model="showRecordDetailDialog"
      :record-data="currentRecordData"
      :loading="recordLoading"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import EducationContentDialog from './EducationContentDialog.vue'
import EducationContentDetailDialog from './EducationContentDetailDialog.vue'
import EducationRecordDialog from './EducationRecordDialog.vue'
import EducationRecordDetailDialog from './EducationRecordDetailDialog.vue'
import FilterPanel from '@/components/FilterPanel.vue'
import EducationContentTable from './EducationContentTable.vue'
import EducationRecordTable from './EducationRecordTable.vue'
import { get, del, post, put } from '@/utils/request.js'

// 定义props
const props = defineProps({
  mode: {
    type: String,
    default: 'form',
    validator: (value) => ['page', 'form'].includes(value),
  },
})

const activeTab = ref('contentLibrary')

// 加载状态
const contentLoading = ref(false)
const recordLoading = ref(false)

// 对话框控制
const showContentDialog = ref(false)
const showContentDetailDialog = ref(false)
const showRecordDialog = ref(false)
const showRecordDetailDialog = ref(false)
const currentContentData = ref(null)
const currentRecordData = ref(null)
const contentDialogMode = ref('add')
const recordDialogMode = ref('add')
const detailLoading = ref(false)

// 内容库筛选条件
const contentFilters = reactive({
  sk: '',
  formats: '',
})

// 内容库过滤器字段配置
const contentFilterFields = [
  {
    key: 'sk',
    type: 'input',
    label: '标题/关键词/分类',
    placeholder: '搜索标题或关键词...',
  },
  {
    key: 'formats',
    type: 'select',
    label: '格式',
    placeholder: '选择格式',
    options: [
      { label: '文字', value: 'TEXT' },
      { label: '文档', value: 'DOCUMENT' },
      { label: '视频', value: 'VIDEO' },
      { label: '图片', value: 'IMAGE' },
      { label: '音频', value: 'AUDIO' },
      { label: 'PPT', value: 'PPT' },
      { label: '其他', value: 'OTHER' },
    ],
  },
]

// 记录筛选条件
const recordFilters = reactive({
  topic: '',
  target: '',
  date: '',
  executor: '',
  method: '',
})

// 记录过滤器字段配置
const recordFilterFields = [
  {
    key: 'topic',
    type: 'input',
    label: '宣教主题',
    placeholder: '搜索主题...',
  },
  {
    key: 'target',
    type: 'input',
    label: '宣教对象',
    placeholder: '产妇姓名/房号',
  },
  {
    key: 'date',
    type: 'date',
    label: '宣教日期',
    placeholder: '选择日期',
  },
  {
    key: 'executor',
    type: 'input',
    label: '执行人',
    placeholder: '执行人姓名',
  },
  {
    key: 'method',
    type: 'select',
    label: '宣教方式',
    placeholder: '选择方式',
    options: [
      { label: '一对一指导', value: 'ONE_ON_ONE_GUIDANCE' },
      { label: '集体讲座', value: 'GROUP_LECTURE' },
      { label: '资料发放', value: 'MATERIAL_DISTRIBUTION' },
      { label: '视频观看', value: 'VIDEO_WATCHING' },
      { label: '演示指导', value: 'DEMONSTRATION_GUIDANCE' },
    ],
  },
]

// 内容库数据
const contentLibrary = ref([])
const contentPagination = reactive({
  page: 1,
  page_size: 10,
  total_count: 0,
  total_page: 0,
})

// 宣教记录列表
const educationRecords = ref([])
const educationRecordsPagination = reactive({
  page: 1,
  page_size: 10,
  total_count: 0,
  total_page: 0,
})

// 加载内容库数据
const loadContentLibrary = async () => {
  contentLoading.value = true
  try {
    const requestParams = {
      ...contentFilters,
      page: contentPagination.page,
      page_size: contentPagination.page_size,
    }

    const data = await get('customer-service/health-education/content/list/', requestParams)
    contentLibrary.value = data.list
    contentPagination.page = data.page
    contentPagination.page_size = data.page_size
    contentPagination.total_count = data.total_count
    contentPagination.total_page = data.total_page
  } catch (error) {
    console.error('获取健康宣教内容列表失败:', error)
    ElMessage.error('获取健康宣教内容列表失败')
    contentLibrary.value = []
    contentPagination.total_count = 0
  } finally {
    contentLoading.value = false
  }
}

// 内容库相关方法
const searchContent = () => {
  contentPagination.page = 1
  loadContentLibrary()
}

const showContentForm = () => {
  currentContentData.value = null
  contentDialogMode.value = 'add'
  showContentDialog.value = true
}

const viewContent = async (content) => {
  try {
    detailLoading.value = true
    const data = await get(`customer-service/health-education/content/detail/${content.rid}/`)
    currentContentData.value = data
    showContentDetailDialog.value = true
  } catch (error) {
    console.error('获取内容详情失败:', error)
    ElMessage.error('获取内容详情失败')
  } finally {
    detailLoading.value = false
  }
}

const editContent = async (content) => {
  try {
    const data = await get(`customer-service/health-education/content/detail/${content.rid}/`)
    currentContentData.value = data
    contentDialogMode.value = 'edit'
    showContentDialog.value = true
  } catch (error) {
    console.error('获取内容详情失败:', error)
    ElMessage.error('获取内容详情失败')
  }
}

const editContentFromDetail = (content) => {
  showContentDetailDialog.value = false
  contentDialogMode.value = 'edit'
  showContentDialog.value = true
}

const deleteContent = async (content) => {
  try {
    await ElMessageBox.confirm(`确定要删除"${content.title}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await del(`customer-service/health-education/content/delete/${content.rid}/`)
    ElMessage.success('删除成功')

    // 刷新列表
    loadContentLibrary()
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除内容失败:', error)
    ElMessage.error('删除内容失败')
  }
}

const handleContentSave = () => {
  // 刷新列表
  loadContentLibrary()
  showContentDialog.value = false
}

const exportContentList = () => {
  ElMessage.info('正在导出内容库...')
}

// 分页事件处理
const handleContentSizeChange = (size) => {
  contentPagination.page_size = size
  contentPagination.page = 1
  loadContentLibrary()
}

const handleContentCurrentChange = (page) => {
  contentPagination.page = page
  loadContentLibrary()
}

// 记录相关方法
const searchRecords = async () => {
  educationRecordsPagination.page = 1
  loadEducationRecords()
}

const loadEducationRecords = async () => {
  educationRecordsPagination.page = 1
  const params = {
    sk: recordFilters.topic,
    app: recordFilters.target,
    apt: recordFilters.date,
    executor: recordFilters.executor,
    way: recordFilters.method,
    page: educationRecordsPagination.page,
    page_size: educationRecordsPagination.page_size,
  }

  console.log('搜索 params', params)
  try {
    const response = await get('customer-service/health-education/record/list/', params)

    educationRecords.value = response?.list || []
    educationRecordsPagination.page = response?.page
    educationRecordsPagination.page_size = response?.page_size
    educationRecordsPagination.total_count = response?.total_count
    educationRecordsPagination.total_page = response?.total_page
  } catch (error) {
    console.error('获取宣教记录列表失败:', error)
    ElMessage.error('获取宣教记录列表失败')
    educationRecords.value = []
    educationRecordsPagination.total_count = 0
  } finally {
    recordLoading.value = false
  }
}

const handleRecordSizeChange = (size) => {
  educationRecordsPagination.page_size = size
  loadEducationRecords()
}

const handleRecordCurrentChange = (page) => {
  educationRecordsPagination.page = page
  loadEducationRecords()
}

const showRecordForm = () => {
  currentRecordData.value = null
  recordDialogMode.value = 'add'
  showRecordDialog.value = true
}

const viewRecord = async (record) => {
  try {
    showRecordDetailDialog.value = true
    const data = await get(`customer-service/health-education/record/detail/${record.eid}/`)
    currentRecordData.value = data
    recordLoading.value = false
  } catch (err) {
    console.error('获取宣教记录详情失败', err)
    ElMessage.error('获取宣教记录详情失败')
  } finally {
    recordLoading.value = false
  }
}

const editRecord = async (record) => {
  try {
    recordDialogMode.value = 'edit'
    recordLoading.value = true
    const data = await get(
      `customer-service/health-education/record/detail/${record.id || record.eid}/`,
    )
    const mapped = {
      id: data.eid,
      topic: data.content,
      targetClient: data.admission || '',
      method: data.way,
      datetime: data.edu_time ? new Date(data.edu_time) : null,
      location: data.edu_place,
      executor: data.executor,
      category: data.content_category,
      contentSummary: data.content_summary,
      keyPoints: data.key_points,
      materials: data.materials_tools,
      feedback: data.feedback,
      comprehensionScore: data.understanding_score,
      participation: data.participation_evaluation,
      followUpPlan: data.follow_up_plan,
      notes: data.remark,
    }
    currentRecordData.value = mapped
    showRecordDialog.value = true
  } catch (error) {
    ElMessage.error('获取宣教记录详情失败')
    console.error(error)
  } finally {
    recordLoading.value = false
  }
}

const deleteRecord = async (record) => {
  try {
    await ElMessageBox.confirm(`确定要删除"${record.content_title}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    await del(`customer-service/health-education/record/delete/${record.id || record.eid}/`)
    ElMessage.success('删除成功')
    loadEducationRecords()
  } catch (error) {
    if (error === 'cancel') {
      return
    }
    console.error('删除记录失败:', error)
    ElMessage.error('删除记录失败')
  }
}

const handleRecordSave = async (data) => {
  if (recordDialogMode.value === 'add') {
    // 添加新记录到列表
    const newRecord = {
      id: `EDU${String(educationRecords.value.length + 1).padStart(3, '0')}`,
      target: data.targetClient,
      ...data,
    }
    educationRecords.value.unshift(newRecord)

    try {
      const response = await post('customer-service/health-education/record/create/', data)
      ElMessage.success('宣教记录添加成功！')
    } catch (error) {
      console.error('宣教记录添加失败:', error)
      ElMessage.error('宣教记录添加失败！')
    }
  } else {
    try {
      const response = await put(
        `customer-service/health-education/record/update/${currentRecordData.value.id}/`,
        data,
      )
      ElMessage.success('宣教记录更新成功')
    } catch (error) {
      console.error('宣教记录更新失败:', error)
      ElMessage.error('宣教记录更新失败！')
    }
  }
  showRecordDialog.value = false
  loadEducationRecords()
}

const exportRecords = () => {
  ElMessage.info('正在导出宣教记录...')
}

// 监听筛选条件变化
watch(
  () => contentFilters,
  () => {
    searchContent()
  },
  { deep: true },
)

// 监听记录筛选条件变化
watch(
  () => recordFilters,
  () => {
    searchRecords()
  },
  { deep: true },
)

// 组件挂载时加载数据
onMounted(() => {
  loadContentLibrary()
  loadEducationRecords()
})
</script>

<style scoped>
.education-tabs :deep(.el-tabs__header) {
  border-bottom: 2px solid #f3a0c0;
}

.education-tabs :deep(.el-tabs__active-bar) {
  background-color: #f3a0c0;
}

.education-tabs :deep(.el-tabs__item.is-active) {
  color: #e77fa1;
}
</style>
