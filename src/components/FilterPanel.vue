<template>
  <div class="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 mb-6">
    <!-- 过滤器主体 -->
    <div class="flex gap-3">
      <!-- 动态渲染过滤字段 -->
      <div
        v-for="field in fields"
        :key="getFieldKey(field)"
        class="filter-item min-w-10"
        :class="getFieldClasses(field)"
      >
        <label class="block text-sm font-medium text-gray-700 mb-2">
          {{ field.label }}
        </label>

        <!-- 输入框类型 -->
        <el-input
          v-if="field.type === 'input'"
          v-model="localFilters[getFieldKey(field)]"
          :placeholder="field.placeholder || `输入${field.label}`"
          clearable
          @input="(value) => handleFilterChange(field, value)"
          @keyup.enter="handleSearch"
          class="w-[178px]!"
        />

        <!-- 选择器类型 -->
        <el-select
          v-else-if="field.type === 'select'"
          v-model="localFilters[getFieldKey(field)]"
          :placeholder="field.placeholder || '全部'"
          :loading="field.loading || false"
          :filterable="field.filterable || false"
          :remote="field.remote || false"
          :remote-method="field.remoteMethod"
          clearable
          class="w-[178px]!"
          @change="(value) => handleFilterChange(field, value)"
        >
          <el-option
            v-for="option in field.options"
            :style="{ width: '100%' }"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <!-- 日期选择器 -->
        <el-date-picker
          v-else-if="field.type === 'date'"
          v-model="localFilters[getFieldKey(field)]"
          type="date"
          :placeholder="field.placeholder || '选择日期'"
          @change="(value) => handleFilterChange(field, value)"
          class="w-[178px]"
          :editable="false"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />

        <!-- 日期时间选择器 -->
        <el-date-picker
          v-else-if="field.type === 'datetime'"
          v-model="localFilters[getFieldKey(field)]"
          type="datetime"
          :placeholder="field.placeholder || '选择日期时间'"
          @change="(value) => handleFilterChange(field, value)"
          class="w-[185px]!"
          :editable="false"
          :clearable="false"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />

        <!-- 日期范围选择器 -->
        <el-date-picker
          v-else-if="field.type === 'dateRange'"
          v-model="localFilters[getFieldKey(field)]"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="(value) => handleFilterChange(field, value)"
          class="max-w-60 !min-w-10 !w-full"
          :editable="false"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />

        <!-- 数字输入框 -->
        <el-input-number
          v-else-if="field.type === 'number'"
          v-model="localFilters[getFieldKey(field)]"
          :placeholder="field.placeholder || `输入${field.label}`"
          class="w-full"
          @change="(value) => handleFilterChange(field, value)"
          :min="field.min"
          :max="field.max"
          :precision="field.precision"
        />

        <!-- 多选框 -->
        <el-select
          v-else-if="field.type === 'multiSelect'"
          v-model="localFilters[getFieldKey(field)]"
          :placeholder="field.placeholder || '全部'"
          :loading="field.loading || false"
          multiple
          clearable
          class="w-full"
          @change="(value) => handleFilterChange(field, value)"
        >
          <el-option
            v-for="option in field.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </div>

      <!-- 搜索按钮 -->
      <div class="filter-item flex items-end w-20 flex-shrink-0 min-w-20">
        <el-button
          @click="handleSearch"
          type="primary"
          class="w-full bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          <el-icon class="mr-2">
            <Search />
          </el-icon>
          搜索
        </el-button>
      </div>
    </div>

    <!-- 活跃过滤器标签和清空按钮 -->
    <div v-if="hasActiveFilters" class="mt-4 flex items-center">
      <span class="text-sm text-gray-600">当前筛选：</span>
      <div class="flex flex-wrap gap-2">
        <el-tag
          v-for="tag in activeFilterTags"
          :key="tag.key"
          closable
          @close="clearFilter(tag.key)"
          class="filter-tag"
          type="info"
        >
          {{ tag.label }}：{{ tag.value }}
        </el-tag>
      </div>
      <el-button
        @click="clearAllFilters"
        class="text-gray-600 border-gray-300 hover:text-pink-500 hover:border-pink-300 ml-5"
        type="default"
        size="small"
      >
        清空筛选
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, computed, ref } from 'vue'
import {
  ElInput,
  ElSelect,
  ElOption,
  ElDatePicker,
  ElButton,
  ElIcon,
  ElTag,
  ElInputNumber,
} from 'element-plus'
import { Search } from '@element-plus/icons-vue'

// 防抖定时器
let debounceTimer = null

const props = defineProps({
  // 过滤器字段配置
  fields: {
    type: Array,
    required: true,
    validator: (fields) => {
      return fields.every(
        (field) =>
          field.key &&
          field.label &&
          field.type &&
          ['input', 'select', 'date', 'datetime', 'dateRange', 'number', 'multiSelect'].includes(field.type),
      )
    },
  },
  // 过滤器数据
  filters: {
    type: Object,
    required: true,
  },
})

const emit = defineEmits(['search'])

// 初始化本地过滤器数据
const localFilters = reactive({})

// 初始化过滤器默认值
const initFilters = () => {
  props.fields.forEach((field) => {
    if (field.type === 'dateRange' && Array.isArray(field.key)) {
      // 如果是dateRange类型且key是数组，从两个属性组合成日期范围
      const [startKey, endKey] = field.key
      const startValue = props.filters[startKey]
      const endValue = props.filters[endKey]
      if (startValue && endValue) {
        localFilters[`${startKey}_${endKey}`] = [startValue, endValue]
      } else {
        localFilters[`${startKey}_${endKey}`] = []
      }
    } else if (props.filters[field.key] !== undefined) {
      localFilters[field.key] = props.filters[field.key]
    } else {
      // 根据字段类型设置默认值
      switch (field.type) {
        case 'multiSelect':
          localFilters[field.key] = []
          break
        case 'dateRange':
          localFilters[field.key] = []
          break
        default:
          localFilters[field.key] = ''
      }
    }
  })
}

// 获取字段样式类
const getFieldClasses = (field) => {
  const baseClasses = []

  // 根据字段类型设置特殊样式
  switch (field.type) {
    case 'dateRange':
      // baseClasses.push('min-w-48')
      break
    case 'multiSelect':
      // baseClasses.push('min-w-40')
      break
    default:
      break
  }

  // 自定义宽度类
  if (field.width) {
    baseClasses.push(field.width)
  }

  return baseClasses
}

// 检查是否有活跃的过滤器
const hasActiveFilters = computed(() => {
  return Object.entries(localFilters).some(([key, value]) => {
    if (value === null || value === undefined) return false
    if (Array.isArray(value)) return value.length > 0
    return String(value).trim() !== ''
  })
})

// 活跃过滤器标签
const activeFilterTags = computed(() => {
  // 创建字段映射，支持数组key
  const fieldMap = new Map()
  props.fields.forEach((f) => {
    if (Array.isArray(f.key)) {
      const [startKey, endKey] = f.key
      fieldMap.set(`${startKey}_${endKey}`, f)
    } else {
      fieldMap.set(f.key, f)
    }
  })

  return Object.entries(localFilters)
    .filter(([key, value]) => {
      const field = fieldMap.get(key)
      if (!field) return false
      if (value === null || value === undefined) return false
      if (Array.isArray(value)) return value.length > 0
      return String(value).trim() !== ''
    })
    .map(([key, value]) => {
      const field = fieldMap.get(key)
      let displayValue = value

      // 处理不同类型的显示值
      if (field.type === 'select' || field.type === 'multiSelect') {
        if (Array.isArray(value)) {
          displayValue = value
            .map((v) => {
              const option = field.options?.find((opt) => opt.value === v)
              return option ? option.label : v
            })
            .join(', ')
        } else {
          const option = field.options?.find((opt) => opt.value === value)
          displayValue = option ? option.label : value
        }
      } else if (field.type === 'dateRange' && Array.isArray(value)) {
        displayValue = value.join(' 至 ')
      }

      return {
        key,
        label: field.label,
        value: displayValue,
      }
    })
})

// 监听外部filters变化
watch(
  () => props.filters,
  (newFilters) => {
    props.fields.forEach((field) => {
      if (field.type === 'dateRange' && Array.isArray(field.key)) {
        // 如果是dateRange类型且key是数组，从两个属性组合成日期范围
        const [startKey, endKey] = field.key
        const localKey = `${startKey}_${endKey}`
        const startValue = newFilters[startKey]
        const endValue = newFilters[endKey]
        if (startValue && endValue) {
          localFilters[localKey] = [startValue, endValue]
        } else {
          localFilters[localKey] = []
        }
      } else if (newFilters[field.key] !== undefined) {
        localFilters[field.key] = newFilters[field.key]
      }
    })
  },
  { deep: true },
)

// 监听fields变化，重新初始化
watch(
  () => props.fields,
  () => {
    initFilters()
  },
  { immediate: true },
)

const handleFilterChange = (field = null, value = null) => {
  // 更新所有字段的数据
  updateAllFilters()

  // 如果指定了字段且该字段启用了自动搜索
  if (field && field.autoSearch) {
    if (field.type === 'input') {
      // 输入框使用防抖
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
      debounceTimer = setTimeout(() => {
        handleSearch()
      }, 500) // 500ms 防抖
    } else {
      // 选择器立即搜索
      handleSearch()
    }
  }
}

// 更新所有过滤器数据
const updateAllFilters = () => {
  // 处理特殊字段类型
  props.fields.forEach((field) => {
    if (field.type === 'dateRange' && Array.isArray(field.key)) {
      // 如果是dateRange类型且key是数组，将日期范围拆分为两个属性
      const [startKey, endKey] = field.key
      const localKey = `${startKey}_${endKey}`
      const dateRange = localFilters[localKey]
      if (Array.isArray(dateRange) && dateRange.length === 2) {
        props.filters[startKey] = dateRange[0]
        props.filters[endKey] = dateRange[1]
      } else {
        props.filters[startKey] = ''
        props.filters[endKey] = ''
      }
    } else {
      // 普通字段直接更新，处理null值
      let value = localFilters[field.key]

      // 如果是null或undefined，根据字段类型设置默认空值
      if (value === null || value === undefined) {
        switch (field.type) {
          case 'multiSelect':
            value = []
            break
          default:
            value = ''
        }
        // 同步更新localFilters
        localFilters[field.key] = value
      }

      props.filters[field.key] = value
    }
  })
}

const handleSearch = () => {
  emit('search')
}

// 清空所有过滤器
const clearAllFilters = () => {
  props.fields.forEach((field) => {
    if (field.type === 'dateRange' && Array.isArray(field.key)) {
      // 如果是dateRange类型且key是数组，清空两个属性
      const [startKey, endKey] = field.key
      const localKey = `${startKey}_${endKey}`
      localFilters[localKey] = []
      props.filters[startKey] = ''
      props.filters[endKey] = ''
    } else {
      switch (field.type) {
        case 'multiSelect':
        case 'dateRange':
          localFilters[field.key] = []
          break
        default:
          localFilters[field.key] = ''
      }
      if (props.filters[field.key] !== undefined) {
        props.filters[field.key] = localFilters[field.key]
      }
    }
  })
}

// 清除单个过滤器
const clearFilter = (key) => {
  const field = props.fields.find((f) => {
    if (Array.isArray(f.key)) {
      const [startKey, endKey] = f.key
      return `${startKey}_${endKey}` === key
    }
    return f.key === key
  })

  if (field) {
    if (field.type === 'dateRange' && Array.isArray(field.key)) {
      // 如果是dateRange类型且key是数组，清空两个属性
      const [startKey, endKey] = field.key
      const localKey = `${startKey}_${endKey}`
      localFilters[localKey] = []
      props.filters[startKey] = ''
      props.filters[endKey] = ''
    } else {
      switch (field.type) {
        case 'multiSelect':
        case 'dateRange':
          localFilters[key] = []
          break
        default:
          localFilters[key] = ''
      }
      if (props.filters[key] !== undefined) {
        props.filters[key] = localFilters[key]
      }
    }
  }
}

// 获取字段的本地key
const getFieldKey = (field) => {
  if (Array.isArray(field.key)) {
    const [startKey, endKey] = field.key
    return `${startKey}_${endKey}`
  }
  return field.key
}
</script>

<style scoped>
/* 容器悬停效果 */
.bg-white:hover {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: rgb(251 207 232);
}

/* 统一的输入框样式 */
:deep(.el-input__wrapper),
:deep(.el-select .el-input__wrapper),
:deep(.el-date-picker .el-input__wrapper),
:deep(.el-input-number .el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover),
:deep(.el-select:hover .el-input__wrapper),
:deep(.el-date-picker:hover .el-input__wrapper),
:deep(.el-input-number:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

/* 过滤器容器 */
.filter-item {
  flex-shrink: 1;
}

/* 平板端优化 */
@media (max-width: 768px) {
  .filter-item {
    min-width: 8rem !important;
  }

  .filter-item.flex-shrink-0 {
    min-width: 5rem !important;
  }
}

@media (max-width: 639px) {
  .flex {
    flex-direction: column;
  }

  .filter-item {
    min-width: 100% !important;
    flex: none;
  }

  :deep(.el-input__inner),
  :deep(.el-select .el-input__inner),
  :deep(.el-date-picker .el-input__inner),
  :deep(.el-input-number .el-input__inner) {
    font-size: 14px;
  }
}
</style>
