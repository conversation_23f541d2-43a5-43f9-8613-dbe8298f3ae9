<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="referral-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto" v-loading="loading">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" class="referral-form">
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">基本信息</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="客户选择" prop="maternity_admission">
              <el-select
                v-model="form.maternity_admission"
                placeholder="请选择客户"
                class="w-full"
                :loading="
                  baseDataStore.newMothers.isLoading() || baseDataStore.newMothers.searchLoading
                "
                filterable
                remote
                :remote-method="baseDataStore.newMothers.performSearch"
                :clearable="true"
                reserve-keyword
                remote-show-suffix
                :disabled="!!props.referralId"
                @clear="baseDataStore.newMothers.clearSearch"
              >
                <el-option
                  v-for="option in baseDataStore.newMothers.getDisplayOptions()"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>

            <!-- 新生儿选择（仅新生儿类型显示） -->
            <el-form-item label="新生儿选择" prop="baby_id" v-if="referralType === 'newborn'">
              <el-select
                v-model="form.baby_id"
                placeholder="请选择新生儿"
                class="w-full"
                :disabled="!form.maternity_admission || !!props.referralId"
              >
                <el-option
                  v-for="baby in availableBabies"
                  :key="baby.value"
                  :label="baby.label"
                  :value="baby.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 转诊信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">转诊信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="转诊科室" prop="referral_department">
              <el-input
                v-model="form.referral_department"
                placeholder="请输入转诊科室"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="紧急程度" prop="emergency_level">
              <el-select v-model="form.emergency_level" placeholder="请选择紧急程度" class="w-full">
                <el-option
                  v-for="option in EMERGENCY_LEVEL_OPTIONS"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </div>

          <el-form-item label="主要症状/原因" prop="main_symptoms_and_reasons">
            <el-input
              v-model="form.main_symptoms_and_reasons"
              type="textarea"
              :rows="4"
              placeholder="请详细描述症状或转诊原因"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="生命体征" prop="vital_signs">
            <el-input
              v-model="form.vital_signs"
              type="textarea"
              :rows="3"
              placeholder="体温、脉搏、呼吸、血压等"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="相关病史" prop="related_medical_history">
            <el-input
              v-model="form.related_medical_history"
              type="textarea"
              :rows="3"
              placeholder="请输入相关病史信息"
              maxlength="300"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="相关检查资料" prop="related_inspection_information">
            <FileUpload
              :file-types="['jpg', 'png', 'pdf', 'doc', 'docx']"
              :max-size="10"
              :multiple="true"
              :limit="10"
              v-model="form.related_inspection_information"
              :urls="form.related_inspection_information_urls"
              action="file/medical-referral/file/upload/"
              field="medical_referral_file"
              upload-text="上传相关检查资料"
              custom-tip-text="支持JPG、PNG、PDF、DOC、DOCX格式，最多上传10个文件，每个文件不超过10MB"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ props.referralId ? '保存修改' : '提交申请' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, nextTick, computed } from 'vue'
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
} from 'element-plus'
import { post, put, get } from '@/utils/request.js'
import { useBaseDataStore } from '@/stores/baseData.js'
import { useDialogScrollToTop } from '@/composables/useScrollToTop.js'
import { EMERGENCY_LEVEL_OPTIONS } from '@/utils/constants.js'
import FileUpload from '@/components/FileUpload.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  referralId: {
    type: [String, Number],
    default: null,
  },
  referralType: {
    type: String,
    default: 'maternal', // 'maternal' | 'newborn'
    validator: (value) => ['maternal', 'newborn'].includes(value),
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

// 基础数据store
const baseDataStore = useBaseDataStore()

// 滚动到顶部的组合函数
const { scrollToTop } = useDialogScrollToTop()

const submitting = ref(false)
const formRef = ref()
const loading = ref(false)
const availableBabies = ref([])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const dialogTitle = computed(() => {
  const typeText = props.referralType === 'maternal' ? '产妇' : '新生儿'
  return props.referralId ? `编辑${typeText}转诊申请` : `新增${typeText}转诊申请`
})

// 表单数据
const form = reactive({
  rid: '',
  maternity_admission: '',
  baby_id: '', // 仅新生儿类型使用
  referral_department: '',
  main_symptoms_and_reasons: '',
  emergency_level: '',
  vital_signs: '',
  related_medical_history: '',
  related_inspection_information: [],
})

// 表单验证规则
const rules = computed(() => ({
  maternity_admission: !props.referralId
    ? [{ required: true, message: '请选择客户', trigger: 'change' }]
    : [],
  baby_id:
    props.referralType === 'newborn' && !props.referralId
      ? [{ required: true, message: '请选择新生儿', trigger: 'change' }]
      : [],
  referral_department: [{ required: true, message: '请选择转诊科室', trigger: 'change' }],
  main_symptoms_and_reasons: [{ required: true, message: '请输入症状描述', trigger: 'blur' }],
  emergency_level: [{ required: true, message: '请选择紧急程度', trigger: 'change' }],
  vital_signs: [{ required: true, message: '请输入生命体征', trigger: 'blur' }],
  related_medical_history: [{ required: true, message: '请输入相关病史', trigger: 'blur' }],
}))

// 监听弹窗显示状态，处理编辑模式数据获取
watch(
  () => props.modelValue,
  async (visible) => {
    if (visible) {
      setTimeout(resetForm, 5)
      if (props.referralId) {
        // 编辑模式：获取详情数据
        await fetchReferralDetail(props.referralId)
      }
      scrollToTop()
    } else {
      resetForm()
    }
  },
)

// 监听产妇选择变化，获取新生儿列表
watch(
  () => form.maternity_admission,
  async (maternityId, oldMaternityId) => {
    // 只有当之前有值时才重置baby_id，避免初始化时清空
    if (oldMaternityId) {
      form.baby_id = ''
    }

    if (maternityId && props.referralType === 'newborn') {
      await fetchBabies(maternityId)
    } else {
      availableBabies.value = []
    }
  },
)

// 获取新生儿列表
const fetchBabies = async (maternityId) => {
  try {
    const response = await get(`customer-service/newborn/list/?aid=${maternityId}`)
    const babies = response.list || []
    availableBabies.value = babies.map((baby) => ({
      label: baby.name || `宝宝${baby.nid}`,
      value: baby.nid,
      origin: baby,
    }))

    // 如果只有一个新生儿，默认选中
    if (babies.length === 1) {
      form.baby_id = babies[0].nid
    }
  } catch (error) {
    console.error('获取新生儿列表失败:', error)
    availableBabies.value = []
  }
}

// 获取转诊申请详情数据（用于编辑模式）
const fetchReferralDetail = async (referralId) => {
  if (!referralId) return

  loading.value = true
  try {
    const apiPath =
      props.referralType === 'maternal'
        ? `customer-service/maternity-medical-referral/detail/${referralId}/`
        : `customer-service/newborn-medical-referral/detail/${referralId}/`

    const response = await get(apiPath)
    if (response) {
      // 转换接口数据为表单格式并填充
      const processedData = transformAPIDataToForm(response)
      Object.assign(form, processedData)
    } else {
      ElMessage.error('未获取到转诊申请详情')
      visible.value = false
    }
  } catch (error) {
    console.error('获取转诊申请详情失败:', error)
    ElMessage.error('获取转诊申请详情失败，请稍后重试')
    visible.value = false
  } finally {
    loading.value = false
  }
}

// 数据转换函数：将接口数据转换为表单格式
const transformAPIDataToForm = (apiData) => {
  if (!apiData) return {}

  return {
    rid: apiData.rid,
    maternity_admission: apiData.aid,
    baby_id: apiData.nid,
    referral_department: apiData.referral_department || '',
    main_symptoms_and_reasons: apiData.main_symptoms_and_reasons || '',
    emergency_level: apiData.emergency_level || '',
    vital_signs: apiData.vital_signs || '',
    related_medical_history: apiData.related_medical_history || '',
    related_inspection_information: apiData.related_inspection_information || [],
  }
}

const handleClose = async () => {
  visible.value = false
}

const resetForm = async () => {
  // 重置表单到初始状态
  Object.assign(form, {
    rid: '',
    maternity_admission: '',
    baby_id: '',
    referral_department: '',
    main_symptoms_and_reasons: '',
    emergency_level: '',
    vital_signs: '',
    related_medical_history: '',
    related_inspection_information: [],
  })

  // 重置搜索状态
  baseDataStore.newMothers.clearSearch()
  availableBabies.value = []

  // 清除验证状态
  await nextTick()
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  console.log('form.related_inspection_information', form.related_inspection_information)
  if (!formRef.value) return

  // 防止重复提交
  if (submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 构造提交数据
    const submitData = {
      referral_department: form.referral_department,
      main_symptoms_and_reasons: form.main_symptoms_and_reasons,
      emergency_level: form.emergency_level,
      vital_signs: form.vital_signs,
      related_medical_history: form.related_medical_history,
      related_inspection_information: form.related_inspection_information,
    }

    let res
    if (!props.referralId) {
      // 新增模式
      const createPath =
        props.referralType === 'maternal'
          ? `customer-service/maternity-medical-referral/create/${form.maternity_admission}/`
          : `customer-service/newborn-medical-referral/create/${form.baby_id}/`

      res = await post(createPath, submitData)
    } else {
      // 编辑模式
      const updatePath =
        props.referralType === 'maternal'
          ? `customer-service/maternity-medical-referral/update/${form.rid}/`
          : `customer-service/newborn-medical-referral/update/${form.rid}/`

      res = await put(updatePath, submitData)
    }

    ElMessage.success(props.referralId ? '转诊申请更新成功' : '转诊申请提交成功')
    emit('success', res)
    visible.value = false
  } catch (error) {
    // 处理表单验证错误
    if (error && typeof error === 'object' && !error.message) {
      // 提取第一个错误信息
      const firstField = Object.keys(error)[0]
      if (firstField && error[firstField] && error[firstField][0]) {
        ElMessage.error(error[firstField][0].message)
      } else {
        ElMessage.error('表单验证失败，请检查输入')
      }
    } else {
      ElMessage.error(error.message || '操作失败')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
