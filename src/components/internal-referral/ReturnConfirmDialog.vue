<template>
  <el-dialog
    v-model="dialogVisible"
    title="转诊返回确认"
    width="600px"
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="return-confirm-dialog"
  >
    <div class="dialog-content">
      <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
        <el-form-item label="返回时间" prop="actual_return_time" required>
          <el-date-picker
            v-model="form.actual_return_time"
            type="datetime"
            placeholder="选择实际返回时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="new Date()"
            class="w-full"
          />
        </el-form-item>

        <el-form-item label="记录文件" prop="rrd" required>
          <FileUpload
            :file-types="['jpg', 'png', 'pdf', 'doc', 'docx']"
            :max-size="10"
            :multiple="true"
            :limit="10"
            v-model="form.rrd"
            :urls="form.rrd_urls"
            action="file/medical-referral-return/file/upload/"
            field="medical_referral_return_file"
            upload-text="上传返回记录"
            custom-tip-text="支持JPG、PNG、PDF、DOC、DOCX格式，最多上传10个文件，每个文件不超过10MB"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="confirmLoading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          确认返回
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import FileUpload from '@/components/FileUpload.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  referralId: {
    type: String,
    default: '',
  },
  referralType: {
    type: String,
    default: 'maternal', // maternal or newborn
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 表单引用
const formRef = ref()

// 确认加载状态
const confirmLoading = ref(false)

// 弹窗显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 表单数据
const form = reactive({
  actual_return_time: '', // 实际返回时间
  rrd: [], // 返回记录文件列表 (return_records -> rrd)
  return_remark: '',
})

// 表单验证规则
const formRules = {
  actual_return_time: [{ required: true, message: '请选择实际返回时间', trigger: 'change' }],
  rrd: [{ required: true, message: '请上传返回记录文件', trigger: 'change' }],
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 确认
const handleConfirm = async () => {
  try {
    confirmLoading.value = true

    // 验证表单
    await formRef.value.validate()
    console.log('form.rrd', form.rrd)

    // 提取文件URL列表
    // const fileUrls = form.rrd.map((file) => file.url || file.response?.url).filter(Boolean)

    // if (fileUrls.length === 0) {
    //   ElMessage.error('请至少上传一个返回记录文件')
    //   return
    // }

    // 发送确认事件，包含实际返回时间和文件列表
    emit('confirm', {
      actual_return_time: form.actual_return_time,
      rrd: form.rrd,
    })
  } catch (error) {
    console.log('标记已返回失败:', error)
  } finally {
    confirmLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.actual_return_time = ''
  form.rrd = []
  formRef.value?.clearValidate()
}

// 监听对话框关闭，重置表单
const handleDialogClose = () => {
  resetForm()
}

// 监听对话框显示状态
watch(
  () => props.modelValue,
  (visible) => {
    if (!visible) {
      handleDialogClose()
    }
  },
)
</script>

<style scoped>
.return-confirm-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-content {
  padding: 0 4px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 15px 20px 20px 20px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-date-editor) {
  width: 100% !important;
}

:deep(.el-date-editor .el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-date-editor .el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-date-editor.is-focus .el-input__wrapper) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}
</style>
