<template>
  <el-dialog
    v-model="visible"
    title="转诊申请详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 申请基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">申请信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>申请单号：</label>
              <span class="font-mono text-sm">{{ detailData.rid }}</span>
            </div>
            <div class="detail-item">
              <label>申请时间：</label>
              <span>{{ detailData.application_time }}</span>
            </div>
            <div class="detail-item">
              <label>审批状态：</label>
              <el-tag :type="getStatusType(detailData.approval_status)">
                {{ detailData.approval_status_label }}
              </el-tag>
            </div>
            <div class="detail-item" v-if="detailData.approval_time">
              <label>审批时间：</label>
              <span>{{ detailData.approval_time }}</span>
            </div>
          </div>
        </div>

        <!-- 患者信息 -->
        <div class="detail-section">
          <h3 class="section-title">
            {{ referralType === 'maternal' ? '产妇信息' : '产妇及新生儿信息' }}
          </h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>产妇姓名：</label>
              <span>{{ detailData.maternity_name }}</span>
            </div>
            <div class="detail-item">
              <label>产妇年龄：</label>
              <span>{{ detailData.maternity_age }}岁</span>
            </div>
            <div class="detail-item" v-if="referralType === 'maternal'">
              <label>产后天数：</label>
              <span>{{ detailData.postpartum_days || '-' }}天</span>
            </div>
            <div class="detail-item">
              <label>房间号：</label>
              <el-tag
                type="info"
                size="small"
                v-if="detailData.room_number && detailData.room_number !== '-'"
              >
                {{ detailData.room_number }}
              </el-tag>
              <span v-else class="text-gray-400">-</span>
            </div>
          </div>
        </div>

        <!-- 转诊信息 -->
        <div class="detail-section">
          <h3 class="section-title">转诊信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>转诊科室：</label>
              <span class="font-medium">{{ detailData.referral_department }}</span>
            </div>
            <div class="detail-item">
              <label>紧急程度：</label>
              <el-tag :type="getEmergencyLevelTagType(detailData.emergency_level)" size="small">
                {{ detailData.emergency_level_label }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>转诊状态：</label>
              <el-tag
                :type="getMedicalReferralStatusTagType(detailData.referral_status)"
                size="small"
              >
                {{ detailData.referral_status_display }}
              </el-tag>
            </div>

            <div class="detail-item" v-if="detailData.actual_return_time">
              <label>转诊返回时间：</label>
                {{ detailData.actual_return_time }}
            </div>
          </div>
        </div>

        <!-- 症状及原因 -->
        <div class="detail-section">
          <h3 class="section-title">主要症状及原因</h3>
          <div class="symptom-content p-4 bg-gray-50 rounded-lg">
            {{ detailData.main_symptoms_and_reasons }}
          </div>
        </div>

        <!-- 生命体征 -->
        <div class="detail-section">
          <h3 class="section-title">生命体征</h3>
          <div class="vital-signs-content p-4 bg-gray-50 rounded-lg">
            {{ detailData.vital_signs }}
          </div>
        </div>

        <!-- 相关病史 -->
        <div class="detail-section">
          <h3 class="section-title">相关病史</h3>
          <div class="history-content p-4 bg-gray-50 rounded-lg">
            {{ detailData.related_medical_history }}
          </div>
        </div>

        <!-- 审批信息 -->
        <div v-if="detailData.approval_opinion || detailData.approval_by" class="detail-section">
          <h3 class="section-title">审批信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item" v-if="detailData.approval_status_label">
              <label>审批状态：</label>
              <span>{{ detailData.approval_status_label }}</span>
            </div>
            <div
              class="detail-item"
              v-if="detailData.approval_by && detailData.approval_by !== '-'"
            >
              <label>审批人：</label>
              <span>{{ detailData.approval_by }}</span>
            </div>
          </div>
          <div v-if="detailData.approval_opinion" class="mt-4">
            <label class="text-sm font-medium text-gray-700">审批意见：</label>
            <div class="approval-content p-4 bg-gray-50 rounded-lg mt-2">
              {{ detailData.approval_opinion }}
            </div>
          </div>
        </div>

        <!-- 相关检查信息 -->
        <div class="detail-section" v-if="detailData.related_inspection_information_urls&&detailData.related_inspection_information_urls.length > 0">
          <h3 class="section-title">相关检查文件</h3>
          <FileDisplayList
            :file-list="detailData.related_inspection_information_urls"
            max-height="200px"
          />
        </div>

        <!-- 转诊出院信息 -->
        <div
          v-if="
            detailData.referral_return_discharge_urls && detailData.referral_return_discharge_urls.length > 0
          "
          class="detail-section"
        >
          <h3 class="section-title">转诊出院文件</h3>
          <FileDisplayList
            :file-list="detailData.referral_return_discharge_urls"
            max-height="200px"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="detailData?.approval_status === 'PENDING'"
          @click="handleProcess"
          type="primary"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          处理申请
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import {
  getAuditStatusTagType,
  getEmergencyLevelTagType,
  getMedicalReferralStatusTagType,
} from '@/utils/constants.js'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import FileDisplayList from '@/components/FileDisplayList.vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemId: {
    type: [String, Number],
    default: null,
  },
  referralType: {
    type: String,
    default: 'maternal', // 'maternal' or 'newborn'
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'process', 'close'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.itemId) {
    fetchDetail()
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.itemId) return

  loading.value = true
  try {
    // 根据转诊类型选择对应的API端点
    const apiEndpoint =
      props.referralType === 'maternal'
        ? `customer-service/maternity-medical-referral/detail/${props.itemId}/`
        : `customer-service/newborn-medical-referral/detail/${props.itemId}/`

    const response = await get(apiEndpoint)
    detailData.value = response
  } catch (error) {
    console.error('获取转诊申请详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
  detailData.value = null
}

// 处理申请
const handleProcess = () => {
  emit('process', detailData.value)
}

// 状态相关方法 - 使用全局常量工具函数
const getStatusType = (status) => {
  return getAuditStatusTagType(status)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1.25rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-right: 0.5rem;
  min-width: 5rem;
  flex-shrink: 0;
}

.symptom-content,
.vital-signs-content,
.history-content,
.inspection-content,
.approval-content {
  color: #374151;
  line-height: 1.625;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

:deep(.el-dialog__body) {
  padding-top: 1.5rem;
}
</style>
