<template>
  <div class="permission-example">
    <h3>权限系统使用示例</h3>

    <!-- 使用权限指令 -->
    <div class="mb-4">
      <h4>使用 v-permission 指令</h4>
      <el-button v-permission="'cus.housekeeping.view'" type="primary">
        查看房务管理（需要 cus.housekeeping.view 权限）
      </el-button>

      <el-button v-permission="'cus.housekeeping.edit'" type="success">
        编辑房务管理（需要 cus.housekeeping.edit 权限）
      </el-button>

      <el-button v-permission="['cus.housekeeping.view', 'cus.housekeeping.edit']" type="warning">
        任一权限即可显示
      </el-button>

      <el-button
        v-permission:all="['cus.housekeeping.view', 'cus.housekeeping.edit']"
        type="danger"
      >
        需要所有权限才显示
      </el-button>
    </div>

    <!-- 使用权限显示指令 -->
    <div class="mb-4">
      <h4>使用 v-permission-show 指令</h4>
      <p v-permission-show="'sys.backend.management.view'">
        这段文本只有拥有后台管理查看权限的用户才能看到
      </p>
    </div>

    <!-- 使用组合式API -->
    <div class="mb-4">
      <h4>使用组合式API</h4>
      <p>当前用户权限：</p>
      <el-tag v-for="permission in userPermissions" :key="permission" class="mr-2 mb-2">
        {{ permission }}
      </el-tag>

      <p class="mt-4">用户角色：{{ userRole }}</p>

      <div class="mt-4">
        <p>权限检查结果：</p>
        <ul>
          <li>可以查看房务管理：{{ canView('housekeeping') ? '是' : '否' }}</li>
          <li>可以编辑房务管理：{{ canEdit('housekeeping') ? '是' : '否' }}</li>
          <li>可以查看后台管理：{{ canView('backend') ? '是' : '否' }}</li>
          <li>可以编辑后台管理：{{ canEdit('backend') ? '是' : '否' }}</li>
        </ul>
      </div>
    </div>

    <!-- 在模板中使用权限检查 -->
    <div class="mb-4">
      <h4>在模板中条件渲染</h4>
      <div v-if="hasPermission('cus.housekeeping.edit')">
        <el-alert type="success" :closable="false">
          您有房务管理编辑权限，可以进行编辑操作
        </el-alert>
      </div>
      <div v-else>
        <el-alert type="warning" :closable="false"> 您没有房务管理编辑权限，只能查看 </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { usePermissions } from '@/utils/permissions'

// 使用权限组合式API
const {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  userPermissions,
  userRole,
  canView,
  canEdit,
} = usePermissions()
</script>

<style scoped>
.permission-example {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

h3,
h4 {
  color: #333;
  margin-bottom: 16px;
}

.mb-4 {
  margin-bottom: 24px;
}

.mr-2 {
  margin-right: 8px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
