<template>
  <div class="file-display-list">
    <div v-if="normalizedFileList && normalizedFileList.length > 0" class="space-y-3">
      <div v-for="(file, index) in normalizedFileList" :key="index" class="file-item">
        <!-- 图片类型 -->
        <div v-if="isImageFile(file)" class="image-container">
          <img
            :src="addFileUrlPrefix(file)"
            :alt="url2filename(file)"
            class="file-image cursor-pointer hover:opacity-80 transition-opacity"
            @click="openFileLink(file)"
            @error="handleImageError"
          />
          <div class="image-filename text-xs text-gray-500 mt-1">
            {{ url2filename(file) }}
          </div>
        </div>

        <!-- 其他文件类型 -->
        <div v-else class="file-link">
          <el-link type="primary" @click="openFileLink(file)">
            {{ url2filename(file) }}
          </el-link>
        </div>
      </div>
    </div>

    <div v-else class="empty-state text-gray-400 text-center py-4">暂无文件</div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { addFileUrlPrefix, openFileLink, url2filename, isImageFile } from '@/utils/utils'

// Props
const props = defineProps({
  fileList: {
    type: [Array, String],
    default: () => [],
  },
  maxHeight: {
    type: String,
    default: '200px',
  },
})

// 规范化文件列表 - 支持字符串和数组
const normalizedFileList = computed(() => {
  if (!props.fileList) return []

  // 如果是字符串，转换为数组
  if (typeof props.fileList === 'string') {
    return props.fileList.trim() ? [props.fileList] : []
  }

  // 如果是数组，直接返回
  if (Array.isArray(props.fileList)) {
    return props.fileList
  }

  return []
})

// 图片加载错误处理
const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  // 可以设置默认图片或隐藏图片
  event.target.style.display = 'none'
}
</script>

<style scoped>
.file-display-list {
  width: 100%;
}

.file-item {
  display: flex;
  flex-direction: column;
}

.image-container {
  display: flex;
  flex-direction: column;
  align-items: start;
}

.file-image {
  max-height: v-bind(maxHeight);
  max-width: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-link {
  display: flex;
  align-items: center;
}

.empty-state {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .file-image {
    max-height: 150px;
  }
}
</style>
