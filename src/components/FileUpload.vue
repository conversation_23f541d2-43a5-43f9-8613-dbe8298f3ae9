<!--
通用文件上传组件 FileUpload
============================

使用示例：
1. 基础文档上传（绑定文件ID）：
   <FileUpload
     v-model="fileId"
     :file-types="['pdf', 'doc', 'docx']"
     :max-size="10"
     action="upload/documents"
     @upload-success="handleFileUploadSuccess"
   />
   // fileId 将是服务器返回的文件ID（response.data.rid）

2. 图片上传（多文件绑定ID数组）：
   <FileUpload
     v-model="imageIds"
     :file-types="['jpg', 'png', 'gif']"
     :max-size="5"
     :multiple="true"
     :limit="3"
     action="upload/images"
   />
   // imageIds 将是ID数组，如: ["file123", "file456"]

3. 数据回显示例（使用urls属性）：
   <FileUpload
     v-model="formData.cover"
     :urls="formData.cover_url"
     :file-types="['jpg', 'png']"
     action="upload/cover"
   />
   // formData.cover 存储文件ID，formData.cover_url 存储显示用的URL

4. 监听上传回调：
   <FileUpload
     v-model="fileId"
     :file-types="['pdf']"
     action="upload/contract"
     @upload-success="handleUploadSuccess"
     @upload-error="handleUploadError"
   />

主要参数：
- file-types: 支持的文件类型 ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'jpg', 'png', 'gif', 'zip', 'rar']
- max-size: 文件大小限制(MB)
- multiple: 是否支持多选
- limit: 文件数量限制
- drag: 是否使用拖拽模式
- disabled: 是否禁用
- field: 上传字段名，默认为'file'
- action: 上传接口地址
- urls: 显示用的URL，可以是字符串或数组，与modelValue按顺序对应
- custom-validator: 自定义验证函数

主要事件：
- @upload-success: 上传成功回调，参数(response, file, fileList)
- @upload-error: 上传失败回调，参数(error, file, fileList)
- @upload-progress: 上传进度回调，参数({ percent, file })
- @change: 文件选择变化回调
- @remove: 文件移除回调
- @exceed: 文件数量超限回调
- @validate-error: 验证失败回调

数据绑定说明：
- v-model 绑定的值现在始终使用文件ID（response.data.rid）
- 单文件模式：绑定字符串类型的文件ID
- 多文件模式：绑定数组类型的文件ID列表
- 数据回显：通过urls属性提供显示用的URL，modelValue存储文件ID
-->
<template>
  <div class="file-upload-wrapper">
    <el-upload
      :class="['file-upload', uploadClass]"
      :drag="drag"
      :action="actionUrl"
      :headers="uploadHeaders"
      :name="field"
      :auto-upload="computedAutoUpload"
      :show-file-list="showFileList"
      :limit="limit"
      :on-change="handleFileChange"
      :before-upload="handleBeforeUpload"
      :on-remove="handleFileRemove"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :http-request="customHttpRequest"
      :accept="acceptString"
      :multiple="multiple"
      :disabled="disabled"
      v-model:file-list="files"
    >
      <template v-if="drag">
        <el-icon class="el-icon--upload text-6xl text-gray-400">
          <UploadFilled />
        </el-icon>
        <div class="el-upload__text text-gray-600">
          {{ uploadText || '将文件拖到此处，或' }}<em class="text-pink-500">点击上传</em>
        </div>
      </template>
      <template v-else>
        <el-button type="primary" :disabled="disabled">
          <el-icon><UploadFilled /></el-icon>
          {{ buttonText || '选择文件' }}
        </el-button>
      </template>

      <template #tip v-if="showTip">
        <div class="el-upload__tip text-sm text-gray-500">
          {{ tipText }}
        </div>
      </template>
    </el-upload>
  </div>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue'
import { ElUpload, ElIcon, ElButton, ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { url2filename } from '@/utils/utils'

// 获取API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

// Props定义
const props = defineProps({
  // 基础配置
  modelValue: {
    type: [Array, Object, String],
    default: () => [],
  },
  // 上传模式：拖拽或按钮
  drag: {
    type: Boolean,
    default: true,
  },
  // 是否显示文件列表
  showFileList: {
    type: Boolean,
    default: true,
  },
  // 文件数量限制
  limit: {
    type: Number,
    default: 1,
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否自动上传
  autoUpload: {
    type: Boolean,
    default: false,
  },
  // 上传地址
  action: {
    type: String,
    default: '#',
  },
  // 支持的文件类型配置
  fileTypes: {
    type: Array,
    default: () => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'],
  },
  // 文件大小限制（MB）
  maxSize: {
    type: Number,
    default: 10,
  },
  // 自定义文本
  uploadText: {
    type: String,
    default: '',
  },
  buttonText: {
    type: String,
    default: '',
  },
  // 是否显示提示信息
  showTip: {
    type: Boolean,
    default: true,
  },
  // 自定义提示文本
  customTipText: {
    type: String,
    default: '',
  },
  // 额外的CSS类
  uploadClass: {
    type: String,
    default: '',
  },
  // 自定义验证函数
  customValidator: {
    type: Function,
    default: null,
  },
  // 上传字段名
  field: {
    type: String,
    default: 'file',
  },
  // 显示用的URL（用于数据回显）
  urls: {
    type: [String, Array],
    default: null,
  },
})

// Emits定义
const emit = defineEmits([
  'update:modelValue',
  'change',
  'remove',
  'exceed',
  'validate-error',
  'upload-success',
  'upload-error',
  'upload-progress',
])

const files = ref([])
// 内部更新标志，避免循环触发watch
const isInternalUpdate = ref(false)
// 记录原始modelValue的类型，用于决定输出格式
const originalValueType = ref('array')

// 监听 modelValue 和 urls 变化，同步更新 files
watch(
  [() => props.modelValue, () => props.urls],
  ([newValue, urls]) => {
    // 如果是内部更新触发的，跳过
    if (isInternalUpdate.value) {
      isInternalUpdate.value = false
      return
    }

    // 记录原始值的类型
    if (typeof newValue === 'string') {
      originalValueType.value = 'string'
    } else if (Array.isArray(newValue)) {
      originalValueType.value = 'array'
    } else {
      originalValueType.value = 'array' // 默认为数组
    }

    // 确保newValue是数组
    const valueArray = Array.isArray(newValue) ? newValue : newValue ? [newValue] : []

    // 处理URLs数组
    let urlArray = []
    if (urls) {
      urlArray = Array.isArray(urls) ? urls : [urls]
    }

    files.value = valueArray.map((item, index) => {
      let displayUrl = item
      let fileId = item

      // 如果有对应的URL，使用URL进行显示，item作为文件ID
      if (urlArray[index]) {
        displayUrl = urlArray[index]
        fileId = item
      }

      return {
        uid: Date.now() + index,
        url: displayUrl, // 用于显示的URL
        fileId: fileId, // 存储文件ID
        name: url2filename(displayUrl),
        status: 'success', // 标记为已上传成功
      }
    })
  },
  { deep: true, immediate: true },
)

// 文件类型映射
const fileTypeMap = {
  // 文档类型
  pdf: {
    mimeTypes: ['application/pdf'],
    extensions: ['.pdf'],
    label: 'PDF',
  },
  doc: {
    mimeTypes: ['application/msword'],
    extensions: ['.doc'],
    label: 'Word文档',
  },
  docx: {
    mimeTypes: ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    extensions: ['.docx'],
    label: 'Word文档',
  },
  xls: {
    mimeTypes: ['application/vnd.ms-excel'],
    extensions: ['.xls'],
    label: 'Excel表格',
  },
  xlsx: {
    mimeTypes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    extensions: ['.xlsx'],
    label: 'Excel表格',
  },
  txt: {
    mimeTypes: ['text/plain'],
    extensions: ['.txt'],
    label: '文本文件',
  },
  // 图片类型
  jpg: {
    mimeTypes: ['image/jpeg'],
    extensions: ['.jpg', '.jpeg'],
    label: 'JPEG图片',
  },
  png: {
    mimeTypes: ['image/png'],
    extensions: ['.png'],
    label: 'PNG图片',
  },
  gif: {
    mimeTypes: ['image/gif'],
    extensions: ['.gif'],
    label: 'GIF图片',
  },
  // 压缩文件
  zip: {
    mimeTypes: ['application/zip'],
    extensions: ['.zip'],
    label: 'ZIP压缩包',
  },
  rar: {
    mimeTypes: ['application/x-rar-compressed'],
    extensions: ['.rar'],
    label: 'RAR压缩包',
  },
}

// 获取token
const getToken = () => {
  return localStorage.getItem('token') || sessionStorage.getItem('token')
}

// 计算属性
const actionUrl = computed(() => {
  if (props.action && props.action !== '#') {
    return `${API_BASE_URL}/${props.action}`
  }
  return '#'
})

// 上传请求头，包含token
const uploadHeaders = computed(() => {
  const token = getToken()
  return token ? { Authorization: `Bearer ${token}` } : {}
})

const acceptString = computed(() => {
  const extensions = []
  props.fileTypes.forEach((type) => {
    if (fileTypeMap[type]) {
      extensions.push(...fileTypeMap[type].extensions)
    }
  })
  return extensions.join(',')
})

const validMimeTypes = computed(() => {
  const mimeTypes = []
  props.fileTypes.forEach((type) => {
    if (fileTypeMap[type]) {
      mimeTypes.push(...fileTypeMap[type].mimeTypes)
    }
  })
  return mimeTypes
})

const supportedTypeLabels = computed(() => {
  return props.fileTypes.map((type) => fileTypeMap[type]?.label || type).join('、')
})

const tipText = computed(() => {
  if (props.customTipText) {
    return props.customTipText
  }
  return `支持 ${supportedTypeLabels.value}，文件大小不超过 ${props.maxSize}MB`
})

const computedAutoUpload = computed(() => {
  // 如果父组件传了action，则自动上传为true
  if (props.action && props.action !== '#') {
    return true
  }
  // 否则使用props中的autoUpload值
  return props.autoUpload
})

// 文件上传前的验证
const handleBeforeUpload = (file) => {
  // 文件类型验证
  const isValidType = validMimeTypes.value.includes(file.type)
  if (!isValidType) {
    const message = `只能上传 ${supportedTypeLabels.value} 文件！`
    ElMessage.error(message)
    emit('validate-error', { type: 'file-type', message, file })
    return false
  }

  // 文件大小验证
  const isValidSize = file.size / 1024 / 1024 < props.maxSize
  if (!isValidSize) {
    const message = `文件大小不能超过 ${props.maxSize}MB！`
    ElMessage.error(message)
    emit('validate-error', { type: 'file-size', message, file })
    return false
  }

  // 自定义验证
  if (props.customValidator) {
    const customResult = props.customValidator(file)
    if (customResult !== true) {
      const message = typeof customResult === 'string' ? customResult : '文件验证失败'
      ElMessage.error(message)
      emit('validate-error', { type: 'custom', message, file })
      return false
    }
  }

  return computedAutoUpload.value // 如果自动上传，允许上传；否则阻止默认上传行为
}

// 简洁的自定义上传函数
const customHttpRequest = (options) => {
  const { file, onProgress, onSuccess, onError } = options

  const formData = new FormData()
  formData.append(props.field, file)

  const xhr = new XMLHttpRequest()

  // 上传进度
  xhr.upload.onprogress = (e) => {
    if (e.lengthComputable) {
      const percent = Math.round((e.loaded / e.total) * 100)
      onProgress?.({ percent })
      emit('upload-progress', { percent, file })
    }
  }

  // 上传完成
  xhr.onload = () => {
    if (xhr.status === 200) {
      try {
        const response = JSON.parse(xhr.responseText)
        onSuccess?.(response)
      } catch {
        onSuccess?.(xhr.responseText)
      }
    } else {
      onError?.(new Error(`上传失败: ${xhr.status}`))
    }
  }

  // 上传错误
  xhr.onerror = () => onError?.(new Error('网络错误'))

  // 发送请求
  xhr.open('POST', actionUrl.value)

  // 添加token
  const token = getToken()
  if (token) {
    xhr.setRequestHeader('Authorization', `Bearer ${token}`)
  }

  xhr.send(formData)
  return xhr
}

// 文件选择变化
const handleFileChange = (file, fileList) => {
  const fileInfo = {
    ...file,
    fileSize: (file.size / 1024 / 1024).toFixed(2) + 'MB',
    fileType: getFileTypeByName(file.name),
  }

  // 只在文件选择时触发change事件，不更新modelValue
  // modelValue的更新应该在上传成功时进行
  emit('change', fileInfo, fileList)
}

// 文件移除
const handleFileRemove = (file, fileList) => {
  // 处理移除后的文件列表，只保留已成功上传的文件URL
  isInternalUpdate.value = true
  emitFileList(fileList)
  emit('remove', file, fileList)
}

// 文件数量超限
const handleExceed = (files, fileList) => {
  ElMessage.warning(
    `当前限制选择 ${props.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`,
  )
  emit('exceed', files, fileList)
}

const emitFileList = (fileList) => {
  isInternalUpdate.value = true
  let files = fileList
    .filter(
      (item) => (item.response && item.response.data && item.response.data.rid) || item.fileId,
    )
    .map((item) => {
      // 使用文件ID
      if (item.response && item.response.data && item.response.data.rid) {
        return item.response.data.rid
      } else if (item.fileId) {
        return item.fileId
      }
      return null
    })
    .filter(Boolean)

  // 根据原始类型和配置决定输出格式
  let result
  if (originalValueType.value === 'string' && props.limit === 1) {
    // 如果原始是字符串类型且限制为1个文件，输出字符串
    result = files.length > 0 ? files[0] : ''
  } else {
    // 否则输出数组
    result = files
  }

  emit('update:modelValue', result)
}

// 上传成功回调
const handleUploadSuccess = (response, file, fileList) => {
  console.log('=== Upload Success ===')
  console.log('当前上传成功的文件:', file)
  console.log('服务器响应:', response)
  console.log('当前文件列表:', fileList)

  // 确保文件对象中有相应属性，用于双向绑定
  if (response.data && file) {
    // 设置url属性用于显示
    if (response.data.url) {
      file.url = response.data.url
    }
    // 设置fileId属性用于双向绑定（必须有RID）
    if (response.data.rid) {
      file.fileId = response.data.rid
    } else {
      console.error('上传响应中缺少文件RID')
    }
  }

  emitFileList(fileList)

  // 触发 upload-success 事件，传递当前上传成功的文件信息
  emit('upload-success', response.data || response, file, fileList)

  // 显示成功消息
  ElMessage.success(`文件 "${file.name}" 上传成功`)
}

// 上传失败回调
const handleUploadError = (error, file, fileList) => {
  console.log('=== Upload Error ===')
  console.log('上传失败的文件:', file)
  console.log('错误信息:', error)
  console.log('当前文件列表:', fileList)

  ElMessage.error(`文件 "${file.name}" 上传失败`)
  emit('upload-error', error, file, fileList)
}

// 根据文件名获取文件类型
const getFileTypeByName = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase()
  const typeEntry = Object.entries(fileTypeMap).find(([, value]) =>
    value.extensions.some((ext) => ext.includes(extension)),
  )
  return typeEntry ? typeEntry[1].label : '未知类型'
}

// 组件挂载时初始化原始值类型
onMounted(() => {
  if (typeof props.modelValue === 'string') {
    originalValueType.value = 'string'
  } else if (Array.isArray(props.modelValue)) {
    originalValueType.value = 'array'
  } else {
    originalValueType.value = 'array' // 默认为数组
  }
})

// 暴露方法
defineExpose({
  clearFiles: () => {
    const result = originalValueType.value === 'string' ? '' : []
    emit('update:modelValue', result)
  },
})
</script>

<style scoped>
.file-upload-wrapper {
  width: 100%;
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
  width: 100%;
}

:deep(.el-upload-dragger:hover) {
  border-color: #ec4899;
  background: #fdf2f8;
}

:deep(.el-upload__text em) {
  font-style: normal;
  font-weight: 600;
}

:deep(.el-upload--picture-card) {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.el-upload--picture-card:hover) {
  border-color: #ec4899;
}

.file-upload.w-full :deep(.el-upload) {
  width: 100%;
}

.file-upload.w-full :deep(.el-upload-dragger) {
  width: 100%;
}
</style>
