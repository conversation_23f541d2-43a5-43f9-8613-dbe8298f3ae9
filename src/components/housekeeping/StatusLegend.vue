<template>
  <div class="status-legend flex gap-4 flex-wrap">
    <div
      v-for="status in displayStatuses"
      :key="status.value"
      class="legend-item flex items-center gap-2"
    >
      <div
        class="legend-color w-3 h-3 rounded-full"
        :style="{ backgroundColor: status.color }"
      ></div>
      <span class="text-sm text-gray-600">{{ status.text }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ROOM_STATUS_MAP, ROOM_STATUS_LEGEND_ORDER } from '@/utils/constants.js'

const props = defineProps({
  // 需要显示的状态列表，如果不传则显示所有状态
  statuses: {
    type: Array,
    default: () => ROOM_STATUS_LEGEND_ORDER,
  },
})

// 计算需要显示的状态
const displayStatuses = computed(() => {
  return props.statuses
    .map((status) => {
      const statusInfo = ROOM_STATUS_MAP[status]
      if (!statusInfo) {
        console.warn(`Unknown room status: ${status}`)
        return null
      }
      return {
        value: status,
        text: statusInfo.text,
        color: statusInfo.color,
      }
    })
    .filter(Boolean)
})
</script>

<style scoped>
.legend-item {
  transition: all 0.2s ease;
}

.legend-item:hover .legend-color {
  transform: scale(1.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-legend {
    justify-content: flex-start;
    gap: 0.75rem;
  }

  .legend-item span {
    font-size: 0.75rem;
  }

  .legend-color {
    width: 0.625rem;
    height: 0.625rem;
  }
}
</style>
