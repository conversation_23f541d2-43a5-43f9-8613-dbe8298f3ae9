<template>
  <el-dialog 
    v-model="dialogVisible" 
    title="变更详情" 
    width="700px"
    destroy-on-close
    @close="handleClose"
  >
    <div class="change-details">
      <!-- 操作详情 -->
      <div v-if="auditLog.operation_detail" class="mb-6">
        <div class="flex items-center mb-3">
          <el-icon class="mr-2 text-blue-500">
            <InfoFilled />
          </el-icon>
          <h4 class="text-base font-medium text-gray-800">操作详情</h4>
        </div>
        <div class="p-4 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg">
          <p class="text-sm text-gray-700 leading-relaxed">{{ auditLog.operation_detail }}</p>
        </div>
      </div>

      <!-- 字段变更 -->
      <div v-if="hasChanges" class="mb-6">
        <div class="flex items-center mb-3">
          <el-icon class="mr-2 text-orange-500">
            <Edit />
          </el-icon>
          <h4 class="text-base font-medium text-gray-800">字段变更</h4>image.png
        </div>
        <div class="space-y-4">
          <div 
            v-for="(change, key) in auditLog.extra_data" 
            :key="key"
            class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
          >
            <div class="mb-4">
              <h5 class="text-base font-medium text-gray-800 mb-1">{{ change.verbose_name }}</h5>
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <div class="text-xs font-medium text-gray-600 mb-1">原值</div>
                <div class="p-2 bg-red-50 border border-red-200 rounded text-sm">
                  <span class="text-red-800 break-all">{{ change.old_value || '(空)' }}</span>
                </div>
              </div>
              <div>
                <div class="text-xs font-medium text-gray-600 mb-1">新值</div>
                <div class="p-2 bg-green-50 border border-green-200 rounded text-sm">
                  <span class="text-green-800 break-all">{{ change.new_value || '(空)' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 上传文件 -->
      <div v-if="auditLog.operation_type === 'UPLOAD' && auditLog.extra_data?.url" class="mb-6">
        <div class="flex items-center mb-3">
          <el-icon class="mr-2 text-green-500">
            <Upload />
          </el-icon>
          <h4 class="text-base font-medium text-gray-800">上传文件</h4>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <el-icon class="mr-2 text-green-600">
                <Document />
              </el-icon>
              <span class="text-sm text-gray-700">{{ getFileName(auditLog.extra_data.url) }}</span>
            </div>
            <el-button 
              @click="handleViewFile(auditLog.extra_data.url)"
              type="primary"
              size="small"
            >
              <el-icon><Link /></el-icon>
              查看文件
            </el-button>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="border-t border-gray-200 pt-4">
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div class="flex items-center">
            <el-icon class="mr-2 text-gray-400">
              <Clock />
            </el-icon>
            <span class="text-gray-500">操作时间：</span>
            <span class="text-gray-800">{{ formatDateTime(auditLog.operation_time) }}</span>
          </div>
          <div class="flex items-center">
            <el-icon class="mr-2 text-gray-400">
              <Key />
            </el-icon>
            <span class="text-gray-500">日志ID：</span>
            <span class="font-mono text-xs text-gray-800">{{ auditLog.lid }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Link, InfoFilled, Edit, Upload, Document, Clock, Key } from '@element-plus/icons-vue'
import { format } from 'date-fns'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  auditLog: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const hasChanges = computed(() => {
  return props.auditLog.extra_data && 
         Object.keys(props.auditLog.extra_data).length > 0 &&
         props.auditLog.operation_type === 'UPDATE'
})

const handleClose = () => {
  dialogVisible.value = false
}

const handleViewFile = (url) => {
  window.open(url, '_blank')
}

const getFileName = (url) => {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    return pathname.split('/').pop() || '文件'
  } catch {
    return '文件'
  }
}

const formatDateTime = (dateTime) => {
  return format(new Date(dateTime), 'yyyy-MM-dd HH:mm:ss')
}
</script>

<style scoped>
.change-details {
  max-height: 60vh;
  overflow-y: auto;
}
</style>