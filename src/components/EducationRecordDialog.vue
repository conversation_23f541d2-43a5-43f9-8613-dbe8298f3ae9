<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="900px"
    align-center
    :before-close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    class="record-dialog"
  >
    <div class="max-h-[70vh] overflow-y-auto">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="record-form"
      >
        <!-- 基本信息 -->
        <div class="form-section mb-6">
          <h4 class="section-title">宣教基本信息</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="宣教主题" prop="topic">
              <el-select
                v-model="formData.topic"
                placeholder="请选择宣教主题"
                class="w-full"
                filterable
                allow-create
                default-first-option
                :loading="topicsLoading"
              >
                <el-option
                  v-for="topic in topicOptions"
                  :key="topic.value"
                  :label="topic.label"
                  :value="topic.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="宣教方式" prop="method">
              <el-select v-model="formData.method" placeholder="请选择宣教方式" class="w-full">
                <el-option label="一对一指导" value="ONE_ON_ONE_GUIDANCE" />
                <el-option label="集体讲座" value="GROUP_LECTURE" />
                <el-option label="资料发放" value="MATERIAL_DISTRIBUTION" />
                <el-option label="视频观看" value="VIDEO_WATCHING" />
                <el-option label="演示指导" value="DEMONSTRATION_GUIDANCE" />
              </el-select>
            </el-form-item>
            <!-- 只在一对一指导时显示宣教对象 -->
            <el-form-item
              label="宣教对象"
              prop="targetClient"
              v-if="formData.method === 'ONE_ON_ONE_GUIDANCE'"
            >
              <el-select
                v-model="formData.targetClient"
                placeholder="请选择宣教对象"
                class="w-full"
                filterable
                allow-create
                default-first-option
                :loading="clientsLoading"
              >
                <el-option
                  v-for="client in clientOptions"
                  :key="client.value"
                  :label="client.label"
                  :value="client.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="宣教时间" prop="datetime">
              <el-date-picker
                v-model="formData.datetime"
                type="datetime"
                placeholder="选择日期时间"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="地点" prop="location">
              <el-input v-model="formData.location" placeholder="例如：VIP01房间 或 活动室" />
            </el-form-item>
            <el-form-item label="执行人" prop="executor">
              <el-input v-model="formData.executor" placeholder="填写执行人姓名" />
            </el-form-item>
          </div>
        </div>

        <!-- 宣教内容 -->
        <div class="form-section mb-6">
          <h4 class="section-title">宣教内容</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="内容分类">
              <el-input v-model="formData.category" placeholder="请输入内容分类" class="w-full" />
            </el-form-item>
            <el-form-item label="宣教内容摘要" prop="contentSummary">
              <el-input
                v-model="formData.contentSummary"
                type="textarea"
                :rows="4"
                placeholder="简述本次宣教的主要内容"
              />
            </el-form-item>
            <el-form-item label="关键知识点">
              <el-input
                v-model="formData.keyPoints"
                type="textarea"
                :rows="3"
                placeholder="记录重点讲解的知识点，用逗号分隔"
              />
            </el-form-item>
            <el-form-item label="使用材料/工具">
              <el-input v-model="formData.materials" placeholder="例如：宣传册、视频、模型等" />
            </el-form-item>
          </div>
        </div>

        <!-- 反馈与效果 -->
        <div class="form-section mb-6">
          <h4 class="section-title">反馈与效果评估</h4>
          <div class="grid grid-cols-1 gap-4">
            <el-form-item label="对象反馈/理解程度">
              <el-input
                v-model="formData.feedback"
                type="textarea"
                :rows="3"
                placeholder="记录产妇的提问、反馈或理解情况"
              />
            </el-form-item>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <el-form-item label="理解程度评分" prop="comprehensionScore">
                <el-rate
                  v-model="formData.comprehensionScore"
                  :max="5"
                  @change="formRef.validateField('comprehensionScore')"
                />
              </el-form-item>
              <el-form-item label="参与度评估" prop="participation">
                <el-select
                  v-model="formData.participation"
                  placeholder="请选择参与度"
                  class="w-full"
                >
                  <el-option label="积极主动" value="ACTIVE" />
                  <el-option label="配合良好" value="GOOD" />
                  <el-option label="一般配合" value="AVERAGE" />
                  <el-option label="被动接受" value="PASSIVE" />
                </el-select>
              </el-form-item>
            </div>
            <el-form-item label="后续跟进计划">
              <el-input
                v-model="formData.followUpPlan"
                type="textarea"
                :rows="2"
                placeholder="制定后续跟进和强化的计划"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 附件上传 -->
        <div class="form-section mb-6">
          <h4 class="section-title">其他</h4>
          <el-form-item label="备注说明">
            <el-input
              v-model="formData.notes"
              type="textarea"
              :rows="2"
              placeholder="其他需要说明的内容"
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="saveDraft" :loading="saving">保存记录</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          :loading="saving"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500 hover:border-pink-600"
        >
          {{ mode === 'add' ? '提交宣教记录' : '保存修改' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import FileUpload from '@/components/FileUpload.vue'
import { get } from '@/utils/request.js'
import { ElMessage } from 'element-plus'
import { computed, reactive, ref, watch } from 'vue'

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  recordData: {
    type: Object,
    default: null,
  },
  mode: {
    type: String,
    default: 'add', // 'add' | 'edit'
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'save'])

// 响应式数据
const formRef = ref()
const saving = ref(false)

// 下拉列表数据
const topicOptions = ref([])
const clientOptions = ref([])
const topicsLoading = ref(false)
const clientsLoading = ref(false)

// 表单数据
const formData = reactive({
  id: '',
  topic: '',
  targetClient: '',
  method: '',
  datetime: null,
  location: '',
  executor: '',
  category: '',
  contentSummary: '',
  keyPoints: '',
  materials: '',
  feedback: '',
  comprehensionScore: 0,
  participation: '',
  followUpPlan: '',
  notes: '',
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value),
})

const dialogTitle = computed(() => {
  return props.mode === 'add' ? '新增宣教活动记录' : '编辑宣教活动记录'
})

// 表单验证规则
const rules = {
  topic: [{ required: true, message: '请输入宣教主题', trigger: 'blur' }],
  targetClient: [{ required: true, message: '请输入宣教对象', trigger: 'blur' }],
  method: [{ required: true, message: '请选择宣教方式', trigger: 'change' }],
  datetime: [{ required: true, message: '请选择宣教时间', trigger: 'change' }],
  location: [{ required: true, message: '请输入宣教地点', trigger: 'blur' }],
  executor: [{ required: true, message: '请输入执行人', trigger: 'blur' }],
  contentSummary: [{ required: true, message: '请输入宣教内容摘要', trigger: 'blur' }],
  comprehensionScore: [
    {
      validator: (rule, value, callback) => {
        if (value > 0) {
          callback()
        } else {
          callback(new Error('请选择理解程度评分'))
        }
      },
      trigger: ['change', 'blur'],
    },
  ],
  participation: [{ required: true, message: '请选择参与度评估', trigger: 'change' }],
}

// 监听记录数据变化
watch(
  () => props.recordData,
  (newData) => {
    if (newData) {
      // 编辑模式，填充数据
      Object.assign(formData, {
        ...newData,
        datetime: newData.datetime ? new Date(newData.datetime) : null,
      })
    }
  },
  { immediate: true },
)

// 获取宣教主题列表
const fetchTopicOptions = async () => {
  try {
    topicsLoading.value = true
    try {
      topicsLoading.value = true
      const response = await get('customer-service/health-education/content/list/')
      if (response && Array.isArray(response.list)) {
        topicOptions.value = response.list.map((item) => ({
          value: item.rid,
          label: item.title,
        }))
      } else {
        topicOptions.value = []
      }
    } catch (error) {
      console.error('获取标题选项失败:', error)
      ElMessage.error('获取标题选项失败')
      topicOptions.value = []
    } finally {
      topicsLoading.value = false
    }
  } catch (error) {
    console.error('获取宣教主题失败:', error)
    ElMessage.error('获取宣教主题失败')
  } finally {
    topicsLoading.value = false
  }
}

// 获取宣教对象列表
const fetchClientOptions = async () => {
  try {
    clientsLoading.value = true
    try {
      clientsLoading.value = true
      const response = await get('customer-service/health-education/maternity/select/list/')
      if (response && Array.isArray(response.list)) {
        clientOptions.value = response.list.map((item) => ({
          value: item.aid,
          label: `${item.maternity_name} / ${item.room_number} 房间`,
        }))
      } else {
        clientOptions.value = []
      }
    } catch (error) {
      console.error('获取产妇列表:', error)
      ElMessage.error('获取产妇列表失败')
      clientOptions.value = []
    } finally {
      clientsLoading.value = false
    }
  } catch (error) {
    console.error('获取产妇列表失败:', error)
    ElMessage.error('获取产妇列表失败')
  } finally {
    clientsLoading.value = false
  }
}

// 方法
const handleOpen = () => {
  if (props.mode === 'add') {
    resetForm()
  }
  // 打开对话框时获取下拉列表数据
  fetchTopicOptions()
  fetchClientOptions()
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: '',
    topic: '',
    targetClient: '',
    method: '',
    datetime: null,
    location: '',
    executor: '',
    category: '',
    contentSummary: '',
    keyPoints: '',
    materials: '',
    feedback: '',
    comprehensionScore: 0,
    participation: '',
    followUpPlan: '',
    notes: '',
  })
  formRef.value?.clearValidate()
}

const saveDraft = () => {
  saving.value = true

  setTimeout(() => {
    ElMessage.success('记录保存成功！')
    saving.value = false
  }, 500)
}

const handleSave = async () => {
  try {
    await formRef.value.validate()

    saving.value = true

    const saveData = {
      ...formData,
      datetime: formData.datetime
        ? formData.datetime.toISOString().replace('T', ' ').substring(0, 16)
        : null,
    }

    console.log('saveData', saveData)

    const submitData = {
      content: saveData.topic, // 宣教主题的rid，关联HealthEducationContent表
      aid: saveData.method === 'ONE_ON_ONE_GUIDANCE' ? saveData.targetClient : null, // 宣教对象入院记录的aid，关联MaternityAdmission表(非一对一情况必须传 aid) ❌
      way: saveData.method, // 宣教方式，枚举
      edu_time: saveData.datetime, // 宣教时间
      edu_place: saveData.location, // 宣教地点，字符串最大255字符
      executor: saveData.executor, // 执行人姓名，字符串最大30字符
      content_category: saveData.category, // 内容分类，字符串最大50字符，可选
      content_summary: saveData.contentSummary, // 内容摘要，文本字段，可选
      key_points: saveData.keyPoints, // 关键知识点，文本字段，可选
      materials_tools: saveData.materials, // 使用材料或工具，文本字段，可选
      feedback: saveData.feedback, // 反馈信息，文本字段，可选
      understanding_score: saveData.comprehensionScore, // 理解程度评分，整数1-5分
      participation_evaluation: saveData.participation, // 参与度评估，枚举
      follow_up_plan: saveData.followUpPlan, // 后续跟进计划，文本字段，可选
      remark: saveData.notes, // 备注信息，文本字段，可选
    }

    // 模拟保存延迟
    setTimeout(() => {
      emit('save', submitData)
      saving.value = false
    }, 500)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请填写必填项')
  }
}
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}

.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}

:deep(.el-input__wrapper) {
  transition: all 0.2s;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-select:hover .el-input__wrapper) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner) {
  transition: all 0.2s;
}

:deep(.el-textarea__inner:hover) {
  border-color: rgb(249 168 212);
}

:deep(.el-textarea__inner:focus) {
  border-color: rgb(236 72 153);
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.2);
}

:deep(.el-upload-dragger) {
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  background-color: #fafafa;
  text-align: center;
  padding: 40px;
  transition: all 0.2s;
}

:deep(.el-upload-dragger:hover) {
  border-color: #ec4899;
  background-color: #fdf2f8;
}

:deep(.el-rate) {
  display: flex;
  align-items: center;
}
</style>
