<template>
  <el-dialog
    v-model="visible"
    title="完成待办事项"
    width="600px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <div v-if="todoData" class="complete-content">
      <!-- 待办事项信息 -->
      <div class="todo-info mb-6">
        <h3 class="section-title">待办事项信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>事项类型：</label>
            <el-tag :type="getTodoTypeTagType(todoData.todo_type)" size="small">
              {{ todoData.todo_type_display }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>指派人：</label>
            <span>{{ todoData.assign_name }}</span>
          </div>
          <div class="info-item full-width">
            <label>事项内容：</label>
            <div class="content-box">
              {{ todoData.todo_content }}
            </div>
          </div>
          <div v-if="todoData.todo_remark" class="info-item full-width">
            <label>备注信息：</label>
            <div class="content-box">
              {{ todoData.todo_remark }}
            </div>
          </div>
        </div>
      </div>

      <!-- 完成信息表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item label="完成时间" prop="complete_time">
          <el-date-picker
            v-model="formData.complete_time"
            type="datetime"
            placeholder="请选择完成时间"
            style="width: 100%"
            format="YYYY-MM-DD HH:mm"
            value-format="YYYY-MM-DD HH:mm"
            :disabled-date="disabledDate"
          />
        </el-form-item>

        <el-form-item label="完成反馈" prop="complete_feedback">
          <el-input
            v-model="formData.complete_feedback"
            type="textarea"
            :rows="4"
            placeholder="请输入完成反馈"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          确认完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { put } from '@/utils/request.js'
import { format } from 'date-fns'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  todoData: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'success'])

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = ref({
  complete_time: '',
  complete_feedback: ''
})

// 表单验证规则
const formRules = {
  complete_time: [
    { required: true, message: '请选择完成时间', trigger: 'change' }
  ],
  complete_feedback: [
    { required: true, message: '请输入完成反馈', trigger: 'blur' },
    { min: 1, max: 500, message: '完成反馈长度在 1 到 500 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取待办事项类型标签类型
const getTodoTypeTagType = (type) => {
  const typeMap = {
    'FEEDBACK': 'primary',
    'COMPLAINT': 'danger',
    'DAILY_CLEANING': 'success',
    'CUSTOMER_CARE': 'warning',
    'CUSTOMER_VISIT': 'info',
    'OTHER': 'default'
  }
  return typeMap[type] || 'default'
}

// 禁用未来日期
const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

// 监听对话框打开
watch(visible, (newValue) => {
  if (newValue) {
    initForm()
  } else {
    resetForm()
  }
})

// 初始化表单
const initForm = () => {
  // 默认设置当前时间为完成时间
  formData.value = {
    complete_time: format(new Date(), 'yyyy-MM-dd HH:mm'),
    complete_feedback: ''
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    complete_time: '',
    complete_feedback: ''
  }
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value || !props.todoData) return

  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const submitData = {
      complete_time: formData.value.complete_time,
      complete_feedback: formData.value.complete_feedback
    }

    await put(`todo/complete/${props.todoData.rid}/`, submitData)
    ElMessage.success('完成成功')
    emit('success')
  } catch (error) {
    if (error.name !== 'ValidationError') {
      console.error('完成待办事项失败:', error)
      ElMessage.error('完成失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}
</script>

<style scoped>
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #ec4899;
  margin-right: 10px;
  border-radius: 2px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.content-box {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  width: 100%;
  min-height: 40px;
  color: #374151;
  line-height: 1.5;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
}

:deep(.el-textarea__inner) {
  resize: vertical;
}
</style>
