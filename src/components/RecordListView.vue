<template>
  <div class="bg-white rounded-lg">
    <div ref="listContainer" class="overflow-x-auto overflow-y-hidden p-4" @scroll="handleScroll">
      <div v-loading="loading" class="flex gap-3 min-w-max">
        <div
          v-for="record in records"
          :key="record.record_id"
          @click="selectRecord(record)"
          :class="[
            'record-item p-3 border border-gray-200 rounded-lg cursor-pointer transition-all duration-200 flex-shrink-0',
            'hover:border-pink-300 hover:bg-pink-50',
            selectedId === record.record_id ? 'border-pink-500 bg-pink-50' : '',
          ]"
        >
          <div class="text-sm font-medium text-gray-800 text-center">
            <slot name="record-content" :record="record">
              {{ record[field] }}
            </slot>
          </div>
        </div>

        <el-empty
          v-if="records.length === 0 && !loading"
          :description="emptyText"
          class="flex-shrink-0 w-full flex items-center justify-center py-8"
        >
          <div class="text-center">
            <el-button
              v-if="showCreateButton"
              type="primary"
              size="small"
              @click="$emit('create')"
              class="bg-pink-500 hover:bg-pink-600 border-pink-500"
            >
              {{ createButtonText }}
            </el-button>
          </div>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'
import { formatDate } from '@/utils/dateUtils.js'

const props = defineProps({
  // API相关配置
  apiUrl: {
    type: String,
    required: true,
  },
  field:{
    type:String,
    default:'record_date'
  },
  // 分页配置
  pageSize: {
    type: Number,
    default: 10,
  },
  // UI配置
  selectedId: {
    type: [String, Number],
    default: null,
  },
  emptyText: {
    type: String,
    default: '暂无记录',
  },
  showCreateButton: {
    type: Boolean,
    default: false,
  },
  createButtonText: {
    type: String,
    default: '创建记录',
  },
  // 自动选择第一条记录
  autoSelectFirst: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['select', 'create'])

const listContainer = ref()

// 数据状态
const records = ref([])
const loading = ref(false)
const currentPage = ref(1)
const totalCount = ref(0)
const hasMore = ref(true)

// 获取列表数据
const fetchList = async (loadMore = false) => {
  if (loading.value) return

  loading.value = true
  try {
    const params = {
      page: loadMore ? currentPage.value + 1 : 1,
      page_size: props.pageSize,
    }

    // 构建API URL
    let url = props.apiUrl

    const response = await get(url, params)

    if (loadMore) {
      records.value.push(...response.list)
      currentPage.value += 1
    } else {
      records.value = response.list
      currentPage.value = 1

      // 自动选择第一条记录
      if (response.list.length > 0 && props.autoSelectFirst) {
        await nextTick()
        selectRecord(response.list[0])
      }
    }

    totalCount.value = response.total_count
    hasMore.value = records.value.length < response.total_count
  } catch (error) {
    console.error('获取记录列表失败:', error)
    ElMessage.error('获取记录列表失败')
  } finally {
    loading.value = false
  }
}

// 选择记录
const selectRecord = (record) => {
  emit('select', record)
}

// 加载更多记录
const handleLoadMore = () => {
  fetchList(true)
}

// 滚动事件处理
const handleScroll = () => {
  if (!listContainer.value || loading.value || !hasMore.value) return

  const { scrollLeft, scrollWidth, clientWidth } = listContainer.value

  // 距离右边100px时开始加载
  if (scrollLeft + clientWidth >= scrollWidth - 100) {
    handleLoadMore()
  }
}

// 刷新数据
const refresh = () => {
  fetchList()
}

// 暴露方法给父组件
defineExpose({
  refresh,
  fetchList,
})

onMounted(() => {
  if (props.apiUrl) {
    fetchList()
  }
})
</script>

<style scoped>
/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 横向滚动样式 */
.overflow-x-auto {
  scroll-behavior: smooth;
}

.record-item {
  min-width: 128px; /* w-32 */
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}
</style>
