<template>
  <el-dialog
    v-model="visible"
    title="宣教活动记录详情"
    width="700px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    class="record-dialog"
  >
    <div v-if="recordData" class="max-h-[70vh] overflow-y-auto">
      <!-- 基本信息 -->
      <div class="form-section mb-6">
        <h4 class="section-title">宣教基本信息</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="form-item">
            <label>宣教主题</label>
            <div>{{ recordData.content_info?.title || '-' }}</div>
          </div>
          <div class="form-item">
            <label>宣教方式</label>
            <div>{{ recordData.way_display || '-' }}</div>
          </div>
          <div class="form-item" v-if="recordData.way === 'ONE_ON_ONE_GUIDANCE'">
            <label>宣教对象</label>
            <div>
              {{ recordData.admission_info?.name || '-' }}
              <span v-if="recordData.edu_place">（{{ recordData.edu_place }}）</span>
            </div>
          </div>
          <div class="form-item">
            <label>宣教时间</label>
            <div>{{ recordData.edu_time || '-' }}</div>
          </div>
          <div class="form-item">
            <label>地点</label>
            <div>{{ recordData.edu_place || '-' }}</div>
          </div>
          <div class="form-item">
            <label>执行人</label>
            <div>{{ recordData.executor || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 宣教内容 -->
      <div class="form-section mb-6">
        <h4 class="section-title">宣教内容</h4>
        <div class="grid grid-cols-1 gap-4">
          <div class="form-item">
            <label>内容分类</label>
            <div>{{ recordData.content_category || '-' }}</div>
          </div>
          <div class="form-item">
            <label>宣教内容摘要</label>
            <div>{{ recordData.content_summary || '-' }}</div>
          </div>
          <div class="form-item">
            <label>关键知识点</label>
            <div style="white-space: pre-line">{{ recordData.key_points || '-' }}</div>
          </div>
          <div class="form-item">
            <label>使用材料/工具</label>
            <div>{{ recordData.materials_tools || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 反馈与效果 -->
      <div class="form-section mb-6">
        <h4 class="section-title">反馈与效果评估</h4>
        <div class="grid grid-cols-1 gap-4">
          <div class="form-item">
            <label>对象反馈/理解程度</label>
            <div>{{ recordData.feedback || '-' }}</div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-item">
              <label>理解程度评分</label>
              <el-rate :model-value="recordData.understanding_score" :max="5" disabled />
            </div>
            <div class="form-item">
              <label>参与度评估</label>
              <div>{{ recordData.participation_evaluation_display || '-' }}</div>
            </div>
          </div>
          <div class="form-item">
            <label>后续跟进计划</label>
            <div>{{ recordData.follow_up_plan || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 其他 -->
      <div class="form-section mb-6">
        <h4 class="section-title">其他</h4>
        <div class="form-item">
          <label>备注说明</label>
          <div>{{ recordData.remark || '-' }}</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  Document,
  Picture,
  VideoPlay,
  Headset,
  Files,
  ChatLineSquare,
} from '@element-plus/icons-vue'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  recordData: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue'])

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})
</script>

<style scoped>
.form-section {
  padding-bottom: 1.5rem;
}
.form-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}
.section-title::before {
  content: '';
  width: 0.25rem;
  height: 1rem;
  background-color: #ec4899;
  margin-right: 0.75rem;
  border-radius: 0.25rem;
}
.form-item {
  margin-bottom: 1rem;
}
.form-item label {
  display: block;
  color: #6b7280;
  font-size: 0.95rem;
  margin-bottom: 0.25rem;
}
</style>
