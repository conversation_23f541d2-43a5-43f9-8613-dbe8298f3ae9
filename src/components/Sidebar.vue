<template>
  <el-aside
    :width="menuStore.collapsed ? '64px' : '256px'"
    class="sidebar-container border-r border-gray-200 bg-gray-50"
  >
    <div class="h-full flex flex-col">
      <!-- Logo -->
      <div class="h-16 flex items-center justify-center border-b border-gray-100">
        <div v-if="!menuStore.collapsed" class="flex items-center space-x-2">
          <img src="@/assets/logo.png" alt="logo" class="w-8 h-8" />
          <span class="text-gray-800 font-bold">月子中心管理台</span>
        </div>
        <House v-else class="w-6 h-6" style="color: rgb(231, 127, 161)" />
      </div>

      <!-- 菜单 -->
      <el-scrollbar class="flex-1">
        <el-menu
          v-if="menuStore.filteredMenus.length > 0"
          :default-active="activeMenu"
          :collapse="menuStore.collapsed"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <template v-for="menuGroup in menuStore.filteredMenus" :key="menuGroup.id">
            <!-- 分组菜单 -->
            <el-sub-menu v-if="menuGroup.isGroup" :index="menuGroup.id" class="menu-group">
              <template #title>
                <span class="menu-text">{{ menuGroup.title }}</span>
              </template>

              <!-- 分组内的菜单项 -->
              <template v-for="menu in menuGroup.children" :key="menu.id">
                <!-- 无子菜单 -->
                <el-menu-item
                  v-if="!menu.children || menu.children.length === 0"
                  :index="menu.path"
                  class="menu-item group-child"
                >
                  <component :is="menu.icon" class="menu-icon" />
                  <template #title>
                    <span class="menu-text">{{ menu.title }}</span>
                  </template>
                </el-menu-item>

                <!-- 有子菜单 -->
                <el-sub-menu v-else :index="menu.id" class="group-child">
                  <template #title>
                    <component :is="menu.icon" class="menu-icon" />
                    <span class="menu-text">{{ menu.title }}</span>
                  </template>
                  <el-menu-item
                    v-for="child in menu.children"
                    :key="child.id"
                    :index="child.path"
                    class="menu-item-child"
                  >
                    <span class="menu-text">{{ child.title }}</span>
                  </el-menu-item>
                </el-sub-menu>
              </template>
            </el-sub-menu>

            <!-- 非分组菜单 (如果有的话) -->
            <template v-else>
              <!-- 无子菜单 -->
              <el-menu-item
                v-if="!menuGroup.children || menuGroup.children.length === 0"
                :index="menuGroup.path"
                class="menu-item"
              >
                <component :is="menuGroup.icon" class="menu-icon" />
                <template #title>
                  <span class="menu-text">{{ menuGroup.title }}</span>
                </template>
              </el-menu-item>

              <!-- 有子菜单 -->
              <el-sub-menu v-else :index="menuGroup.id">
                <template #title>
                  <component :is="menuGroup.icon" class="menu-icon" />
                  <span class="menu-text">{{ menuGroup.title }}</span>
                </template>
                <el-menu-item
                  v-for="child in menuGroup.children"
                  :key="child.id"
                  :index="child.path"
                  class="menu-item-child"
                >
                  <span class="menu-text">{{ child.title }}</span>
                </el-menu-item>
              </el-sub-menu>
            </template>
          </template>
        </el-menu>
      </el-scrollbar>
    </div>
  </el-aside>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useMenuStore } from '@/stores/menu'
import { House } from '@element-plus/icons-vue'

// 定义组件名称以满足 ESLint 多词要求
defineOptions({
  name: 'SideBar',
})

const route = useRoute()
const menuStore = useMenuStore()

// 当前激活的菜单
const activeMenu = computed(() => {
  // 如果路由meta中指定了activeMenu，使用指定的菜单路径
  if (route.meta?.activeMenu) {
    return route.meta.activeMenu
  }
  return route.path
})
</script>

<style scoped>
.sidebar-container {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.sidebar-menu {
  border: none !important;
  width: 100% !important;
  overflow-x: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 确保菜单在非折叠状态下有固定宽度 */
.sidebar-menu:not(.el-menu--collapse) {
  width: 256px !important;
}

/* 菜单图标样式 */
.menu-icon {
  width: 20px !important;
  height: 20px !important;
  margin-right: 8px !important;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  color: #6b7280 !important;
}

/* 折叠状态下的图标样式 */
.sidebar-menu.el-menu--collapse .menu-icon {
  width: 20px !important;
  height: 20px !important;
  margin-right: 0 !important;
}

/* 分组菜单样式 */
.menu-group {
  position: relative;
}

/* 分组菜单标题样式 */
:deep(.menu-group > .el-sub-menu__title) {
  background-color: #f8f9fa !important;
  color: #374151 !important;
  font-weight: 600 !important;
  border-bottom: none !important;
  padding-left: 24px !important;
}

/* 分组菜单标题悬停效果 */
:deep(.menu-group > .el-sub-menu__title:hover) {
  background-color: #f3f4f6 !important;
}

/* 分组内的菜单项缩进 */
:deep(.group-child) {
  margin-left: 8px !important;
}

/* 分组内菜单项的标题 */
:deep(.group-child .el-sub-menu__title) {
  padding-left: 40px !important;
}

/* 分组内无子菜单的项目 */
:deep(.group-child.el-menu-item) {
  padding-left: 40px !important;
}

/* 分组内子菜单项 */
:deep(.group-child .el-menu-item) {
  padding-left: 60px !important;
}

/* 菜单项样式 */
.menu-item {
  position: relative;
}

.menu-item-child {
  position: relative;
}

/* 菜单文本样式 */
.menu-text {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 1;
  transform: translateX(0);
}

/* 折叠状态下文本动画 */
.sidebar-menu.el-menu--collapse .menu-text {
  opacity: 0;
  transform: translateX(-10px);
}

/* 所有菜单项都有边框，保持宽度一致 */
:deep(.el-menu-item) {
  border-left: 3px solid transparent !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.el-sub-menu__title) {
  border-left: 3px solid transparent !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 菜单项悬停效果 */
:deep(.el-menu-item:hover) {
  background-color: rgba(231, 127, 161, 0.16) !important;
}

:deep(.el-sub-menu__title:hover) {
  background-color: rgba(231, 127, 161, 0.16) !important;
}

/* 激活状态样式 */
:deep(.el-menu-item.is-active) {
  background-color: rgba(231, 127, 161, 0.3) !important;
  color: rgb(231, 127, 161) !important;
  border-left: 3px solid rgb(231, 127, 161) !important;
  font-weight: 600 !important;
  transform: translateX(0) !important;
  box-shadow: 0 2px 8px rgba(231, 127, 161, 0.15);
}

:deep(.el-menu-item.is-active .menu-icon) {
  color: rgb(231, 127, 161) !important;
}

/* 子菜单项样式 */
:deep(.el-menu-item-group__title) {
  display: none;
}

/* 箭头图标样式优化 - 让箭头更粗 */
:deep(.el-sub-menu__icon-arrow) {
  font-weight: 900 !important;
  font-size: 16px !important;
  stroke-width: 3px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.el-sub-menu.is-opened > .el-sub-menu__title .el-sub-menu__icon-arrow) {
  transform: rotateZ(180deg) !important;
}

/* 确保过渡动画平滑 */
:deep(.el-menu) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Logo区域动画 */
.h-16 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 折叠状态下的Logo动画 */
.h-16 > div {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 子菜单展开收起动画 */
:deep(.el-menu--collapse .el-sub-menu > .el-sub-menu__title .el-sub-menu__icon-arrow) {
  opacity: 0;
  transform: scale(0.8);
}

/* 菜单项内容区域动画 */
:deep(.el-sub-menu .el-menu) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 工具提示样式优化 */
:deep(.el-tooltip__trigger) {
  width: 100%;
  display: flex;
  align-items: center;
}

/* 折叠状态下分组菜单的样式调整 */
:deep(.el-menu--collapse .menu-group .el-sub-menu__title) {
  padding-left: 20px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.el-menu--collapse .group-child) {
  margin-left: 0 !important;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.el-menu--collapse .group-child .el-sub-menu__title) {
  padding-left: 20px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

:deep(.el-menu--collapse .group-child.el-menu-item) {
  padding-left: 20px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 防止横向滚动条 */
:deep(.el-scrollbar__view) {
  overflow-x: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 确保所有文本不会产生横向滚动 */
:deep(.el-sub-menu__title),
:deep(.el-menu-item) {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 分组菜单标题文本的动画 */
:deep(.menu-group .el-sub-menu__title .menu-text) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 折叠状态下分组标题的动画 */
:deep(.el-menu--collapse .menu-group .el-sub-menu__title .menu-text) {
  opacity: 0;
  transform: translateX(-15px);
}

/* 添加整体滚动区域的动画 */
.el-scrollbar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
