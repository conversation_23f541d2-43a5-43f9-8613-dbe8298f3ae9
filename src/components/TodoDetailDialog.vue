<template>
  <el-dialog
    v-model="visible"
    title="待办事项详情"
    width="800px"
    align-center
    :before-close="handleClose"
  >
    <div v-loading="loading" class="detail-content max-h-[70vh] overflow-y-auto">
      <div v-if="detailData" class="space-y-6">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>事项类型：</label>
              <el-tag :type="getTodoTypeTagType(detailData.todo_type)" size="small">
                {{ detailData.todo_type_display }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>状态：</label>
              <el-tag :type="getTodoStatusTagType(detailData.todo_status)" size="small">
                {{ detailData.todo_status_display }}
              </el-tag>
            </div>
            <div class="detail-item">
              <label>指派人：</label>
              <span class="font-medium">{{ detailData.assign_name }}</span>
            </div>
            <div class="detail-item">
              <label>执行人：</label>
              <span class="font-medium">{{ detailData.assigned_to_name }}</span>
            </div>
            <div class="detail-item">
              <label>关联产妇：</label>
              <span>{{ detailData.maternity || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>完成时间：</label>
              <span>{{ detailData.complete_time || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 事项内容 -->
        <div class="detail-section">
          <h3 class="section-title">事项内容</h3>
          <div class="detail-item full-width">
            <div class="content-box">
              {{ detailData.todo_content }}
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="detail-section">
          <h3 class="section-title">备注信息</h3>
          <div class="detail-item full-width">
            <div class="content-box">
              {{ detailData.todo_remark || '无备注' }}
            </div>
          </div>
        </div>

        <!-- 完成反馈 -->
        <div v-if="detailData.complete_feedback" class="detail-section">
          <h3 class="section-title">完成反馈</h3>
          <div class="detail-item full-width">
            <div class="content-box">
              {{ detailData.complete_feedback }}
            </div>
          </div>
        </div>

        <!-- 产妇入院信息 -->
        <div v-if="detailData.maternity_admission" class="detail-section">
          <h3 class="section-title">产妇入院信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>姓名/房间号：</label>
              <span>{{ detailData.maternity || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>入院编号：</label>
              <el-tag type="info">{{ detailData.maternity_admission || '-' }}</el-tag>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div  class="detail-section">
          <h3 class="section-title">系统信息</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="detail-item">
              <label>创建时间：</label>
              <el-tag type="info">{{ detailData.created_at || '-' }}</el-tag>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <el-tag type="info">{{ detailData.updated_at || '-' }}</el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>

    

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="detailData && detailData.is_editable"
          type="warning"
          @click="handleEdit"
          class="bg-orange-500 hover:bg-orange-600 border-orange-500"
        >
          编辑
        </el-button>
        <el-button
          v-if="detailData && detailData.is_editable && detailData.todo_status !== 'COMPLETED'"
          type="danger"
          @click="handleDelete"
          class="bg-red-500 hover:bg-red-600 border-red-500"
        >
          删除
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request.js'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  todoId: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close', 'edit', 'delete'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取待办事项状态标签类型
const getTodoStatusTagType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success'
  }
  return statusMap[status] || 'default'
}

// 获取待办事项类型标签类型
const getTodoTypeTagType = (type) => {
  const typeMap = {
    'FEEDBACK': 'primary',
    'COMPLAINT': 'danger',
    'DAILY_CLEANING': 'success',
    'CUSTOMER_CARE': 'warning',
    'CUSTOMER_VISIT': 'info',
    'OTHER': 'default'
  }
  return typeMap[type] || 'default'
}

// 监听对话框打开，获取详情数据
watch(visible, (newValue) => {
  if (newValue && props.todoId) {
    fetchDetail()
  } else if (!newValue) {
    // 对话框关闭时延迟清空数据，等待关闭动画完成
    setTimeout(() => {
      detailData.value = null
    }, 300)
  }
})

// 获取详情数据
const fetchDetail = async () => {
  if (!props.todoId) return

  loading.value = true
  try {
    const response = await get(`todo/assign/detail/${props.todoId}/`)
    detailData.value = response
  } catch (error) {
    console.error('获取待办事项详情失败:', error)
    ElMessage.error('获取详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 编辑待办事项
const handleEdit = () => {
  emit('edit', detailData.value)
}

// 删除待办事项
const handleDelete = () => {
  emit('delete', detailData.value)
}
</script>

<style scoped>
.detail-section {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1.5rem;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background-color: #ec4899;
  margin-right: 10px;
  border-radius: 2px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.detail-item label {
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
  flex-shrink: 0;
}

.content-box {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  width: 100%;
  min-height: 60px;
  color: #374151;
  line-height: 1.5;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
