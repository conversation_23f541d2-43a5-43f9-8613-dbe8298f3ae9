<template>
  <div class="grid grid-cols-2 gap-6">
    <!-- 待处理事项 -->
    <el-card class="task-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">待处理事项</span>
          <el-badge :value="totalPendingTasks" class="item" type="danger" />
        </div>
      </template>
      <div class="task-list">
        <div
          v-for="(task, index) in pendingTasks"
          :key="index"
          class="task-item"
          @click="handleTaskClick(task)"
        >
          <div
            class="flex items-center justify-between p-4 rounded-lg hover:bg-red-50 cursor-pointer transition-all duration-300 border border-transparent hover:border-red-200 hover:shadow-sm"
          >
            <div class="flex items-center gap-3">
              <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span class="text-sm text-gray-700 font-medium">{{ task.text }}</span>
            </div>
            <el-badge :value="task.count" :type="task.urgent ? 'danger' : 'primary'" />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 今日提醒 -->
    <el-card class="reminder-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="card-title">今日提醒</span>
          <el-badge :value="totalReminders" class="item" type="info" />
        </div>
      </template>
      <div class="reminder-list">
        <div
          v-for="(reminder, index) in todayReminders"
          :key="index"
          class="reminder-item"
          @click="handleReminderClick(reminder)"
        >
          <div
            class="flex items-center justify-between p-4 rounded-lg hover:bg-pink-50 cursor-pointer transition-all duration-300 border border-transparent hover:border-pink-200 hover:shadow-sm"
          >
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center">
                <component :is="reminder.icon" class="w-4 h-4 text-pink-500" />
              </div>
              <span class="text-sm text-gray-700 font-medium">{{ reminder.text }}</span>
            </div>
            <el-tag size="small" type="info">{{ reminder.count }}</el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, markRaw } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, User, Calendar, Bell } from '@element-plus/icons-vue'

// 待处理事项数据
const pendingTasks = ref([
  {
    text: '待审核交接班记录',
    count: 3,
    urgent: true,
    type: 'handover',
    route: '/staff/handover',
  },
  {
    text: '待处理客诉',
    count: 1,
    urgent: true,
    type: 'complaint',
    route: '/feedback',
  },
  {
    text: '房间维护提醒',
    count: 2,
    urgent: true,
    type: 'maintenance',
    route: '/housekeeping/rooms',
  },
  {
    text: '今日健康检查',
    count: 5,
    urgent: true,
    type: 'health-check',
    route: '/maternal-records',
  },
])

// 今日提醒数据
const todayReminders = ref([
  {
    text: '新生儿满月体检',
    count: '2人',
    icon: markRaw(User),
    type: 'checkup',
    route: '/maternal-records',
  },
  {
    text: '预约入住提醒',
    count: '3人',
    icon: markRaw(Calendar),
    type: 'admission',
    route: '/housekeeping/checkin',
  },
  {
    text: '预约出院提醒',
    count: '1人',
    icon: markRaw(Clock),
    type: 'discharge',
    route: '/housekeeping/checkout',
  },
  {
    text: '排班提醒通知',
    count: '5人',
    icon: markRaw(Bell),
    type: 'schedule',
    route: '/staff/schedules',
  },
])

// 计算总数
const totalPendingTasks = computed(() => {
  return pendingTasks.value.reduce((total, task) => total + task.count, 0)
})

const totalReminders = computed(() => {
  return todayReminders.value.length
})

// 处理任务点击
const handleTaskClick = (task) => {
  ElMessage.info(`处理${task.text}`)
  // 这里可以添加路由跳转逻辑
  // router.push(task.route)
}

// 处理提醒点击
const handleReminderClick = (reminder) => {
  ElMessage.info(`查看${reminder.text}`)
  // 这里可以添加路由跳转逻辑
  // router.push(reminder.route)
}
</script>

<style scoped>
.task-card,
.reminder-card {
  height: 420px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.task-card:hover,
.reminder-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(231, 127, 161, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  color: rgb(231, 127, 161);
  font-size: 16px;
  font-weight: 600;
}

.task-list,
.reminder-list {
  height: 320px;
  overflow-y: auto;
}

.task-item,
.reminder-item {
  margin-bottom: 12px;
}

.task-item:last-child,
.reminder-item:last-child {
  margin-bottom: 0;
}

/* 自定义滚动条 */
.task-list::-webkit-scrollbar,
.reminder-list::-webkit-scrollbar {
  width: 6px;
}

.task-list::-webkit-scrollbar-track,
.reminder-list::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb,
.reminder-list::-webkit-scrollbar-thumb {
  background: rgba(231, 127, 161, 0.3);
  border-radius: 3px;
}

.task-list::-webkit-scrollbar-thumb:hover,
.reminder-list::-webkit-scrollbar-thumb:hover {
  background: rgba(231, 127, 161, 0.5);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
