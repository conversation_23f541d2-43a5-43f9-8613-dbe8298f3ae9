<template>
  <div class="mb-8">
    <div class="grid grid-cols-7 gap-3">
      <div
        v-for="(item, index) in quickMenuItems"
        :key="index"
        class="relative bg-white rounded-xl border border-gray-200 p-4 flex flex-col items-center justify-center gap-3 cursor-pointer transition-all duration-300 hover:shadow-lg hover:border-primary/40 hover:bg-gradient-to-br hover:from-primary/5 hover:to-primary/10 group transform hover:-translate-y-1"
        @click="handleMenuClick(item)"
      >
        <!-- 通知徽章 -->
        <div class="absolute -top-1 -right-1 z-10">
          <el-badge
            v-if="item.badge"
            :value="item.badge"
            :type="item.badge > 3 ? 'danger' : 'warning'"
          >
          </el-badge>
        </div>

        <!-- 图标容器 -->
        <div
          class="w-12 rounded-full bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center group-hover:from-primary/20 group-hover:to-primary/30 transition-all duration-300"
        >
          <component
            :is="item.icon"
            class="w-6 h-6 text-primary group-hover:scale-110 transition-transform duration-300"
          />
        </div>

        <!-- 标签 -->
        <span
          class="text-xs text-center text-gray-700 font-medium leading-tight group-hover:text-primary transition-colors duration-300"
        >
          {{ item.label }}
        </span>

        <!-- 装饰性底部线条 -->
        <div
          class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-primary to-primary/60 group-hover:w-8 transition-all duration-300"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  User,
  House,
  ChatLineRound,
  Calendar,
  Document,
  TrendCharts,
  CreditCard,
  OfficeBuilding,
  Plus,
  SwitchButton,
  Refresh,
  Star,
} from '@element-plus/icons-vue'

const router = useRouter()

const quickMenuItems = ref([
  {
    icon: markRaw(House),
    label: '快速入住',
    active: true,
    route: '/housekeeping/checkin',
    badge: 5,
  },
  { icon: markRaw(OfficeBuilding), label: '房务查看', active: false, route: '/housekeeping/rooms' },
  { icon: markRaw(User), label: '母婴记录', active: false, route: '/maternal-records' },
  { icon: markRaw(CreditCard), label: '结算管理', active: false, route: '/billing' },
  {
    icon: markRaw(SwitchButton),
    label: '办理退房',
    active: false,
    route: '/housekeeping/checkout',
  },
  { icon: markRaw(Plus), label: '新增客户', active: false, route: '/maternal-records/customers' },
  { icon: markRaw(User), label: '访客登记', active: false, route: '/visitor-management' },
  { icon: markRaw(Calendar), label: '排班管理', active: false, route: '/staff/schedules' },
  { icon: markRaw(Refresh), label: '交接班', active: true, route: '/staff/handover', badge: 2 },
  { icon: markRaw(Star), label: '清洁管理', active: false, route: '/infection-control' },
  { icon: markRaw(ChatLineRound), label: '客户反馈', active: true, route: '/feedback', badge: 2 },
  { icon: markRaw(Document), label: '健康宣教', active: false, route: '/health-education' },
  { icon: markRaw(Document), label: '知识库', active: false, route: '/knowledge-base' },
  { icon: markRaw(TrendCharts), label: '报表导出', active: false, route: '/reports' },
])

const handleMenuClick = (item) => {
  if (item.route) {
    router.push(item.route)
  } else {
    ElMessage.info(`跳转到${item.label}`)
  }
}
</script>

<style scoped>
.text-primary {
  color: rgb(231, 127, 161);
}

.border-primary\/40 {
  border-color: rgba(231, 127, 161, 0.4);
}

.from-primary\/5 {
  --tw-gradient-from: rgba(231, 127, 161, 0.05);
}

.to-primary\/10 {
  --tw-gradient-to: rgba(231, 127, 161, 0.1);
}

.from-primary\/10 {
  --tw-gradient-from: rgba(231, 127, 161, 0.1);
}

.to-primary\/20 {
  --tw-gradient-to: rgba(231, 127, 161, 0.2);
}

.from-primary\/20 {
  --tw-gradient-from: rgba(231, 127, 161, 0.2);
}

.to-primary\/30 {
  --tw-gradient-to: rgba(231, 127, 161, 0.3);
}

.from-primary {
  --tw-gradient-from: rgb(231, 127, 161);
}

.to-primary\/60 {
  --tw-gradient-to: rgba(231, 127, 161, 0.6);
}
</style>
