<template>
  <div class="mb-8">
    <div v-loading="loading" class="grid grid-cols-4 gap-4">
      <el-card
        v-for="(stat, index) in statistics"
        :key="index"
        class="stat-card"
        :body-style="{ padding: '20px' }"
        shadow="hover"
      >
        <div class="relative overflow-hidden">
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-20 h-20 opacity-10">
            <div
              :class="`w-full h-full rounded-full bg-${stat.color}-500 transform translate-x-6 -translate-y-6`"
            ></div>
          </div>

          <div class="relative z-10">
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <div :class="`text-sm mb-2 font-medium text-${stat.color}-600`">
                  {{ stat.title }}
                </div>
                <div class="flex items-baseline gap-2 mb-2">
                  <span :class="`text-3xl font-bold text-${stat.color}-600`">
                    {{ stat.mainValue }}
                  </span>
                  <span v-if="stat.unit" :class="`text-lg font-medium text-gray-500`">
                    {{ stat.unit }}
                  </span>
                </div>
                <div class="text-xs text-gray-500 font-medium">{{ stat.description }}</div>

                <!-- 额外信息显示 -->
                <div v-if="stat.extraInfo" class="mt-2 space-y-1">
                  <div
                    v-for="(info, infoIndex) in stat.extraInfo"
                    :key="infoIndex"
                    class="flex justify-between items-center text-xs"
                  >
                    <span class="text-gray-500">{{ info.label }}</span>
                    <span :class="`font-semibold text-${info.color || 'gray'}-600`">
                      {{ info.value }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 迷你图表区域 -->
              <div v-if="stat.showChart" class="w-16 h-16 flex items-center justify-center ml-4">
                <div
                  :class="`w-14 h-14 rounded-full bg-gradient-to-br from-${stat.color}-100 to-${stat.color}-200 flex items-center justify-center shadow-inner`"
                >
                  <div :class="`text-xs font-bold text-${stat.color}-600`">
                    {{ stat.chartPercent }}%
                  </div>
                </div>
              </div>
            </div>

            <!-- 进度条 -->
            <div
              v-if="stat.progress !== undefined"
              class="w-full bg-gray-200 rounded-full h-1.5 mt-3"
            >
              <div
                :class="`bg-gradient-to-r from-${stat.color}-400 to-${stat.color}-600 h-1.5 rounded-full transition-all duration-1000 ease-out`"
                :style="{ width: `${stat.progress}%` }"
              ></div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 第二行卡片 -->
    <div v-loading="loading" class="grid grid-cols-4 gap-4 mt-4">
      <el-card
        v-for="(stat, index) in secondRowStatistics"
        :key="index"
        class="stat-card"
        :body-style="{ padding: '20px' }"
        shadow="hover"
      >
        <div class="relative overflow-hidden">
          <!-- 背景装饰 -->
          <div class="absolute top-0 right-0 w-20 h-20 opacity-10">
            <div
              :class="`w-full h-full rounded-full bg-${stat.color}-500 transform translate-x-6 -translate-y-6`"
            ></div>
          </div>

          <div class="relative z-10">
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <div :class="`text-sm mb-2 font-medium text-${stat.color}-600`">
                  {{ stat.title }}
                </div>
                <div class="flex items-baseline gap-2 mb-2">
                  <span :class="`text-3xl font-bold text-${stat.color}-600`">
                    {{ stat.mainValue }}
                  </span>
                  <span v-if="stat.unit" :class="`text-lg font-medium text-gray-500`">
                    {{ stat.unit }}
                  </span>
                </div>
                <div class="text-xs text-gray-500 font-medium">{{ stat.description }}</div>

                <!-- 额外信息显示 -->
                <div v-if="stat.extraInfo" class="mt-2 space-y-1">
                  <div
                    v-for="(info, infoIndex) in stat.extraInfo"
                    :key="infoIndex"
                    class="flex justify-between items-center text-xs"
                  >
                    <span class="text-gray-500">{{ info.label }}</span>
                    <span :class="`font-semibold text-${info.color || 'gray'}-600`">
                      {{ info.value }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 迷你图表区域 -->
              <div v-if="stat.showChart" class="w-16 h-16 flex items-center justify-center ml-4">
                <div
                  :class="`w-14 h-14 rounded-full bg-gradient-to-br from-${stat.color}-100 to-${stat.color}-200 flex items-center justify-center shadow-inner`"
                >
                  <div :class="`text-xs font-bold text-${stat.color}-600`">
                    {{ stat.chartPercent }}%
                  </div>
                </div>
              </div>
            </div>

            <!-- 进度条 -->
            <div
              v-if="stat.progress !== undefined"
              class="w-full bg-gray-200 rounded-full h-1.5 mt-3"
            >
              <div
                :class="`bg-gradient-to-r from-${stat.color}-400 to-${stat.color}-600 h-1.5 rounded-full transition-all duration-1000 ease-out`"
                :style="{ width: `${stat.progress}%` }"
              ></div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 定义props
const props = defineProps({
  data: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 计算第一行统计卡片数据
const statistics = computed(() => {
  if (!props.data) {
    // 返回骨架数据用于loading状态
    return Array(4)
      .fill(null)
      .map(() => ({
        title: '加载中...',
        mainValue: '--',
        description: '--',
        color: 'primary',
        progress: 0,
      }))
  }

  const data = props.data

  return [
    {
      title: '当前入住率',
      mainValue: `${data.current_occupancy_rate.rate}`,
      unit: '%',
      description: `${data.current_occupancy_rate.occupied_rooms}/${data.current_occupancy_rate.total_rooms} 间房已入住`,
      color: 'primary',
      progress: data.current_occupancy_rate.rate,
      showChart: true,
      chartPercent: Math.round(data.current_occupancy_rate.rate),
    },
    {
      title: '今日入住办理',
      mainValue: data.today_checkin_checkout.actual_checkin,
      unit: '人',
      description: `预期 ${data.today_checkin_checkout.expected_checkin} 人办理`,
      color: 'green',
      progress:
        data.today_checkin_checkout.expected_checkin > 0
          ? (data.today_checkin_checkout.actual_checkin /
              data.today_checkin_checkout.expected_checkin) *
            100
          : data.today_checkin_checkout.actual_checkin > 0
            ? 100
            : 0,
    },
    {
      title: '今日退房办理',
      mainValue: data.today_checkin_checkout.actual_checkout,
      unit: '人',
      description: `预期 ${data.today_checkin_checkout.expected_checkout} 人退房`,
      color: 'orange',
      progress:
        data.today_checkin_checkout.expected_checkout > 0
          ? (data.today_checkin_checkout.actual_checkout /
              data.today_checkin_checkout.expected_checkout) *
            100
          : data.today_checkin_checkout.actual_checkout > 0
            ? 100
            : 0,
    },
    {
      title: '在住产妇/新生儿',
      mainValue: `${data.current_mothers_count}/${data.current_newborns_count}`,
      unit: '位',
      description: '产妇/新生儿总数',
      color: 'pink',
      extraInfo: [
        { label: '产妇', value: `${data.current_mothers_count}位`, color: 'pink' },
        { label: '新生儿', value: `${data.current_newborns_count}位`, color: 'primary' },
      ],
    },
  ]
})

// 计算第二行统计卡片数据
const secondRowStatistics = computed(() => {
  if (!props.data) {
    return Array(4)
      .fill(null)
      .map(() => ({
        title: '加载中...',
        mainValue: '--',
        description: '--',
        color: 'primary',
        progress: 0,
      }))
  }

  const data = props.data

  return [
    {
      title: '待入住客户',
      mainValue: data.pending_checkin_customers.total_pending,
      unit: '人',
      description: '总待入住客户数',
      color: 'purple',
      extraInfo: [
        {
          label: '今日待入住',
          value: `${data.pending_checkin_customers.today_pending}人`,
          color: 'purple',
        },
        {
          label: '逾期待入住',
          value: `${data.pending_checkin_customers.overdue_pending}人`,
          color: 'red',
        },
      ],
      progress:
        data.pending_checkin_customers.total_pending > 0
          ? ((data.pending_checkin_customers.total_pending -
              data.pending_checkin_customers.overdue_pending) /
              data.pending_checkin_customers.total_pending) *
            100
          : 0,
    },
    {
      title: '分娩待入住',
      mainValue: data.delivery_pending_checkin.delivery_pending_7days,
      unit: '人',
      description: '7天内待入住',
      color: 'teal',
      extraInfo: [
        {
          label: '逾期未入住',
          value: `${data.delivery_pending_checkin.delivery_overdue}人`,
          color: 'red',
        },
      ],
    },
    {
      title: '签约情况',
      mainValue: data.contracted_pending_checkin.contracted_pending,
      unit: '人',
      description: '已签约待入住',
      color: 'green',
      extraInfo: [
        {
          label: '未签约',
          value: `${data.contracted_pending_checkin.uncontracted_pending}人`,
          color: 'orange',
        },
      ],
    },
    {
      title: '房间使用状况',
      mainValue: data.room_usage_status.total_stats.total_rooms,
      unit: '间',
      description: '总房间数',
      color: 'purple',
      extraInfo: [
        {
          label: '已入住',
          value: `${data.room_usage_status.total_stats.checked_in}间`,
          color: 'green',
        },
        {
          label: '可入住',
          value: `${data.room_usage_status.total_stats.available}间`,
          color: 'primary',
        },
        {
          label: '不可用',
          value: `${data.room_usage_status.total_stats.unavailable_maintenance + data.room_usage_status.total_stats.unavailable_cleaning + data.room_usage_status.total_stats.unavailable_switch_room + data.room_usage_status.total_stats.unavailable_other}间`,
          color: 'red',
        },
      ],
    },
  ]
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(231, 127, 161, 0.3);
}

.text-primary-600 {
  color: rgb(207, 114, 145);
}

.text-primary-500 {
  color: rgb(231, 127, 161);
}

.text-green-600 {
  color: #059669;
}

.text-green-500 {
  color: #10b981;
}

.text-orange-600 {
  color: #d97706;
}

.text-orange-500 {
  color: #f59e0b;
}

.text-pink-600 {
  color: #db2777;
}

.text-pink-500 {
  color: #ec4899;
}

.text-red-500 {
  color: #ef4444;
}

.text-red-600 {
  color: #dc2626;
}

.text-purple-600 {
  color: #9333ea;
}

.text-purple-500 {
  color: #a855f7;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-indigo-500 {
  color: #6366f1;
}

.text-teal-600 {
  color: #0d9488;
}

.text-teal-500 {
  color: #14b8a6;
}

.bg-primary-500 {
  background-color: rgb(231, 127, 161);
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-orange-500 {
  background-color: #f59e0b;
}

.bg-pink-500 {
  background-color: #ec4899;
}

.bg-purple-500 {
  background-color: #a855f7;
}

.bg-indigo-500 {
  background-color: #6366f1;
}

.bg-teal-500 {
  background-color: #14b8a6;
}

.from-primary-100 {
  --tw-gradient-from: rgba(231, 127, 161, 0.1);
}

.to-primary-200 {
  --tw-gradient-to: rgba(231, 127, 161, 0.2);
}

.from-green-100 {
  --tw-gradient-from: rgba(16, 185, 129, 0.1);
}

.to-green-200 {
  --tw-gradient-to: rgba(16, 185, 129, 0.2);
}

.from-orange-100 {
  --tw-gradient-from: rgba(245, 158, 11, 0.1);
}

.to-orange-200 {
  --tw-gradient-to: rgba(245, 158, 11, 0.2);
}

.from-pink-100 {
  --tw-gradient-from: rgba(236, 72, 153, 0.1);
}

.to-pink-200 {
  --tw-gradient-to: rgba(236, 72, 153, 0.2);
}

.from-purple-100 {
  --tw-gradient-from: rgba(168, 85, 247, 0.1);
}

.to-purple-200 {
  --tw-gradient-to: rgba(168, 85, 247, 0.2);
}

.from-indigo-100 {
  --tw-gradient-from: rgba(99, 102, 241, 0.1);
}

.to-indigo-200 {
  --tw-gradient-to: rgba(99, 102, 241, 0.2);
}

.from-teal-100 {
  --tw-gradient-from: rgba(20, 184, 166, 0.1);
}

.to-teal-200 {
  --tw-gradient-to: rgba(20, 184, 166, 0.2);
}

.from-primary-400 {
  --tw-gradient-from: rgba(231, 127, 161, 0.8);
}

.to-primary-600 {
  --tw-gradient-to: rgb(207, 114, 145);
}

.from-green-400 {
  --tw-gradient-from: rgba(16, 185, 129, 0.8);
}

.to-green-600 {
  --tw-gradient-to: #059669;
}

.from-orange-400 {
  --tw-gradient-from: rgba(245, 158, 11, 0.8);
}

.to-orange-600 {
  --tw-gradient-to: #d97706;
}

.from-pink-400 {
  --tw-gradient-from: rgba(236, 72, 153, 0.8);
}

.to-pink-600 {
  --tw-gradient-to: #db2777;
}

.from-purple-400 {
  --tw-gradient-from: rgba(168, 85, 247, 0.8);
}

.to-purple-600 {
  --tw-gradient-to: #9333ea;
}

.from-indigo-400 {
  --tw-gradient-from: rgba(99, 102, 241, 0.8);
}

.to-indigo-600 {
  --tw-gradient-to: #4f46e5;
}

.from-teal-400 {
  --tw-gradient-from: rgba(20, 184, 166, 0.8);
}

.to-teal-600 {
  --tw-gradient-to: #0d9488;
}
</style>
