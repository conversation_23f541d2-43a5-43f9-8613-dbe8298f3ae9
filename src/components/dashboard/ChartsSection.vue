<template>
  <div class="mb-8">
    <div v-loading="loading" class="grid grid-cols-2 gap-6 mb-6">
      <!-- 入住率趋势图 -->
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="chart-title">入住率趋势</span>
            <el-tag size="small" type="info">近7天</el-tag>
          </div>
        </template>
        <div class="chart-container">
          <v-chart v-if="!loading && data" :option="trendChartOption" autoresize />
          <div
            v-else-if="!loading && !data"
            class="flex items-center justify-center h-full text-gray-400"
          >
            暂无数据
          </div>
        </div>
      </el-card>

      <!-- 客户来源分析 -->
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="chart-title">客户来源分析</span>
            <el-tag size="small" type="success"
              >总计 {{ data?.customer_source_analysis?.total_customers || 0 }} 人</el-tag
            >
          </div>
        </template>
        <div class="chart-container">
          <v-chart v-if="!loading && data" :option="customerSourceOption" autoresize />
          <div
            v-else-if="!loading && !data"
            class="flex items-center justify-center h-full text-gray-400"
          >
            暂无数据
          </div>
        </div>
      </el-card>
    </div>

    <!-- 第二行图表 -->
    <div v-loading="loading" class="grid grid-cols-2 gap-6">
      <!-- 房间使用状况 -->
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="chart-title">房间使用状况</span>
            <el-tag size="small" type="warning"
              >总计 {{ data?.room_usage_status?.total_stats?.total_rooms || 0 }} 间</el-tag
            >
          </div>
        </template>
        <div class="chart-container">
          <v-chart v-if="!loading && data" :option="roomUsageOption" autoresize />
          <div
            v-else-if="!loading && !data"
            class="flex items-center justify-center h-full text-gray-400"
          >
            暂无数据
          </div>
        </div>
      </el-card>

      <!-- 楼层分布状况 -->
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="chart-title">楼层房间分布</span>
            <el-tag size="small" type="primary"
              >{{ data?.room_usage_status?.floor_breakdown?.length || 0 }} 个楼层</el-tag
            >
          </div>
        </template>
        <div class="chart-container">
          <v-chart v-if="!loading && data" :option="floorDistributionOption" autoresize />
          <div
            v-else-if="!loading && !data"
            class="flex items-center justify-center h-full text-gray-400"
          >
            暂无数据
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { use } from 'echarts/core'
import { ROOM_STATUS_MAP, CHECK_IN_SOURCE_MAP } from '@/utils/constants'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册必要的组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
])

// 定义props
const props = defineProps({
  data: {
    type: Object,
    default: null,
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

// 入住率趋势图配置
const trendChartOption = computed(() => {
  if (!props.data?.occupancy_trend_7days) {
    return {}
  }

  const trendData = props.data.occupancy_trend_7days

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E77FA1',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
      formatter: (params) => {
        const data = params[0]
        const dataItem = trendData[data.dataIndex]
        return `${dataItem.weekday} (${dataItem.date_display})<br/>
                入住率: ${data.value}%<br/>
                已入住: ${dataItem.occupied_rooms}间<br/>
                总房间: ${dataItem.total_rooms}间`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: trendData.map((item) => item.date_display),
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      show: false,
      max: 100,
    },
    series: [
      {
        data: trendData.map((item) => item.occupancy_rate),
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#E77FA1',
          width: 3,
        },
        itemStyle: {
          color: '#E77FA1',
          borderWidth: 2,
          borderColor: '#fff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(231, 127, 161, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(231, 127, 161, 0.05)',
              },
            ],
          },
        },
      },
    ],
  }
})

// 客户来源饼图配置
const customerSourceOption = computed(() => {
  if (!props.data?.customer_source_analysis) {
    return {}
  }

  const sourceData = props.data.customer_source_analysis.source_breakdown

  // 使用constants.js中的映射和定义颜色（避免使用蓝色，使用暖色调）
  const colorMap = {
    IN_HOSPITAL: '#E77FA1',    // 主粉红色
    OUT_HOSPITAL: '#f59e0b',   // 琥珀色
    OTHER: '#14b8a6',          // 青绿色
  }

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}人 ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E77FA1',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
    },
    legend: {
      bottom: '8%',
      left: 'center',
      textStyle: {
        color: '#666',
        fontSize: 12,
      },
    },
    series: [
      {
        name: '客户来源',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '40%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            color: '#333',
            formatter: '{b}\n{c}人\n{d}%',
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        labelLine: {
          show: false,
        },
        data: sourceData.map((item) => ({
          value: item.count,
          name: CHECK_IN_SOURCE_MAP[item.source_code]?.text || item.source_name,
          itemStyle: {
            color: colorMap[item.source_code] || '#6b7280',
          },
        })),
      },
    ],
  }
})

// 房间使用状况饼图配置
const roomUsageOption = computed(() => {
  if (!props.data?.room_usage_status) {
    return {}
  }

  const roomData = props.data.room_usage_status.total_stats

  const usageData = [
    { name: '已入住', value: roomData.checked_in, color: ROOM_STATUS_MAP.CHECKED_IN.color },
    { name: '预订中', value: roomData.reserved, color: ROOM_STATUS_MAP.RESERVED.color },
    { name: '可入住', value: roomData.available, color: ROOM_STATUS_MAP.AVAILABLE.color },
    { name: '维护中', value: roomData.unavailable_maintenance, color: ROOM_STATUS_MAP.UNAVAILABLE_MAINTENANCE.color },
    { name: '清洁中', value: roomData.unavailable_cleaning, color: ROOM_STATUS_MAP.UNAVAILABLE_CLEANING.color },
    { name: '换房中', value: roomData.unavailable_switch_room, color: ROOM_STATUS_MAP.UNAVAILABLE_SWITCH_ROOM.color },
    { name: '其他', value: roomData.unavailable_other, color: ROOM_STATUS_MAP.UNAVAILABLE_OTHER.color },
  ].filter((item) => item.value > 0) // 只显示有数据的项

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}间 ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E77FA1',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
    },
    legend: {
      bottom: '8%',
      left: 'center',
      textStyle: {
        color: '#666',
        fontSize: 11,
      },
    },
    series: [
      {
        name: '房间状况',
        type: 'pie',
        radius: ['30%', '70%'],
        center: ['50%', '40%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold',
            color: '#333',
            formatter: '{b}\n{c}间',
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        labelLine: {
          show: false,
        },
        data: usageData.map((item) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
          },
        })),
      },
    ],
  }
})

// 楼层分布柱状图配置
const floorDistributionOption = computed(() => {
  if (!props.data?.room_usage_status?.floor_breakdown) {
    return {}
  }

  const floorData = props.data.room_usage_status.floor_breakdown

  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#E77FA1',
      borderWidth: 1,
      textStyle: {
        color: '#333',
      },
      formatter: (params) => {
        const dataIndex = params[0].dataIndex
        const floor = floorData[dataIndex]
        return `${floor.floor_name}<br/>
                总房间: ${floor.total_rooms}间<br/>
                已入住: ${floor.checked_in}间<br/>
                预订中: ${floor.reserved}间<br/>
                可入住: ${floor.available}间<br/>
                换房中: ${floor.unavailable_switch_room}间`
      },
    },
    grid: {
      left: '8%',
      right: '4%',
      bottom: '8%',
      top: '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: floorData.map((item) => item.floor_name),
      axisLine: {
        lineStyle: { color: '#E5E7EB' },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: '#F3F4F6',
          type: 'dashed',
        },
      },
    },
    series: [
      {
        name: '已入住',
        type: 'bar',
        stack: 'total',
        data: floorData.map((item) => item.checked_in),
        itemStyle: {
          color: ROOM_STATUS_MAP.CHECKED_IN.color,
        },
        barWidth: '60%',
      },
      {
        name: '预订中',
        type: 'bar',
        stack: 'total',
        data: floorData.map((item) => item.reserved),
        itemStyle: {
          color: ROOM_STATUS_MAP.RESERVED.color,
        },
      },
      {
        name: '可入住',
        type: 'bar',
        stack: 'total',
        data: floorData.map((item) => item.available),
        itemStyle: {
          color: ROOM_STATUS_MAP.AVAILABLE.color,
        },
      },
      {
        name: '不可用',
        type: 'bar',
        stack: 'total',
        data: floorData.map(
          (item) =>
            item.unavailable_maintenance +
            item.unavailable_cleaning +
            item.unavailable_switch_room +
            item.unavailable_other,
        ),
        itemStyle: {
          color: '#6b7280', // 使用灰色表示不可用状态的总和
        },
      },
    ],
  }
})
</script>

<style scoped>
.chart-card {
  height: 320px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border-color: rgba(231, 127, 161, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  color: rgb(231, 127, 161);
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  height: 220px;
  width: 100%;
}
</style>
