{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "allowJs": true, "checkJs": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*", "src/**/*.vue", "*.js", "*.vue"], "exclude": ["node_modules", "dist"]}