参考 @TransferView.vue @TransferTable.vue @TransferDetailDialog.vue @TransferFormDialog.vue ,完成此页面及其子组件,保持UI风格和代码结构一致,
列表接口:customer-service/disinfection/list/
有参数sk,clean_area,clean_type,disinfection_document（消毒规范文档名字）
数据结构:{
        "list": [
            {
                "rid": "687e3d72-8289-4c88-86e5-98756803569d",
                "clean_area": "产房A区",
                "clean_type": "日常清洁",
                "disinfection_document_name": "产房消毒操作规范",
                "clean_time": "2025-07-11 14:30:00",
                "cleaner": "张三",
                "disinfectant_info": "75%酒精，500ml",
                "result": "PENDING",
                "supervisor": "李四"
            },
            {
                "rid": "664a77a5-089c-4505-8695-5f8e8990e3fa",
                "clean_area": "产房A区",
                "clean_type": "日常清洁",
                "disinfection_document_name": "产房消毒操作规范",
                "clean_time": "2025-07-11 14:30:00",
                "cleaner": "张三",
                "disinfectant_info": "75%酒精，500ml",
                "result": "PENDING",
                "supervisor": "李四"
            }
        ],
        "page": 1,
        "page_size": 10,
        "total_count": 2,
        "total_page": 1
    }
详情接口:customer-service/disinfection/detail/687e3d72-8289-4c88-86e5-98756803569d/:{
        "rid": "687e3d72-8289-4c88-86e5-98756803569d",
        "clean_area": "产房A区",
        "clean_type": "日常清洁",
        "disinfection_document": {
            "rid": "415c2edf-2cca-4115-8e5d-2fe3d960b20f",
            "name": "产房消毒操作规范",
            "version": "v2.1.0",
            "publish_date": "2024-12-01",
            "applicable_scope": "适用于产房、新生儿护理室、母婴同室等区域的日常消毒和特殊情况下的终末消毒操作。包括但不限于床位、医疗设备、空气、地面、墙面等的消毒处理。",
            "status": "IN_EFFECT",
            "status_display": "生效中",
            "description": "本规范详细说明了产房区域的消毒流程、消毒剂选择、操作步骤、质量控制要求等内容。旨在确保产房环境的无菌状态，预防医院感染，保障母婴安全。规范包含日常消毒、术前消毒、术后消毒、终末消毒等不同场景的具体操作指导。",
            "creator_name": "张桂梅",
            "created_at": "2025-07-11 10:05:32",
            "updated_at": "2025-07-11 10:05:57"
        },
        "clean_time": "2025-07-11 14:30:00",
        "cleaner": "张三",
        "disinfectant_info": "75%酒精，500ml",
        "disinfection_process": "先用清水擦拭表面，再用75%酒精进行消毒，最后用干净抹布擦干。重点清洁了床铺、桌面、门把手等高频接触区域。",
        "result": "PENDING",
        "result_display": "等待中",
        "supervisor": "李四",
        "remark": "清洁过程中发现地面有轻微污渍，已彻底清除。建议增加清洁频次。",
        "creator_name": "张桂梅",
        "created_at": "2025-07-11 10:06:43",
        "updated_at": "2025-07-11 10:08:05"
    }

创建接口post:customer-service/disinfection/create/:{
  // 清洁区域
  "clean_area": "产房A区",
  
  // 清洁类型
  "clean_type": "日常清洁",
  
  // 参照消毒规范文档的rid
  "disinfection_document": "415c2edf-2cca-4115-8e5d-2fe3d960b20f",
  
  // 清洁时间 (ISO 8601格式)
  "clean_time": "2025-07-11 14:30:00",
  
  // 清洁人
  "cleaner": "张三",
  
  // 监督人
  "supervisor": "李四",
  
  // 清洁结果 (枚举值: NOT_EXECUTED/PENDING/PASS/FAIL)
  "result": "PENDING",
  
  // 消毒剂名称/浓度/用量
  "disinfectant_info": "75%酒精，500ml",
  
  // 清洁/消毒过程简述
  "disinfection_process": "先用清水擦拭表面，再用75%酒精进行消毒，最后用干净抹布擦干。重点清洁了床铺、桌面、门把手等高频接触区域。",
  
  // 备注信息
  "remark": "清洁过程中发现地面有轻微污渍，已彻底清除。建议增加清洁频次。"
}
更新接口put:customer-service/disinfection/update/687e3d72-8289-4c88-86e5-98756803569d/
删除接口put:customer-service/disinfection-document/delete/4d94ef85-2bd9-479b-a899-8b8a3cd1b7db/

枚举可到 @constants.js 中查找使用
